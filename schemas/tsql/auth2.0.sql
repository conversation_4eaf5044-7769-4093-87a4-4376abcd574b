CREATE TABLE `oauth_access_tokens` (
   `access_token` varchar(150) NOT NULL DEFAULT '' COMMENT 'token',
   `expires` timestamp NULL DEFAULT NULL COMMENT '过期时间',
   `scope` varchar(255) DEFAULT NULL COMMENT '授权scope',
   `client_id` varchar(80) NOT NULL DEFAULT '' COMMENT '客户端ID',
   `user_id` varchar(20) DEFAULT NULL,
   `revoked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被收回',
   `created_at` timestamp NULL DEFAULT NULL,
   `updated_at` timestamp NULL DEFAULT NULL,
   PRIMARY KEY (`access_token`),
   KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `oauth_authorization_codes` (
     `authorization_code` varchar(150) NOT NULL DEFAULT '',
     `expires` datetime DEFAULT NULL,
     `redirect_url` varchar(255) DEFAULT NULL,
     `scope` varchar(255) DEFAULT NULL,
     `client_id` varchar(80) NOT NULL DEFAULT '',
     `user_id` int(11) unsigned DEFAULT NULL,
     `created_at` datetime DEFAULT NULL,
     `updated_at` datetime DEFAULT NULL,
     `revoked` tinyint(1) NOT NULL DEFAULT '0',
     PRIMARY KEY (`authorization_code`),
     KEY `client_id` (`client_id`),
     KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `oauth_clients` (
     `client_id` varchar(80) NOT NULL,
     `name` varchar(255) DEFAULT NULL,
     `client_secret` varchar(255) DEFAULT NULL,
     `redirect_uri` varchar(255) DEFAULT NULL,
     `grant_types` varchar(80) DEFAULT NULL,
     `scope` varchar(255) DEFAULT NULL,
     `created_at` datetime DEFAULT NULL,
     `updated_at` datetime DEFAULT NULL,
     PRIMARY KEY (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `oauth_jwt` (
     `client_id` varchar(80) NOT NULL DEFAULT '',
     `subject` varchar(80) DEFAULT NULL,
     `public_key` text,
     `created_at` datetime DEFAULT NULL,
     `updated_at` datetime DEFAULT NULL,
     PRIMARY KEY (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `oauth_refresh_tokens` (
    `refresh_token` varchar(150) NOT NULL DEFAULT '',
    `expires` datetime DEFAULT NULL,
    `scope` varchar(255) DEFAULT NULL,
    `client_id` varchar(80) NOT NULL DEFAULT '',
    `user_id` varchar(20) DEFAULT NULL,
    `revoked` tinyint(1) NOT NULL DEFAULT '0',
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    PRIMARY KEY (`refresh_token`),
    KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `oauth_scopes` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `scope` varchar(80) DEFAULT NULL,
    `is_default` tinyint(1) DEFAULT NULL,
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `scope` (`scope`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;