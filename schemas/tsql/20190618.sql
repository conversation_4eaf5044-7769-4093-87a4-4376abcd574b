
CREATE TABLE `business_trip` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `reason_application` varchar(50) DEFAULT '' COMMENT '出差理由',
  `traffic_tools` tinyint(4) DEFAULT '0' COMMENT '交通工具 1飞机 2火车 3汽车 4其他',
  `other_traffic_name` varchar(20) DEFAULT '' COMMENT '其他交通工具名称',
  `oneway_or_roundtrip` tinyint(4) DEFAULT '0' COMMENT '单程1往返2',
  `departure_city` varchar(50) DEFAULT '' COMMENT '出发城市',
  `destination_city` varchar(50) DEFAULT '' COMMENT '目的城市',
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `days_num` int(11) DEFAULT '1' COMMENT '出差天数',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `reason` varchar(500) DEFAULT '' COMMENT '驳回原因',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '审批状态 1 待审核 2 审核通过 3 驳回 4 撤销',
  `is_push` tinyint(4) DEFAULT '0' COMMENT '推送',
  `apply_user` int(11) DEFAULT '0' COMMENT '申请人',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `apply_user` (`apply_user`),
  KEY `approve_user` (`approve_user`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='出差记录表'



CREATE TABLE `business_trip_img` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `business_trip_id` bigint(10) unsigned DEFAULT '0' COMMENT '出差记录编号',
  `img_path` varchar(255) DEFAULT '' COMMENT '图片路径',
  `deleted` tinyint(4) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `business_trip_id` (`business_trip_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='出差申请图片表'

