
CREATE TABLE `staff_audit_tool_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `staff_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '分类 1补卡 2请假 3申请LH费 加班类型 5=工作日，6=节假日加班，7=晚班 8-节假日正常上班',
  `original_type` tinyint(3) unsigned DEFAULT '0' COMMENT '原始状态 1 待审核 2 审核通过 3 驳回',
  `original_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联id type 1234-staff_audit 5678-hr_overtime',
  `operator` int(11) DEFAULT '0' COMMENT '操作员工id',
  `operator_name` varchar(80) DEFAULT '' COMMENT '操作人名称',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='员工申请 撤销工具日志表';

有sql
alter table staff_audit modify `status` tinyint(1) DEFAULT '1' COMMENT '补卡状态 1 申请中 2 审核通过 3 驳回 4 撤销';
alter table hr_overtime modify column `state` int(2) DEFAULT NULL COMMENT '状态：1=待审批，2=已同意，3=驳回 4=撤销';

alter table hr_overtime add column `is_anticipate` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0- 非预申请（后补申请） 1- 预申请';
加班 增加 打卡校验 和 节假日正常上班类型
sql
alter table hr_overtime modify `type` int(2) DEFAULT NULL COMMENT '加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班';
