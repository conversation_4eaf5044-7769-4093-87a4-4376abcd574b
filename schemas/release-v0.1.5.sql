-- Network Operations 子审批流
-- 1. 审批节点配置
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (215, 2, '1', '申请节点', 0, 1, NULL, NULL, 1, NULL, 0, '2021-04-30 06:33:34', '2021-04-30 07:46:41');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (216, 2, '1', 'DM审批节点', 1, 18, 1, NULL, 1, NULL, 0, '2021-04-30 06:34:23', '2021-05-07 08:16:33');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (217, 2, '1', 'AM审批节点', 1, 19, 1, NULL, 1, NULL, 0, '2021-04-30 06:34:35', '2021-05-07 08:16:35');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (218, 2, '1', 'WFM审批节点', 1, 2, NULL, '38642,44862,48076,49000,51247,59552,22574,23458', 1, NULL, 0, '2021-04-30 06:34:43', '2021-05-10 03:31:13');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (219, 2, '1', '所属部门审批节点', 1, 4, 2, NULL, 1, NULL, 0, '2021-04-30 06:35:01', '2021-04-30 07:46:52');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (220, 2, '1', '一级部门审批节点', 1, 4, 1, NULL, 1, NULL, 0, '2021-04-30 06:35:11', '2021-05-06 06:38:00');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (221, 2, '1', '结束节点', 99, 1, NULL, NULL, 1, NULL, 0, '2021-04-30 06:35:32', '2021-04-30 07:46:58');


-- 2. 审批节点关系创建
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 216, '$p1 && $p2 != -1', 'isCommonStaff,hcWorkNode', '（申请人为普通员工 或 WFM部门员工 或 网点主管） 且 工作地点为网点: 开始->DM', 10, 0, '2021-05-08 02:39:22', '2021-05-06 06:47:22');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 217, '$p1 && $p2 != -1', 'isDM,hcWorkNode', '申请人为dm 且 工作地点为网点：开始->AM', 10, 0, '2021-05-08 02:39:16', '2021-05-06 06:47:40');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 218, '$p1 && $p2 != -1', 'isAM,hcWorkNode', '申请人为am 且 工作地点为网点：开始->WFM', 10, 0, '2021-05-08 02:39:32', '2021-05-06 06:47:49');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 219, '$p1', 'isBelowNodeDepartmentManager', '申请人为所属以下部门负责人：开始->所属部门负责人', 10, 0, '2021-05-08 02:39:52', '2021-05-06 06:48:57');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 220, '$p1', 'isNodeDepartmentManager', '申请人为所属部门负责人：开始->一级部门负责人', 10, 0, '2021-05-08 02:40:13', '2021-05-06 06:49:06');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 215, 221, '$p1', 'isHigherFirstDepartmentManager', '申请人为一级及以上部门负责人：开始->结束', 10, 0, '2021-05-08 02:40:20', '2021-05-06 06:49:14');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 216, 217, NULL, NULL, NULL, 10, 0, '2021-05-06 06:49:42', '2021-05-06 06:49:42');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 217, 218, NULL, NULL, NULL, 10, 0, '2021-05-06 06:49:57', '2021-05-06 06:49:57');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 218, 219, NULL, NULL, NULL, 10, 0, '2021-05-06 06:50:19', '2021-05-06 06:50:19');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 219, 220, NULL, NULL, NULL, 10, 0, '2021-05-06 06:50:33', '2021-05-06 06:50:33');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '1', 220, 221, NULL, NULL, NULL, 10, 0, '2021-05-06 06:50:39', '2021-05-06 06:50:39');



-- 非 Network Operations 子审批流
-- 1. 审批节点创建
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (222, 2, '2', '申请节点', 0, 1, NULL, NULL, 1, NULL, 0, '2021-04-30 07:29:49', '2021-04-30 07:49:23');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (223, 2, '2', '所属部门审批节点', 1, 4, 2, NULL, 1, NULL, 0, '2021-04-30 07:30:48', '2021-04-30 07:47:04');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (224, 2, '2', '一级部门审批节点', 1, 4, 1, NULL, 1, NULL, 0, '2021-04-30 07:31:00', '2021-04-30 07:47:07');
INSERT INTO `workflow_node_base`(`id`, `flow_id`, `code`, `name`, `type`, `auditor_type`, `auditor_level`, `auditor_id`, `approval_policy`, `specify_approver`, `deleted`, `created_at`, `updated_at`) VALUES (225, 2, '2', '结束节点', 99, 1, NULL, NULL, 1, NULL, 0, '2021-04-30 07:31:11', '2021-04-30 07:47:10');


-- 2.审批节点关系创建
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '2', 222, 223, '$p1', 'isBelowNodeDepartmentManager', '申请人为所属以下部门负责人 或 其他员工：开始->所属部门负责人', 10, 0, '2021-05-08 02:40:43', '2021-05-06 07:36:46');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '2', 222, 224, '$p1', 'isNodeDepartmentManager', '申请人为所属部门负责人：开始->一级部门负责人', 10, 0, '2021-05-08 02:40:54', '2021-05-06 07:37:12');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '2', 222, 225, '$p1', 'isHigherFirstDepartmentManager', '申请人为一级及以上部门负责人：开始->结束', 10, 0, '2021-05-08 02:52:05', '2021-05-06 07:37:19');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '2', 223, 224, NULL, NULL, NULL, 10, 0, '2021-05-06 07:37:31', '2021-05-06 07:37:31');
INSERT INTO `workflow_node_relate_base`(`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `deleted`, `updated_at`, `created_at`) VALUES (2, '2', 224, 225, NULL, NULL, NULL, 10, 0, '2021-05-06 07:37:38', '2021-05-06 07:37:38');

-- 折扣提醒表
CREATE TABLE `freight_discount_expire_remind` (
                                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                                  `remind_staff_info_id` int(11) NOT NULL COMMENT '收信人员工id',
                                                  `related_id` int(11) NOT NULL,
                                                  `client_id` varchar(32) DEFAULT NULL COMMENT '客户ID',
                                                  `customer_type_category` tinyint(3) unsigned DEFAULT NULL COMMENT '客户类型',
                                                  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户名称',
                                                  `customer_mobile` varchar(50) DEFAULT NULL COMMENT '客户手机号',
                                                  `price_type` tinyint(4) DEFAULT NULL COMMENT '价格类型',
                                                  `price_rule_category` tinyint(4) unsigned DEFAULT NULL COMMENT '折扣类型',
                                                  `current_disc` int(10) DEFAULT NULL COMMENT '当前折扣率',
                                                  `request_disc` int(10) DEFAULT NULL COMMENT '申请折扣率',
                                                  `disc_start_date` datetime(3) DEFAULT NULL COMMENT '折扣开始日期',
                                                  `disc_end_date` datetime(3) DEFAULT NULL COMMENT '折扣结束日期',
                                                  `valid_dates` int(4) DEFAULT '0' COMMENT '有效时长',
                                                  `state` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1-生效中 2-已过期 3-已取消 4-已删除',
                                                  `channel` int(2) DEFAULT '0' COMMENT '数据来源(0:network 1:Sales/PMD部门 2:Shop部门)',
                                                  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '收信人员工id',
                                                  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                                  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户优惠过期提醒';

INSERT INTO setting_env (code, set_val, remark, created_at, updated_at) VALUES ('discount_network_manager', '21318', 'network user operation manager', '2021-05-11 08:18:38', '2021-05-11 08:18:38');
INSERT INTO setting_env (code, set_val, remark, created_at, updated_at) VALUES ('discount_shop_operation_manager', '20467', '折扣消息推送 shop operation 负责人', '2021-05-11 07:59:32', '2021-05-11 13:55:31');