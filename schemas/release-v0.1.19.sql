-- ###菲律宾车辆关联 语言包  --bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-- 刘春华 start
update translations set t_value = 'Please enter 10-20 digit fuel card number' where t_key = 'fuel_manage_is_oil_number' and lang = 'en';
update translations set t_value = '请输入10-20位数字油卡号' where t_key = 'fuel_manage_is_oil_number' and lang = 'zh-CN';


INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'oil_subsidy_type_1', '补贴到工资卡', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'oil_subsidy_type_1', 'Subsidy to salary card', '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'oil_subsidy_type_2', '补贴到油卡', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'oil_subsidy_type_2', 'Subsidy to petrol card', '2021-06-21 03:35:56', null, 1);


INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'oil_subsidy_type_title', '油费补贴方式', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'oil_subsidy_type_title', 'Fuel subsidy method', '2021-06-21 03:35:56', null, 1);


INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'car_long_title', '车厢长(米)', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'car_long_title', 'Carriage length (m)', '2021-06-21 03:35:56', null, 1);


INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'car_width_title', '车厢宽(米)', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'car_width_title', 'Carriage width (m)', '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'car_high_title', '车厢高(米)', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'car_high_title', 'Carriage hight (m)', '2021-06-21 03:35:56', null, 1);


INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'vehicle_registration_number_title', '机动车登记编号', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'vehicle_registration_number_title', 'Vehicle Registration No.', '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'vehicle_registration_date_title', '机动车登记日期', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'vehicle_registration_date_title', 'Vehicle Registration Date', '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'driving_license_vehicle_restrictions_title', '驾照车辆限制', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'driving_license_vehicle_restrictions_title', "Driver's license vehicle restrictions", '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'approval_status_title', '信息审核状态', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'approval_status_title', "Information review status", '2021-06-21 03:35:56', null, 1);




alter table vehicle_info add vehicle_registration_number varchar(100)  NOT NULL  DEFAULT '' COMMENT '机动车登记编号' after engine_number;
alter table vehicle_info add vehicle_registration_date  date DEFAULT NULL COMMENT '机动车登记日期' after vehicle_registration_number;

alter table vehicle_info add vehicle_proof_number varchar(100)  NOT NULL DEFAULT '' COMMENT '车辆凭证号码' ;

alter table vehicle_info add vehicle_proof_pay_date  date DEFAULT NULL COMMENT '车辆凭证付款时间' after vehicle_proof_number ;

alter table vehicle_info add vehicle_proof_img  varchar(255) NOT NULL DEFAULT '' COMMENT '车辆凭证图片';

alter table vehicle_info add driving_license_vehicle_restrictions varchar(255)  NOT NULL DEFAULT '' COMMENT '驾照车辆限制如1,3,4'  after driver_license_type;

alter table vehicle_info add car_long decimal(10,3) NOT NULL DEFAULT 0 COMMENT '车厢长';
alter table vehicle_info add car_width decimal(10,3) NOT NULL DEFAULT 0 COMMENT '车厢宽';
alter table vehicle_info add car_high decimal(10,3) NOT NULL DEFAULT 0 COMMENT '车厢高';


alter table vehicle_info add oil_subsidy_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '油卡补贴方式1:工资卡2:油卡' after oil_company;
alter table vehicle_info modify vehicle_img varchar(5000) DEFAULT '' COMMENT '车辆图片';


-- ###菲律宾车辆关联  语言包  --bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-bi库执行-bi库执行--- 刘春华 end

-- ###雷光 油卡补贴
ALTER TABLE `staff_mileage_record_prepaid_info` ADD COLUMN `subsidy_type` tinyint(2) DEFAULT 1 COMMENT '补贴方式：1工资卡 2油卡', ALGORITHM=INPLACE, LOCK=NONE;
ALTER TABLE `staff_mileage_record_prepaid` ADD COLUMN `subsidy_type` tinyint(2) DEFAULT 1 COMMENT '补贴方式：1工资卡 2油卡', ALGORITHM=INPLACE, LOCK=NONE;
