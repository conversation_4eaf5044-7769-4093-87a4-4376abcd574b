<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>受益人保险单</title>
    <style>
        @page {
size: 210mm 297mm;
margin: 30mm 10mm 10mm
}

        body {
margin: 0
}

        p {
margin-bottom: 2mm
}

.box {
width: 100%;
margin: 0 auto;
font-family: "Times New Roman", Times, serif, Arial Unicode MS, thailand, SimSun;
font-size: 4mm;
line-height: 6mm;
text-align: justify
}

.text-dotted {
font-style: normal;
border-bottom: 2px dotted#333
}

.text-solid {
border-bottom: 1px solid#333
}

.grid {
width: 97%;
margin: 3mm;
border-spacing: 0;
border-top: 1px solid black;
border-left: 1px solid black
}

.grid td {
border-bottom: 1px solid black;
border-right: 1px solid black;
padding-left: 3mm
}

.div-flex {
display: flex;
margin-top: 2mm;
}

.square {
width: 15px;
height: 15px;
margin-left: 5px;
margin-right: 5px;
border: 1px solid black;
}
.sign-table td {
height: 15mm;
}
</style>
</head>

<body>
<div class="box">
        <h4 style="text-align: center;">
            <div>หนังสือระบุผู้รับผลประโยชน์กรมธรรม์ประกันภัยกลุ่ม</div>
            <div>Letter Identifying Beneficiaries of Group Insurance Policy</div>
        </h4>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">ชื่อผู้เอาประกัน / ชื่อพนักงาน:</div>
            <div class="text-dotted" style="width: 220mm;"> ${staff_name}</div>
        </div>
        <p>Insured member name / Employee name</p>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">รหัสพนักงาน Employee No.: </div>
            <div class="text-dotted" style="width: 220mm;">${staff_info_id}</div>
        </div>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">เลขที่บัตรประชาชน ID Card No. / Passport No.:</div>
            <div class="text-dotted" style="width: 220mm;">${identity}</div>
        </div>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">วัน/เดือน/ปี เกิด Birth Date:</div>
            <div class="text-dotted" style="width: 220mm;">${birthday}</div>
        </div>
        <div style="display: flex;margin-top: 3mm;justify-content: space-between">
            <div>
                <div style="display: flex;">
                    <!-- 性别勾选 -->
                    <div>เพศ:</div>
                    <div class="square">
                        <strong><#if sex=='1'>√</#if></strong>
                    </div>
                    <div>ชาย </div>
                    <div class="square">
                       <strong> <#if sex=='2'>√</#if></strong>
                    </div>
                    <div>หญิง </div>
                </div>
                <div> Sex /Male /Female</div>
            </div>
            <div>
                <div style="display: flex;">
                    <!-- 婚姻状况勾选 -->
                    <div>สถานภาพ: </div>
                    <div class="square">
                       <strong> <#if marital=='1'>√</#if></strong>
                    </div>
                    <div>โสด </div>
                    <div class="square">
                        <strong><#if marital=='2'>√</#if></strong>
                    </div>
                    <div>สมรส</div>

                    <div class="square">
                        <strong><#if marital=='3'>√</#if></strong>
                    </div>
                    <div>หย่า </div>
                    <div class="square">
                        <strong><#if marital=='4'>√</#if></strong>
                    </div>
                    <div>หม้าย </div>
                </div>
                <div>Status Single /Married /Divorced /Widowed</div>
            </div>

        </div>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">ที่อยู่ &nbsp;Address :</div>
            <div class="text-dotted" style="width: 240mm;">${register_address}</div>
        </div>
        <div class="div-flex" style="width: 100%;overflow: hidden;">
            <div style="flex-shrink:0">โทรศัพท์มือถือ Mobile Phone: </div>
            <div class="text-dotted" style="width: 240mm;">${mobile}</div>
        </div>
        <p>ขอทำหนังสือฉบับนี้ไว้เพื่อเป็นการแสดงเจตนาแต่งตั้งผู้รับผลประโยชน์ของกรมธรรม์กลุ่ม ตามรายละเอียดด้านล่างนี้
        </p>
        <p>The applicant would like to designate the following person (s) to be the beneficiary (ies) of the
            aforementioned group insurance policy. </p>
        <table class="grid" style="text-align: center;">
            <tbody>
                <tr>
                    <td>ลำดับ<div>No.</div>
                    </td>
                    <td>ชื่อผู้รับผลประโยชน์ <div>Beneficiary name</div>
                    </td>
                    <td>ความสัมพันธ์<div>Relationship</div>
                    </td>
                    <td>ส่วนแบ่งผลประโยชน์ <div>(%) % Share</div>
                    </td>
                </tr>
                <#list beneficiary as value>
                    <tr>
                        <td>${value_index + 1}</td>
                        <td>${value.beneficiary_name!''}</td>
                        <td>${value.relation_name!''}</td>
                        <td>${value.beneficiary_proportion!''}</td>
                    </tr>
                </#list>
            </tbody>
        </table>
        <p style="text-indent: 10mm;">1. ข้าพเจ้ายินยอมให้บริษัทและผู้รับประกันภัย จัดเก็บ ใช้ และเปิดเผย
            ข้อเท็จจริงเกี่ยวกับสุขภาพและข้อมูลของข้าพเจ้าต่อบริษัทประกันภัยอื่น หรือบริษัทประกันภัยต่อ
            หรือหน่วยงานที่มีอำนาจตามกฎหมาย หรือบุคลากรทางการแพทย์ เพื่อการขอเอาประกันภัยหรือการจ่ายเงินตามกรมธรรม์
            หรือประโยชน์ทางการแพทย์ </p>
        <p style="text-indent: 10mm;">1. I have given the consent to the company and the insurance company to keep, use
            and disclose the facts of
            my medical or any records given to the insurance companies or reinsurance companies or legitimate
            organizations or registered medical practitioners. Such disclosure of information shall be for the purpose
            of insurance application or asking the payment under this policy as well as medical advantage.</p>
        <p style="text-indent: 10mm;">2. ข้าพเจ้ารับทราบว่า บริษัทฯและผู้รับประกันภัยจะเก็บรวบรวม ใช้ เปิดเผย
            และ/หรือโอนข้อมูลส่วนบุคคล
            รวมถึงข้อมูลที่อ่อนไหวของข้าพเจ้า เพื่อการขอเอาประกันภัย การพิจารณารับประกันภัย รวมถึงหากแต่ไม่จำกัดเพียง
            การจ่ายเงินตามกรมธรรม์ประกันภัย ภายใต้นโยบายและกฎหมายว่าด้วยคุ้มครองข้อมูลส่วนบุคคล รวมทั้งรับทราบว่า
            บริษัทจะเปิดเผยข้อมูลส่วนบุคคลของข้าพเจ้าแก่สำนักงานคณะกรรมการกำกับและส่งเสริมการประกอบธุรกิจประกันภัย
            (สำนักงาน คปภ.)
            เพื่อประโยชน์ในการกำกับดูแลและส่งเสริมธุรกิจประกันภัยตามกฎหมายว่าด้วยประกันชีวิตและกฎหมายว่าด้วยคณะกรรมการกำากับและส่งเสริมการประกอบธุรกิจประกันภัย
            (รายละเอียดการเก็บรวมรวมใช้และเปิดเผยของสำนักงาน คปภ. ปรากฎตามนโยบายคุ้มครองข้อมูลส่วนบุคคลของสำนักงาน คปภ.
            ตามที่ปรากฎบนเว็บไซต์ )</p>
        <p style="text-indent: 10mm;">2. I hereby acknowledge that the company will collect, use, disclose and/or
            transfer my personal data as well
            as my sensitive data for the purposes of insurance application, underwriting including but not limited to
            insurance policy benefit payment according to the Company’s Personal Data Protection Policy. I also
            acknowledge that the Company will disclose my personal data to the laws pertaining to life insurance and the
            Office of Insurance Commission. (Details of the OIC’s collection, use and disclosure are subject to the
            OIC’s Personal Data Protection Policy as shown in (<a href="http://www.oic.or.th"
                target="">www.oic.or.th</a>).</p>
        <p style="text-indent: 10mm;">เพื่อเป็นหลักฐานจึงได้ลงลายมือชื่อไว้เป็นสำคัญ</p>
        <p style="text-indent: 10mm;">IN WITNESS WHEREOF, hereby undersigned in the presence of the witnesses.</p>

        <table style="margin-left: 10mm;" class="sign-table">
            <tr style="vertical-align: bottom">
                <td>
                    <div>ลงชื่อ</div>
                </td>
                <td>
                    <div class="text-dotted" style="width: 100mm;text-align: center;">
                        <#if staff_sign_img??>
                            <img style="width: auto;height: 30mm;object-fit: cover" src="${staff_sign_img}" alt="">
                        </#if>
                    </div>
                </td>
                <td>
                    <div>ผู้ขอเอาประกันภัย Insured Signature</div>
                </td>
            </tr>
            <tr style="vertical-align: bottom">
                <td></td>
                <td>
                    <div style="display: flex;margin-top: 3mm;">
                        <div style="margin-left: 10mm;">（</div>
                        <div class="text-dotted" style="width: 80mm;text-align: center;">${staff_name}</div>
                        <div>）</div>
                    </div>
                </td>
                <td>
                </td>
            </tr>
            <tr style="vertical-align: bottom">
                <td>
                    <div> ลงชื่อ</div>
                </td>
                <td>
                    <div style="display: flex;">

                        <div class="text-dotted" style="width: 100mm;">

                        </div>
                    </div>
                </td>
                <td>
                    <div>พยาน Witness</div>
                </td>
            </tr>
            <tr style="vertical-align: bottom">
                <td></td>
                <td>
                    <div style="display: flex;margin-top: 3mm;">
                        <div style="margin-left: 10mm;">（</div>
                        <div class="text-dotted" style="width: 80mm;"></div>
                        <div>）</div>
                    </div>
                </td>
                <td>
                </td>
            </tr>
            <tr style="vertical-align: bottom">
                <td>
                    <div> ลงชื่อ</div>
                </td>
                <td>

                    <div class="text-dotted" style="width: 100mm;">

                    </div>
                </td>
                <td>
                    <div>พยาน Witness</div>
                </td>
            </tr>
            <tr style="vertical-align: bottom">
                <td></td>
                <td>
                    <div style="display: flex;margin-top: 3mm;">
                        <div style="margin-left: 10mm;">（</div>
                        <div class="text-dotted" style="width: 80mm;"></div>
                        <div>）</div>
                    </div>
                </td>
                <td>
                </td>
            </tr>



        </table>
        <table style="margin-left: 10mm;" class="sign-table">
            <tr style="vertical-align: bottom">
                <td>
                    <div>วันที่ Issue Date</div>
                </td>
                <td>

                    <div class="text-dotted" style="width: 84mm;text-align: center;">
                        ${sign_date}
                    </div>
                </td>
                <td>
                </td>
            </tr>
        </table>

    </div>
</body>

</html>