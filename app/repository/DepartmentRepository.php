<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;

//use FlashExpress\bi\App\Models\bi\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;

class DepartmentRepository extends BaseRepository
{

    public function initialize()
    {

        parent::initialize();
    }

    /**
     * 角色查询
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function staffRole($paramIn = [])
    {
        $staffId    = $paramIn['staff_id'];
        $roleSql    = "
                     --
                     SELECT hr_staff_info_position.position_category
                     FROM `hr_staff_info_position` 
                     left join roles on hr_staff_info_position.position_category =roles.id  
                     WHERE staff_info_id =" . $staffId;
        $data       = $this->getDI()->get('db_rby')->query($roleSql);
        $roleData   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $returnData = $roleData;
        return $returnData;
    }

    /**
     * 角色查询
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function staffRoles($paramIn = [])
    {
        $staffId    = $paramIn['staff_id'];
        $roleSql    = "
                     --
                     SELECT hr_staff_info_position.position_category,roles.name
                     FROM `hr_staff_info_position` 
                     left join roles on hr_staff_info_position.position_category =roles.id  
                     WHERE staff_info_id =" . $staffId;
        $data       = $this->getDI()->get('db_rby')->query($roleSql);
        $roleData   = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData = $roleData;
        return $returnData;
    }


    /**
     * 根据id,返回该id和其子部门id
     * @param $parent_id
     * @return array
     */
    public function getDepartIds($parent_id)
    {
        $sql = " SELECT id FROM `sys_department` WHERE ancestry = :parent_id";
        $data = $this->getDI()->get('db_fle')->query($sql, ["parent_id" => $parent_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $arr = [$parent_id];
        if (!empty($data)) {
            $arr = array_merge($arr, array_column($data, "id"));
        }
        return $arr;
    }

    /**
     * 根据id,返回该id和其子部门id
     * @param $parent_id
     * @return array
     */
    public function getDepartmentList($parent_id)
    {
        $sql = "--
            SELECT id,name 
            FROM `sys_department` 
            WHERE ancestry = :parent_id OR id = :parent_id";
        $data = $this->getDI()->get('db_fle')->query($sql, ["parent_id" => $parent_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 根据id获得部门名字。没有返回空
     * @param $id
     * @return string
     */

    public function getDepartmentNameById($id){
        $sql = "select `name` from `sys_department` where id= :id";
        $arr = $this->getDI()->get("db_fle")->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,["id"=>$id]);
        if(empty($arr)){
            return "";
        }
        return $arr['name'];
    }

    /**
     * 获取一级部门及其所有的子部门
     * @param $departmentId
     * @return string
     */
    public function getParentAndChildDetartment($departmentId)
    {
        $return = [];
        $sql    = "select id, ancestry from sys_department where ancestry = ? and deleted = 0";
        $departmentIdArr = $this->getDI()->get("db_rbi")->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [$departmentId]);

        if (empty($departmentIdArr)) {
            return $departmentId;
        } else {

            $departmentIds = array_column($departmentIdArr, 'id');
            foreach ($departmentIds as $department) {
                $dep = $this->getParentAndChildDetartment($department);
                if (!empty($dep)) {
                    array_push($return, $dep);
                }
            }
            return !empty($return) ? implode(',', $return) : '';
        }
    }

    /**
     * 获取指定部门及其子部门id列表
     * @param int  $department_id
     * @param bool $only_id
     * @param bool $is_contain_self
     *
     * @return mixed
     */
    public function getDepartmentListById($department_id = 0, bool $only_id = true, bool $is_contain_self = true)
    {
        if (empty($department_id)) {
            return [];
        }

        // 获取所有部门数据
        $all_department_list = FleSysDepartmentModel::find([
            'conditions' => 'deleted = :deleted:',
            'bind' => ['deleted' => 0],
            'columns' => ['id', 'ancestry', 'name'],
        ])->toArray();

        // 获取指定部门的所有部门数据
        $list_id_map = array_column($all_department_list, null, 'id');
        $current_department = $list_id_map[$department_id] ?? [];
        if (empty($current_department)) {
            return [];
        }

        $all_children_list = $is_contain_self ? [$current_department] : [];

        $this->getAllChildrenDepartmentList($all_department_list, $department_id, $all_children_list);
        if ($only_id) {
            return array_unique(array_column($all_children_list, 'id'));
        } else {
            return $all_children_list;
        }
    }

    /**
     * 递归查找子部门
     * @param $department_list
     * @param $ancestry
     * @param $all_children_list
     */
    protected function getAllChildrenDepartmentList($department_list, $ancestry, &$all_children_list)
    {
        foreach ($department_list as $item) {
            if ($ancestry == $item['ancestry']) {
                $all_children_list[] = $item;
                $this->getAllChildrenDepartmentList($department_list, $item['id'], $all_children_list);
            }
        }
    }

    //获取C-Level （CEO,COO,CPO,CFO）
    public function get_c_level(){
        $data = SysDepartmentModel::find("type in (4,5) and deleted = 0")->toArray();
        if(!empty($data))
            $data = array_column($data,'manager_id');
        return $data;
    }

    //根据部门id返回数据
    public function getDepartmentByIds($departmentIds){
        $info = SysDepartmentModel::find(
            [
                'conditions' => 'id in ({departmentIds:array})',
                'bind' => ['departmentIds' => $departmentIds],
                'columns' => ['id', 'name', 'ancestry_v3'],
            ]
        )->toArray();
        return $info;
    }

    /**
     * 查询指定部门下模糊匹配的部门
     * @param $name
     * @param $departmentIds
     */
    public function findDepartmentsByIdsAndName($name, $departmentIds){
        if(empty($name) || empty($departmentIds)){
            return [];
        }
        $info = SysDepartmentModel::find([
            'conditions' => 'id in ({departmentIds:array}) and name like :name:',
            'bind' => ['departmentIds' => $departmentIds, 'name' => '%' . $name . '%'],
            'columns' => ['id', 'name']])->toArray();
        return $info;
    }

    /**
     * 获取部门负责人所负责的部门id及子部门id
     * @param $staff_info_id
     * @return array
     */
    public function getStaffOrganizationInfo($staff_info_id)
    {
        $managerDept = $this->getDepartmentByManager($staff_info_id);
        if(!$managerDept) {
            return [];
        }
        $allManagerDept = array_column($managerDept, 'id');
        foreach ($managerDept as $oneManagerDept) {
            $childDept = $this->getDeptByLink($oneManagerDept['ancestry_v3']);
            if(empty($childDept)) {
                continue;
            }
            $childDeptToId = array_column($childDept, 'id');

            $allManagerDept = array_merge($allManagerDept, $childDeptToId);
        }

        return array_map('intval', array_values(array_unique($allManagerDept)));
    }


    /**
     * 获取部门负责人，所负责的部门
     * @param $staffId
     * @return mixed
     */
    public function getDepartmentByManager($staffId)
    {
        return SysDepartmentModel::find([
            'conditions' => 'deleted = :deleted: and manager_id = :manager_id:',
            'bind' => [
                'deleted' => 0,
                'manager_id' => $staffId,
            ],
            'columns' => ['id', 'ancestry_v3'],
        ])->toArray();
    }

    /**
     * 通过部门链查询，子部门
     * @param $ancestry_v3
     * @return mixed
     */
    public function getDeptByLink($ancestry_v3)
    {
        return SysDepartmentModel::find(["conditions" => "ancestry_v3 like :ancestry: and deleted = 0 ",
            "bind"=>["ancestry" => $ancestry_v3.'/%'],
            'columns' => 'id'
        ])->toArray();
    }

    /**
     * 获取部门信息--废弃
     * @param array $paramIn
     * @return array
     */
    public function getDepartmentInfo($paramIn = [])
    {
        $departmentIds = $paramIn["department_ids"] ?? "";
        $ancestryIds = $paramIn["ancestry_ids"] ?? "";
        if (!$departmentIds && !$ancestryIds) {
            return [];
        }

        return SysDepartmentModel::find([
            'conditions' => 'id in ({deptId:array}) or ancestry in ({ancestry:array})',
            'bind' => ['deptId' => $departmentIds, 'ancestry' => $ancestryIds],
        ])->toArray();
    }

    /**
     * 查询 指定部门所有 子部门 + 父级部门 信息
     * @param $departmentIds
     * @return array
     */
    public function getDepartmentChildInfo($departmentIds)
    {
        if(!is_array($departmentIds)) {
            return [];
        }
        if(empty($departmentIds)) {
            return [];
        }
        $parentInfo = SysDepartmentModel::find([
            'conditions' => 'id in ({deptId:array})',
            'bind' => ['deptId' => $departmentIds],
        ])->toArray();

        if(empty($parentInfo)) {
            return [];
        }
        $parentIds = array_column($parentInfo, 'id');

        $childDepartmentId = [];
        foreach ($parentInfo as $oneParent) {
            $childInfo = $this->getDeptByLink($oneParent['ancestry_v3']);
            if(empty($childInfo)) {
                continue;
            }
            $ids = array_column($childInfo, 'id');
            $childDepartmentId = array_merge($childDepartmentId, $ids);
        }

        $allDeptId = array_values(array_unique(array_merge($childDepartmentId, $parentIds)));

        return $allDeptId;
    }

    /**
     * 获取指定部门信息
     * @param $department_id
     * @param string $columns
     * @return array
     */
    public function getSpecDepartmentInfo($department_id, string $columns = 'id,name'): array
    {
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id: and deleted = 0',
            'bind' => [
                'id' => $department_id
            ],
            'columns' => $columns
        ]);
        if (empty($departmentInfo)) {
            return [];
        }
        return $departmentInfo->toArray();
    }

    /**
     * 根据部门 ID 获取公司信息
     * @param $after_department_id
     * @return array
     */
    public function getCompanyInfoByDepartmentId($after_department_id)
    {
        $departmentInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id: and deleted = 0',
            'bind' => [
                'id' => $after_department_id
            ],
            'columns' => 'company_id'
        ]);
        if (empty($departmentInfo)) {
            return [];
        }
        $companyInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id: and deleted = 0',
            'bind' => [
                'id' => $departmentInfo->company_id
            ],
            'columns' => 'id,name'
        ]);
        return $companyInfo ? $companyInfo->toArray() : [];
    }

    /**
     * 根据部门链，获取2级部门负责人（）
     * @param $department_chain
     * @param int $level
     * @return array
     */
    public function getHubAreaManger($department_chain, int $level = SysDepartmentModel::DEPARTMENT_LEVEL_1): array
    {
        if (empty($department_chain)) {
            return [];
        }
        $departmentChainList = explode('/', $department_chain);
        $departmentInfo = SysDepartmentModel::find([
            'conditions' => 'id in({department_ids:array}) and level = :level: and deleted = 0',
            'bind' => [
                'department_ids' => $departmentChainList,
                'level' => $level,
            ],
            'columns' => 'manager_id'
        ])->toArray();
        return array_column($departmentInfo, 'manager_id');
    }

    /**
     * 根据部门ID获取部门链中某级部门的部门ID
     * @param $department_id
     * @param int $level
     * @return string
     */
    public function getSpecLevelDepartmentInChain($department_id, int $level = SysDepartmentModel::DEPARTMENT_LEVEL_1): string
    {
        if (empty($department_id)) {
            return "";
        }
        $departmentInfo = SysDepartmentModel::findFirst($department_id);
        if (empty($departmentInfo)) {
            return "";
        }
        $departmentChainList = explode('/', $departmentInfo->ancestry_v3);
        if (empty($departmentChainList)) {
            return "";
        }
        $specLevelDepartmentId = SysDepartmentModel::findFirst([
            'conditions' => 'id in({department_ids:array}) and deleted = 0 and level = :level:',
            'bind'       => [
                'department_ids' => $departmentChainList,
                'level'          => $level,
            ],
            'columns'    => 'id',
        ]);
        if (empty($specLevelDepartmentId)) {
            return "";
        }
        return $specLevelDepartmentId->id;
    }
}
