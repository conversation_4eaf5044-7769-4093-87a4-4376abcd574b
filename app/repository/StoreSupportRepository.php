<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2Model;
use FlashExpress\bi\App\Server\SettingEnvServer;

class StoreSupportRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    //插入网点支援数据
    public function insert_store_support($table,$insert)
    {
        try {
            $insertSql = $this->getInsertDbSql($table, $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("StoreSupportRepository:insert_store_apply:" . $e->getMessage());
            $last_id = '';
        }
        return $last_id;
    }

    //获取指定班次
    public function getShiftList($shift_ids)
    {
        $shift_list = HrShiftModel::find([
            'columns' => 'id, type, start, end',
            'conditions' => "id IN ({shift_ids:array})",
            'order' => 'type,start asc',
            'bind' => [
                'shift_ids' => $shift_ids
            ],
        ])->toArray();
        $shift_list = array_column($shift_list, null, 'id');
        return $shift_list;
    }

}