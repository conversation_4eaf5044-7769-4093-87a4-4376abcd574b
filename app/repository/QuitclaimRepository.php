<?php
/**
 * Author: Bruce
 * Date  : 2023-04-18 20:44
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\LeaveQuitclaimInfoModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class QuitclaimRepository extends BaseRepository
{

    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 获取员工信息
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public function getStaffInfo($staffId, $columns = '*')
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);

        return !empty($staffInfo) ? $staffInfo->toArray() : [];
    }

    /**
     * 获取职位信息
     * @param $jobId
     * @param string $columns
     * @return array
     */
    public function getJobTitleInfo($jobId, $columns = '*')
    {
        //获取职位名称
        $jobInfo = HrJobTitleModel::findFirst([
            'columns'    => $columns,
            'conditions' => "id = :job_id:",
            'bind'       => [
                'job_id' => $jobId,
            ],
        ]);
        return !empty($jobInfo) ? $jobInfo->toArray() : [];
    }

    /**
     * 获取quitclaim 信息
     * @param $quitclaimId
     * @param string $columns
     * @return array
     */
    public function getQuitclaimInfo($quitclaimId, $columns = '*')
    {
        $quitClaimInfo = LeaveQuitclaimInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => "id = :quitclaim_id:",
            'bind'       => ['quitclaim_id' => $quitclaimId],
        ]);

        return !empty($quitClaimInfo) ? $quitClaimInfo->toArray() : [];
    }

    /**
     * 查询离职员工 发送 通知信息
     * @param $page_size
     * @param $page
     * @param $params
     * @return mixed
     */
    public function getQuitclaimList($page_size, $params = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'lqi.id as quitclaim_id',
            'lqi.staff_info_id',
            'hsi.name',
            'hsi.hire_type',
            'hsi.mobile',
            'hsi.personal_email',
            'send_notice_number',
            'send_notice_time',
            'status',
            'short_url',
        ]);
        $builder->from(['lqi' => LeaveQuitclaimInfoModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "lqi.staff_info_id = hsi.staff_info_id", 'hsi');

        $builder = $this->buildQuitclaimWhere($builder, $params);

        $builder->limit($page_size);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 条件构建
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function buildQuitclaimWhere($builder, $params)
    {
        $builder->where('hsi.state = :state:', ['state' => HrStaffInfoModel::STATE_2]);
        $builder->andWhere('hsi.formal in ({formal:array}) and is_sub_staff = 0', ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]]);
        $builder->andWhere('lqi.is_new = :is_new:', ['is_new' => 1]);
        //发送次数
        if (!empty($params['send_notice_number'])) {// 0  或  1,2
            $builder->andWhere('lqi.send_notice_number in ({send_notice_number:array})',
                ['send_notice_number' => $params['send_notice_number']]);
        }
        //最近发送通知时间
        if (!empty($params['send_notice_time_start']) && !empty($params['send_notice_time_end'])) {
            $builder->andWhere('lqi.send_notice_time >= :send_notice_time_start: and lqi.send_notice_time <= :send_notice_time_end:',
                ['send_notice_time_start' => $params['send_notice_time_start'], 'send_notice_time_end' => $params['send_notice_time_end']]);
        }
        //审核进度
        if (!empty($params['status'])) {
            $builder->andWhere('lqi.status in ({status:array})', ['status' => $params['status']]);
        }

        //创建时间
        if (!empty($params['created_at'])) {
            $builder->andWhere('lqi.created_at <= :created_at:', ['created_at' => $params['created_at']]);
        }

        return $builder;
    }
}