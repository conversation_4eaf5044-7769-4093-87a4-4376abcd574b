<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrJdModel;

class HrJDRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public static function getJobName($job_id)
    {
        if (empty($job_id)) {
            return '';
        }
        $jdInfo = HrJdModel::findFirst([
            'conditions' => "job_id = :job_id:",
            'bind'       => [
                'job_id' => $job_id,
            ],
            'columns'    => 'job_name',
        ]);
        return $jdInfo->job_name ?? '';
    }

    public static function getJDList($job_ids)
    {
        $jd_list = [];
        if (!empty($job_ids)) {
            $jd_list = HrJdModel::find([
                'conditions' => "job_id IN ({job_id:array}) AND state = 1",
                'bind'       => [
                    'job_id' => $job_ids,
                ],
                'columns'    => 'job_id,job_name',
            ])->toArray();
        }
        return $jd_list;
    }
}