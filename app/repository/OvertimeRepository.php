<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\AuditPermissionModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;

class OvertimeRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    //Van courier - 13和Bike courier - 110 申请页OT类型只展示一条“休息日和假期正常上班” type 3 2019-10-23 去掉的 职位要加回来
    public static $overtime3_permission = array(
        11,13,14,15,16,50,65,79,83,101,152,158,175,222,264,272,296,310,314,316,321,337
        ,110
        ,451
    );

    //ot权限表 overtime_permission
    public function get_OT_permission($company_id){
        $module_id = AuditPermissionModel::MODULE_P_1;
        return AuditPermissionModel::find("company_id = {$company_id} and module_type = {$module_id}")->toArray();

    }

    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOvertime($paramIn = [])
    {
        $overtimeId = '';
        try {
            $insertSql  = $this->getInsertDbSql('hr_overtime', $paramIn);
            $this->getDI()->get('db')->query($insertSql);
            $overtimeId = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("overtimeRe:addOvertime-" . $e->getMessage());
            /* 有异常回滚 */
            $overtimeId = '';
        }
        return $overtimeId;
    }

    /**
     * 审批加班
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateOvertime($paramIn = []){
        $higher_staff_id = $paramIn['higher_staff_id'];
        $state = $paramIn['state'];
        $reject_reason = $paramIn['reject_reason'];
        $overtimeId = $paramIn['overtime_id'];
        $db         = $this->getDI()->get('db');
        $db->begin();
        try {
            $sql  = 'update hr_overtime set state='.$state.',reject_reason="'.$reject_reason.'",higher_staff_id='.$higher_staff_id.' where overtime_id='.$overtimeId;
            $db->execute($sql);
            $db->commit();
        }catch (\Exception $e){
            /* 有异常回滚 */
            $db->rollback();
            $overtimeId = '';
            $this->getDI()->get('logger')->write_log("overtimeRe:updateOvertime-" . $e->getMessage());
        }
        return $overtimeId;
    }

    /**
     * 1.上级正常，返回上级，2。如果上级离职，找原上级的上级【只有三级，不递归】,3.新上级入职hris更新后直接展示最新ORDER BY id DESC的上级
     * 【历史问题继续调用此方法，新业务请逐渐迁移到StaffServer同名方法】
     * @param $staffId
     * @return array
     */
    public function getHigherStaffId($paramIn = []): array
    {
        $staffId   = $paramIn['staff_info_id'];
        $mangerArr['value'] = 0;
        $Sql       = "select `value` from `hr_staff_items` where `staff_info_id`=" . $staffId . " AND `item`='MANGER'  ORDER BY id DESC";
        $data      = $this->getDI()->get('db_rby')->query($Sql);
        $mangerId = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($mangerId)) { //无上下级关系
            return $mangerArr;
        }
        $Sql       = "select state from `hr_staff_info` where `staff_info_id`=" . intval($mangerId['value']) ;
        $data      = $this->getDI()->get('db_rby')->query($Sql);
        $mangerInfo = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if( $mangerInfo['state'] != 1 ){
            $Sql       = "select `value` from `hr_staff_items` where `staff_info_id`=" . intval($mangerId['value']) . " AND `item`='MANGER'  ORDER BY id DESC";
            $data      = $this->getDI()->get('db_rby')->query($Sql);
            $mangerId = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            return $mangerId ?  $mangerId : $mangerArr;
        }else{
            return $mangerId ?  $mangerId : $mangerArr;
        }
    }

    /**
     * 加班详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function infoOvertime($paramIn = []){
        $overtimeid          = $paramIn['overtime_id'];
        $sql = "select 
                  *,
                  created_at as real_create,
                  CONVERT_TZ(created_at,'+00:00', '".$this->timezone."' ) AS created_at, 
                  CONVERT_TZ(updated_at,'+00:00', '".$this->timezone."' ) AS updated_at 
                  from hr_overtime 
                  where 
                  overtime_id = ".$overtimeid;
        $data           = $this->getDI()->get('db')->query($sql);
        $returnData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     *
     * 根据主键获取记录
     * @param $overtime_id
     * @return mixed
     *
     */
    public function getInfoById($overtime_id){
        $sql = "select * from hr_overtime where overtime_id = {$overtime_id} ";
        $data         = $this->getDI()->get('db')->query($sql);
        $returnData   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * @param $audit_log  model->staff_audit_tool_log  日志表组装数据
     * @param $change_status  model->hr_overtime 更改状态 模型
     */
    public function cancel($audit_log, $change_status){
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $audit_log);
        $up_sql = "update hr_overtime set state = {$change_status['state']} where overtime_id = {$change_status['overtime_id']} ";
        $db       = $this->getDI()->get('db');
        try{
            $db->begin();
            $db->execute($insetSql);
            $db->execute($up_sql);
            $db->commit();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("overtimeRe:cancel-" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;


    }

    /**
     * 根据日期获取 待审核和 审核通过的 ot 用于判断是否存在LH
     * @param $staff_id
     * @param $lh_time
     * @return mixed
     */
    public function getOvertimeInfo($staff_id, $lh_time){
        $sql = " select count(1) from hr_overtime where staff_id = {$staff_id} 
                  and start_time <= '{$lh_time}' and end_time >= '{$lh_time}' and state in (1,2)";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }

    //验证补申请的日期 是否存在 真是 ot记录
    public function checkApplyOvertime($staff_id, $date){
        $sql = " select count(1) from hr_overtime where staff_id = {$staff_id} 
                  and (date(start_time) = '{$date}' or date(end_time) = '{$date}') and state in (1,2)";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }

    //补申请方法获取真实ot 申请 按日期返回
    public function getActOvertime($staff_id,$start_date,$end_date){
        $sql = "select *,date(start_time) date_at from hr_overtime where staff_id = {$staff_id} and date(start_time) >='{$start_date}' 
                and date(start_time) <= '{$end_date}' and state in (1,2)";
        $data  = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 根据日期 获取当天的ot  有可能多条记录
     * @param $staff_id
     * @param $date_at
     * @return mixed
     */
    public function getOtByDate($staff_id, $date_at){
        $sql = "select * from hr_overtime where staff_id = {$staff_id} and date_at = '{$date_at}'";
        $data  = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    public function get_workdays($staff_id,$date): array
    {
        $workDays = HrStaffWorkDayModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND date_at = :date_at:',
            'bind'       => ['staff_info_id'=>$staff_id,'date_at'=>$date],
        ]);
        return $workDays ? $workDays->toArray():[];
    }


    /**
     * 计算员工 时间区间内 加班小时数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @param $type
     * @return int
     */
    public function get_duration($staff_id, $start_date, $end_date, $type)
    {
        if (empty($staff_id)) {
            return 0;
        }
        //有可能传多个类型
        if (!is_array($type)) {
            $type = [$type];
        }
        //多个工号
        if (!is_array($staff_id)) {
            $staff_id = [$staff_id];
        }

        $num = HrOvertimeModel::sum([
            'column'     => 'duration',
            'conditions' => "staff_id in ({ids:array}) and date_at between  :start: and :end: and state in (1,2) and type in ({types:array}) ",
            'bind'       => [
                'ids'   => $staff_id,
                'start' => $start_date,
                'end'   => $end_date,
                'types' => $type,
            ],
        ]);

        return $num ?? 0;
    }

    //获取ot列表
    public function getOtList($staffId, $startDate, $endDate, $extend = [])
    {
        $conditions       = "staff_id = :staff_id: and date_at between  :start: and :end: ";
        $bind['staff_id'] = $staffId;
        $bind['start']    = $startDate;
        $bind['end']      = $endDate;
        $typeArr          = $extend['types'] ?? [];
        $stateArr         = $extend['states'] ?? [];
        $salaryStateArr   = $extend['salary_state'] ?? [];

        //筛选加班类型
        if (!empty($typeArr)) {
            $conditions    .= " and type in ({types:array}) ";
            $bind['types'] = $typeArr;
        }
        //筛选状态
        if (!empty($stateArr)) {
            $conditions     .= " and state in ({states:array}) ";
            $bind['states'] = $stateArr;
        }
        //筛选有效类型
        if (!empty($salaryStateArr)) {
            $conditions            .= " and salary_state in ({salary_states:array}) ";
            $bind['salary_states'] = $salaryStateArr;
        }

        $data = HrOvertimeModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        return $data;
    }

}
