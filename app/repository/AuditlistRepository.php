<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditUnionModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;

class AuditlistRepository extends BaseRepository
{

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    public function initialize()
    {
        parent::initialize();
    }
    public $is_individual_contractor = false;

    public function setIsIndividualContractor($is_individual_contractor)
    {
        $this->is_individual_contractor = $is_individual_contractor;
    }


    /**
     * 获取audit聚合数据
     * @param array $where
     * @return array
     */
    public function get($paramIn = [])
    {
        $table = $paramIn['table'];
        if (isset($paramIn['table'])) {
            unset($paramIn['table']);
        }
        $condition = !empty($paramIn) ? " where ": "";
        foreach ($paramIn as $k => $v) {
            $condition .= "`{$k}` = '{$v}'";
        }

        $audit_sql = "select * from {$table}". $condition;
        $data = $this->getDI()->get('db')->query($audit_sql);
        return $data->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 获取聚合数据详情
     */
    public function getStaffAuditUnion($paramIn = [])
    {
        $id_union   = $paramIn['id_union'];
        $type_union = $paramIn['type_union'];

        $auditUnionsql = "
            --
            select 
                id
                ,id_union
                ,staff_id_union
                ,type_union
                ,status_union
                ,store_id
                ,data
                ,`table`
                ,DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_at
                ,summary
                ,origin_id
                ,approval_id
            from staff_audit_union where `id_union` = '{$id_union}' and `type_union`= {$type_union} and approval_id = 0";
        $data = $this->getDI()->get('db')->query($auditUnionsql);
        return $data->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 删除数据
     * @param array $where
     * @return boolean
     */
    public function delete($where)
    {
        $audit_sql = "delete from staff_audit_union where ". $where;
        $db        = $this->getDI()->get('db');
        $db->begin();
        $data = $db->execute($audit_sql);
        if(!$data)
        {
            $db->rollback();
            return false;
        }
        $db->commit();
        return true;
    }

    /**
     * 插入数据
     * @param $insetData
     * @return bool
     */
    public function insert($insetData)
    {
        $insetSql = $this->getInsertDbSql('staff_audit_union', $insetData);
        $db       = $this->getDI()->get('db');
        $db->begin();
        $data = $db->execute($insetSql);
        if(!$data)
        {
            $db->rollback();
            return false;
        }
        $id = $db->lastInsertId();
        $db->commit();
        return $id;
    }

    /**
     * 同步staff_audit_union
     * 同步策略：先删除后添加
     */
    public function sync($delData, $insertData,$un_unique = '')
    {
        $delete_sql = "delete from staff_audit_union where id_union = '{$delData}' ";
        if(!empty($un_unique)){
            $type = intval($insertData['type_union']);
            $delete_sql .= " and type_union =  {$type} and approval_id = {$insertData['approval_id']}";
        }
        $insetSql   = $this->getInsertDbSql('staff_audit_union', $insertData);
        $db = $this->getDI()->get('db');
        try{

            $db->begin();
            $db->execute($delete_sql);
            $db->execute($insetSql);

            $db->commit();
            $this->getDI()->get('logger')->write_log("auditListRe:hc_sync-" . json_encode($insertData));
            return true;
        }catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("auditListRe:hc_sync-" . $e->getMessage());
            return false;
        }
    }

    /**
     * 组织sql语句
     * @param string $table
     * @param array  $where
     * @param int    $pageNum
     * @param int    $pageSize
     * @param array  $sort
     */
    protected function generalQuery($where, $pageNum = null, $pageSize = null, $sort = null, $isForceIndex = null)
    {
        //[1]组织查询语句
        $querySql   = [];
        $date       = gmdate("Y-m-d H:i:s", time() - 86400 * 90);
        $dateFilter = " AND created_at > '{$date}' ";

        $baseSql    = "--
                SELECT 
                    id
                    ,id_union
                    ,staff_id_union
                    ,type_union,status_union
                    ,store_id
                    ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS created_at
                    ,summary,approval_id
                    ,is_cancel
                FROM staff_audit_union"
        ;
        if (count($where) > 1) {
            foreach ($where as $k => $v) {
                if ($k + 1 == count($where) && $isForceIndex) { //最后一个 & 强制索引
                    $querySql[] = sprintf("%s force index(idx_approval_id) where %s %s", $baseSql, $v, $dateFilter);
                } else {
                    $querySql[] = sprintf("%s where  %s %s", $baseSql, $v, $dateFilter);
                }
            }
            $querySql = implode(' UNION ALL  ', $querySql);
        } else {
            if ($isForceIndex) {
                $querySql = sprintf("%s force index(idx_approval_id) where %s %s", $baseSql, current($where), $dateFilter);
            } else {
                $querySql = sprintf("%s where %s %s", $baseSql, current($where), $dateFilter);
            }
        }

        //[2]分页计算
        if (!empty($pageNum) && !empty($pageSize)) {
            if ($pageNum > 1) { //第二页以后20条
                $pageOffset = ($pageNum - 2) * $pageSize + 30;
            } else { //第一页30条
                $pageOffset = 0;
            }
            $page = ' LIMIT '. $pageSize . ' OFFSET ' . $pageOffset . '';
        } else {
            $page = '';
        }

        //[3]排序
        if (!empty($sort)) {
            $orderBy = " ORDER BY {$sort}";
        } else {
            $orderBy = "";
        }
        return $querySql . $orderBy . $page;
    }

    /**
     * 获取审批记录审批状态
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getApprovalAuditStatus($paramIn = [])
    {
        $id             = $paramIn['id'];
        $approval_id    = $paramIn['approval_id'];
        $type           = $paramIn['type'];
        $audit_sql = "
            --
            SELECT status_union 
            FROM staff_audit_union 
            WHERE id_union = '{$id}' AND approval_id = {$approval_id} AND type_union = {$type}";
        $returnData     = $this->getDI()->get('db')->query($audit_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 获取用户头像
     * @param $staff_id
     * @return string
     */
    public function getPersonAvator($staff_id): string
    {
        if (empty($staff_id)) {
            return '';
        }

        //获取头像
        $data = StaffWorkAttendanceAttachmentModel::findFirst([
            'columns' => 'work_attendance_path as image',
            'conditions' => 'staff_info_id = :staff_id: and deleted = 0 ',
            'bind' => ['staff_id' => $staff_id]
        ]);
        if (!empty($data)) {
            $data = $data->toArray();
            $avator_data = 'https://' . env('hris_profile_uri') . $data['image'];
        } else {
            $avator_data = 'https://' . env('hris_profile_uri') . env('default_avator');
        }
        return $avator_data;
    }

    /**
     * 获取审批人id
     * @param array $paramIn
     * @return array
     */
    public function getApprovalIds($paramIn = [])
    {
        $id     = $paramIn['id'];
        $type   = $paramIn['type'];

        $query_sql = "
            --
            select approval_id 
            from staff_audit_union where id_union = '{$id}' and status_union = 107 and type_union = {$type}";
        $data = $this->getDI()->get('db')->query($query_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($data, 'approval_id');
    }


    public static function auditTypeForFilter():array
    {
        $list = enums::$at;
        foreach ($list as $key => &$value) {
            if(isCountry(['th','ph']) && $key == enums::APPROVAL_TYPE_CANCEL_CONTRACT){
                $value = 'th_workflow_type_63';
            }
        }
        return $list;
    }


    /**
     * 获取审批类型
     * @param int $type     审批类型
     * @return string
     */
    public function getAudityType($type) : string
    {
        $workflow_type_63 = 'workflow_type_63';
        if (isCountry(['TH','PH'])) {
            $workflow_type_63 = 'th_workflow_type_63';
        }
        $hire_13_key = 'th_workflow_type_2';
//        if(isCountry('TH')){
//            $hire_13_key = 'th_workflow_type_2';
//        }
//        if(isCountry('PH')){
//            $hire_13_key = 'leave_41';
//        }

        $workflow_type_2 = $this->is_individual_contractor ? $hire_13_key : '7002';

        $t = $this->getTranslation();
        $typeArray = [
            '1' => $t->_('7001'),
            AuditListEnums::APPROVAL_TYPE_ICAT => $t->_('ic_reissue'),//泰国个人代理的补卡
            '2' => $t->_($workflow_type_2),
            '3' => $t->_('7003'),
            '4' => $t->_('7004'),
            AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID => $t->_('apply_bonus'),
            AuditListEnums::APPROVAL_TYPE_OVERTIME_OS => $t->_('workflow_type_77'),
            '6' => $t->_('7007'),//6我的hc审批
            '9' => $t->_('7005'),
            '10' => $t->_('7006'),
            '11' => $t->_('7008'),
            '12' => $t->_('7009'),
            '13' => $t->_('7010'),
            '14' => $t->_('7011'),
            '16' => $t->_('7013'),
            '17' => $t->_('7015'),
            '18' => $t->_('7016'),
            '19' => $t->_('7017'),
            '20' => $t->_('7016'),
            '21' => $t->_('7021'),
            '22' => $t->_('7022'), // 薪资审批
            '25' => $t->_('7026'), // 运营产品-shop
            '26' => $t->_('7025'), // 运营产品-sales/Project Managerment
            '27' => $t->_('7035'),
            '30' => $t->_('7024'), // 薪资审批
            '23' => $t->_('7023'), // 出差外勤打卡
            '33' => $t->_('7033'), // 消息审批
            '32' => $t->_('7028'), // 黄牌项目出差
            '31' => $t->_('7027'), // 出差外勤打卡
            '34' => $t->_('7034'), // 罚款申诉
            '35' => $t->_('7036'), // offer签字
            '36' => $t->_('os_price_title'), // 罚款申诉
            '37' => $t->_('go_out_application'), // 外出申请
            '38' => $t->_('go_out_clock_in'), // 外出打卡审批
            '39' => $t->_('store_support_support'), // 网点申请支援
            '40' => $t->_('staff_support_support_store'), // 员工申请支援网点
            '41' => $t->_('approval_type_system_cs'), // 众包申请
            '42' => $t->_('abnormal_expense'), // 异常费用管理
            '46' => $t->_('asset_apply'), // 资产申请
            '48' => $t->_('workflow_type_48'), // 众包黑名单审批
            '50' => $t->_('wms_apply'), // 资产申请
            '52' => $t->_('workflow_type_52'),
            '54' => $t->_('workflow_type_54'), // HR处罚申诉
            AuditListEnums::APPROVAL_TYPE_OUTSOURCING_OT => $t->_('outsourcing_ot_approval'), // 外协加班申请
            '49' => $t->_('workflow_type_49'), // 虚假里程
            '56' => $t->_('workflow_type_56'), // Quitclaim审核申请
            '55' => $t->_('workflow_type_55'), // 车辆变更审批
            '58' => $t->_('workflow_type_58'),//续签合同
            AuditListEnums::APPROVAL_TYPE_REINSTATEMENT => $t->_('workflow_type_59'),//员工恢复在职审批
            AuditListEnums::APPROVAL_TYPE_STOP => $t->_('workflow_type_60'),        //停职审批
            AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT => $t->_($workflow_type_63),//申请解约
            AuditListEnums::APPROVAL_HIRE_TYPE_CHANGE => $t->_('hire_change_apply'),//转个人代理申请
            AuditListEnums::APPROVAL_TYPE_REEMPLOYMNET => $t->_('workflow_type_61'),        //重新申请
            AuditListEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT => $t->_('workflow_type_68'),//公司解约个人代理
            AuditListEnums::APPROVAL_TYPE_ADVANCE_FUEL => $t->_('workflow_type_69'),//预支邮费申请
            AuditListEnums::APPROVAL_TYPE_JT_SPECIAL => $t->_('workflow_type_70'), //特殊批量转岗
            AuditListEnums::APPROVAL_TYPE_SUSPEND_WORK => $t->_('workflow_type_72'),//个人代理暂停接单申请
            AuditListEnums::APPROVAL_TYPE_IC_RENEWAL => $t->_('workflow_type_74'),//个人代理续约合同
            AuditListEnums::APPROVAL_TYPE_SICK_CERTIFICATE => $t->_('sick_certificate'),//病假材料申请
            AuditListEnums::APPROVAL_TYPE_FRANCHISEES => $t->_('workflow_type_76'),//注册加盟商
            AuditListEnums::APPROVAL_TYPE_SUSPENSION => $t->_('workflow_type_80'),//停职申请
            AuditListEnums::APPROVAL_TYPE_WORK_HOME => $t->_('workflow_type_81'),//居家打卡审批
            AuditListEnums::APPROVAL_TYPE_CS_PAYMENT => $t->_('workflow_type_82'),//众包结算审批
        ];
        return $typeArray[$type] ?? '';
    }

    /**
     * 获取审批状态
     * @param int $status     审批状态
     * @return string
     */
    public function getAuditStatus($status) : string
    {
        $statusArray = [
            '101' => $this->getTranslation()->_('5104'),
            '102' => $this->getTranslation()->_('4017'),
            '103' => $this->getTranslation()->_('5105'),
            '104' => $this->getTranslation()->_('cancel'),
            '105' => $this->getTranslation()->_('closed'),
            '106' => $this->getTranslation()->_('4017'),
            '107' => $this->getTranslation()->_('4015'),
            '108' => $this->getTranslation()->_('send_request'),
        ];
        return $statusArray[$status] ?? '';
    }

    /**
     * 获取审批状态
     * @param int $status     审批状态
     * @return string
     */
    public function getAuditState($state) : string
    {
        $statusArray = [
            '1' => $this->getTranslation()->_('5104'),
            '2' => $this->getTranslation()->_('4017'),
            '3' => $this->getTranslation()->_('5105'),
            '4' => $this->getTranslation()->_('cancel'),
            '5' => $this->getTranslation()->_('closed'),
        ];
        return $statusArray[$state] ?? '';
    }
    /**
     * 获取审批状态 个人代理翻译 只有马来
     * @param int $status     审批状态
     * @return string
     */
    public function getAuditStateUnpaid($state) : string
    {
        $statusArray = [
            '1' => $this->getTranslation()->_('pending_unpaid'),
            '2' => $this->getTranslation()->_('approved_unpaid'),
            '3' => $this->getTranslation()->_('dismissed_unpaid'),
            '4' => $this->getTranslation()->_('cancel_unpaid'),
            '5' => $this->getTranslation()->_('closed_unpaid'),
        ];
        return $statusArray[$state] ?? '';
    }

    /**
     * 获取审批人可执行操作
     * @param array $paramIn    出入参数
     *        int   staff_id    当前请求用户ID
     *        int   submitter_id 申请人ID
     *        int   status      当前审批状态
     * @return array
     */
    public function getSimpleApprovalOptions($paramIn = [])
    {
        $staffId       = $paramIn['staff_id'];
        $submitterId   = $paramIn['submitter_id'];
        $status        = $paramIn['status'];
        $approvalStaff = $paramIn['approver'] ?? '';

        //查询上级主管
        if (empty($approvalStaff)) {
            $server = new \FlashExpress\bi\App\Server\StaffServer();
            $higherStaff = $server->getHigherStaffId($submitterId);
            if (!empty($higherStaff)) {
                $higherStaff = $higherStaff['value'];
            }
        } else {
            $higherStaff = $approvalStaff;
        }

        if(!empty($higherStaff)) { //存在上级
            if($higherStaff == $staffId || (is_array($higherStaff) && in_array($staffId, $higherStaff))) { //审批人
                if ($status == 1) {
                    $options = $this->generateoptions([1,2]);   //同意驳回
                } else {
                    if ($status == 2) {
                        $options = $this->generateoptions([3]);  //撤销
                    } else {
                        $options = $this->generateoptions([]);   //无操作
                    }
                }
            } else { //提交人
                if ($status == 1) {
                    $options = $this->generateoptions([3]);      //撤销
                } else {
                    $options = $this->generateoptions([]);       //无操作
                }
            }
        } else {
            if($status == 1) {
                if($staffId != $submitterId) {
                    $options = $this->generateoptions([]);       //无操作
                } else {
                    $options = $this->generateoptions([3]);      //撤销
                }
            } else {
                $options = $this->generateoptions([]);           //无操作
            }
        }
        if (empty($options)) {
            $options  = (object)null;
        }
        return $options;
    }

    /**
     * 组织用户操作权限数据
     * @param array $options    用户权限
     * @return array
     */
    public function generateoptions($options) : array
    {
        if (empty($options)) {
            return [];
        }

        $returnData = [];
        foreach ($options as $option) {
            $returnData['code'.$option] = $option;
        }
        return $returnData;
    }

    /**
     * 根据审批级别获取该级审批意见
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getApprovalInfo($paramIn = [])
    {
        $auditId    = $paramIn['audit_id'] ?? '';
        $type       = $paramIn['type'] ?? '';
        $level      = $paramIn['level'] ?? '';
        $status     = $paramIn['status'] ?? '';
        $where      = '';

        if (empty($auditId) || empty($type)) {
            return [];
        }

        if (!empty($status)) {
            $where = " AND status = {$status} ";
        }

        if (!empty($level)) {
            $where .= " AND level = {$level} ";
        }

        if (!empty($auditId)) {
            $where .= " AND audit_id = {$auditId} ";
        }

        $querySql = "--
            SELECT
                audit_id,
                type,
                level,
                status,
                submitter_id,
                staff_ids
            FROM staff_audit_approval
            WHERE
                 type = {$type}
                 {$where}
        ";
        $data = $this->getDI()->get('db')->query($querySql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 获取未审批员工IDs
     *
     * @param int $staffId
     * @param int $submitterId
     * @return mixed
     */
    public function getNotApprovals($staffId, $submitterId = null)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("aa.submitter_id,aa.biz_value,DATE_FORMAT(CONVERT_TZ(sr.leave_date,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') leave_date");
        $builder->from(['aa' => AuditApprovalModel::class]);
        $builder->leftjoin(StaffResignModel::class, 'aa.biz_value = sr.resign_id', 'sr');

        if (!empty($submitterId)) {
            $builder->where("aa.submitter_id = :submitter_id:", [
                'submitter_id' => $submitterId
            ]);
        }
        $builder->andWhere('aa.approval_id = :approval_id: and aa.state = :state: and aa.biz_type  in ({biz_type:array})', [
            'approval_id' => $staffId,
            'state'       => enums::APPROVAL_STATUS_PENDING,
            'biz_type'    => [AuditListEnums::APPROVAL_TYPE_RN,AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT]
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        //按照离职日期的正序排序
        $leaveDate = array_column($list, 'leave_date');
        array_multisort($leaveDate, SORT_ASC, $list);

        return $list;
    }


    /**
     * 获取审批级别审批状况
     *
     * @param int $submitterId
     * @return mixed
     */
    public function getResignDetailByStaffId($submitterId)
    {

        $sql = "--
        SELECT 
            saa.audit_id,
            saa.status
        FROM
            staff_audit_approval as saa
        LEFT JOIN 
            staff_resign sr ON saa.audit_id = sr.resign_id
        WHERE 
            saa.staff_ids = :submitterId and saa.type = :typeUnin and saa.status = 6 and sr.status = :status and saa.level = 1
        ";
        $stmt= $this->getDI()->get('db')->prepare($sql);
        $stmt->bindvalue("submitterId", $submitterId, \PDO::PARAM_INT);
        $stmt->bindvalue("typeUnin", enums::$audit_type['RN'], \PDO::PARAM_INT);
        $stmt->bindvalue("status", enums::$audit_status['panding'], \PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取待审批数
     * @param $type
     * @param $staff_approval_id
     * @return int
     */
    public function getAuditListPendingCount($type, $staff_approval_id): int
    {
        $couInfo = AuditApprovalModel::findFirst([
            'columns' => 'count(1) as cou',
            'conditions' => "biz_type in ({type:array}) and approval_id = :staff_info_id: and state = 1 and deleted = 0",
            'bind' => [
                'type'  => $type,
                'staff_info_id' => $staff_approval_id,
            ],
        ]);
        return $couInfo->cou ?? 0;
    }

    /**
     * 获取我的申请-待审批列表
     * @param int $staffId
     * @param array $conditions
     * @param string $columns
     * @param string $order
     * @param int $pageNum
     * @param int $pageSize
     * @return mixed
     */
    public function getAuditApplyList(int $staffId, array $conditions, string $columns, string $order = '', int $pageNum = 0, int $pageSize = 10)
    {
        //查询主表
        $class = AuditApplyModel::class;

        //获取90天最小id
        $dateTime = date("Y-m-d H:i:s", strtotime("-3 month"));
        $id = $this->findFixedId($class, "submitter_id = :submitter_id: and created_at >= :date_time:", [
            'date_time' => $dateTime,
            'submitter_id' => $staffId
        ]);

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['aa' => $class]);
        $builder->andWhere('submitter_id = :staff_id:', ['staff_id' => $staffId]); //申请人列表，没有搜索申请人工号功能

        if (isset($conditions['audit_type'])) {
            $builder->inWhere('biz_type', $conditions['audit_type']);
        } else {
            $builder->inWhere('biz_type', AuditListEnums::getAllAuditTypes());
        }

        if (isset($conditions['state'])) {
            $builder->inWhere('state', $conditions['state']);
        }
        $builder->andWhere('id >= :main_id: and deleted = :deleted:', ['main_id' => $id, 'deleted' => 0]);

        if (isset($conditions['is_count']) && $conditions['is_count']) {
            $result = $builder->getQuery()->getSingleResult()->cou ?? 0;
        } else {
            $builder->limit($pageSize, ($pageNum - 1) * $pageSize);

            if ($order == AuditListEnums::LIST_SORT_ASC) {
                $builder->orderby('created_at ASC');
            } else {
                $builder->orderby('created_at DESC');
            }
            $result = $builder->getQuery()->execute()->toArray();
        }
        return $result;
    }

    /**
     * 获取我的审批待审批列表
     * @param int $staffId
     * @param array $conditions
     * @param string $columns
     * @param string $order
     * @param int $pageNum
     * @param int $pageSize
     * @return mixed
     */
    public function getAuditApprovalList(int $staffId, array $conditions, string $columns, string $order = '', int $pageNum = 0, int $pageSize = 10)
    {
        //查询主表
        $class = AuditApprovalModel::class;

        //获取90天内数据
        $dateTime = date("Y-m-d H:i:s", strtotime("-3 month"));
        $id = $this->findFixedId($class, "approval_id = :approval_id: and created_at >= :date_time:", [
            'date_time' => $dateTime,
            'approval_id' => $staffId
        ]);

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['aa' => $class]);

        if (isset($conditions['submitter_id'])) {
            $builder->inWhere('submitter_id', $conditions['submitter_id']);
        }

        $builder->andWhere('approval_id = :staff_id:', ['staff_id' => $staffId]);

        if (isset($conditions['audit_type'])) {
            $builder->inWhere('biz_type', $conditions['audit_type']);
        } else {
            $builder->inWhere('biz_type', AuditListEnums::getAllAuditTypes());
        }

        if (isset($conditions['state'])) {
            $builder->inWhere('state', $conditions['state']);
        }
        $builder->andWhere('id >= :main_id: and deleted = 0', ['main_id' => $id]);

//        $builder->andWhere('aa.created_at >= :date_time:', ['date_time' => $dateTime]);

        if (isset($conditions['is_count']) && $conditions['is_count']) {
            $builder->columns('count(1) as cou');
            $result = $builder->getQuery()->getSingleResult()->cou ?? 0;
        } else {
            $builder->columns($columns);
            $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
            switch($order)
            {
                // 走 idx_list 索引  approval_id,created_at,audit_time
                case AuditListEnums::LIST_SORT_DESC:
                    $order_by  = ' created_at DESC ';
                    break;
                case AuditListEnums::LIST_SORT_ASC:
                    $order_by  = ' created_at ASC ';
                    break;
                case AuditListEnums::LIST_AUDIT_TIME_DESC:
                    $order_by =  ' audit_time DESC ';
                    break;
                case AuditListEnums::LIST_AUDIT_TIME_ASC:
                    $order_by =  ' audit_time ASC ';
                    break;
                default:
                    $order_by = ' created_at DESC';
                    break;
            }
            $builder->orderby($order_by);

            $result = $builder->getQuery()->execute()->toArray();
        }
        return $result;
    }

    /**
     * 获取我的审批待审批列表
     * @param int $staffId
     * @return mixed
     */
    public function getAuditApprovalPendingList(int $staffId)
    {
        //查询主表
        $class = AuditApprovalModel::class;

        //获取90天内数据
        $pendingCount = [];
        $dateTime     = date("Y-m-d H:i:s", strtotime("-3 month"));
        foreach (AuditListEnums::getAllAuditTypes() as $bizType) {
            //查询列表
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['aa' => $class]);
            $builder->andWhere('approval_id = :staff_id:', ['staff_id' => $staffId]);
            $builder->andWhere('biz_type = :biz_type:', ['biz_type' => $bizType]);
            $builder->andWhere('state = :state: and deleted = 0', ['state' => enums::APPROVAL_STATUS_PENDING]);
            $builder->columns('count(1) as cou');
            $builder->andWhere('created_at >= :created_at:', ['created_at' => $dateTime]);
            $pendingCount[] = $builder->getQuery()->getSingleResult()->cou ?? 0;
        }
        return array_sum($pendingCount);
    }

    /**
     * 获取我的审批待审批列表
     * @param int $staffId
     * @param array $conditions
     * @param string $columns
     * @param string $order
     * @param int $pageNum
     * @param int $pageSize
     * @return array | int
     */
    public function getCCList(int $staffId, array $conditions, string $columns, string $order = '', int $pageNum = 0, int $pageSize = 10)
    {
        //查询主表
        $class = AuditCCModel::class;
        //状态获取 audit_apply 的状态
        $audit_apply  = AuditApplyModel::class;



        //获取90天最小id
        $dateTime = date("Y-m-d H:i:s", strtotime("-3 month"));
        $id = $this->findFixedId($class, " cc_staff_id = :approval_id: and created_at >= :date_time:", [
            'date_time' => $dateTime,
            'approval_id' => $staffId
        ]);

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['audit_cc' => $class]);
        $builder->leftJoin($audit_apply,'audit_cc.biz_type = audit_apply.biz_type and audit_cc.biz_value = audit_apply.biz_value','audit_apply');

        if (isset($conditions['submitter_id'])) {
            $builder->inWhere('audit_cc.submitter_id', $conditions['submitter_id']);
        }

        $builder->andWhere('audit_cc.cc_staff_id = :staff_id: and  audit_cc.deleted = 0 ', ['staff_id' => $staffId]);

        if (isset($conditions['audit_type'])) {
            $builder->inWhere('audit_cc.biz_type', $conditions['audit_type']);
        } else {
            $builder->inWhere('audit_cc.biz_type', AuditListEnums::getAllAuditTypes());
        }

        if (isset($conditions['submitter_id'])) {
            $builder->inWhere('audit_cc.submitter_id', $conditions['submitter_id']);
        }

        if (isset($conditions['audit_read_type']) && $conditions['audit_read_type'] != 3) {
            $builder->andWhere('audit_cc.is_read = :is_read:', ['is_read' => $conditions['audit_read_type']]);
        }

        $builder->andWhere('audit_cc.id >= :main_id:', ['main_id' => $id]);

        if (isset($conditions['is_count']) && $conditions['is_count']) {
            $result = $builder->getQuery()->getSingleResult()->cou ?? 0;
        } else {
            $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
            $builder->orderBy('audit_cc.id DESC');
            $result = $builder->getQuery()->execute()->toArray();
        }
        return $result;
    }

    /**
     * 获取审批列表中的指定id
     * @param $class
     * @param $condition
     * @param $bind
     * @return int
     */
    public function findFixedId($class, $condition, $bind): int
    {
        if (!class_exists($class)) {
            return 0;
        }
        $specifiedId = $class::findFirst([
            'columns' => 'min(id) as id',
            'conditions' => $condition,
            'bind' => $bind,
        ]);
        if (empty($specifiedId)) {
            return 0;
        }
        return $specifiedId->id ?? 0;
    }

    /**
     * 获取指定类型的待审批数据
     * @param array $auditType
     * @param array $staffIds
     * @return array
     */
    public function getSpecTypesPendingData(array $auditType, array $staffIds = []): array
    {
        //获取90天最小id
        $dateTime = date("Y-m-d H:i:s", strtotime("-3 month"));
        $id = $this->findFixedId(AuditApplyModel::class, "biz_type IN({type:array}) and state = 1 and created_at >= :date_time:", [
            'date_time' => $dateTime,
            'type' => $auditType
        ]);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('aap.approval_id,aa.created_at origin_time');
        $builder->from(['aa' => AuditApplyModel::class]);
        $builder->leftJoin(AuditApprovalModel::class, 'aa.flow_id = aap.flow_id', 'aap');
        $builder->inWhere('aa.biz_type', $auditType);
        if (!empty($staffIds)) {
            $builder->inWhere('aap.approval_id', $staffIds);
        }
        $builder->andWhere('aa.state = :state:', ['state' => enums::APPROVAL_STATUS_PENDING]);
        $builder->andWhere('aap.state = :state: and aap.deleted = 0', ['state' => enums::APPROVAL_STATUS_PENDING]);
        $builder->andWhere('aa.id >= :main_id:', ['main_id' => $id]);
        return $builder->getQuery()->execute()->toArray();
    }


}

