<?php
namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;

class AuditApplyRepository extends BaseRepository
{
    public function editOtTypeOfSummary($biz_value, $submitter_id, $ot_type)
    {
        try {
            $apply = AuditApplyModel::findFirst([
                'conditions' => 'biz_type=:biz_type: and biz_value=:biz_value: and submitter_id=:submitter_id:',
                'bind'       => [
                    'biz_type'     => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                    'biz_value'    => $biz_value,
                    'submitter_id' => $submitter_id,
                ],
            ]);
            if ($apply && $apply->summary) {
                $summary = json_decode($apply->summary, true);
                foreach ($summary as &$item) {
                    if (strtolower($item['key']) == 'ot_type') {
                        $item['value'] = $ot_type;
                    }
                }
                $apply->summary = json_encode($summary, JSON_UNESCAPED_UNICODE);
                $apply->save();
            }
          $approvals =  AuditApprovalModel::find([
                'conditions' => 'biz_type=:biz_type: and biz_value=:biz_value: and submitter_id=:submitter_id: and deleted = 0',
                'bind'       => [
                    'biz_type'     => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                    'biz_value'    => $biz_value,
                    'submitter_id' => $submitter_id,
                ],
            ]);
            if ($approvals) {
                foreach ($approvals as $approval) {
                    $summary = json_decode($approval->summary, true);
                    foreach ($summary as &$item) {
                        if (strtolower($item['key']) == 'ot_type') {
                            $item['value'] = $ot_type;
                        }
                    }
                    $approval->summary = json_encode($summary, JSON_UNESCAPED_UNICODE);
                    $approval->save();
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage(), 'error');
        }
    }

    /**
     * 获取审批数据
     * @param $params
     * @param $columns
     * @return array
     */
    public static function getAuditApplyList($params, $columns = '*', $isDeleted = false)
    {
        if(empty($params['biz_type'])) {
            return [];
        }

        $conditions = 'biz_type = :biz_type:';
        $bind['biz_type'] = $params['biz_type'];

        if(!empty($params['state'])) {
            $conditions .= ' and state = :state:';
            $bind['state'] = $params['state'];
        }

        if(!empty($params['biz_value'])) {
            $conditions .= ' and biz_value = :biz_value:';
            $bind['biz_value'] = $params['biz_value'];
        }

        if($isDeleted) {
            $conditions .= ' and deleted = :deleted:';
            $bind['deleted'] = enums::DELETED_NO;
        }

        return AuditApplyModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }

}