<?php
/**
 * Author: Bruce
 * Date  : 2023-12-05 10:59
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;

class StaffHikvisionRepository extends BaseRepository
{
    /**
     * 获取HIK信息
     * @param $staffInfoId
     * @param string $columns
     * @return array
     */
    public function getStaffHikvisionInfo($staffInfoId, $columns = '*')
    {
        $staffInfo = StaffHikvisionModel::findFirst([
            'columns'    => $columns,
            'conditions' => "staff_info_id = :staff_id:",
            'bind'       => [
                'staff_id' => $staffInfoId,
            ],
        ]);

        return !empty($staffInfo) ? $staffInfo->toArray() : [];
    }

    /**
     * 查询数据
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getListQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(*) as count');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(StaffHikvisionModel::class, 'hsi.staff_info_id = sh.staff_info_id ', 'sh');
        $builder = $this->getBuilderWhere($builder, $params);

        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title',
            'hsi.sex',
            'hsi.nationality',
            'sh.id as staff_hikvision_id',
        ]);

        if (empty($params['not_limit'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        }

        $builder->orderBy('hsi.id desc');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 构建 sql
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        $builder->where('hsi.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        $builder->andWhere('hsi.state != :state:', ['state' => HrStaffInfoModel::STATE_2]);
        $builder->andWhere('hsi.sys_store_id = :store_id:', ['store_id' => $params['sys_store_id']]);

        //不需要展示的员工工号
        if (!empty($params['not_need_display_staff_ids'])) {
            $builder->andWhere('hsi.staff_info_id not in ({not_need_display_staff_ids:array})',
                ['not_need_display_staff_ids' => $params['not_need_display_staff_ids']]);
        }

        //去除已同步的 就是 未上传的。 未上传员工 = 网点全部员工 - 已同步的员工 - 待处理的员工
        if (!empty($params['is_sync'])) {
            $builder->andWhere('sh.is_sync = :is_sync: ', ['is_sync' => $params['is_sync']]);
        }

        if(!empty($params['syncs'])) {
            $builder->andWhere('sh.is_sync in ({is_syncs:array})', ['is_syncs' => $params['syncs']]);
        }

        //员工姓名
        if (!empty($params['staff_keyword'])) {
            $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:)',
                ['staff_keyword' => '%' . $params['staff_keyword'] . '%']);
        }

        return $builder;
    }
}