<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/6/17
 * Time: 下午6:23
 */


use FlashExpress\bi\App\library\ApiClient;
use  FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\Enums\SysStoreCateEnums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffJobLevelLogModel;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;

use App\Country\Tools;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Models\backyard\StaffLeaveForDimissionModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;

use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysForLeaveModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;

use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoExtendMode;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\library\OssHelper;


//员工扩展任务类
class StaffExtendTask extends BaseTask{

    //员工自动请假任务  总部员工() 周日自动补全 请假  网点员工 根据hrs中员工配置休息日 操作自动请假 每天凌晨2点跑 前一天数据
    public function add_leaveAction($param){
        //return '停掉 先保留';
        $logger = $this->getDI()->get('logger');
        //指定日期 跑任务 方便测试
        $tmp = time();
        if(!empty($param[0])){
            $tmp = strtotime($param[0]);
            $check = date('Y-m-d', strtotime($param[0]));
            if($check == '1970-01-01')
                die("wrong date input \r\n");

            $date = date('Y-m-d',$tmp);
            $week = date('w', $tmp);
        }else{
            $date = date('Y-m-d',$tmp );
            $week = date('w', $tmp);
        }


        //获取昨天天礼拜几 转化成int
        if($week == 0)
            $week = 7;

        try{
            //获取所有符合条件员工  总部+网点  新需求 朝晖：总部员工 也需要查询轮休表 因为 有的人属于总部但是 在网点工作 是单休 查询条件当月如果有设置轮休记录 默认该员工是 单休 不做自动请假操作
            //总部 如果不是周末 不用取 部门 4 和 25  算总部员工 有轮休 就是单休 没有就是双休
            //新增 week_working_day 字段 判断 工作5天还是6天
            $dep_data = array();
            if($week == 6 || $week == 7){

                $where = "( formal in (1,4) and state in (1,3) and is_sub_staff = 0 and week_working_day = 5 ) ";

                $staff_dep_sql = "select staff_info_id,sys_store_id ,sys_department_id from hr_staff_info
                                  where {$where}
                          ";

                $dep_data = $this->getDI()->get('db_rby')->query($staff_dep_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            }

            $insert = $split = $log_arr = array();
            if(!empty($dep_data)){
                $exist = array();
                //拿工号 去查询 是否已经有请假记录
                $staff_ids = array_column($dep_data,'staff_info_id');
                $id_str = implode(',',$staff_ids);
                $exist_sql = "select sp.staff_info_id 
                              from staff_audit_leave_split sp
                              join staff_audit a on sp.audit_id = a.audit_id
                              where sp.staff_info_id in ({$id_str}) and sp.date_at = '{$date}' 
                              and a.status in (1,2)
                              ";

                $exist_data = $this->getDI()->get('db')->query($exist_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($exist_data)){
                    $exist = array_column($exist_data,'staff_info_id');
                    $exist = array_unique($exist);
                }

                //去重
                $unique = array();
                foreach ($dep_data as $staff){
                    if(in_array( $staff['staff_info_id'], $exist))//已经存在请假 不需要自动补假
                        continue;

//                    if(in_array($staff['staff_info_id'], $work_ids))//设置过轮休 不操作自动请假 视为该员工 单休 自己请
//                        continue;

                    if(in_array($staff['staff_info_id'], $unique)){
                        continue;
                    }else{
                        $unique[] = $staff['staff_info_id'];
                    }


                    $log_arr[] = $row['staff_info_id'] = $staff['staff_info_id'];
                    $row['audit_type'] = 2;
                    $row['leave_type'] = 15;
                    $row['leave_start_time'] = $date.' 09:00:00';
                    $row['leave_start_type'] = 1;
                    $row['leave_end_time'] = $date.' 18:00:00';
                    $row['leave_end_type'] = 2;
                    $row['leave_day'] = 1;
                    $row['status'] = 2;
                    $row['audit_reason'] = 'system_task';

                    $insert[] = $row;

                    //拼接 staff_audit_leave_split 数据
                    $v['audit_id'] = 0;
                    $v['staff_info_id'] = $staff['staff_info_id'];
                    $v['date_at'] = $date;
                    $v['type'] = 0;
                    $split[] = $v;
                }

            }

//            //网点员工  --网点员工工作日配置 错误 导致 自动请假数据错误 注释
            $staff_store_sql = "select s.staff_info_id,s.sys_store_id,sys_department_id from hr_staff_info s
                                -- join hr_staff_shift sf on s.staff_info_id = sf.staff_info_id
                                where s.formal in (1,4)  and s.state in (1,3) and s.is_sub_staff = 0
                                and s.week_working_day = 6
                                ";
            $store_data = $this->getDI()->get('db_rby')->query($staff_store_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            //拿工号 去查询 是否已经有请假记录
            $staff_ids = array_column($store_data,'staff_info_id');

            //获取员工 是否为休息日
            $staff_model = new StaffRepository();
            $rest_days = $staff_model->getWorkdays($staff_ids,$date);
            //只跑 配置休息日的员工
            if(!empty($rest_days)){
                $staff_ids = array_column($rest_days,'staff_info_id');
                $staff_ids = array_unique($staff_ids);
                $id_str = implode(',',$staff_ids);


                //已经存在别的假期
                $exist_sql = "select sp.staff_info_id 
                              from staff_audit_leave_split sp
                              join staff_audit a on sp.audit_id = a.audit_id
                              where sp.staff_info_id in ({$id_str}) and sp.date_at = '{$date}' 
                              and a.status in (1,2)
                              ";
                $exist_data = $this->getDI()->get('db')->query($exist_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                $exist = array();
                if(!empty($exist_data)){
                    $exist = array_column($exist_data,'staff_info_id');
                }
                //这周 已经存在 休息日类型的假期 员工
                $monday = weekStart($date) . " 00:00:00";
                $sunday = weekEnd($date) . " 23:59:59";
                $sql = "select s.staff_info_id 
                        from staff_audit_leave_split s 
                        join staff_audit a on s.staff_info_id = a.staff_info_id and s.audit_id = a.audit_id
                        where s.date_at between '{$monday}' and '{$sunday}' and a.audit_type = 2 and a.leave_type = 15
                        and a.status in (1,2)
                        ";
                $has_leave = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if(!empty($has_leave)){
                    $has = array_column($has_leave,'staff_info_id');
                    $exist = array_merge($exist, $has);
                    $exist = array_unique($exist);
                }

                //去重
                foreach ($staff_ids as $staff){
                    if(in_array($staff, $exist))//已经存在请假 不需要自动补假
                        continue;

                    $log_arr[] = $row['staff_info_id'] = $staff;
                    $row['audit_type'] = 2;
                    $row['leave_type'] = 15;
                    $row['leave_start_time'] = $date.' 09:00:00';
                    $row['leave_start_type'] = 1;
                    $row['leave_end_time'] = $date.' 18:00:00';
                    $row['leave_end_type'] = 2;
                    $row['leave_day'] = 1;
                    $row['status'] = 2;
                    $row['audit_reason'] = 'system_task';
                    $insert[] = $row;


                    //拼接 staff_audit_leave_split 数据
                    $v['audit_id'] = 0;
                    $v['staff_info_id'] = $staff;
                    $v['date_at'] = $date;
                    $v['type'] = 0;
                    $split[] = $v;
                }

            }


            if(!empty($insert)){
                $db = $this->getDI()->get("db");
                $db->begin();
                $model = new Repository\AuditRepository();
                $flag = $model->batch_insert('staff_audit', $insert);
                $model->batch_insert('staff_audit_leave_split',$split);

                if($flag){
                    $logger->write_log('staff_extend_task 自动请假任务成功 '.json_encode($log_arr),'info');
                    $db->commit();
                }else{
                    $logger->write_log('staff_extend_task 自动请假任务失败  '.json_encode($log_arr));
                    $db->rollback();
                }
            }else{
                $logger->write_log('staff_extend_task 自动请假任务 无记录 ','info');
            }

        }catch (\Exception $e){
            echo $e->getMessage();
            $logger->write_log('staff_extend_task:自动请假任务 报错 '.$e->getMessage());
        }

    }

    //监控考勤错误日历
    public function attendanceAction(){
        $logger = $this->getDI()->get('logger');
        $date = date('Y-m-01',time());

        $sql = "select staff_info_id,attendance_date,started_remark,end_remark from `staff_work_attendance`  where
                TIMESTAMPDIFF (hour,`started_at` ,`end_at` ) <0 
                and `attendance_date`  > '{$date}'";

        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $count = count($data);
        if($count > 0){
            $num = 0;
            foreach ($data as $da){
                if(strstr($da['started_remark'],'(sync)') || strstr($da['end_remark'],'(sync)'))
                    $num++;
            }

            if($count - $num > 0)
                $logger->write_log("监控考勤 考勤错误数据 {$count}条 记录，其中产品化项目同步{$num}条 ".json_encode($data));
        }

    }

    //（需求：所属网点为 “Head office” 且工作天数为 “6” 需带入到 FBI——HRIS——网点轮休管理，默认休息日为：周日，可修改）逻辑：每月 1号凌晨 处理 下个月整月 相关员工 周日为休息日
    //需求变更 由于新入职员工 无法实时配置 周日默认休息  任务修改为 每天凌晨 跑 当月和下月数据  时间区间有  一个月 更改为2个月
    public function work_dayAction($param){
        return; //废弃 新脚本StaffFixedDayOffTask
        $logger = $this->getDI()->get('logger');
        try{
            $sql = "select staff_info_id from hr_staff_info
                    where state = 1 and formal in(1,4)
                    and is_sub_staff = 0
                    and week_working_day = 6
                    and sys_store_id = '-1'
                    ";
            //如果是泰国  剔除仓储的人
            if(isCountry()){
                $sql = "select staff_info_id from hr_staff_info as i left join `sys_department`  as d on i.`node_department_id`  = d.id 
                    where i.state = 1 and i.formal in(1,4)
                    and i.is_sub_staff = 0
                    and i.week_working_day = 6
                    and i.sys_store_id = '-1' and (d.`company_id`  not in ('30001','50001','60001','70001')   or  d.`company_id` is null)";
            }

            if(!empty($param[0]))
                $sql .= " and staff_info_id = {$param[0]}";


            $staff_ids = $this->getDI()->get('db_rby')->fetchAll($sql);
            if(empty($staff_ids))
                die('no staff_ids');

            $staff_ids = array_column($staff_ids,'staff_info_id');

            //时间 区间 两个月 当月和下月 去除所有周日
            $sundays = array();
            $current_date =  date('Y-m-d', time());
            //因为 周期是 每周一天 开始时间要取最早的礼拜1
            $start_date = weekStart($current_date);
            if(!empty($param[1]))
                $start_date = $param[1];
            $end_date =  date('Y-m-01', strtotime('+1 month'));

            $month_end = date('Y-m-d', strtotime("{$end_date} +1 month -1 day"));//下个月的最后一天

            //如果 该员工对应 周 有人工配置 则 该员工 该周日 跳过配置 论析
            $staff_str = "(" . implode(',',$staff_ids) .")";
            $work_sql = "select staff_info_id ,date_at,operator from hr_staff_work_days 
                        where `date_at` between '{$start_date}' and '{$month_end}' and staff_info_id in {$staff_str}
                        ";

            $work_staff = $this->getDI()->get('db')->fetchAll($work_sql);
            $sub_staff = $exist = array();
            if(!empty($work_staff)){
                //根据 配置 如期 获取 所在周日 下面 默认操作时候 跳过该周日 日期
                foreach($work_staff as $v){
                    if($v['operator'] == 10000){
                        $exist[$v['staff_info_id']][] = $v['date_at'];//系统已经跑过的轮休
                        continue;

                    }
                    $week_end = weekEnd($v['date_at']);
                    $sub_staff[$v['staff_info_id']][] = $week_end;
                }
            }

            while($start_date <= $month_end){
                if(date('w',strtotime($start_date)) == 0){
                    $sundays[] = $start_date;
                }
                $start_date = date('Y-m-d',strtotime("{$start_date} +1 day"));
            }
            $insert = array();
            foreach ($staff_ids as $staff_id){
                foreach ($sundays as $sunday){
                    //根据 配置 如期 获取 所在周日 下面 默认操作时候 跳过该周日 日期
                    if(!empty($sub_staff[$staff_id]) && in_array($sunday,$sub_staff[$staff_id]))
                        continue;

                    //如果系统已经设置 跳过
                    if(!empty($exist[$staff_id]) && in_array($sunday,$exist[$staff_id]))
                        continue;

                    $row['staff_info_id'] = $staff_id;
                    $row['month'] = substr($sunday,0,7);
                    $row['date_at'] = $sunday;
                    $row['operator'] = '10000';
                    $row['remark'] = 'sys_task';
                    $insert[] = $row;
                }
            }

            $model = new Repository\AuditRepository();
            $flag = $model->batch_insert('hr_staff_work_days', $insert);
            if($flag || empty($insert)){
                $logger->write_log('staff_extend_task:轮休默认周日成功 ','info');
            }else{
                $logger->write_log('staff_extend_task:轮休默认周日失败  ','info');
            }

        }catch (\Exception $e){
            $logger->write_log('staff_extend_task:自动请假任务失败 ' . $e->getMessage());
        }

    }




    //加班申请 走审批流 并且 展示 揽派件与考勤时长的 一个比例 供审批参考 https://l8bx01gcjr.feishu.cn/docs/doccn9Z71tvsd6AB4aaSJtwArdb
    //每天 过泰国时间中午 12点跑一次前一天 数据 防止 跨天班次 导致计算不准确 保存表 statistic_for_overtime
    //新版需求 兼容 shop 类型网点 Shop officer及Shop cashier 职位 https://l8bx01gcjr.feishu.cn/docs/doccnbcaB3z2KfP7o8fRVmQhN8R#mrmHUv
    public function overtime_statisticAction($param){
        //统计的 职位  新增 Shop officer 98 及Shop cashier 97 但是这两个职位 没有需要统计的数据
        $statistic_job_title = "451,37,97,98";
        $statistic_store_category = "1,2,10,4,5,7,13";
        //（上周类型网点总揽收量+ 上周类型网点总派件量）/（上周类型网点出勤总人次*9） 新需求 要加个职位统计时长出勤什么的 https://flashexpress.feishu.cn/docx/doxcnHhCk5cfPkDrhOqFjwqt0me
        $dc_job = array(enums::$job_title['dc_officer'],enums::$job_title['assistant_branch_supervisor']);
        $shop_job = array(97,98);
        $dc_category = array(1,2,10,13);
        $shop_category = array(4,5,7);
        //上周所有网点总揽件量+上周所有网点总派件量   delivery_avg_count  store_pickup_data_emr
        $yesterday = date("Y-m-d",strtotime("-1 day"));
        if(!empty($param[0]))
            $yesterday = date('Y-m-d',strtotime($param[0]));//指定日期

       try{
           //修改需求 取 网点分类是 1，2，10
           $sql = " select id,category from sys_store where category in ({$statistic_store_category}) and state = 1";
           $stores = $this->getDI()->get('db_fle')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

           //主要维度数据 网点 为空 不继续往下跑了
           if(empty($stores)){
               $this->getDI()->get('logger')->write_log("overtime_statistic_{$yesterday} not store data",'info');
               die('not store data');
           }

           $all_store = array_column($stores,'id');
           $category_store = array_column($stores,'category','id');
           unset($stores);
           $str = "'" . implode("','",$all_store) . "'";

           $table = isCountry('TH') ? 'store_pickup_data_emr' : 'store_pickup_data';

           //揽件数量
           $sql = " select store_id , count_total from $table where stat_date = '{$yesterday}' and store_id in ({$str})";
           $pickup = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
           if(!empty($pickup)){
               $pickup = array_column($pickup,'count_total', 'store_id');
           }

           //派件数量
           $sql = " select store_id , delivery_count from delivery_avg_count where created_at = '{$yesterday}' and store_id in ({$str})";
           $delivery = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
           if(!empty($delivery)){
               $delivery = array_column($delivery,'delivery_count', 'store_id');
           }

           //考勤时长 指定网点 所有人
           $sql = " select staff_info_id,organization_id, started_at,end_at ,TIMESTAMPDIFF(MINUTE,started_at,end_at) time_last 
                from staff_work_attendance where attendance_date = '{$yesterday}' 
                and started_at is not null and end_at is not null
                and organization_type = 1 and organization_id is not null and organization_id in ({$str})
               ";
           $attendance_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

           //统计对应职位总人数
           $sql = "select staff_info_id,sys_store_id,job_title from hr_staff_info where state = 1 
                    and is_sub_staff = 0 and  job_title in ({$statistic_job_title}) 
                  ";
           $staff_data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
           $dc_staffs = $shop_staffs = array();
           if(!empty($staff_data)){
               //按 职位分组
               foreach ($staff_data as $sta){
                   if(in_array($sta['job_title'],$dc_job))
                       $dc_staffs[] = $sta['staff_info_id'];
                   if(in_array($sta['job_title'],$shop_job))
                       $shop_staffs[] = $sta['staff_info_id'];
               }
           }
           //对应网点的 指定职位的员工 总数数量 dc => dc职位
           $dc_num = array_column($staff_data,'sys_store_id','staff_info_id');
           $job_num_37 = array();
           foreach ($dc_num as $staff => $store){
               if(in_array($staff,$dc_staffs)){
                   if(empty($job_num_37[$store])){
                       $job_num_37[$store] = 1;
                   }else{
                       $job_num_37[$store]++;
                   }
               }
           }

           if(!empty($attendance_data)){
               $in_all_time = $job_time = array();//网点所有员工工作时长 和 指定职位 工作时长 当天网点 dc office 总人数
               foreach ($attendance_data as $att){
                   $time = intval($att['time_last']);//持续时间 分钟 取整
                   //按网点类型分组
                   if(in_array($category_store[$att['organization_id']],$dc_category)){
                       //初始化 一期是时长
                       if(empty($in_all_time[$att['organization_id']]))
                           $in_all_time[$att['organization_id']] = $time;
                       else
                           $in_all_time[$att['organization_id']] += $time;
                       //如果这个人 是一期 dc职位 单独计算该职位的考勤时长
                       if(!empty($dc_staffs) && in_array($att['staff_info_id'], $dc_staffs)){
                           if(empty($job_time[$att['organization_id']])){
                               $job_time[$att['organization_id']] = $time;
                           }else{
                               $job_time[$att['organization_id']] += $time;
                           }
                       }
                   }else if(in_array($category_store[$att['organization_id']],$shop_category)){
                       //二期 shop 相关 只计算出勤员工数量
                       if(empty($in_all_time[$att['organization_id']]))
                           $in_all_time[$att['organization_id']] = 1;
                       else
                           $in_all_time[$att['organization_id']]++;
                   }
               }
           }

           //组织数据
           $insert = array();
           foreach ($all_store as $store){
               $row['store_id'] = $store;
               $row['date_at'] = $yesterday;
               $row['delivery_num'] = empty($delivery[$store]) ? 0 : $delivery[$store];
               $row['pickup_num'] = empty($pickup[$store]) ? 0 : $pickup[$store];
               $row['attendance_time'] = empty($in_all_time[$store]) ? 0 : $in_all_time[$store];
               $row['attendance_time_for_37'] = empty($job_time[$store]) ? 0 : $job_time[$store];
               $row['number_for_37'] = empty($job_num_37[$store]) ? 0 : $job_num_37[$store];//对应 dc职位的 网点人数
               //针对二期兼容 新增字段 1 一期  2 二期 按网点类型区分
               $row['flag'] = 1;
               if(in_array($category_store[$store],$shop_category))
                   $row['flag'] = 2;
               $insert[] = $row;
           }

           //验证是否存在数据 删除 重新跑
           $model = new Repository\StatisticForOvertimeRepository();

           $check_data = $model->find_by_date($yesterday);
           if($check_data)
               $model->delete_by_date($yesterday);
           $flag = $model->batch_insert('statistic_for_overtime', $insert);

           if($flag){
               $this->getDI()->get('logger')->write_log('overtime_statistic_固化加班申请数据比例成功 '.$yesterday,'info');
           }else{
               $this->getDI()->get('logger')->write_log('overtime_statistic_固化加班申请数据比例失败 '.$yesterday,'info');
           }
       }catch (\Exception $e){
           $this->getDI()->get('logger')->write_log('overtime_statistic_固化加班申请数据比例失败 '.$yesterday);
       }

    }


    //同步 车队 外协员工 考勤记录 每天泰国时间凌晨过后跑前一天
    public function fleet_attendanceAction($p){
        if(!empty($p[0]))
            $start_date = $p[0];

        //获取数据
        $api_url = env('fleet_url');
        $method = 'syncSignin';
        $con = new ControllerBase();
        $param['page'] = 1;
        $param['page_size'] = 5000;

        $param['date'] = empty($start_date) ? date('Y-m-d',strtotime('-1 day')) : $start_date;
        $insert = $staff_ids = array();

        //调试
        if(isset($p[1])){
            echo date('Y-m-d H:i:s',time());

            $fle_rpc = (new ApiClient($api_url,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            var_dump($res);exit;
        }

        while(true){
            $fle_rpc = (new ApiClient($api_url,'',$method, $this->lang));
            $fle_rpc->setParams($param);
            $res = $fle_rpc->execute();

            if($res['result']['code'] != 1)
                break;

            if(empty($res['result']['data']))
                break;

            $data = $res['result']['data'];
            //组装数据
            foreach ($data as $da){
                if(empty($da['started_at']) && empty($da['end_at']))
                    continue;

                $row['staff_info_id'] = $staff_ids[] = $da['staff_info_id'];
                $row['attendance_date'] = $da['attendance_date'];
                $row['started_at'] = $da['started_at'];
                $row['started_staff_lat'] = $da['started_staff_lat'];
                $row['started_staff_lng'] = $da['started_staff_lng'];
                $row['end_at'] = $da['end_at'];
                $row['end_staff_lat'] = $da['end_staff_lat'];
                $row['end_staff_lng'] = $da['end_staff_lng'];
                $row['started_remark'] = $row['end_remark'] = 'fleet interface';//备注

                $insert[] = $row;
            }
            $param['page']++;
        }

        //判断 员工是否存在 staff info
        if(empty($staff_ids)){
            $this->logger->write_log('fleet_attendance:接口没返回数据 ','info');
            return;
        }
        $str = implode(',', $staff_ids);
        $check_sql = "select staff_info_id from hr_staff_info where staff_info_id in ({$str})";
        $check = $this->getDI()->get('db_rby')->query($check_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if(empty($check)){
            $this->logger->write_log('fleet_attendance:没查询到员工信息 ','info');
            return;
        }

        $check = array_column($check,'staff_info_id');
        //获取这些人打卡数据
        $att_sql = "select staff_info_id from staff_work_attendance 
        where staff_info_id in ({$str}) and attendance_date = '{$param['date']}'";
        $exist_att = $this->getDI()->get('db')->query($att_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($exist_att))
            $exist_att = array_column($exist_att,'staff_info_id');

        foreach ($insert as $k => $in){
            //没有该员工信息
            if(!in_array($in['staff_info_id'],$check)){
                unset($insert[$k]);
                continue;
            }
            //已存在打卡记录
            if(!empty($exist_att) && in_array($in['staff_info_id'],$exist_att)){
                unset($insert[$k]);
                continue;
            }
        }

        $insert = array_values($insert);

        //保存数据
        $model = new Repository\StaffWorkAttendanceRepository();
        $flag = $model->batch_insert('staff_work_attendance', $insert);

        if($flag){
            $this->logger->write_log('fleet_attendance:导入车队外协考勤记录成功 ','info');
        }else{
            $this->logger->write_log('fleet_attendance:导入车队外协考勤记录失败 ','info');
        }
    }

    //每天根据员工职等不同 当天凌晨之后 固化截止当天应有年假
    public function freeze_by_levelAction($param){
        //任务锁
        $key = 'freeze_by_level_lock';
        $redis = $this->getDI()->get("redisLib");
        $rs = $redis->set($key, 1, array('nx', 'ex' => 10 * 60));//锁10分钟
        if (!$rs && RUNTIME == 'pro') {
            echo 'task is running';
            return ;
        }

        $today = date('Y-m-d');
        ini_set('memory_limit', '-1');
        if(!empty($param[0]))
            $today = $param[0];
        $param_staff_id = '';
        if(!empty($param[1]))
            $param_staff_id = $param[1];
        try{
            //判断是不是闰年 确定分母是多少
            $year = intval(date('Y',strtotime($today)));
            $all_days = 365;
            if(year_type($year))
                $all_days = 366;

            //去掉 实习生筛选 实习生没有年假 只取 在职的 并且雇佣类型是 1，2，3，4 https://l8bx01gcjr.feishu.cn/docs/doccn9HSIE7xyuJ8LibYEspiVCd
            $condition = " state in (1,3) and is_sub_staff = 0 and formal in (1,4)";
            if(!empty($param_staff_id))
                $condition .= " and staff_info_id = {$param_staff_id}";

            $staff_list = HrStaffInfoModel::find([
                'conditions' => $condition,
                'columns' => 'staff_info_id,job_title_level,job_title_grade_v2,hire_date'
            ])->toArray();
            if(empty($staff_list))
                die('没有员工数据');

            //查询 已经存在的员工额度数据 判断 是否为新入职的 需要insert 其他 update
            $exist_list = StaffDaysFreezeModel::find([
                'columns' => 'staff_info_id,job_title_level,job_title_grade,updated_at,date_at'
            ])->toArray();
            $exist_list = empty($exist_list) ? array() : array_column($exist_list,null,'staff_info_id');
            $staff_list = array_column($staff_list,null,'staff_info_id');

//            $dep_model = new Repository\DepartmentRepository($this->lang);
//            $c_staffs = $dep_model->get_c_level();

            $model = new StaffDaysFreezeModel();
            $log_model = new StaffJobLevelLogModel();
            foreach ($staff_list as $staff_id => $staff_info){
                if(empty($staff_id))
                    continue;
                $in = array();
                $up_model = null;

                //获取 员工应有额度天数 用历史最高职等 先注释 以后用
//                $freeze_grade = $exist_list[$staff_id]['job_title_grade'] ?? 0;
//                $level_day = $this->staff_level_days($staff_info,$freeze_grade);
                $level_day = $this->staff_level_days($staff_info,0);//老挝 菲律宾 泰国



//                //多国家 c级别的天数都是 20天
//                if(in_array($staff_id,$c_staffs))
//                    $level_day = enums::C_LEVEL_DAYS;

                if(!empty($exist_list[$staff_id])){
                    $update_date = $exist_list[$staff_id]['date_at'];
                    if($update_date >= $today && !empty($param_staff_id)) //如果更新时间 并且传了工号 大于当天零点 说明已经更新过
                        continue;

                    //已存在记录 更新操作
                    $up_model = StaffDaysFreezeModel::findFirst($staff_id);
                    $up_model->days = $add_day = $up_model->days + round($level_day / $all_days,enums::ROUND_NUM);
                    //如果 是每年的 元旦 初始化数据 当天已经是1月1号 数据重置
                    if ($today == date('Y-01-01')) {
                        $up_model->days = round($level_day / $all_days, enums::ROUND_NUM);
                        $up_model->used_days = 0;
                    }
                    //如果职等变更 记录log
                    if($staff_info['job_title_level'] != $exist_list[$staff_id]['job_title_level'] || $staff_info['job_title_grade_v2'] != $exist_list[$staff_id]['job_title_grade']){
                        $log_in['staff_info_id'] = $staff_id;
                        $log_in['date_at'] = $today;
                        $log_in['job_title_level'] = $staff_info['job_title_level'];
                        $log_in['job_title_grade'] = $staff_info['job_title_grade_v2'];

                        //有变更职级 更新一下 职等取最高的
                        $up_model->job_title_level = $staff_info['job_title_level'];
                        //还原 原来逻辑
                        $up_model->job_title_grade = $staff_info['job_title_grade_v2'];
                        $up_model->date_at = $today;

                        $clone_m = clone $log_model;
                        $clone_m->create($log_in);
                    }

                    $flag = $up_model->update();
                    if(!$flag)
                        $this->logger->write_log("freeze_by_level {$today} 更新数据失败 {$staff_id}");
                    else
                        $this->logger->write_log("freeze_by_level {$today} 更新数据成功 {$staff_id} {$add_day}",'info');
                    continue;
                }


                //新增 新入职的员工
                $in['staff_info_id'] = $staff_id;
                $in['leave_type'] = enums::LEAVE_TYPE_1;
                $in['date_at'] = $today;
                $in['days'] = round($level_day / $all_days,enums::ROUND_NUM);
                $in['job_title_level'] = $staff_info['job_title_level'];
                $in['job_title_grade'] = $staff_info['job_title_grade_v2'];
                $clone_m = clone $model;
                $flag = $clone_m->create($in);
                if(!$flag)
                    $this->logger->write_log("freeze_by_level {$today} 插入数据失败 {$staff_id}");

                $log_in['staff_info_id'] = $staff_id;
                $log_in['date_at'] = $today;
                $log_in['job_title_level'] = $staff_info['job_title_level'];
                $log_in['job_title_grade'] = $staff_info['job_title_grade_v2'];

                $clone_m = clone $log_model;
                $clone_m->create($log_in);
            }


            $redis->delete($key);
        }catch (\Exception $e){
            $this->logger->write_log("freeze_by_level {$today} 任务数据失败 ".$e->getMessage());
            die('freeze_by_level 任务异常');
        }


    }


    /**
     *
     *  计算 年假 对应员工的 应有额度
     * @param $staff_info 需要 grade 和hire time
     * @param int $freeze_grade 固化的历史最高职等
     * @return mixed
     * @throws ReflectionException
     */
    public function staff_level_days($staff_info,$freeze_grade = 0){
        //看哪个职等高
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $leave_server = Tools::reBuildCountryInstance($leave_server,[$this->lang,$this->timezone]);
        $grade = $freeze_grade;
        if($staff_info['job_title_grade_v2'] > $freeze_grade)
            $grade = $staff_info['job_title_grade_v2'];

        //按历史最高职级 给额度 老挝 固定额度 freeze 存的是历史最高职级
        $level_day = $leave_server->get_year_leave_days(array('job_title_grade_v2' => $grade));

//        //额外加上 入职满整年天数
//        $over_year = $leave_server->over_one_year_days($staff_info);
//        $level_day += $over_year;
//
//        //不能超过上限
//        $max_days = $leave_server->get_year_leave_max_days(array('job_title_grade_v2' => $grade));
//        if($level_day > $max_days)
//            $level_day = $max_days;

        return $level_day;
    }




    //初始化staff_days_freeze 年假改版 初始化任务 https://l8bx01gcjr.feishu.cn/docs/doccn9HSIE7xyuJ8LibYEspiVCd
    public function freeze_firstAction($param){

        ini_set('memory_limit', '-1');
        try{
            $staff_id = '';
            if(!empty($param[0]))
                $staff_id = $param[0];

            $condition = " state in (1,3) and is_sub_staff = 0 and formal in (1,4) ";
            if(!empty($staff_id))
                $condition .= " and staff_info_id = {$staff_id}";

            $staff_list = HrStaffInfoModel::find([
                'conditions' => $condition,
                'columns' => 'staff_info_id,job_title_level,hire_date,job_title_grade_v2'
            ])->toArray();
            if(empty($staff_list))
                die('没有员工数据');

            //判断是不是闰年 确定分母是多少
            $year = intval(date('Y'));
            $all_days = 365;
            if(year_type($year))
                $all_days = 366;

            //计算 天数 当年入职 和非当年入职
            $today_tmp = strtotime(date('Y-m-d',strtotime('+1 day')));//字段属性 是带00的
            $sub_day = strtotime(date('Y-01-01'));
            $today = date('Y-m-d');
            $count_days_today = ($today_tmp - $sub_day) / (24 * 3600);

            $dep_model = new Repository\DepartmentRepository($this->lang);
            $c_staffs = $dep_model->get_c_level();

            //获取职等对应额度
            $audit_re = new AuditRepository($this->lang);
            $leave_server = Tools::reBuildCountryInstance(new LeaveServer($this->lang,$this->timezone),[$this->lang,$this->timezone]);

            $insert = $level_log = array();
            foreach ($staff_list as $v){
                $count_days = $count_days_today;
                $row = $log_row = array();
                if(empty($v['hire_date'])){
                    $this->logger->write_log('freeze_first 初始化职等额度 缺少数据 '.json_encode($v),'info');
                    continue;
                }
                $hire_tmp = strtotime($v['hire_date']);
                //如果 当年入职 判断 入职日期 到今天 有多少天
                if(date('Y',$hire_tmp) == $year)
                    $count_days = ($today_tmp - $hire_tmp) / (24 * 3600);
                $count_days = intval($count_days);

                //获取 历史最高职级 对应的年假额度天数
                $grade = StaffJobLevelLogModel::findFirst([
                    'columns' => 'staff_info_id,max(job_title_grade) as grade',
                    'conditions' => "staff_info_id = {$v['staff_info_id']}",
                ]);
                if(!empty($grade) && $grade->grade > $v['job_title_grade_v2'])
                    $v['job_title_grade_v2'] = $grade->grade;

                $level_day = $leave_server->get_year_leave_days($v);

                //额外加上 入职满整年天数
                $over_year = $leave_server->over_one_year_days($v);
                $level_day += $over_year;

                //不能超过上限
                $max_days = $leave_server->get_year_leave_max_days($v);
                if($level_day > $max_days)
                    $level_day = $max_days;

                if(in_array($v['staff_info_id'],$c_staffs))
                    $level_day = enums::C_LEVEL_DAYS;

                $days = round($count_days * $level_day / $all_days,enums::ROUND_NUM);
                $this->logger->write_log("freeze_first 初始化职等额度 {$v['staff_info_id']} {$count_days} {$level_day}",'info');

                $row['staff_info_id'] = $v['staff_info_id'];
                $row['leave_type'] = 1;
                $row['date_at'] = $today;
                $row['days'] = $days;
                $row['job_title_level'] = $v['job_title_level'];
                $row['job_title_grade'] = $v['job_title_grade_v2'];
                $insert[] = $row;

                $log_row['staff_info_id'] = $v['staff_info_id'];
                $log_row['date_at'] = $today;
                $log_row['job_title_level'] = $v['job_title_level'];
                $log_row['job_title_grade'] = $v['job_title_grade_v2'];
                $level_log[] = $log_row;
            }

            $audit_re->batch_insert('staff_days_freeze', $insert);
            $audit_re->batch_insert('staff_job_level_log', $level_log);

            echo '任务 跑完了';
        }catch (\Exception $e){
            die('任务出错了' . $e->getMessage());
        }

    }


    //监控每天固化数据
    public function check_freezeAction(){
        $date = date('Y-m-d 00:00:00',strtotime('-1 day'));

        $builder = $this->modelsManager->createBuilder();
        $column = "f.staff_info_id";
        $builder->columns($column);
        $builder->from(['f' => StaffDaysFreezeModel::class]);
        $builder->join(HrStaffInfoModel::class, 'f.staff_info_id = s.staff_info_id ', 's');
        $builder->andWhere('f.updated_at  < :date:', ['date' => $date]);
        $builder->andWhere(" s.state in (1,3) and formal in (1,4) ");

        $data = $builder->getQuery()->execute()->toArray();

        $count = count($data);
        $ids = array_column($data,'staff_info_id');
        if($count > 0)
            $this->logger->write_log("check_freeze 数据任务 {$date} 存在问题员工 数量 ".$count.json_encode($ids));
        else
            $this->logger->write_log('check_freeze 数据任务没问题 '.$count ,'info');
    }

    //监控 是否导入数据 发送邮件
    public function check_hub_attendanceAction($param){
        $date = date("Y-m-d",strtotime('-1 day'));
        if(!empty($param[0]))
            $date = $param[0];

        try{
            $sql = "select count(1) from staff_attendance_source_data where date_at = '{$date}'";
            $count = $this->getDI()->get('db')->fetchColumn($sql);


            $mail_server = new MailServer();
            $title = "分拨中心{$count} 刷脸考勤监控(hub attendance data monitor) 数量";
            $mail = SettingEnvModel::findFirst("code = 'hub_attendance_mail'");
            $mail = empty($mail) ? array() : explode(',',$mail->set_val);
            $content = "<p>考勤数据 日期:{$date} 导入员工数量:{$count}</p>";
            $flag =  $mail_server->send_mail($mail,$title,$content);
            $flag = intval($flag);
            $this->logger->write_log('hub 考勤监控任务发送 '.$flag ,'info');
            die("发送 ".json_encode($mail)." 结果 ".$flag);
        }catch (\Exception $e){
            $this->logger->write_log('hub 考勤监控任务发送失败 '.$e->getMessage() );
            die("发送异常 ".$e->getMessage());
        }

    }




    //初始化 任务 跑一次性假期类型 数据到 staff_leave_remaining_days
    public function leave_for_oneAction($param){
        $leaveType = (int)$param[0];
        if(empty($leaveType)){
           dd('需要传类型');
        }

        try{
            $audit_server = Tools::reBuildCountryInstance(new AuditServer($this->lang,$this->timezone),[$this->lang,$this->timezone]);
            $model = new StaffLeaveRemainDaysModel();

            $types = array_merge($audit_server->one_time,$audit_server->one_send);
            if(!in_array($leaveType,$types)){
                dd('类型错误');
            }
            $apply_data = StaffAuditModel::find([
                'columns' => 'staff_info_id,leave_type,created_at,sum(leave_day) num',
                'conditions' => 'audit_type = 2 and leave_type = :leave_type: and status in (1,2)',
                'bind' => ['leave_type' => $leaveType],
                'group' => 'staff_info_id,leave_type'
            ])->toArray();
            //获取对应假期额度
            $limit_days = StaffDaysForLeaveModel::findFirst([
                'conditions' => 'leave_type = :leave_type:',
                'bind' => ['leave_type' => $leaveType],
            ]);

            if(in_array($leaveType,$audit_server->one_time) && empty($apply_data)){
                die('只能申请一次的假期 没数据不用初始化');
            }

            foreach ($apply_data as $data){
                //查询 有没有数据
                $info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => 'leave_type = :leave_type: and staff_info_id = :staff_id:',
                    'bind' => [
                        'leave_type' => $leaveType,
                        'staff_id' => $data['staff_info_id']
                    ]
                ]);
                if(!empty($info)){
                    $info->freeze_days = $limit_days->days ?? 0;
                    $info->update();
                    continue;
                }
                //新增
                $row['staff_info_id'] = $data['staff_info_id'];
                $row['leave_type'] = $data['leave_type'];
                $row['year'] = date('Y',strtotime("{$data['created_at']}"));
                $row['freeze_days'] = $limit_days->days ?? 0;
                $row['days'] = 0;
                //一次性的假期 额度是0
                if(in_array($leaveType,$audit_server->one_send)){
                    $row['days'] = $row['freeze_days'] - $data['num'];
                }

                $row['leave_days'] = $data['num'];
                $clone = clone $model;
                $clone->create($row);

            }

        }catch (\Exception $e){
            echo $e->getMessage();
        }
    }




    //临时跑任务 存到
    public function leave_dataAction($param){
        ini_set('memory_limit', '-1');
        try{

            $staff_id = '';
            if(!empty($param[0]))
                $staff_id = $param[0];

            $today = date('Y-m-d');

            $condition = " state in (1,3) and is_sub_staff = 0 and formal = 1 and hire_type in (1,2,3,4) ";
            if(!empty($staff_id))
                $condition .= " and staff_info_id = {$staff_id}";

            $staff_list = HrStaffInfoModel::find([
                'conditions' => $condition,
                'columns' => 'staff_info_id,hire_date,job_title_level,hire_date,job_title_grade_v2'
            ])->toArray();

            if(empty($staff_list))
                die('没有员工数据');


            $leave_server = new LeaveServer($this->lang,$this->timezone);

            foreach ($staff_list as $staff_info){
                $staff_id = $staff_info['staff_info_id'];
                if(empty($staff_info['hire_date']))
                    continue;
                $hire_date = date('Y-m-d', strtotime($staff_info['hire_date']));

                //职级对应额度
                $level_day = $leave_server->get_year_leave_days($staff_info);

                //所有年假记录 取拆分表数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('s.staff_info_id,s.id,s.date_at,s.type,s.audit_id');
                $builder->from(['s' => \FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel::class]);
                $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
                $builder->andWhere("s.staff_info_id = :staff_id:",['staff_id' => $staff_id]);
                $builder->andWhere('a.leave_type = :leave_type:',['leave_type' => enums::LEAVE_TYPE_1]);
                $builder->inWhere('a.status',array(1,2));
                $builder->orderBy("s.date_at asc");
                $split_data = $builder->getQuery()->execute()->toArray();

                if(empty($split_data))
                    continue;

                $cycle = 1;
                $step = date('Y-m-d',strtotime("{$hire_date} +1 year"));//当前周期 结算日

                $next_invalid = date('Y-m-d',strtotime("{$step} +90 day"));//当前周期 失效日
                //如果是第一周期 全部更新为 1
                if($today < $step){
                    //所有请假 都算当前周期 不能直接取 freeze 表
                    $sql = "select sum(if(s.`type`=0,1,0.5)) as num,group_concat(id) split_id
                            from staff_audit_leave_split s
                            join staff_audit a on a.audit_id = s.audit_id
                            where a.staff_info_id = {$staff_id}
                            and a.audit_type = 2 and leave_type = 1 
                            and a.status in (1,2) ";
                    $sp_info = $this->getDI()->get('db')->fetchOne($sql);
                    $used_days = empty($sp_info['num']) ? 0 : $sp_info['num'];
                    $sp_id = empty($sp_info['split_id']) ? '' : $sp_info['split_id'];

                    $row['staff_info_id'] = $staff_info['staff_info_id'];
                    $row['cycle'] = $cycle;
                    //当前周期 的 已过去的天数
                    $count_days = (strtotime($today) - strtotime($hire_date)) / (24 * 3600);
                    $row['days'] = half_num($count_days * $level_day / 365) - $used_days;//剩余额度
                    $row['leave_days'] = $used_days;//使用额度
                    $row['level_days'] = $row['level_days_new'] = $level_day;//职级额度
                    $row['add_days'] = 0;//满周年额度
                    $row['split_id'] = $sp_id;
                    $row['is_effective'] = 2;

                    $this->getDI()->get('db')->insertAsDict(
                        'leave_for_data', $row
                    );

                    continue;
                }

                $left_days = $level_day;
                $add_days = 0;
                //非第一周期
                $this_cycle = ceil((time() - strtotime($staff_info['hire_date'])) / (365 * 24 * 3600));
                $a = $this_cycle - 1;
                //当前时间 上个周期失效日
                $last_invalid = date('Y-m-d',strtotime("{$staff_info['hire_date']} +{$a} year +90 day"));
                $effect = $today < $last_invalid ? 1 : 0;
                $this_used = 0;
                $id_str = $this_id = array();


                foreach ($split_data as $split){
                    $sub_days = empty($split['type']) ? 1 : 0.5;

                    //额度不够了 或者 超过有效期了 周期递增 left_days 更新额度
                    if($left_days == 0 || $split['date_at'] >= $next_invalid){
//                        echo $cycle;
                        //如果计算周期 已经是当前周期了就不递增了 所有申请都标记当前周期
                        if($cycle == $this_cycle){
                            $this_used += $sub_days;
                            $this_id[] = $split['id'];
                            continue;

                        }else{
                            //固化周期数据
                            $row['staff_info_id'] = $staff_info['staff_info_id'];
                            $row['cycle'] = $cycle;
                            $row['days'] = $left_days;//剩余额度
                            $row['leave_days'] = $level_day + $add_days - $left_days;//使用额度
                            $row['level_days'] = $row['level_days_new'] = $level_day;//职级额度
                            $row['add_days'] = $add_days;//满周年额度
                            $row['split_id'] = implode(',',$id_str);
                            $row['is_effective'] = $effect;
                            $this->getDI()->get('db')->insertAsDict(
                                'leave_for_data', $row
                            );

                            $id_str = array();
                            //周期递增
                            $cycle++;
                            $next_invalid = date('Y-m-d',strtotime("{$next_invalid} +1 year"));
                            $step = date('Y-m-d', strtotime("{$step} +1 year"));

                            //满周年 额度
                            $add_days = $cycle - 1;
                            $left_days = $level_day + $add_days;

                        }
                    }

                    if($cycle == $this_cycle){
                        $this_used += $sub_days;
                        $this_id[] = $split['id'];
                        continue;
                    }


                    //打标记 减额度
                    if($split['date_at'] < $next_invalid && $left_days > 0){
                        //如果 剩0。5 需要拆分 周期递增 不放update 里面 直接更新
                        if($left_days < $sub_days){//只能是 0.5 < 1 情况

                            $left_days = 0;

                        }else{//够减 放update

                            $left_days -= $sub_days;
                        }
                        $id_str[] = $split['id'];

                    }else{//一整年都没请年假 要找到对应周期
                        $cycle = ceil((strtotime($split['date_at']) - strtotime($staff_info['hire_date'])) / (365 * 24 * 3600));

                        $step = date('Y-m-d',strtotime("{$staff_info['hire_date']} +{$cycle} year"));
                        $next_invalid = date('Y-m-d',strtotime("{$step} +90 day"));//365 + 90
                        $add_days = $cycle - 1;
                        $left_days = $level_day + $add_days;

                        if($cycle == $this_cycle){
                            $this_id[] = $split['id'];
                            $this_used += $sub_days;

                        }else{
                            $left_days -= $sub_days;
                            $id_str = array($split['id']);
                        }
                    }

                }

                //操作 当前周期
                $count_days = (strtotime($today) - strtotime("{$step} -1 year")) / (24 * 3600);
                if($count_days > 365){
                    //如果不是 从当前的上个周期 算的 要循环 把每一年的额度 弄了
                    while ($count_days > 365){
                        $add_days = $cycle - 1;
                        $row['staff_info_id'] = $staff_info['staff_info_id'];
                        $row['cycle'] = $cycle;
                        $row['days'] = $level_day + $add_days;//剩余额度
                        $row['leave_days'] = 0;//使用额度
                        $row['level_days'] = $row['level_days_new'] = $level_day + $add_days;//职级额度
                        $row['add_days'] = $add_days;//满周年额度 算到level_days 这里是0
                        $row['is_effective'] = 0;//当前时间是否在 上个周期 有效期
                        $row['split_id'] = '';

                        $this->getDI()->get('db')->insertAsDict(
                            'leave_for_data', $row
                        );

                        $cycle++;
                        $count_days -= 365;
                    }

                }

                $add_days = $cycle - 1;
                $row['staff_info_id'] = $staff_info['staff_info_id'];
                $row['cycle'] = $cycle;
                $row['days'] = half_num($count_days * ($level_day + $add_days) / 365) - $this_used;//剩余额度
                $row['leave_days'] = $this_used;//使用额度
                $row['level_days'] = $row['level_days_new'] = $count_days * ($level_day + $add_days) / 365;//职级额度
                $row['add_days'] = 0;//满周年额度 算到level_days 这里是0
                $row['is_effective'] = $effect;//当前时间是否在 上个周期 有效期
                $row['split_id'] = implode(',',$this_id);

                $this->getDI()->get('db')->insertAsDict(
                    'leave_for_data', $row
                );

                continue;

            }

        }catch (\Exception $e){
            die('初始化额度报错 ' .$e->getMessage());
        }
    }





    //计算 天数 对应额度是多少
    public function countDays($shouldDays, $startDate, $endDate = '')
    {
        $step = round($shouldDays / 365, enums::ROUND_NUM);

        //计算天数
        if (empty($endDate)) {
            $endDate = date('Y-m-d');
        }

        $countDays = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600);
        $countDays = abs($countDays) + 1;//算上结束日期那一天
        return $step * $countDays;
    }



    //把2022年 或者 22年挪到当前周期的 超额使用的天数标记为 指定周期
    public function currentCycleFlag($staff_info, $days, $searchCycle, $cycle)
    {
        //22年 的记录 split
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.*');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:", ['staff_id' => $staff_info['staff_info_id']]);
        $builder->andWhere('a.leave_type = :leave_type:', ['leave_type' => enums::LEAVE_TYPE_1]);
        $builder->andWhere('s.year_at = :year:', ['year' => $searchCycle]);
        $builder->inWhere('a.status', [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL]);
        $builder->orderBy('s.date_at desc');
        $splitInfo = $builder->getQuery()->execute();

        if (empty($splitInfo->toArray())) {
            return;
        }
        $left  = $days;
        $model = new StaffAuditLeaveSplitModel();
        foreach ($splitInfo as $item) {
            if ($left <= 0) {
                break;
            }
            $num = ($item->type == 0) ? 1 : 0.5;
            //拆分一半一半
            if ($left > 0 && $left - $num < 0) {
                //下午 变当前周期
                $item->type    = 2;
                $item->year_at = $cycle;
                $item->update();

                //增加一条 上午 2022的记录
                $insert['staff_info_id'] = $staff_info['staff_info_id'];
                $insert['audit_id']      = $item->audit_id;
                $insert['date_at']       = $item->date_at;
                $insert['type']          = 1;
                $insert['year_at']       = $searchCycle;
                $clone                   = clone $model;
                $clone->create($insert);
                break;
            }

            $item->year_at = $cycle;
            $item->update();
            $left -= $num;
        }
    }

    //旧版本的 获取 最高职等 用staff_job_level_log
    public function getHighestGrade($staff_info)
    {
        $logGrade = StaffJobLevelLogModel::findFirst([
            'columns'    => 'max(job_title_grade) as job_title_grade',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_info['staff_info_id']],
        ]);

        if (empty($logGrade)) {
            return $staff_info['job_title_grade_v2'];
        }
        return $logGrade->job_title_grade > $staff_info['job_title_grade_v2'] ? $logGrade->job_title_grade : $staff_info['job_title_grade_v2'];
    }


    //年假相关任务 跑数据 获取符合条件的员工
    protected function annualStaffList($date, $param)
    {
        $builder = $this->modelsManager->createBuilder();
        $column = "s.staff_info_id,s.state,s.hire_date,s.job_title_level,s.job_title_grade_v2,s.wait_leave_state,s.hire_type,e.annual_date, s.working_country";
        $builder->columns($column);
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInfoExtendMode::class, 's.staff_info_id = e.staff_info_id ', 'e');
        $builder->andWhere(" formal = 1 and is_sub_staff = 0 and hire_date < :date_at: ", ['date_at' => $date]);
        if(!empty($param['staff_info_id'])){
            $builder->andWhere('s.staff_info_id = :staff_id:', ['staff_id' => $param['staff_info_id']]);
        }
        if(!empty($param['hire_type'])){
            $builder->inWhere('s.hire_type', $param['hire_type']);
        }
        if(!empty($param['state'])){
            $builder->inWhere('s.state', $param['state']);
        }
        $staff_list = $builder->getQuery()->execute()->toArray();
        return $staff_list;
    }

    /**
     * @param $staff_id
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getNewCycleRemains($staff_id)
    {
        return StaffLeaveRemainDaysModel::find(
            [
                'conditions' => 'staff_info_id = :staff_id: and year < 2022 and leave_type = :leave_type:',
                'bind'       => [
                    'staff_id'   => $staff_id,
                    'leave_type' => enums::LEAVE_TYPE_1,
                ],
                'order' => 'year asc',
            ]
        );
    }

    //每年 1月1号 跑数据
    public function initLeaveAction($param){
        $type = $param[0] ?? '';
        //取 正式 和实习生
        $condition = " state in (1,3) and formal in (1,4) and is_sub_staff = 0";

        $staff_list = HrStaffInfoModel::find(
            [
                'conditions' => $condition,
            ]
        )->toArray();

        $leaveServer = new LeaveServer($this->lang,$this->timezone);
        $leaveServer = Tools::reBuildCountryInstance($leaveServer, [$this->lang, $this->timezone]);
        //已经固化的 类型 按年发放的
        $types = $leaveServer->initType;
        //制定类型 初始化
        if(!empty($type)){
            $types = [$type];
        }

        foreach ($staff_list as $staff){
            foreach ($types as $type) {
                $leaveObj = $leaveServer->getInstanceObj($type);
                $leaveObj->taskInitialize($staff);
            }
        }

        echo '跑完了';
    }


    //初始化假期到remain 表 上线跑一次 必须传类型
    public function oneInitRemainAction($param){
        $type = $param[0] ?? '';
        if(empty($type)){
            die('need leave type');
        }
        //取 正式 和实习生
        $condition = " state in (1,3) and formal in (1,4) and is_sub_staff = 0";

        $staff_list = HrStaffInfoModel::find(
            [
                'conditions' => $condition,
            ]
        )->toArray();

        $leaveServer = new LeaveServer($this->lang,$this->timezone);
        $leaveServer = Tools::reBuildCountryInstance($leaveServer, [$this->lang, $this->timezone]);

        foreach ($staff_list as $staff){
            $leaveObj = $leaveServer->getInstanceObj($type);
            $leaveObj->taskInitialize($staff);
        }

        echo '跑完了' . $type;
    }


    //上线跑一次 如果 工作所在国家 非本地国家 清空年假额度
    public function clearAnnualAction(){
        //存在配置项的工号
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('leave_for_not_local_staff')
            ->setParameters(['staff_info_id' => 17245])//现在不支持不传工号
            ->getConfig();
        $setting = $res['response_data'] ?? '';
        //如果 工号存在配置 也有权限
        $setting = empty($setting) ? [] : explode(',', $setting);

        //获取非对应国家工作的人
        $countryCode = strtoupper(env('country_code'));
        $code = HrStaffInfoModel::$countryToCode[$countryCode];

        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id',
            'conditions'=> 'state in (1,3) and formal = 1 and is_sub_staff = 0 and working_country != :code:',
            'bind' => ['code' => $code]
        ])->toArray();
        if(empty($staffData)){
            echo '没有数据';
            return;
        }

        $excel_data = [];
        //泰国 有300 多人 其他国家更少
        foreach ($staffData as $staff){
            //特殊配置工号 要累计年假
            if(in_array($staff['staff_info_id'], $setting)){
                continue;
            }
            //查年假额度
            $remainData = StaffLeaveRemainDaysModel::find([
                'conditions' => 'staff_info_id = :staff_id: and leave_type = 1',
                'bind' => ['staff_id' => $staff['staff_info_id']]
            ]);
            foreach ($remainData as $remain){
                $row = [
                    $remain['id'],
                    $remain['staff_info_id'],
                    $remain['year'],
                    $remain['freeze_days'],
                    $remain['days'],
                    $remain['leave_days'],
                ];
                $excel_data[] = $row;
            }

            //删除年假
            $remainData->delete();
        }
        $header = [
            'id',
            'staff_info_id',
            'year',
            'freeze_days',
            'days',
            'leave_days',
        ];
        $file_data = (new OssHelper)->exportExcel($header, $excel_data, 'working_country_annual_days');
        $this->logger->write_log(['clear_annual' => $file_data], 'notice');
        var_dump($file_data);
    }


}