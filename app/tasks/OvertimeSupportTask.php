<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 7/24/24
 * Time: 2:44 PM
 */

use FlashExpress\bi\App\Server\OvertimeServer;
use App\Country\Tools;
use FlashExpress\bi\App\library\RocketMQ;

class OvertimeSupportTask extends RocketMqBaseTask
{

    public function initialize()
    {
        $this->tq = "ot-support-shift";
        parent::initialize();
    }


    /**
     * mainAction
     * @param $msgBody
     * @return bool
     * 只有菲律宾有
     */
    protected function processOneMsg($msgBody)
    {
        try {
            if (empty($msgBody)) {
                return false;
            }
            $param = $this->getMessageData($msgBody);
            $param = $param['data'];//多了一层
            $overtimeServer = new OvertimeServer($this->lang, $this->timezone);
            $overtimeServer = Tools::reBuildCountryInstance($overtimeServer,[$this->lang, $this->timezone]);
            $param['is_mq'] = true;//标记队列消费来的
            $this->getDI()->get('logger')->write_log('addSupportChangeLog param' . json_encode($param), 'info');
            $flag = $overtimeServer->addSupportChangeLog($param);
            $this->getDI()->get('logger')->write_log('OvertimeSupportTask result' . json_encode($flag), 'info');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('OvertimeSupportTask '.$e->getMessage());
            return false;
        }
        return true;
    }


    public function testAction(){

        $param['id']        = 242;
        $param['lang'] = 'zh-CN';
        $param['change_type'] = 1;
        $param['is_mq'] = true;//标记队列消费来的
//        $param['origin_begin_date'] = '2024-12-01';
//        $param['origin_end_date'] = '2024-12-22';
//        $param['origin_shift_id'] = 38;
//        $overtimeServer = new OvertimeServer($this->lang, $this->timezone);
//        $overtimeServer = Tools::reBuildCountryInstance($overtimeServer,[$this->lang, $this->timezone]);
//        $flag = $overtimeServer->addSupportChangeLog($param);
//        var_dump($flag);;exit;




        $rmq                          = new RocketMQ($this->tq);
        $rid                          = $rmq->sendToMsg($param);//有序
        var_dump($rid);exit;
        $this->logger->write_log('sign_dsheet '.$rid.'data:'.json_encode($sendData), $rid ? 'info' : 'error');
    }

}