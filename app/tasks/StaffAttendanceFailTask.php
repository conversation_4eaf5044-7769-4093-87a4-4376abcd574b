<?php

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\AttendanceImageVerifyServer;
use \FlashExpress\bi\App\Server\QueueMQServer;
use \FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Controllers\AttendanceController;

class StaffAttendanceFailTask extends RocketMqBaseTask
{

    public function initialize()
    {
        $this->tq = "attendance-fail";//业务上先择的消费rmq的内容
        parent::initialize(); // TODO: Change the autogenerated stub
    }


    /**
     * mainAction
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        try {
            $this->getDI()->get('logger')->write_log('processOneMsg ' . base64_decode($msgBody) , 'info');
            $msg_data = $this->getMessageData($msgBody);
            if (empty($msg_data)) {
                return false;
            }
            //递减
            $redis = $this->getDI()->get('redisLib');
            $num = $redis->decr(AttendanceImageVerifyServer::REDIS_KEY_IMAGE_VERIFY_ERR_NO); //替换AttendanceController::$face_verify_error_key

            $this->getDI()->get('logger')->write_log('attendance face mq_num_sub ' . json_encode($msg_data).' num:'.$num , 'info');
            
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('attendance face mq_num_sub 异常 '.$e->getMessage());
            return false;
        }
        return true;
    }


    public function delAction(){
        $redis = $this->getDI()->get('redisLib');
        $re = $redis->del(AttendanceImageVerifyServer::REDIS_KEY_IMAGE_VERIFY_ERR_NO);
        $res = $redis->del(AttendanceController::$switch_two_hour);
        var_dump($re,$res);
    }

    /**
     * 延时的测试
     */
    public function sendMsgAction($param)
    {
        $type = '';
        if(!empty($param[0]))
            $type = intval($param[0]);

        $redis = $this->getDI()->get('redisLib');
        $num = null;
        if(empty($type)){
            $redis->incr(AttendanceController::$face_verify_error_key);
            $num = $redis->get(AttendanceController::$face_verify_error_key);
        }else if($type == 1){
            $redis->decr(AttendanceController::$face_verify_error_key);
            $num = $redis->get(AttendanceController::$face_verify_error_key);

        }else if($type == 2){
            $redis->set(AttendanceController::$switch_two_hour,0, 2 * 60 * 60);
            $num = $redis->get(AttendanceController::$switch_two_hour);
        }else if($type == 3){

            $s = (new QueueMQServer($this->lang,$this->timezone));
            $data = [
                'id' => 123
            ];
            $num = $s->sendToMsg($data,5,'attendance-fail');
            $redis->incr(AttendanceController::$face_verify_error_key);
        }else if($type == 5)
            $num = $redis->get(AttendanceController::$face_verify_error_key);
        var_dump($num);exit;


        $s = (new QueueMQServer($this->lang,$this->timezone));
        $data = [
            "staff_info_id"     => env('rmq_pushup_test','20508'),  //上级id
            "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
            "message_title"     => 'message_title',  //标题
            "message_content"   => 'message_content',//内容
            "message_scheme"    => 'flashbackyard://fe/tab?index=approval', //地址
        ];
        $s->sendToMsg($data,5);
    }

}