<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 2021/6/15
 * Time: 11:31 AM
 */

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\AttendanceHikData;
use FlashExpress\bi\App\Server\AttendanceHubServer;

use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\TaskDataLogModel;
use FlashExpress\bi\App\Server\StaffServer;


class AttendanceHubTask extends BaseTask
{

    protected $app_key;
    protected $app_secret;

    public $pre_url;
    public $time;//时间戳
    public $content_type = "application/json";//类型
    public $accept       = "*/*";//accept

    public $hub_data_path   = "/artemis/api/acs/v2/door/events";//path
    public $hub_search_path = "/artemis/api/resource/v2/door/search";
    public $charset         = 'utf-8';

    protected $hik_failed_key = 'hik_failed';


    public function initialize()
    {
        $this->app_key    = env('hik_key', '22467188');
        $this->app_secret = env('hik_secret', 'sjvrmCkwXdDBPpcYmgd9');
        parent::initialize();
    }


    //查询资源 可能不用了
    public function event_searchAction()
    {
        for ($i = 1; $i++;) {
            //请求参数
            $postData['pageNo']   = $i;
            $postData['pageSize'] = 1000;
            [$msec, $sec] = explode(' ', microtime());
            $this->time    = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
            $setting_model = new SettingEnvServer();
            $this->pre_url = $setting_model->getSetVal('hik_host');
            $this->pre_url = "https://***************:443";

            $sign     = $this->get_sign($postData, $this->hub_search_path);
            $options  = [
                CURLOPT_HTTPHEADER => [
                    "Accept:".$this->accept,
                    "Content-Type:".$this->content_type,
                    "x-Ca-Key:".$this->app_key,
                    "X-Ca-Signature:".$sign,
                    "X-Ca-Timestamp:".$this->time,
                    "X-Ca-Signature-Headers:"."x-ca-key,x-ca-timestamp",
                ],
            ];
            $header[] = "Accept".$this->accept;
            $header[] = "Content-Type:".$this->content_type;
            $header[] = "Content-MD5:flash";
            $header[] = "X-Ca-Key:".$this->app_key;
            $header[] = "X-Ca-Signature:".$sign;
            $header[] = "X-Ca-Timestamp:".$this->time;
            $header[] = "X-Ca-Signature-Headers:x-ca-key,x-ca-timestamp";
            $this->getDI()->get('logger')->write_log("attendance hik request {$this->pre_url}{$this->hub_search_path} ".json_encode($postData),
                'info');

            $result = $this->curlPost($this->pre_url.$this->hub_search_path, json_encode($postData), $options);
            $result = json_decode($result, true);
            if (!empty($result) && $result['code'] == 0) {
                //保存数据到数据库
                if ($postData['pageNo'] == $result['data']['totalPage']) {//最后一页
                    //如果没有数据
                    if (empty($result['data']['list'])) {
                        $this->getDI()->get('logger')->write_log("attendance hik response ".json_encode($result),
                            'info');
                        break;
                    }
                    //把最后一页的数据 存进去

                } else {//保存

                }
            } else {
                $this->getDI()->get('logger')->write_log("attendance hik response failed".json_encode($result), 'info');
                break;
            }
        }
    }


    /**
     * 获取刷脸数据
     * 手动回复数据 日期传 想操作数据日期的 后一天
     * @param $param
     */
    function get_hub_dataAction($param)
    {
        ini_set('memory_limit', '-1');
        $attendance_date = date('Y-m-d');
        $endHIS = date('H:i:s');
        if (!empty($param[0])) {
            $endHIS = '23:00:00';
            $attendance_date = $param[0];
        }
        $flag = $this->hikDataSuccess($attendance_date);
        if($flag){
            die($attendance_date . "已经跑过了");
        }

        try {
            set_time_limit(0);
            //请求参数
//            $postData['doorIndexCodes'] = array('LAS_HUB_IN3');//没用
//            $postData['doorName'] = "LAS__HUB_IN3_Door1";//没用 测试参数 数据能小点
            $postData['endTime']          = $attendance_date."T12:00:00".$this->timezone;//事件结束时间
            $postData['pageSize']         = 1000;
            $postData['receiveEndTime']   = $attendance_date."T{$endHIS}".$this->timezone;//事件到达海康结束时间
            $postData['receiveStartTime'] = date("Y-m-d",
                    strtotime("{$attendance_date} -2 day"))."T20:00:00".$this->timezone;//事件到达海康开始时间
            $postData['startTime']        = date("Y-m-d",
                    strtotime("{$attendance_date} -2 day"))."T20:00:00".$this->timezone;//事件开始时间

            $this->getDI()->get('logger')->write_log("param ".json_encode($postData), 'info');
            /**
             * 返回有用的字段
             * ["eventTime"]=>
             * string(25) "2021-06-27T02:03:30+07:00"
             * ["personId"]=>
             * string(6) "403154"
             * ["doorName"]=>
             * string(17) "NO3_HUB_OUT_Door1"
             *  ["devName"]=>
             * string(11) "NO3_HUB_OUT"
             *  ["jobNo"]=>
             * string(6) "403154"
             */

            [$msec, $sec] = explode(' ', microtime());
            $this->time    = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
            $setting_model = new SettingEnvServer();
            $this->pre_url = $setting_model->getSetVal('hik_host');

            $server = new AttendanceHubServer($this->lang, $this->timezone);
            //关系映射表数据 只有泰国有
            $matchStores = $server->formatMatchStores();
            $i      = 1;//页码
            while (true) {
                $postData['pageNo'] = $i;
                $sign               = $this->get_sign($postData, $this->hub_data_path);
                $options            = [
                    CURLOPT_HTTPHEADER => [
                        "Accept:".$this->accept,
                        "Content-Type:".$this->content_type,
                        "x-Ca-Key:".$this->app_key,
                        "X-Ca-Signature:".$sign,
                        "X-Ca-Timestamp:".$this->time,
                        "X-Ca-Signature-Headers:"."x-ca-key,x-ca-timestamp",
                    ],
                ];

                $result = $this->curlPost($this->pre_url.$this->hub_data_path, json_encode($postData), $options);
                $result = json_decode($result, true);
                //请求成功
                if (!empty($result) && $result['code'] === '0') {
                    //如果有数据 就保存
                    $flag = $server->save_hik_data($result['data']['list'], $attendance_date, $matchStores);
                    if (!$flag) {
                        $this->getDI()->get('logger')->write_log("attendance hik save failed ".json_encode($result));
                    }

                    //如果是最后一页 保存以后 断掉
                    if ($postData['pageNo'] >= $result['data']['totalPage']) {
                        $model = new TaskDataLogModel();
                        $insert['code'] = TaskDataLogModel::CODE_HIK_DATA;
                        $insert['month'] = date('Y-m', strtotime($attendance_date));
                        $insert['date_at'] = $attendance_date;
                        $insert['state'] = TaskDataLogModel::TASK_ORDER_1;//串联1号任务 状态
                        $model->create($insert);
                        break;
                    }

                } else {//异常情况 返回数据result为空 或者 没有code
                    $this->getDI()->get('logger')->write_log("attendance hik request ".json_encode($postData) .' response '.$result,'notice');
                    $this->hikRedisJudge($attendance_date);
                    sleep(8);
                    continue;
                }
                $i++;
                sleep(2);
            }
            $this->getDI()->get('logger')->write_log($attendance_date." get attendance from hik success", 'alert');
        } catch (BusinessException $be) {
            $this->getDI()->get('logger')->write_log($be->getMessage());
        } catch (\Exception $e) {
            throw $e;
        }
    }

    //判断海康 redis 失败次数判断 超过5次 报警 删数据 返回 false 表示 超过5次
    protected function hikRedisJudge($attendance_date)
    {
        $redis = $this->getDI()->get('redisLib');
        $redis->incr($this->hik_failed_key);
        $fail_num = $redis->get($this->hik_failed_key);
        if ($fail_num > 5) {//重试5次 失败 删除当天数据 手动执行任务
            $real_date = date('Y-m-d', strtotime("{$attendance_date} -1 day"));
            AttendanceHikData::find("date_at = '{$real_date}'")->delete();
            $redis->set($this->hik_failed_key, 0);
            throw new BusinessException('get attendance from hik 失败 !!!!!!');
        }
        return true;
    }



    /**
     * 以appSecret为密钥，使用HmacSHA256算法对签名字符串生成消息摘要，对消息摘要使用BASE64算法生成签名（签名过程中的编码方式全为UTF-8）
     * @param $postData
     * @param $url
     * @return string
     */
    function get_sign($postData, $url)
    {
        $sign_str = $this->get_sign_str($postData, $url); //签名字符串
        $priKey   = $this->app_secret;
        $sign     = hash_hmac('sha256', $sign_str, $priKey, true); //生成消息摘要
        $result   = base64_encode($sign);
        return $result;
    }

    function get_sign_str($postData, $url)
    {
        // $next = "\n";
        $next = "\n";
        $str  = "POST".$next.$this->accept.$next.$this->content_type.$next;
        $str  .= "x-ca-key:".$this->app_key.$next;
        $str  .= "x-ca-timestamp:".$this->time.$next;
        $str  .= $url;
        return $str;
    }

    public function getSignContent($params)
    {
        ksort($params);
        $stringToBeSigned = "";
        $i                = 0;
        foreach ($params as $k => $v) {
            if (false === $this->checkEmpty($v) && "@" != substr($v, 0, 1)) {
                // 转换成目标字符集
                $v = $this->characet($v, $this->charset);
                if ($i == 0) {
                    $stringToBeSigned .= "?$k"."="."$v";
                } else {
                    $stringToBeSigned .= "&"."$k"."="."$v";
                }
                $i++;
            }
        }
        unset ($k, $v);
        return $stringToBeSigned;
    }

    function get_message($postData)
    {
        $str = str_replace(['{', '}', '"'], '', json_encode($postData));
        return base64_encode(md5($str));
    }

    /**
     * 校验$value是否非空
     *  if not set ,return true;
     *    if is null , return true;
     **/
    protected function checkEmpty($value)
    {
        if (!isset($value)) {
            return true;
        }
        if ($value === null) {
            return true;
        }
        if (trim($value) === "") {
            return true;
        }
        return false;
    }

    /**
     * 转换字符集编码
     * @param $data
     * @param $targetCharset
     * @return string
     */
    function characet($data, $targetCharset)
    {
        if (!empty($data)) {
            $fileType = $this->charset;
            if (strcasecmp($fileType, $targetCharset) != 0) {
                $data = mb_convert_encoding($data, $targetCharset, $fileType);
            }
        }
        return $data;
    }

    public function curlPost($url = '', $postData = '', $options = [])
    {
        if (is_array($postData)) {
            $postData = http_build_query($postData);
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); //设置cURL允许执行的最长秒数
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    //覆盖考勤 数据源 手动跑 日期传想操作的数据日期
    public function recover_attendanceAction($param)
    {
        try {
            echo "---------------覆盖刷脸操作结果 -------- 任务开始".PHP_EOL;
            ini_set('memory_limit', '-1');
            //取数据
            $date = date("Y-m-d", strtotime("-1 day"));
            $taskDate = date('Y-m-d');
            if (!empty($param[0])) {
                $date = date('Y-m-d', strtotime($param[0]));
                $taskDate = date('Y-m-d', strtotime("{$date} +1 day"));
            }
            $info = $this->hikDataSuccess($taskDate);
            if($info && $info->state > TaskDataLogModel::TASK_ORDER_1){//二号任务状态
                die($date . "已经覆盖了");
            }
            if($info === false || $info->state != TaskDataLogModel::TASK_ORDER_1){
                die($date . "没有跑前置任务");
            }

            $data = AttendanceHikData::find("date_at = '{$date}'")->toArray();

            if (empty($data)) {
                $this->getDI()->get('logger')->write_log("recover_attendance no data! 没可能啊 是不是 get_hub_data任务{$date}没跑啊");
            }

            $server = new AttendanceHubServer($this->lang, $this->timezone);
            $flag   = $server->hub_attendance($data, $date);

            if ($flag !== true) {
                $this->getDI()->get('logger')->write_log("recover_attendance 操作结果 ".$flag);
            } else {
                $info->state = TaskDataLogModel::TASK_ORDER_2;//二号任务状态
                $info->update();
                $this->getDI()->get('logger')->write_log("recover_attendance 操作结果 ".$flag, 'info');
            }
            echo "---------------覆盖刷脸操作结果 {$flag}-------- 任务结束".PHP_EOL;
        } catch (ValidationException $e) {
            $this->getDI()->get('logger')->write_log("recover_attendance 操作结果 ".$e->getMessage(), 'notice');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("recover_attendance 操作结果 ".$e->getMessage(), 'error');
        }
    }


    //刷一下 打卡表 working day  数据 刷一个月的
    public function fixWorkDayAction(){
        $month = '2023-01';//默认刷 23年 1月
        if(!empty($param[0])){
            $month = $param[0];
        }
        $start = $step = $month . '-01';
        $end = date('Y-m-d',strtotime("{$start} last day of"));//最后一天

        if($end > date('Y-m-d')){
            $end = date('Y-m-d');
        }

        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id,sys_store_id,week_working_day',
        ])->toArray();

        $staffIds = array_column($staffData,'staff_info_id');

        $attendanceData = StaffWorkAttendanceModel::find([
            'columns' => "staff_info_id,concat(staff_info_id,'_',attendance_date) as u_key",
            'conditions' => 'staff_info_id in ({ids:array}) and attendance_date between :start: and :end: and (started_state = :state: or end_state = :state:)',
            'bind' => ['ids' => $staffIds,'start' => $start,'end' => $end,'state' => StaffWorkAttendanceModel::STATE_HUB_ATTENDANCE],
        ])->toArray();

        $attendanceData = empty($attendanceData) ? [] : array_column($attendanceData,'staff_info_id','u_key');

        $staffRe = new StaffRepository($this->lang);
        while ($step <= $end){
            foreach ($staffData as $staff){
                //先看有没有打卡数据
                $key = "{$staff['staff_info_id']}_{$step}";
                if(empty($attendanceData[$key])){
                    continue;
                }
                $isWork = $staffRe->get_is_working_day($staff,$step);
                $flag = StaffWorkAttendanceModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
                    'bind' => ['staff_id' => $staff['staff_info_id'],'date' => $step],
                ])->update(['working_day' => $isWork]);

                $this->getDI()->get('logger')->write_log("fix workday {$staff['staff_info_id']} {$step} {$flag}",'info');
            }
            $step = date('Y-m-d',strtotime("{$step} +1 day"));
        }
        echo '跑完了';
    }


    /**
     * 海康数据拉取失败告警
     * @return true|void
     * @throws Exception
     */
    public function hikHealthyAction()
    {
        $attendance_date = date('Y-m-d');
        if ($this->hikDataSuccess($attendance_date)) {
           return true;
        }
        if($spt = (new StaffServer())->getStaffEmpId(87438)){
            $content = env('country_code').' ' . $attendance_date . ' 海康数据拉取失败 火速处理！！！';
            $params = [
                'content'     => $content, //推送内容
                'contentType' => 1, //内容类型 1表示文字  2表示html(只发送body标签内部的数据即可，不包括body标签，推荐使用这种) 3表示markdown
                'spt'         => $spt,
            ];
            $params =  json_encode($params, JSON_UNESCAPED_UNICODE);
            HttpCurl::post("https://wxpusher.zjiecode.com/api/send/message/simple-push", $params) ;
        }
    }
    //看拉海康数据任务是否跑成功
    public function hikDataSuccess($date){
        $code = TaskDataLogModel::CODE_HIK_DATA;
        $info = TaskDataLogModel::findFirst("date_at = '{$date}' and code = '{$code}'");
        if(!empty($info)){
            return $info;
        }
        return false;
    }


}
