<?php

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\bi\RoleStaffMenusModel;
use Phalcon\Db;
use FlashExpress\bi\App\Server\ProbationServer;

class ProbationTask extends BaseTask
{

    public $country_code = 'th';
    public $add_hour = '7';  //误差时间


    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
        $this->country_code = strtolower(env("country_code",'th'));
	    $this->add_hour = $this->getDI()['config']['application']['add_hour'];
	
	    set_time_limit(60*30); //执行时间 60 分钟
	    $memory_limit = 1024; //内存限制  mb
	    ini_set('memory_limit', $memory_limit.'M');
	    
    }

    public function sendEmailAttachmentAction($params)
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($params[0])) {
            return;
        }
        $bll->sendEmailAttachment($params[0]);
    }

    /**
     * 第一阶段评估插入
     * 每天执行，
     * @param array $params
     */

    public function firstAction(array $params)
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
	    //第二阶段转正评估
	    $this->console->handle(
		    [
			    "task" => 'probation',
			    "action" => 'second',
		    ]
	    );
	
	
	    //处理快要超时的
	    $this->console->handle(
		    [
			    "task" => 'probation',
			    "action" => 'deal_deadline',
		    ]
	    );
	
	
	    //发送转正通知
	    $this->console->handle(
		    [
			    "task" => 'probation',
			    "action" => 'send_msg_to_staff',
		    ]
	    );
	
	
	    //获得考勤每天执行
	    $this->console->handle(
		    [
			    "task" => 'probation',
			    "action" => 'get_attendance',
		    ]
	    );
	    

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }
        $evaluate_day = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[1]);                          //第一阶段
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day[1] - 1);
        $staffs          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs,'staff_info_id')));
        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day_exceed[1]);
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day_exceed[1] - 1);
        $staffs_two      = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, $bll->job_grade_exceed,'',true);    //查询 14 级以上的人
        $staffs          = array_merge($staffs, $staffs_two);                                         //合并两个数据组
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger("第一阶段没有找到符合的数据");
            return false;
        }

        $db = $this->getDI()->get("db");
        $staffIds = array_column($staffs,'staff_info_id');
        if (isCountry('TH')){
            $find = HrProbationModel::find([
                'columns'    => 'staff_info_id',
                'conditions' => 'staff_info_id in ({ids:array}) and first_audit_status !=1',
                'bind'       => ['ids' => $staffIds],
            ])->toArray();
            $existStaffIds = array_column($find, 'staff_info_id');
        }else{
            $existStaffIds = $bll->getExistHrProbationByStaffIds($staffIds);
        }
        
        $nonFrontLineProbationStaff = isCountry('TH') ? $bll->getNonFrontLineProbationStaff($staffIds,1) : [];
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == 1 &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' manager_id is null 第一阶段评估没有找到上级','error');
                continue;
            }
            if (in_array($staff['staff_info_id'],$existStaffIds)) {
                $this->myLogger('staff  ' . $staff['staff_info_id'] . ' 已经存在');
                continue;
            }
            if (isCountry('TH')){
                if (
                    isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                    $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                    isset($nonFrontLineProbationStaff['probation_staff']) &&
                    !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
                ){
                    if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                        feishu_push("TH,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                        continue;
                    }
                    $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                    $tpl_id = null;
                }else{
                    $target_info = null;
                    $tpl_id = $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']);
                }
            }else{
                $tpl_id = $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']);
                $target_info = null;
            }
            
            $this->myLogger("firstAction 第一阶段评估插入 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                $db->begin();
                //判断此人什么等级
                $formal_days = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ?  $bll->formal_days : $bll->formal_days_exceed;
                /**
                 * 员工120天转正，计算规则举例子：员工3月1日入职，那么员工会在6月28日凌晨转正（第120天当天转正，不能超过120天）
                 */
                $formal_at = $this->getDateByDays($staff['hire_date'],$formal_days - 1, 1);
                //每阶段评估时间
                $evaluate_time =(int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                
                $res = $db->updateAsDict("hr_probation", [
                    "first_audit_status" => HrProbationModel::FIRST_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
                ], ["conditions" => "staff_info_id = ".$staff['staff_info_id']]);
                if (!$res) {
                    throw new Exception("hr_probation update fail");
                }
                $probation_id = $db->lastInsertId();
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $staff['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'audit_id' => $staff['manager_id'],
                    'tpl_id' => $tpl_id,
                    'score' => $target_info,
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    //第一次上级评审截止3天以后
                    'deadline_at' => $this->getDateByDays($start, $evaluate_time['1']['1'] ?? 3, 1),

                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'version'=>$bll->version,
                ]);
                if (!$res) {
                    throw new Exception("hr_probation_audit insert fail");
                }
                $db->commit();
                //发送push
                $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);
            } catch (\Exception $e) {
                $db->rollback();
                //实习只进入一直，大概就是有重复进入的报错。改成info
                $this->myLogger("staff=" . $staff['staff_info_id'] . " first insert fail,message=" . $e->getMessage(), "info");
            }
        }


        $this->myLogger("第一阶段评估执行完毕======end");


        
    }

    /**
     * 第二阶段评估插入
     * 每天执行，
     * @param array $params
     */
    public function secondAction(array $params=[])
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }
        $evaluate_day = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[2]);
        $hire_date_end = $this->getDateByDays($start, $evaluate_day[2] - 1);
        $staffs = $bll->getStaffs($hire_date_begin, $hire_date_end,0,0,$bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . $hire_date_end . ' ' . implode(',',array_column($staffs,'staff_info_id')));

        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day_exceed[2]);
        $hire_date_end = $this->getDateByDays($start, $evaluate_day_exceed[2] - 1);
        $staffs_two = $bll->getStaffs($hire_date_begin, $hire_date_end,0,$bll->job_grade_exceed,'',true);//查询 14 级以上的人
        $staffs = array_merge($staffs,$staffs_two);//合并两个数据组
//        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . $hire_date_end . ' ' . implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger("第二阶段评估没有数据=====end");
            return;
        }
        $staffIds = array_column($staffs,'staff_info_id');
        $nonFrontLineProbationStaff = isCountry('TH') ? $bll->getNonFrontLineProbationStaff($staffIds,2) : [];
        $db = $this->getDI()->get("db");
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !isCountry(['MY']) &&
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_SECOND &&
                !empty($staff['second_audit_status']) &&
                $staff['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null 第二阶段评估没有上级");
                continue;
            }

            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            if (isCountry('TH')){
                if (
                    isset($nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']]) &&
                    $nonFrontLineProbationStaff['probation_data'][$staff['staff_info_id']] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE &&
                    isset($nonFrontLineProbationStaff['probation_staff']) &&
                    !in_array($staff['staff_info_id'],$nonFrontLineProbationStaff['probation_staff'])
                ){
                    if (empty($nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'])){
                        feishu_push("TH,该员工目标未制定 staff_info_id:{$staff['staff_info_id']}",'https://open.feishu.cn/open-apis/bot/v2/hook/355f5d1f-3bfe-4218-a9d9-9554837af929');
                        continue;
                    }
                    $target_info = $nonFrontLineProbationStaff['target'][$staff['staff_info_id']]['target_info'];
                    $tpl_id = null;
                }else{
                    $target_info = $item['score'];
                    $tpl_id = $item['tpl_id'] ?? null;
                }
            }else{
                $tpl_id = $item['tpl_id'];
                $target_info = $item['score'];
            }

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['cur_level'] != 1 || $item['audit_status'] == 1) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " 第一次评审没有完成或者已经进入第二次评审 cur_level ".$item['cur_level']." audit_status ".$item['audit_status']);
                continue;
            }

            $this->myLogger("secondAction 需要执行的数据 staff= " . $staff['staff_info_id'] . " cur_level ".$item['cur_level']." audit_status ".$item['audit_status']);

            try {
                $db->begin();
                //每阶段评估时间
                $evaluate_time =(int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;
                
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $item['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'cur_level' => 2,
                    'tpl_id' => $tpl_id,
                    'score' => $target_info,
                    'audit_id' => $staff['manager_id'],
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    //第二次上级评审截止6天以后，75天0点，80天24：00，改成85天24:00,86
                    'second_deadline_at'=> $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'version'=>$bll->version,
                ]);
                if (!$res) {
                    throw new Exception("hr_probation_audit insert fail");
                }

                $res = $db->updateAsDict("hr_probation", [
                    "cur_level" => 2,
                    "is_active" => HrProbationModel::IS_ACTIVE_DEFAULT,
                    'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                ], ["conditions" => "staff_info_id = ".$staff['staff_info_id']]);
                if (!$res) {
                    throw new Exception("hr_probation update fail");
                }

                $db->commit();

                //发送push
                $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);

            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("staff=" . $staff['staff_info_id'] . " second insert fail,message=" . $e->getMessage(), "error");
            }
        }

        $this->myLogger("第二阶段执行完毕======end");
    }




    /**
     * 处理快到截止日期的员工，如7.26日执行，>=7.26 and <7.27
     * 每天凌晨执行处理为超时
     */
    //处理 快到截止日期的员工，每天凌晨，执行一次，判断截止日期，到今天的。
    public function deal_deadlineAction()
    {

        $bll = new ProbationServer($this->lang, $this->add_hour);
        $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        $end = $this->getDateByDays($start, 1, 1);


        $staffs = $bll->getStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("处理超时,没有数据,no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }
        $db = $this->getDI()->get("db");
        $staffIds = array_column($staffs, "id");
        $strStaffIds = implode(",", $staffIds);



        try {
            $db->begin();
            foreach ($staffs as $hrProbationAudit) {
                if ($hrProbationAudit['cur_level'] == ProbationServer::CUR_LEVEL_FIRST) {
                    $updateData = [
                        'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT,
                        'updated_at'         =>  gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                } else {
                    $updateData = [
                        'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT,
                        'updated_at'          => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                }
                $updateData['is_active'] = HrProbationModel::IS_ACTIVE_DEFAULT;
                $db->updateAsDict("hr_probation", $updateData,
                    ["conditions" => 'id =' . $hrProbationAudit['probation_id']]);
            }
            $res = $db->updateAsDict("hr_probation_audit", [
                "audit_status" => 3,
                'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
            ], ["conditions" => "id in (" . $strStaffIds . ")"]);
            if (!$res) {
                throw new Exception("处理超时-hr_probation_audit update fail");
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->myLogger("处理超时-staff=" . $strStaffIds . "deal_deadline fail,message=" . $e->getMessage(), "error");
        }

        $this->myLogger("处理超时-probation staffs on deadline between" . $start . "===" . $end . " ======end");
    }

    /**
     * 一次性脚本 刷数据 
     * 历史激活后一直没有评估的评估表变更状态为已超时
     * 历史已经超时的评估表不限制下班打卡，从上线后发送的评估表超时了再限制。
     */
    public function one_deal_deadlineAction()
    {
        $db = $this->getDI()->get("db");
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hpa' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, 'hp.id =hpa.probation_id', 'hp');
        $builder->columns('hp.staff_info_id,hpa.probation_id,hp.cur_level,hpa.id');
        $builder->Where('hpa.audit_status = 1 and hp.is_system=0 and hp.is_active = 1 and hpa.deadline_at=:deadline_at: and hp.status = 1',
            ["deadline_at" => '2099-01-01']);
        $list = $builder->getQuery()->execute()->toArray();

        if (!empty($list)) {
            $hpaIds = array_column($list, "id");
            $hpaIds = implode(",", $hpaIds);
            try {
                $db->begin();
                foreach ($list as $hrProbationAudit) {
                    if ($hrProbationAudit['cur_level'] == ProbationServer::CUR_LEVEL_FIRST) {
                        $updateData = [
                            'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT,
                            'updated_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                        ];
                    } else {
                        $updateData = [
                            'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT,
                            'updated_at'          => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                        ];
                    }
                    $updateData['is_active'] = HrProbationModel::IS_ACTIVE_DEFAULT;
                    $db->updateAsDict("hr_probation", $updateData,
                        ["conditions" => 'id =' . $hrProbationAudit['probation_id']]);
                    $this->myLogger("处理超时,hr_probation staff_id: " . $hrProbationAudit['staff_info_id']);
                }
                $res = $db->updateAsDict("hr_probation_audit", [
                    "audit_status"       => 3,
//                    "second_deadline_at" => null,
                    "deadline_at"        => '2024-12-18',
                    'updated_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                ], ["conditions" => "id in (" . $hpaIds . ")"]);
                if (!$res) {
                    throw new Exception("处理超时-hr_probation_audit update fail");
                }
                $db->commit();
                $this->myLogger("处理超时,hr_probation_audit Ids: " . $hpaIds);
            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("处理超时-staff=" . $hpaIds . "deal_deadline fail,message=" . $e->getMessage(),
                    "error");
            }
            $this->myLogger("处理超时-probation======end");
        }else{
            $this->myLogger("处理超时,没数据 ");
        }

        // 历史已经超时的评估表不限制下班打卡，从上线后发送的评估表超时了再限制。
        if (isCountry('PH')){
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['hpa' => HrProbationAuditModel::class]);
            $builder->leftjoin(HrProbationModel::class, 'hp.id =hpa.probation_id', 'hp');
            $builder->columns('hp.staff_info_id,hpa.probation_id,hp.cur_level,hpa.id');
            $builder->Where('hpa.audit_status = 3 and hpa.deadline_at=:deadline_at: and hp.status = 1',
                ["deadline_at" => '2099-01-01']);
            $list1 = $builder->getQuery()->execute()->toArray();
            if (!empty($list1)) {
                $hpaIds = array_column($list1, "id");
                $hpaIds = implode(",", $hpaIds);
                $res    = $db->updateAsDict("hr_probation_audit", [
                    "audit_status"       => 3,
//                    "second_deadline_at" => null,
                    "deadline_at"        => '2024-12-18',
                    'updated_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                ], ["conditions" => "id in (" . $hpaIds . ")"]);
                if (!$res) {
                    $this->myLogger("处理历史超时数据-hr_probation_audit update fail======end");
                    return;
                }
                $this->myLogger("处理历史超时数据,hr_probation_audit Ids: " . $hpaIds);
            }else{
                $this->myLogger("处理历史超时数据,没数据 ");
            }
        }
    }


    /**
     * 第二阶段的提醒截止日期前两天，每隔5小时推送一次 给审批人发送 push
     * 每天7:00,12:00,17:00执行
     */
    public function send_msg_to_higherAction()
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);

        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多3天（比开始时间多2天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于

        $end = $this->getDateByDays($start, 2, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("第二阶段提醒 push 消息  send msg to higher:no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        $auditIdArr = array_column($staffs, "audit_id");
        $auditIdArr = array_unique($auditIdArr);

        $approval_html_url = $bll->get_setting_by_code('probation_html_url');
        $approval_index_url = $bll->get_setting_by_code('probation_index_url');


        foreach ($auditIdArr as $k => $v) {
            $data = [];
            $data['staff_info_id'] = $v;
            $data['src'] = 'backyard';
            $data['message_title'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_notice');
            $data['message_content'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_to_higher');
            $data['message_scheme'] = $approval_html_url . urlencode($approval_index_url);
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higherAction_pushMessage params:' . json_encode($data), 'info');
	        $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
	        $ret->setParams($data);
	        $_data = $ret->execute();
	        $this->getDI()->get('logger')->write_log("send_msg_to_higherAction_pushMessage:pushMessage-return- " . json_encode($_data), 'info');
	        if (!$_data['result']) {
                $this->myLogger("higher:staff_info_id=" . $v . "  发送push失败", "info");
            }
        }

        $this->myLogger("第二阶段提醒 push 消息 send msg to higher:probation staffs on deadline between" . $start . "===" .
                        $end .
                        "======end");
    }


    /**
     * 第二阶段的提醒HRBP
     * 每天7:00执行
     */
    public function send_msg_to_hrbpAction($params)
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($params[0])) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        } else {
            $start = $params[0].' 00:00:00';
        }

        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多2天（比开始时间多一天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<


        //找第二次评审
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);
        if (empty($staffs)) {
            $this->myLogger("send_msg_to_hrbpAction 第二阶段的提醒HRBP send msg to hrbp:no probation staffs on deadline " . $start);
            return;
        }

        $evaluatedPerson = array_column($staffs, "staff_info_id");
        $evaluatedPerson = array_unique($evaluatedPerson);
        $hrbpMap = $bll->findHRBP($evaluatedPerson);
        if (empty($hrbpMap)) {
            $this->myLogger("send_msg_to_hrbpAction 第二阶段的提醒HRBP send msg to hrbp:not found hrbp from staff id in (" .
                implode(",",
                    $evaluatedPerson) . ')');
            return;
        }
        $languageMap =  (new \FlashExpress\bi\App\Server\StaffServer())->getBatchStaffLanguage(array_keys($hrbpMap));
        foreach ($hrbpMap as $hrbpId => $staffIds) {
            $this->myLogger(json_encode(['hrbp'=>$hrbpId,'被评估人'=>$staffIds],JSON_UNESCAPED_UNICODE));
            $bll->sendPushMessageToHrbp($languageMap[$hrbpId],$hrbpId,$staffIds);
        }

        //根据网点，部门对应出HRBP的关系
        //您负责的区域员工有转正评估未完成，请尽快提醒该员工上级进行转正评估，详情查看FBI-员工试用期管理
        $this->myLogger("send_msg_to_hrbpAction 第二阶段的提醒HRBP send msg to hrbp:probation staffs on deadline " .
                        $start . "=====end");
    }

    /**
     * 发送审核通过给已通过的员工，并修改状态成，已转正
     * 每天执行
     */
    public function send_msg_to_staffAction($params=[])
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        //今天
        $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
        $end = $this->getDateByDays($start, 1, 1);//<

        $staffs = $bll->getStaffsByFormalDate($start, $end,[ProbationServer::STATUS_PASS],true);
        if (!isCountry('MY')) {
            $staffs = array_filter($staffs, function ($v) {
                return !(
                    $v['status'] == HrProbationModel::STATUS_FORMAL &&
                    $v['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE &&
                    $v['second_status'] == HrProbationModel::SECOND_STATUS_PASS
                );
            });
        }
        if (empty($staffs)) {
            $this->myLogger("send_msg_to_staffAction send msg to staff:no probation staffs on formal_at is " . $start);
            return;
        }
        $staffIdArr = array_column($staffs, "staff_info_id");
        $staffStr = implode(",",
            $staffIdArr);
        $this->myLogger('发送审核通过给已通过的员工，并修改状态成，已转正 转正日期'.$start .' staffIds ' . $staffStr);
        $flag = $bll->formalStaffs($staffIdArr);

        if (!$flag) {
            $this->myLogger("send_msg_to_staffAction send msg to staff: formal staff_info_id in (" . $staffStr . ")", "error");
            return;
        }
        $_cur_level = 2;
        foreach ($staffs as $staff) {
            // 发送阶段完成发送签字消息 被评估人和被评估人的上级
            if (isCountry('TH') && !empty($staff['probation_channel_type']) && $staff['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE){
                $rpc_params = [
                    'staff_info_id'       => $staff['staff_info_id'],
                    'customize_cur_level' => $_cur_level,
                ];
                $hcmCli     = new ApiClient('hcm_rpc', '', 'probation_stage_done_message', $this->lang);
                $hcmCli->setParams($rpc_params);
                $returnData = $hcmCli->execute();
                if ($returnData["result"]['code'] != 1) {
                    $this->getDI()->get('logger')->write_log([
                        'function'   => 'probation_stage_done_message',
                        'returnData' => $returnData,
                        'params'     => $rpc_params,
                    ]);
                }
            }
            
            $bll->putFormalLog(-1,$staff['staff_info_id'],2,4);


            $html = $bll->getMsgTemplateByUserId($staff['staff_info_id'],"hr_probation_field_msg_to_staff",['name'=>$staff['name'],'formal_at'=>$staff['formal_at']]);

            $staff_info_id = $staff['staff_info_id'];
            $id = time() . $staff_info_id . rand(1000000, 9999999);
            $param['staff_users'] = array($staff_info_id);//数组 多个员工id
            $param['message_title'] = $bll->getMsgTemplateByUserId($staff['staff_info_id'],'hr_probation_field_msg_to_staff_title');
            $param['message_content'] = $html;
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id'] = $id;
            $param['category'] = -1;
	
	        $this->getDI()->get('logger')->write_log('send_msg_to_staffAction param:' . json_encode($param), 'info');
	        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
	        $bi_rpc->setParams($param);
	        $res = $bi_rpc->execute();
	        $this->getDI()->get('logger')->write_log('send_msg_to_staffAction result:' . json_encode($res), 'info');
	        if ($res && $res['result']['code'] == 1) {
		        $kitId    = $res['result']['data'][0];
		        $this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
                if (isCountry('TH')){
                    // 仅泰国非一线在用
                    $this->getDI()->get('db')->updateAsDict('hr_probation', ['result_notification' => HrProbationModel::RESULT_NOTIFICATION_YES],'staff_info_id = ' . $staff['staff_info_id']);
                }
	        }else{
		        $this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staff_info_id, 'error');
	        }
	        
        }

        $this->myLogger("send_msg_to_staffAction send msg to staff: probation staffs on formal_at between" . $start .
                        "===" . $end . "=====end");
    }

    /**
     *获得考勤
     *每天执行
     * 纪律考核
     */
    public function get_attendanceAction()
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $now = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        //第一阶段时间
        $day_1 = $bll->first_check_days;
        //查询 18 级一下的
        $start_1 = $this->getDateByDays($now, $day_1);     //>=45 天之前
        $end_1 = $this->getDateByDays($now, $day_1 -1 ); //<44天

        //第二阶段时间
        $day_2 = $bll->second_check_days;
        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2 = $this->getDateByDays($now, $day_2-1);

        $staffs_1 = $bll->getStaffs($start_1, $end_1,0,0,$bll->job_grade_exceed); // 查询 17 级及以下的人
        $staffs_2 = $bll->getStaffs($start_2, $end_2,0,0,$bll->job_grade_exceed); // 查询 17 级及以下的人
        $this->myLogger('正式员工'.$bll->job_grade_exceed.'级及以下 第一阶段入职日期 now- '.$day_1.'-1=' . $start_1. ' staffIds:'. json_encode(array_column($staffs_1,'staff_info_id')));
        $this->myLogger('正式员工'.$bll->job_grade_exceed.'级及以下 第二阶段入职日期 now-'.$day_2.'-1=' . $start_2. ' staffIds:'. json_encode(array_column($staffs_2,'staff_info_id')));
        //查询 18 级以上的
        $day_1 = $bll->first_check_days_exceed;
        $start_1 = $this->getDateByDays($now, $day_1);
        $end_1 = $this->getDateByDays($now, $day_1 - 1 );

        $day_2 = $bll->second_check_days_exceed;

        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2 = $this->getDateByDays($now, $day_2 -1);

        $staffs_1_two = $bll->getStaffs($start_1, $end_1,0,$bll->job_grade_exceed); // 查询 17 级及以上的人
        $staffs_2_two = $bll->getStaffs($start_2, $end_2,0,$bll->job_grade_exceed); // 查询 17 级及以上的人
        $this->myLogger('正式员工'.$bll->job_grade_exceed.'级以上 第一阶段入职日期  now- '.$day_1.'-1=' . $start_1. ' staffIds:'. json_encode(array_column($staffs_1_two,'staff_info_id')));
        $this->myLogger('正式员工'.$bll->job_grade_exceed.'级以上 第二阶段入职日期  now- '.$day_2.'-1=' . $start_2. ' staffIds:'. json_encode(array_column($staffs_2_two,'staff_info_id')));
        $staffs_1  = array_merge($staffs_1,$staffs_1_two);//合并两个数据组
        $staffs_2  = array_merge($staffs_2,$staffs_2_two);//合并两个数据组
        if (empty($staffs_1) && empty($staffs_2)) {
            $this->myLogger('get_attendance  staffs 为空，不用执行' . '=====end');
            return;
        }

        $data = [];

        if (!empty($staffs_1)) {
            foreach ($staffs_1 as $staff) {
                $tmp = [];
                $tmp['cur_level'] = 1;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }

        if (!empty($staffs_2)) {
            foreach ($staffs_2 as $staff) {
                $tmp = [];
                $tmp['cur_level'] = 2;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }


        foreach ($data as $staff) {
            if ($bll->isHaveAttentdance($staff['staff_info_id'], $staff['cur_level'])) {
                continue;
            }
            $flag = $bll->addAttendance($staff);
            if (!$flag) {
                $this->myLogger("get_attendance staffs==add attendance error==" . $staff['staff_info_id'], "error");
            }
        }
        $this->myLogger('get_attendance staffs end' );
    }

    /*
     * 发送给上级的上级，提示有人没评估
     */
    public function send_msg_to_higher_of_higherAction(){

        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);


        //截止81天，减一天
        //10-24的时候，找截止时间是25号的
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsBySecondDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("send_msg_to_higher_of_higherAction :no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        //员工xxxxx的转正评估，其上级xxxxx尚未进行评估，请及时联系该上级进行评估
        //查询 cpo 工号 如果审批人是 cpo   就不发了   也没说发
        $cpo_staff_info_id =  $bll->cpo_staff_id;

        foreach ($staffs as $k => $v) {
            if(empty($v['higher_id']) || $v['manager_id'] == $cpo_staff_info_id){
                $this->myLogger("send_msg_to_higher_of_higherAction :staff_info_id=" . $v['staff_info_id'] . "===上级ID=".$v['manager_id']."====没有直线上级", "info");
                continue;
            }


            $staff_info_id = $v['higher_id'];
            $id = time() . $staff_info_id . rand(1000000, 9999999);
            $param['staff_users'] = array($staff_info_id);//数组 多个员工id
            $param['message_title'] = $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_notice');
            $param['message_content'] =  $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_to_higher_of_higher',$v);
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id'] = $id;
            $param['category'] = -1;
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-param:' . json_encode($param), 'info');
	        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
	        $bi_rpc->setParams($param);
	        $res = $bi_rpc->execute();
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-result:' . json_encode($res), 'info');
	        if ($res && $res['result']['code'] == 1) {
		        $kitId    = $res['result']['data'][0];
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
	        }else{
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message失败' . $staff_info_id, 'info');
	        }
        }

        $this->myLogger("send_msg_to_higher_of_higherAction :probation staffs on deadline between" . $start . "===" .
                        $end . "======end");
    }






    public function myLogger($str, $level = "info")
    {
        echo $str . PHP_EOL;
        $now = gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600);
        $this->getDI()->get('logger')->write_log("ProbationTask on " . $now . ":" . $str, $level);
    }

	
	
	
	/**
	 * 获得日期
	 * @param $date
	 * @param $days 40
	 * @param int $flag 0=40天之前，1=40天之后
	 * @return false|string
	 */
	public function getDateByDays($date, $days, $flag = 0)
	{
		
		$default = '-';
		if (!empty($flag)) {
			$default = '+';
		}
		return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
	}

    /**
     * 转正评估-目标制定入职前提醒 目前仅TH
     * 根据 TA Offer 候选人的“预计入职日期”前7天，入职当天，入职第五天、入职第七天BY提醒，只要没有制定目标就提醒
     */
    public function probationGoalRemindAction()
    {
        $log  = '任务: ProbationTask probationGoalRemindAction 开始时间: '.date('Y-m-d H:i:i:s').PHP_EOL;
        $server = new ProbationServer($this->lang, $this->add_hour);
        $server->probationGoalRemind();
        $log .= "结束时间: ".date('Y-m-d H:i:i:s').PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);

    }


    public function oneImportNonFrontLineAction()
    {
        $log  = '任务: ProbationTask oneImportNonFrontLineAction 开始时间: '.date('Y-m-d H:i:i:s').PHP_EOL;
        $server = new ProbationServer($this->lang, $this->add_hour);
        $server->oneImportNonFrontLine();
        $log .= "结束时间: ".date('Y-m-d H:i:i:s').PHP_EOL;
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);

    }


	
}