<?php

use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\StaffEmailRecord;

class ChinastaffTask extends BaseTask {

    const KEY = 'china_staff_transfer';
    const EVENT_1 = 1; //入职
    const EVENT_2 = 2; //离职

    private $induction_template = [
        'title' => 'Entry reminder for Chinese employees',
        'content' => "<html>"
        . "<table style='border-collapse: collapse;' border='1' width='1150' cellpadding='10' cellspacing='0' align='center'>"
        . "<tr style='border-bootom:1px solid #e5e5e5;'><th>工号</br>รหัสพนักงาน</th> <th>姓名</br>ชื่อ-สกุล</th><th>部门</br>แผนก</th><th>职位</br>ตำแหน่ง</th><th>个人号码</br>เบอร์โทรศัพท์</th><th>入职日期</br>วันที่เริ่มงาน</th><th>企业邮箱</br>อีเมลบริษัท</th></tr>"
        . "<tr style='border-bootom:1px solid #e5e5e5;'><th>%s</th> <th>%s</th><th>%s</th><th>%s</th><th>%s</th><th>%s</th><th>%s</th></tr>"
        . "</table>"
        . "</html>"
    ];
    private $departure_template = [
        'title' => 'Resignation reminder for Chinese employees',
        'content' => "<html>"
        . "<table style='border-collapse: collapse;' border='1' width='1150' cellpadding='10' cellspacing='0' align='center'>"
        . "<tr style='border-bootom:1px solid #e5e5e5;'><th>工号</br>รหัสพนักงาน</th> <th>姓名</br>ชื่อ-สกุล</th><th>部门</br>แผนก</th><th>职位</br>ตำแหน่ง</th><th>个人号码</br>เบอร์โทรศัพท์</th><th>入职日期</br>วันที่เริ่มงาน</th><th>离职日期</br>เวลาลาออก</th><th>企业邮箱</br>อีเมลบริษัท</th></tr>"
        . "<tr style='border-bootom:1px solid #e5e5e5;'><th>%s</th> <th>%s</th><th>%s</th><th>%s</th><th>%s</th><th>%s</th><th>%s</th><th>%s</th></tr>"
        . "</table>"
        . "</html>"
    ];
    private $email;

    public function initialize() {
        parent::initialize();

        //邮件接收人
        $china_staff_transfer = $this->getEnv(self::KEY);
        $this->email = explode(',', $china_staff_transfer);

        if (!$this->email) {
            $this->logger->write_log("task：chinastaff 没有邮件接收人", 'info');
            return false;
        }
    }

    public function mainAction() {

        ini_set('memory_limit', '100M'); // 临时设置最大内存占用为4G
        set_time_limit(0); // 设置脚本最大执行时间 为0 永不过期    

        $this->logger->write_log("task start", 'info');
    }

    //入职提醒 实时发送
    public function inductionAction() {

        try {

            ini_set('memory_limit', '100M'); // 临时设置最大内存占用为4G
            set_time_limit(0); // 设置脚本最大执行时间 为0 永不过期    
            //中国国籍在外务工人员
            $china_staff = $this->chinaStaff();

            if ($china_staff) {
                $staff_arr = array_column($china_staff, 'staff_info_id');
                $ids = implode(',', $staff_arr);
                //已发送入职邮件
                $inductionStaffEmail = $this->sendedEmail(1);
                //当日入职员工 状态为入职
                $sql = "select staff_info_id,name, sys_department_id, job_title, mobile, date_format(hire_date,'%Y-%m-%d') hire_date, email,node_department_id,job_title from `hr_staff_info` as a"
                        . " where hire_date BETWEEN DATE_FORMAT( CURDATE(), '%Y-%m-%d %H:%i:%s' )"
                        . "  AND DATE_ADD(CURDATE(), INTERVAL 1 DAY) "
                        . "AND state = 1 "
                        . "AND staff_info_id IN (" . $ids . ")";
                $current_induction = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

                if ($current_induction) {
                    foreach ($current_induction as $k => $v) {
                        //剔除已发送过邮件的记录
                        if (in_array($v['staff_info_id'], $inductionStaffEmail)) {
                            continue;
                        }
                        $department = $this->getDepartment($v['node_department_id']);
                        $job = $this->getJobTitle($v['job_title']);
                        //替换模板数据
                        $content = sprintf($this->induction_template['content'], $v['staff_info_id'], $v['name'], $department['name'], $job['job_name'], $v['mobile'], $v['hire_date'], $v['email']);
                        //发送邮件
                        foreach ($this->email as $k2 => $v2) {
                            $sendEmail = \FlashExpress\bi\App\library\Mail::send([$v2], $this->induction_template['title'], $content);
                            if ($sendEmail) {
                                $params['staff_info_id'] = $v['staff_info_id'];
                                $params['email'] = $v2;
                                $params['event'] = self::EVENT_1;
                                //写入DB
                                $this->emailSendRecord($params);

                                $this->logger->write_log("task：chinastaff 发送成功-email：" . $v2, 'info');
                            } else {
                                $this->logger->write_log("task：chinastaff 发送失败-email：" . $v2, 'info');
                            }
                        }
                    }
                } else {
                    $this->logger->write_log("task：chinastaff 没有需要发送的邮件：", 'info');
                }
            }
            echo "任务完成！";
        } catch (Exception $e) {
            $this->logger->write_log("task：chinastaff 中国籍员工入职邮件通知发送功能异常：{$e->getMessage()}", 'notice');
        }
    }

    //离职提醒 每天泰国时间9:00
    public function departureNoticAction() {
        try {
            ini_set('memory_limit', '100M'); // 临时设置最大内存占用为4G
            set_time_limit(0); // 设置脚本最大执行时间 为0 永不过期    
            //离职员工
            $departure_staff = $this->getDepartureList();

            //已发送入职邮件
            $departureStaffEmail = $this->sendedEmail(2);

            //所有中国籍员工
            $all_china_staff = $this->chinaAllStaff();
            if ($departure_staff) {
                $all_china_staff = $this->arr_reduce($all_china_staff);
                foreach ($departure_staff as $k => $v) {

                    //剔除已发送过邮件人员
                    if ($departureStaffEmail && in_array($v['staff_info_id'], $departureStaffEmail)) {
                        continue;
                    }

                    //中国籍才发送邮件
                    if (in_array($v['staff_info_id'], $all_china_staff)) {
                        $department = $this->getDepartment($v['node_department_id']);
                        $job = $this->getJobTitle($v['job_title']);
                        //替换模板数据
                        $content = sprintf($this->departure_template['content'], $v['staff_info_id'], $v['name'], $department['name'], $job['job_name'], $v['mobile'], $v['hire_date'], $v['leave_date'], $v['email']);
                        foreach ($this->email as $k2 => $v2) {
                            $sendEmail = \FlashExpress\bi\App\library\Mail::send([$v2], $this->departure_template['title'], $content);
                            if ($sendEmail) {
                                $params['staff_info_id'] = $v['staff_info_id'];
                                $params['email'] = $v2;
                                $params['event'] = self::EVENT_2;
                                //写入DB
                                $this->emailSendRecord($params);

                                $this->logger->write_log("task：chinastaff 发送成功-email：" . $v2, 'info');
                            } else {
                                $this->logger->write_log("task：chinastaff 发送失败-email：" . $v2, 'info');
                            }
                        }
                    }
                }
            }
            echo "任务完成！";
        } catch (Exception $e) {
            $this->logger->write_log("task：chinastaff 中国籍员工离职邮件通知发送功能异常：{$e->getMessage()}", 'notice');
        }
    }

    //所有中国籍员工
    private function chinaAllStaff()
    {
        $sql = "SELECT staff_info_id FROM hr_staff_info WHERE  nationality = 2 and working_country != 2 ";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //当日注册中国国籍在外务工人员
    private function chinaStaff()
    {
        $start_time = date('Y-m-d 00:00:00');
        $end_time = date('Y-m-d 23:59:59');
        $sql = "SELECT  staff_info_id FROM hr_staff_info WHERE created_at BETWEEN '{$start_time}' AND '{$end_time}' AND nationality = 2 and working_country != 2 ";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //10天内离职人员并且没有发送过邮件
    private function getDepartureList() {

        //已发送
        $sql = "--
                select 
                    staff_info_id,name, 
                    sys_department_id, 
                    job_title, mobile, 
                    date_format(hire_date,'%Y-%m-%d') hire_date,
                    date_format(leave_date,'%Y-%m-%d') leave_date,
                    email,
                    node_department_id,
                    job_title 
                from hr_staff_info as a
                where leave_date BETWEEN DATE_SUB(CURDATE(),INTERVAL 10 DAY)
                AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)
                AND state = 2 ";

        return  $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //获取配置参数
    private function getEnv($code) {
        $setting = SettingEnvModel::findFirst([
                    'conditions' => "code = :code:",
                    'bind' => ['code' => $code],
                    'columns' => ['set_val']
        ]);

        return !empty($setting) ? $setting->set_val : '';
    }

    /**
     * 邮件发送成功记录
     * @param type $params
     */
    private function emailSendRecord($params) {

        try {
            //中国国籍在外务工人员
            $model = new StaffEmailRecord();
            $model->staff_info_id = $params['staff_info_id'];
            $model->email = $params['email'];
            $model->event = $params['event'];
            $model->save();
            $this->logger->write_log("task：chinastaff 事件：{$params['event']} 写入DB成功！：", 'info');
        } catch (Exception $e) {
            $this->logger->write_log("task：chinastaff 事件：{$params['event']} 写入DB失败！info：{$e->getMessage()}", 'notice');
        }
    }

    //数组合并
    private function arr_reduce($param) {
        return array_reduce($param, function ($result, $value) {
            return array_merge($result, array_values($value));
        }, array());
    }

    //获取部门信息
    private function getDepartment($id) {
        $department = SysDepartmentModel::findFirst(["conditions" => " id = :id: ",
                    "bind" => ["id" => $id],
                    'columns' => 'name',
        ]);
        if (!$department) {
            return ['name' => $id];
        }
        return $department ? $department : ['name' => $id];
    }

    //获取部门信息
    private function getJobTitle($id) {
        $job = HrJobTitleModel::findFirst(["conditions" => " id = :id: ",
                    "bind" => ["id" => $id],
                    'columns' => 'job_name',
        ]);
        if (!$job) {
            return ['job_name' => $id];
        }
        return $job = $job->toArray();
    }

    /**
     * 已发送邮件
     * @param type $event 1： 入职 2：离职
     * @return type
     */
    private function sendedEmail($event = 1) {
        $sended = StaffEmailRecord::find([
                    'conditions' => 'event = :event:',
                    'bind' => [
                        'event' => $event
                    ],
                    'columns' => "staff_info_id",
                    'group' => "staff_info_id",
                    'order'=> "id desc"
                ])->toArray();

        return $sended ? array_column($sended, 'staff_info_id') : [];
    }

}
