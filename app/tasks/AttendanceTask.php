<?php


use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\HrOutSourcingBlacklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\AttendanceDetectCheatServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\PunchInServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Models\backyard\StaffPickupDeliveryDataModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;


class AttendanceTask extends \BaseTask
{

    /**
     * 泰国高级主管同步考勤到虚拟账号
     * @param $params
     * @return true
     * @throws BusinessException
     */
    public function syncAttendanceToVirtualStaffAction($params): bool
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        if (!empty($params[0])) {
            $date_at = $params[1] ?: date('Y-m-d');
            $server->syncAttendanceToVirtualStaffPop($params[0], $date_at);
        }
        $redis = $this->getDI()->get('redisLib');
        while ($data = $redis->rpop(RedisEnums::LIST_SYNC_ATTENDANCE_TO_VIRTUAL_STAFF)) {
            sleep(4);
            $this->getDI()->get("logger")->write_log('syncAttendanceToVirtualStaff data : ' . $data, "info");
            $data = json_decode($data, true);
            try {
                $server->syncAttendanceToVirtualStaffPop($data['staff_id'], $data['attendance_date']);
            } catch (BusinessException $be) {
                echo $be->getMessage();
                $this->getDI()->get("logger")->write_log('syncAttendanceToVirtualStaff data : ' . $be->getMessage(),
                    "error");
            }
        }
        return true;
    }

    /**
     * 外协上班打卡记录所在外协公司
     * @return void
     */
    public function syncAttendanceForCompanyNameEfAction()
    {
        $server = new AttendanceServer($this->lang, $this->timezone);

        $redis = $this->getDI()->get('redisLib');
        while ($data = $redis->rpop(RedisEnums::SYNC_ATTENDANCE_FOR_COMPANY_NAME_EF)) {
            sleep(3);
            $this->getDI()->get("logger")->write_log('syncAttendanceForCompanyNameEf data : ' . $data, "info");
            $data = json_decode($data, true);
            $server->syncAttendanceForCompanyNameEfPop($data['staff_info_id'], $data['attendance_date']);
        }
    }

    /**
    * @description 发送消息
    * 内容
    *  标题：{员工工号} 账号暂停使用通知
    *  您的网点外协员工 {员工工号} 被稽查到有疑似作弊行为，该账号从明日起将被暂停使用，请等待核查结果。
    *
    *
    * 通知对象：黑名单外协员工所在网点职位为网点主管(Branch Supervisor)
    */
    public function sendSearchFaceMessageAction()
    {
        $redis = $this->getDI()->get('redisLib');
        $server = new AttendanceServer($this->lang, $this->timezone);

        while ($redis_data = $redis->rpop(AttendanceServer::REDIS_SEARCH_STAFF_FACE_MESSAGE_KEY)) {
            try {
                if (!empty($redis_data)) {
                    $this->logger->write_log(sprintf("[sendSearchFaceMessage task][%s]", $redis_data), 'info');

                    $data = json_decode($redis_data, true);
                    $server->sendDetectedMessage($data);
                } else {
                    sleep(1);
                }
            } catch (\Exception $e) {
                $this->logger->write_log(__CLASS__.__METHOD__.' exception:'.$e->getMessage());
                echo date('Y-m-d H:i:s').__METHOD__.$e->getMessage().PHP_EOL;
                break;
            }
        }
        sleep(5);
    }

    /**
     * @description 发送邮件
     * 内容
     *  标题：{日期} 疑似作弊外协账号名单
     *  正文：{日期} 在外协员工打卡环节，稽查到以下外协账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假外协账号作弊情况，名单见附件。
     *
     * 取值说明：
     *  {日期}：取前一日日期，格式：2023年1月13日
     *
     * 通知对象：泰国HR邮件组、泰国Network QC邮件组、产品邮件组
     * 发送时间：09:00AM
     *
     * 表头：
     *  1. 外协工号 ｜ 2. 外协身份证号 ｜ 3. 打卡扫描照片 ｜ 4. 正式员工底片 ｜ 5. 正式员工工号 ｜ 6. 打卡时间
     */
    public function sendEmailForPunchCardAction()
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("*");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00", strtotime("-1 day"))]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('state = :state: and type = :type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        $lastDate = date("Y-m-d", strtotime("-1 day"));


        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            echo "无数据",PHP_EOL;
            return false;
        }
        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id in({staff_ids:array})",
            "bind" => [
                "staff_ids" => $staffIds,
            ],
            "columns" => "staff_info_id,name,identity,mobile,mobile_company,company_name_ef,sys_store_id",
        ])->toArray();

        $storeIdsArr       = array_column($staffInfo, 'sys_store_id');
        $storeIds          = getIdsStr($storeIdsArr);
        $storeInfoList     = (new SysListRepository())->getStoreList(['ids' => $storeIds]);
        $storeInfoListToId = array_column($storeInfoList, 'name', 'id');
        $staffInfoArr      = array_column($staffInfo, null, 'staff_info_id');

        $data = [];
        foreach ($list as $item) {
            $identity        = $staffInfoArr[$item['staff_info_id']]['identity'] ?? '';
            $mobile          = $staffInfoArr[$item['staff_info_id']]['mobile'] ?? '';
            $mobile_company  = $staffInfoArr[$item['staff_info_id']]['mobile_company'] ?? '';
            $company_name_ef = $staffInfoArr[$item['staff_info_id']]['company_name_ef'] ?? '';
            $name            = $staffInfoArr[$item['staff_info_id']]['name'] ?? '';
            $storeId         = $staffInfoArr[$item['staff_info_id']]['sys_store_id'] ?? '';
            $storeName       = $storeInfoListToId[$storeId] ?? '';
            $mobileData = [];
            if(!empty($mobile_company)) {
                $mobileData[] = $mobile_company;
            }
            if(!empty($mobile)) {
                $mobileData[] = $mobile;
            }
            $mobileString = !empty($mobileData) ? implode(',', $mobileData) : '';

            $data[] = [
                $storeName,                                                        //{外协员工所属网点名称}：取疑似作弊的外协员工工号所属网点
                $item['staff_info_id'],
                $name,                                                             //{外协姓名}：取疑似作弊的外协员工姓名
                $mobileString,                                                     //{外协电话}：取疑似作弊的外协员工个人电话及企业电话，如有多个，中间逗号隔开
                $identity,
                $item['os_submit_face_image_path'],
                $item['work_attendance_path'],
                $item['match_staff_info_id'],
                date("Y-m-d H:i:s", strtotime($item['created_at'])),
                $company_name_ef,                                                   //{供应商名称}：取疑似作弊的外协员工工号的外协公司名称，没有为空
            ];
        }

        if (isCountry("TH")) {
            $t = $this->getTranslation("th");
        } else {
            $t = $this->getTranslation("en");
        }

        $header = [
            $t->_('att_os_staff_store_name'), //外协员工所属网点名称
            $t->_('att_os_staff_no'),         //外协工号
            $t->_('att_os_staff_name'),       //外协姓名
            $t->_('att_os_staff_phone'),      //外协电话
            $t->_('att_os_staff_identity'),   //外协身份证号
            $t->_('att_punch_in_image'),      //打卡扫描照片
            $t->_('att_staff_image_url'),     //正式员工底片
            $t->_('att_staff_no'),            //正式员工工号
            $t->_('attendance_time'),         //打卡时间
            $t->_('att_os_company_name'),     //供应商名称
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data,'detected_data');

        //标题：{日期} 疑似作弊外协账号名单
        //正文：{日期} 在外协员工打卡环节，稽查到以下外协账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假外协账号作弊情况，名单见附件。
        $title    = $t->_('att_os_email_title', ['detected_date' => $lastDate]);
        $content  = $t->_('att_os_email_content', ['detected_date' => $lastDate]);
        $content  .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here')) ;

        //获取发送邮件列表
        $emailList = (new AttendanceServer($this->lang, $this->timezone))->getEmailList();

        if (empty($emailList)) {
            $this->logger->write_log('sendEmailForPunchCardAction, 收件人不能为空:'. $emailList, 'notice');
        }
        $sendEmail = \FlashExpress\bi\App\library\Mail::send(
            $emailList,
            $title,
            $content);

        if ($sendEmail) {
            echo ' send success!';
            $this->logger->write_log("sendEmailForRegularInspectAction success!", "notice");
            return true;
        } else {
            $this->logger->write_log('sendEmailForPunchCardAction, 发送邮件失败:'. $emailList, 'notice');
            return false;
        }
    }


    /**
     * 众包外协人脸作弊数据同步FMS
     * @param $params
     * @return void
     */
    public function crowdSourcingCheatSyncAction($params)
    {
        $server  = new AttendanceDetectCheatServer();
        $date_at = $params[0] ?? date('Y-m-d');
        $server->syncCrowdSourcingCheatToFMS($date_at);
    }


    /**
     * @description 发送邮件
     * 内容
     *  标题：{日期} 疑似作弊外协账号名单
     *  正文：{日期}因稽查到以下外协账号，有非本人登陆操作记录，疑似有作弊行为，现已将以下账号移入外协黑名单。
     * 请通知对应供应商公司指派新的外协员工，并核实作弊情况是否真实，若有系统误判，请及时与泰国产品部门申诉。
     * 名单见附件。
     *
     * 取值说明：
     *  {日期}：取当日日期，格式：2023年1月13日
     *
     * 通知对象：泰国Network QC邮件组、产品邮件组
     * 发送时间：23:30AM
     *
     * 表头：
     *  1. 外协工号 ｜ 2. 外协身份证号｜ 3. 账号留存底片 ｜ 4. 打卡扫描照片 ｜ 5. 违规时间
     */
    public function sendEmailForRegularInspectAction()
    {

        $hire_type_limit = HrStaffInfoModel::HIRE_TYPE_12;

        //个人外协
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->join(HrStaffItemsModel::class,
            "b.staff_info_id = s.staff_info_id and b.item = 'OUTSOURCING_TYPE' and b.value = 'individual'", 'b');
        $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime("+1 day"))]);
        $builder->andWhere('s.state = :state: and s.type = :type: and hsi.hire_type != :hire_type:', [
            'state'     => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
            'type'      => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
            'hire_type' => $hire_type_limit,
        ]);
        $individualList = $builder->getQuery()->execute()->toArray();

        $model = new SettingEnvServer();
        $email = $model->getSetVal('outsourcing_attendance_person_email');
        $iEmailList = !empty($email) ? explode(',', $email) : [];
        $this->doSendEmail($individualList, $iEmailList);

        //同城外协
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->join(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime("+1 day"))]);
        $builder->andWhere('s.state = :state: and s.type = :type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
        ]);
        $builder->andWhere('hsi.hire_type = 11 and hsi.company_name_ef like :company:', ['company' => 'VIC']);//模糊吗？？
        $sameCityList = $builder->getQuery()->execute()->toArray();

        $email = $model->getSetVal('outsourcing_attendance_intra_city_email');
        $sEmailList = !empty($email) ? explode(',', $email) : [];

        $this->doSendEmail($sameCityList, $sEmailList);

        //公司外协
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->join(HrStaffItemsModel::class, "b.staff_info_id = s.staff_info_id and b.item = 'OUTSOURCING_TYPE' and b.value = 'company'", 'b');
        $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime("+1 day"))]);
        $builder->andWhere('s.state = :state: and s.type = :type:  and hsi.hire_type != :hire_type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
            'hire_type' => $hire_type_limit,
        ]);
        $companyList = $builder->getQuery()->execute()->toArray();


        $diff = array_diff(array_column($companyList, 'staff_info_id'),
            array_column($sameCityList, 'staff_info_id')
        );
        if (!empty($diff)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
                s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
                s.created_at,s.updated_at
            ");
            $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
            $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
            $builder->join(HrStaffItemsModel::class, "b.staff_info_id = s.staff_info_id and b.item = 'OUTSOURCING_TYPE' and b.value = 'company'", 'b');
            $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00")]);
            $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime("+1 day"))]);
            $builder->andWhere('s.state = :state: and s.type = :type:  and hsi.hire_type != :hire_type:', [
                'state'     => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
                'type'      => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
                'hire_type' => $hire_type_limit,
            ]);
            $builder->inWhere("s.staff_info_id", $diff);
            $companyList = $builder->getQuery()->execute()->toArray();

            $email = $model->getSetVal('outsourcing_attendance_company_email');
            $cEmailList = !empty($email) ? explode(',', $email) : [];
            
            $this->doSendEmail($companyList, $cEmailList);
        } else {
            echo "没有公司外协人员作弊数据";
        }

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("*");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->where('s.created_at > :start_time:', ['start_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.created_at <= :end_time:', ['end_time' => date("Y-m-d 00:00:00", strtotime("+1 day"))]);
        $builder->andWhere('s.state = :state: and s.type = :type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        $staffIds = array_column($list, 'staff_info_id');
        if (!empty($staffIds)) {
            $staffInfo = HrStaffInfoModel::find([
                "staff_info_id in({staff_ids:array}) and hire_type != :hire_type:",
                "bind"    => [
                    "staff_ids" => $staffIds,
                    'hire_type' => $hire_type_limit,
                ],
                "columns" => "staff_info_id,name,identity,mobile",
            ])->toArray();
            $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');

            foreach ($list as $item) {
                if (!isset($staffInfoArr[$item['staff_info_id']])) {
                    continue;
                }
                $identity = $staffInfoArr[$item['staff_info_id']]['identity'];
                $mobile = $staffInfoArr[$item['staff_info_id']]['mobile'];
                //加入外协黑名单
                $info = HrOutSourcingBlacklistModel::findFirst([
                    "(identity = :id: or mobile = :mobile:) and status = 1 and reason_code = :reason_code:",
                    "bind" => [
                        "id"          => $identity,
                        "mobile"      => $mobile,
                        'reason_code' => HrOutSourcingBlacklistModel::REASON_CODE_FACE_DIFF,
                    ],
                ]);
                if (empty($info)) {
                    $this->addOsBlacklist($item['staff_info_id']);
                }
            }
        }

        $this->logger->write_log("sendEmailForRegularInspectAction success!", "notice");
    }

    protected function addOsBlacklist($staff_info_id)
    {
        $hcm_rpc = new ApiClient('hcm_rpc', '', 'add_os_blacklist');
        $hcm_rpc->setParams(
            [
                "staff_info_id" => $staff_info_id,
                "reason_code"   => 1,//转换了 方便不同国家调用
                "type"          => 2,
            ]
        );
        $hcm_rpc->execute();
    }


    /**
     * @description 生成员工信息csv文件
     * 老库
     */
    public function generateStaffInfoAction()
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        $jobTitle = $server->getCourierJobTitle();

        //1. 全网在职、待离职、停职快递员岗位人脸库
        //2. 全网离职时间<=30天的快递员岗位人脸库
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id, 
            hsi.job_title, 
            ss.id as store_id, 
            ss.name as store_name,
            ss.manage_piece, 
            ss.manage_region, 
            sw.work_attendance_bucket, 
            sw.work_attendance_path
        ");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(StaffWorkAttendanceAttachmentModel::class, "hsi.staff_info_id = sw.staff_info_id and sw.deleted = 0", 'sw');
        $builder->join(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');
        $builder->andWhere('((hsi.state in(1,3) and hsi.formal = 1) or (hsi.state = 2 and hsi.formal = 1 and hsi.leave_date > :leave_date:)) and hsi.is_sub_staff = 0', [
            'leave_date' => date("Y-m-d", strtotime("-30 days")),
        ]);
        $builder->inWhere('hsi.job_title', $jobTitle);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            echo "无数据";
            exit();
        }

        $regionInfo = \FlashExpress\bi\App\Models\backyard\SysManageRegionModel::find()->toArray();
        $regionDetail = array_column($regionInfo, 'name', 'id');

        $data = [];
        foreach ($list as $v) {
            $data[] = [
                strtoupper(env('country_code', 'Th')),
                $v['staff_info_id'],
                $v['store_name'],
                $v['store_id'],
                $regionDetail[$v['manage_region']] ?? "",
                $v['manage_region'],
            ];
        }

        $header = [
            "国家Code",
            "员工工号",
            "网点名称",
            "网点ID",
            "大区名称",
            "大区ID",
        ];
        $file_name = (new OssHelper)->exportSvc($header, $data, genFileName('staff_info'));

        if (empty($file_name)) {
            $this->logger->write_log("生成文件失败");
            exit();
        }

        $params = [
            'file_path' => $file_name['data'],
            'created_at' => date("Y-m-d H:i:s"),
        ];

        $model = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst([
            "code = 'download_courier_info'",
        ]);
        $model->set_val = json_encode($params);
        $model->save();

        $this->logger->write_log("定期生成员工信息csv 完成", "notice");
    }

    /**
     * @description 生成正式员工信息csv文件
     * 正式员工 在职、停职、在编、非子账号、雇佣类型（正式+月薪制）
     * 新库
     * https://flashexpress.feishu.cn/docx/N7F0dLFIiofylLx6JcmcW7SOn47
     */
    public function generateFullTimeStaffInfoAction()
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        $jobTitle = $server->getAgentCourierJobTitle();

        //全网在职、停职快递员岗位人脸库
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id, 
            hsi.job_title, 
            ss.id as store_id, 
            ss.name as store_name,
            ss.manage_piece, 
            ss.manage_region, 
            sw.work_attendance_bucket, 
            sw.work_attendance_path
        ");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(StaffWorkAttendanceAttachmentModel::class, "hsi.staff_info_id = sw.staff_info_id and sw.deleted = 0", 'sw');
        $builder->join(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');
        $builder->andWhere('hsi.state in(1,3) and hsi.formal = 1 and hsi.is_sub_staff = 0');
        $builder->inWhere('hsi.hire_type', [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2]);
        $builder->inWhere('hsi.job_title', $jobTitle);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            echo "无数据";
            exit();
        }

        $regionInfo = SysManageRegionModel::find()->toArray();
        $regionDetail = array_column($regionInfo, 'name', 'id');

        $data = [];
        foreach ($list as $v) {
            $data[] = [
                strtoupper(env('country_code', 'Th')),
                $v['staff_info_id'],
                $v['store_name'],
                $v['store_id'],
                $regionDetail[$v['manage_region']] ?? "",
                $v['manage_region'],
            ];
        }

        $header = [
            "国家Code",
            "员工工号",
            "网点名称",
            "网点ID",
            "大区名称",
            "大区ID",
        ];
        $file_name = (new OssHelper)->exportSvc($header, $data, genFileName('staff_info'));

        if (empty($file_name)) {
            $this->logger->write_log("生成文件失败");
            exit();
        }

        $params = [
            'file_path' => $file_name['data'],
            'created_at' => date("Y-m-d H:i:s"),
        ];

        $model = SettingEnvModel::findFirst([
            "code = 'download_formal_courier_info'",
        ]);
        if (empty($model)) {
            $insert['code']    = 'download_formal_courier_info';
            $insert['set_val'] = json_encode($params);
            $model             = new SettingEnvModel();
            $model->create($insert);
            $this->logger->write_log("定期生成正式员工信息csv 完成", "notice");
            return;
        }
        $model->set_val = json_encode($params);
        $model->save();

        $this->logger->write_log("定期生成正式员工信息csv 完成", "notice");
    }

    /**
     * ！！！废弃 ai那边没用
     * @description 生成员工底片变更信息csv文件
     * https://flashexpress.feishu.cn/docx/N7F0dLFIiofylLx6JcmcW7SOn47
     * php app/cli.php attendance generateAttachment
     */
    public function generateAttachmentAction()
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        $jobTitle = $server->getAgentCourierJobTitle();
        $data = date('Y-m-d 00:00:00', strtotime('-7 days'));

        //全网在职、停职快递员岗位人脸库
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id, 
            hsi.name as staff_name, 
            sw.work_attendance_bucket as bucket, 
            sw.work_attendance_path as path
        ");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(StaffWorkAttendanceAttachmentModel::class, "hsi.staff_info_id = sw.staff_info_id and sw.deleted = 0", 'sw');
        $builder->andWhere('hsi.state in(1,3) and hsi.formal = 1 and hsi.is_sub_staff = 0');
        $builder->andWhere('sw.created_at >= :created_at:', ['created_at' => $data]);
        $builder->andWhere('sw.deleted = 0');
        $builder->inWhere('hsi.hire_type', [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2]);
        $builder->inWhere('hsi.job_title', $jobTitle);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            echo "无数据";
            exit();
        }

        $flag = 0;//是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }

        $data = [];
        foreach ($list as $v) {
            $data[] = [
                $v['staff_info_id'],
                $v['staff_name'],
                $server->format_oss($v, $flag),
            ];
        }

        $header = [
            "员工工号",
            "员工姓名",
            "员工底片",
        ];
        $file_name = (new OssHelper)->exportSvc($header, $data, genFileName('staff_attachment_info'));
        if (empty($file_name)) {
            $this->logger->write_log("生成文件失败");
            exit();
        }

        $params = [
            'file_path' => $file_name['data'],
            'created_at' => date("Y-m-d H:i:s"),
        ];

        $model = SettingEnvModel::findFirst([
            "code = 'download_courier_attachment_transfer'",
        ]);
        $model->set_val = json_encode($params);
        $model->save();

        $this->logger->write_log("定期生成员工底片变更信息csv文件 完成", "notice");
    }

    /**
     * @description 生成员工打卡图片csv文件
     */
    public function generateStaffAttendanceInfoAction()
    {
        ini_set('memory_limit', '4048M');
        $server = new AttendanceServer($this->lang, $this->timezone);
        $jobTitle = $server->getCourierJobTitle();

        //1. 全网在职、待离职、停职快递员岗位人脸库
        //2. 全网离职时间<=30天的快递员岗位人脸库
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id, 
            sw.started_path,
            sw.started_bucket,
            sw.end_path,
            sw.end_bucket
        ");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(\FlashExpress\bi\App\Models\StaffWorkAttendance::class, "hsi.staff_info_id = sw.staff_info_id", 'sw');
        $builder->andWhere('((hsi.state in(1,3) and hsi.formal = 1) or (hsi.state = 2 and hsi.formal = 1 and hsi.leave_date > :leave_date:)) and hsi.is_sub_staff = 0', [
            'leave_date' => date("Y-m-d", strtotime("-30 days")),
        ]);
        $builder->andWhere("sw.attendance_date > :attendance_date:", ['attendance_date' => date("Y-m-d", strtotime("-7 days"))]);
        $builder->inWhere('hsi.job_title', $jobTitle);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            echo "无数据";
            exit();
        }

        $data = [];
        $env_bll = new BySettingRepository();
        $point = $env_bll->get_setting('server_point');
        if (empty($point)) {
            $point = "";
        }
        foreach ($list as $v) {

            if ($v['started_path']) {
                $data[] = [
                    strtoupper(env('country_code', 'Th')),
                    $v['staff_info_id'],
                    $this->formatOss($v['started_bucket'], $point, $v['started_path']),
                ];
            }

            if ($v['end_path']) {
                $data[] = [
                    strtoupper(env('country_code', 'Th')),
                    $v['staff_info_id'],
                    $this->formatOss($v['end_bucket'], $point, $v['end_path']),
                ];
            }
        }
        unset($list);

        $header = [
            "国家Code",
            "员工工号",
            "url",
        ];
        $file_name = (new OssHelper)->exportSvc($header, $data, genFileName('staff_attendance'));

        if (empty($file_name)) {
            $this->logger->write_log("生成 staff_attendance 文件失败");
            exit();
        }

        $params = [
            'file_path' => $file_name['data'],
            'created_at' => date("Y-m-d H:i:s"),
        ];

        $model = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst([
            "code = 'download_courier_attendance_info'",
        ]);
        $model->set_val = json_encode($params);
        $model->save();

        $this->logger->write_log("定期生成员工出勤信息csv 完成", "notice");
    }

    /**
     * @param $path
     * @param $point
     * @param $bucket
     * @return string
     */
    private function formatOss($bucket, $point, $path)
    {
        return sprintf("https://%s%s%s", $bucket, $point, $path);
    }

    /**
     * @description 发送邮件
     * @param $list
     * @param $emailList
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    private function doSendEmail($list, $emailList)
    {
        if (empty($list)) {
            $this->logger->write_log('doSendEmail, list不能为空:'. json_encode($list), 'notice');
        }

        if (empty($emailList)) {
            $this->logger->write_log('doSendEmail, 收件人不能为空:'. json_encode($emailList), 'notice');
        }

        $lastDate = date("Y-m-d");

        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            echo "无数据",PHP_EOL;
            return false;
        }
        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id in({staff_ids:array})",
            "bind" => [
                "staff_ids" => $staffIds,
            ],
            "columns" => "staff_info_id,name,identity,mobile,mobile_company,company_name_ef,sys_store_id",
        ])->toArray();

        $storeIdsArr = array_column($staffInfo,'sys_store_id');
        $storeIds                   = getIdsStr($storeIdsArr);
        $storeInfoList = (new SysListRepository())->getStoreList(['ids' => $storeIds]);
        $storeInfoListToId = array_column($storeInfoList, 'name', 'id');

        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');

        $data = [];
        $publicRepo = new PublicRepository();
        foreach ($list as $item) {
            $identity        = $staffInfoArr[$item['staff_info_id']]['identity'] ?? '';
            $mobile          = $staffInfoArr[$item['staff_info_id']]['mobile'] ?? '';
            $mobile_company  = $staffInfoArr[$item['staff_info_id']]['mobile_company'] ?? '';
            $company_name_ef = $staffInfoArr[$item['staff_info_id']]['company_name_ef'] ?? '';
            $name            = $staffInfoArr[$item['staff_info_id']]['name'] ?? '';
            $storeId         = $staffInfoArr[$item['staff_info_id']]['sys_store_id'] ?? '';
            $storeName       = $storeInfoListToId[$storeId] ?? '';
            $mobileData = [];
            if(!empty($mobile_company)) {
                $mobileData[] = $mobile_company;
            }
            if(!empty($mobile)) {
                $mobileData[] = $mobile;
            }
            $mobileString = !empty($mobileData) ? implode(',', $mobileData) : '';

            $data[] = [
                $storeName,                                                        //{外协员工所属网点名称}：取疑似作弊的外协员工工号所属网点
                $item['staff_info_id'],                                            //{外协工号} ：取未通过人脸校验的外协员工工号
                $name,                                                             //{外协姓名}：取疑似作弊的外协员工姓名
                $mobileString,                                                     //{外协电话}：取疑似作弊的外协员工个人电话及企业电话，如有多个，中间逗号隔开
                $identity,
                $item['os_face_image_source_path'],                                 //{账号留存底片}：取该疑似作弊外协员工在系统内留存的底片
                $item['os_submit_face_image_path'],                                 //{打卡扫描照片}：取当日KIT人脸校验时留存的扫描面部照片
                date("Y-m-d H:i:s", strtotime($item['created_at'])),         //{违规时间}：取人脸校验未识别通过的时间，精确到时-分-秒
                $company_name_ef,                                                   //{供应商名称}：取疑似作弊的外协员工工号的外协公司名称，没有为空
            ];

            //加入外协黑名单
            $info = HrOutSourcingBlacklistModel::findFirst([
                "(identity = :id: or mobile = :mobile:) and status = 1  and reason_code = :reason_code:",
                "bind" => [
                    "id"          => $identity,
                    "mobile"      => $mobile,
                    'reason_code' => HrOutSourcingBlacklistModel::REASON_CODE_FACE_DIFF,
                ],
            ]);
            if (empty($info)) {
                $this->addOsBlacklist($item['staff_info_id']);
            }

            $storeSupervisor = HrStaffInfoModel::find([
                "sys_store_id = :store_id: and job_title = :job_title: and state = 1 and formal = 1 and is_sub_staff = 0",
                "bind" => [
                    "store_id" => $item['organization_id'],
                    "job_title" => enums::$job_title['branch_supervisor'],
                ],
                "columns" => "staff_info_id",
            ])->toArray();

            if (empty($storeSupervisor)) {
                continue;
            }

            $storeSupervisorArr = array_column($storeSupervisor, 'staff_info_id');
            $staffLang = (new StaffServer())->getBatchStaffLanguage($storeSupervisorArr);

            foreach ($storeSupervisorArr as $staffId) {
                //给网点主管发送网点员工账号停用消息
                $lang = $staffLang[$staffId] ?? "en";
                $t = $this->getTranslation($lang);

                $title = $t->_('store_staff_account_deactivation_notice', [
                    'staff_info_id' => $item['staff_info_id'],
                ]);
                $content = $t->_('store_staff_account_deactivation_content', [
                    'staff_info_id' => $item['staff_info_id'],
                ]);

                $content .= $item['staff_info_id'] . "<br/>";
                $content .= ($staffInfoArr[$item['staff_info_id']]['name'] ?? '') . "<br/>";
                $content .= date("Y-m-d H:i:s", strtotime($item['created_at'])) . "<br/>";

                $param = [
                    'staff_info_id'   => $staffId,
                    'message_title'   => $title,
                    'message_content' => $content,
                    'type'            => MessageEnums::CATEGORY_GENERAL,
                ];
                $publicRepo->sendMessageToSubmitter($param);
            }
        }

        if (isCountry("TH")) {
            $t = $this->getTranslation("th");
        } else {
            $t = $this->getTranslation("en");
        }

        $header = [
            $t->_('att_os_staff_store_name'),                   //外协员工所属网点名称
            $t->_('att_os_staff_no'),                           //外协工号
            $t->_('att_os_staff_name'),                         //外协姓名
            $t->_('att_os_staff_phone'),                        //外协电话
            $t->_('att_os_staff_identity'),                     //外协身份证号
            $t->_('att_staff_account_image_url'),               //账号留存底片
            $t->_('att_staff_punch_card_image_url'),            //打卡扫描照片
            $t->_('att_created_at'),                            //违规时间
            $t->_('att_os_company_name'),                       //供应商名称
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data,'detected_data');

        //标题：{日期} 疑似作弊外协账号名单
        //正文：{日期}因稽查到以下外协账号，有非本人登陆操作记录，疑似有作弊行为，现已将以下账号移入外协黑名单。
        // 请通知对应供应商公司指派新的外协员工，并核实作弊情况是否真实，若有系统误判，请及时与泰国产品部门申诉。
        // 点击链接下载文件
        $title    = $t->_('att_os_email_title', ['detected_date' => $lastDate]);
        $content  = $t->_('att_email_detect_content_1', ['detected_date' => $lastDate]) . "<br/>";
        $content  .= $t->_('att_email_detect_content_2') ."<br/>";
        $content  .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here')) ;

        $sendEmail = \FlashExpress\bi\App\library\Mail::send(
            $emailList,
            $title,
            $content);

        if ($sendEmail) {
            echo ' send success!';
            return true;
        } else {
            $this->logger->write_log('sendEmailForRegularInspectAction, 发送邮件失败:'. json_encode($emailList), 'notice');
            return false;
        }
    }

    /**
     * @description 发送邮件
     * 内容
     *  标题：{日期} 疑似作弊个人代理与正式员工账号名单
     *  正文：{日期} 在个人代理人脸验证环节，稽查到以下个人代理账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假账号作弊情况。
     * 点击链接下载名单附件。
     *
     * 取值说明：
     *  {日期}：取前一日日期，格式：2024-04-13
     *
     * 通知对象：泰国HR邮件组、泰国Network QC邮件组、产品邮件组
     * 发送时间：于第二日04:30AM
     *
     * 表头：
     *  1. 个人代理ID ｜ 2. 身份证号 ｜ 3. 打卡扫描照片 ｜ 4. 正式员工底片 ｜ 5. 正式员工工号 ｜ 6. 打卡时间
     *
     * php app/cli.php attendance sendEmailForPunchCardAgent
     */
    public function sendEmailForPunchCardAgentAction()
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("*");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->where('s.created_at >= :start_time:', ['start_time' => date("Y-m-d 00:00:00", strtotime("-1 day"))]);
        $builder->andWhere('s.created_at < :end_time:', ['end_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.state = :state: and s.type in({type:array})', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH,
            'type'  => [
                StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT,
                StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE,
            ],
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        $lastDate = date('Y-m-d', strtotime('-1 day'));

        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            echo "无数据",PHP_EOL;
            return false;
        }
        //匹配正式员工id
        $allStaffIds = $staffIds;
        $matchStaffIds = array_column($list,'match_staff_info_id');
        $matchStaffIds = array_diff($matchStaffIds,[null]);
        if($matchStaffIds){
            $allStaffIds = array_merge($staffIds,$matchStaffIds);
        }
        $allStaffIds = array_values($allStaffIds);

        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id in({staff_ids:array})",
            "bind" => [
                "staff_ids" => $allStaffIds,
            ],
            "columns" => "staff_info_id,name,identity,mobile,mobile_company,company_name_ef, sys_store_id, job_title",
        ])->toArray();
        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');
        //大区片区等
        $storeIds = array_column($staffInfo, 'sys_store_id');
        $storeIds = array_diff($storeIds,['-1']);
        $storeIds = array_values(array_unique($storeIds));
        $storeInfo = [];
        if(!empty($storeIds)){
            $storeServer = new SysStoreServer($this->lang, $this->timezone);
            $storeInfo = $storeServer->batchStorePieceRegion($storeIds);
        }
        //是否支援
        $supportData   = $this->supportInfo($lastDate, $staffIds);
        $masterSupport = empty($supportData) ? [] : array_column($supportData, null, 'staff_info_id');
        $subSupport    = empty($supportData) ? [] : array_column($supportData, null, 'sub_staff_info_id');
        //主账号 和子账号 id
        $masterIds = empty($supportData) ? [] : array_column($supportData, 'staff_info_id');
        $subIds    = empty($supportData) ? [] : array_column($supportData, 'sub_staff_info_id');

        //打卡坐标
        $attendanceData = $this->getAttendanceCoordinate($lastDate, $staffIds);
        //揽派件信息 所有员工的
        $parcelNum = StaffPickupDeliveryDataModel::find([
            'columns'    => 'staff_info_id, (pickup_count + fh_pickup_count + delivery_count) as num',
            'conditions' => 'staff_info_id in ({ids:array}) and stat_date = :date_at:',
            'bind'       => [
                'ids'     => $allStaffIds,
                'date_at' => $lastDate,
            ],
        ])->toArray();
        $parcelNum = empty($parcelNum) ? [] : array_column($parcelNum, 'num', 'staff_info_id');
        //职位
        $jobTitles = HrJobTitleModel::find([
            'columns'    => 'id, job_name',
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => array_column($staffInfo, 'job_title')],
        ])->toArray();
        $jobTitles = empty($jobTitles) ? [] : array_column($jobTitles, 'job_name', 'id');
        //轮休
        $workdayInfo = [];
        if (!empty($matchStaffIds)) {
            $workdayInfo = $this->workdayInfo($lastDate, $matchStaffIds);
        }
        //取底片 如果是支援 要取主账号的
        $photoIds = array_merge($staffIds, $masterIds);
        //排除子账号
        $photoIds  = array_values(array_diff($photoIds, $subIds));

        $photoData = StaffWorkAttendanceAttachmentModel::find([
            'columns'    => 'staff_info_id, work_attendance_path, work_attendance_bucket',
            'conditions' => 'staff_info_id in ({ids:array}) and deleted = 0',
            'bind'       => ['ids' => $photoIds],
        ])->toArray();

        $photoData = empty($photoData) ? [] : array_column($photoData, null, 'staff_info_id');
        //子账号匹配主账号
        $subMaster = array_column($subSupport, 'staff_info_id', 'sub_staff_info_id');
        $env_bll   = new BySettingRepository();
        $point     = $env_bll->get_setting('server_point');

        $matchStaffPunchInCard = StaffWorkAttendanceModel::find([
            'columns'    => 'staff_info_id, started_path, started_bucket',
            'conditions' => 'staff_info_id in ({ids:array}) and attendance_date = :attendance_date:',
            'bind'       => [
                'ids'             => $matchStaffIds,
                'attendance_date' => date('Y-m-d', strtotime('-1 day')),
            ],
        ])->toArray();
        $matchStaffPunchInCard = array_column($matchStaffPunchInCard, null, 'staff_info_id');

        $data = [];
        foreach ($list as $item) {
            $staffId = $item['staff_info_id'];
            $coordinate = '';
            if(!empty($attendanceData[$staffId]) && !empty($attendanceData[$staffId]['started_staff_lat'])){
                $coordinate = $attendanceData[$staffId]['started_staff_lat'] . ',' . $attendanceData[$staffId]['started_staff_lng'];
            }
            $matchId = $item['match_staff_info_id'];
            $staffStoreId = $staffInfoArr[$staffId]['sys_store_id'] ?? '';
            $matchStoreId = $staffInfoArr[$matchId]['sys_store_id'] ?? '';
            $identity        = $staffInfoArr[$staffId]['identity'] ?? '';
            $name            = $staffInfoArr[$staffId]['name'] ?? '';
            //是否支援
            $supportFlag = false;
            if(!empty($masterSupport[$staffId]) || !empty($subSupport[$staffId])){
                $supportFlag = true;
            }
            //底片地址 要主账号的
            $masterId = $staffId;
            if(!empty($subMaster[$staffId])){
                $masterId = $subMaster[$staffId];
            }
            $photo = '';
            if(!empty($photoData[$masterId])){
                $photo = sprintf("https://%s%s%s", $photoData[$masterId]['work_attendance_bucket'], $point, $photoData[$masterId]['work_attendance_path']);
            }

            $matchStaffPunchInCardPhoto = '';
            if (!empty($matchStaffPunchInCard[$matchId])) {
                $matchStaffPunchInCardPhoto = sprintf("https://%s%s%s", $matchStaffPunchInCard[$matchId]['started_bucket'], $point, $matchStaffPunchInCard[$matchId]['started_path']);
            }
            $main_account = StaffRepository::getMasterStaffIdBySubStaff($item['staff_info_id'])?:null;

            $data[] = [
                $item['staff_info_id'],
                $main_account,
                $name,
                $jobTitles[$staffInfoArr[$staffId]['job_title']] ?? '',
                $storeInfo[$staffStoreId]['region_name'] ?? '',
                $storeInfo[$staffStoreId]['piece_name'] ?? '',
                $storeInfo[$staffStoreId]['store_name'] ?? '',
                $identity,
                $coordinate,
                ($storeInfo[$staffStoreId]['lat'] ?? '') . ',' . ($storeInfo[$staffStoreId]['lng'] ?? ''),
                $supportFlag ? 'yes' : 'no',
                $parcelNum[$staffId] ?? 0,
                date("Y-m-d H:i:s", strtotime($item['created_at'])),//打卡时间
                $item['os_submit_face_image_path'],                 //打卡照片
                $photo,                                             //打卡底片
                $item['work_attendance_path'],                      //匹配上的底片
                $matchId,
                $staffInfoArr[$matchId]['name'] ?? '',
                $jobTitles[$staffInfoArr[$matchId]['job_title'] ?? ''] ?? '',
                $storeInfo[$matchStoreId]['region_name'] ?? '',
                $storeInfo[$matchStoreId]['piece_name'] ?? '',
                $storeInfo[$matchStoreId]['store_name'] ?? '',
                in_array($matchId, $workdayInfo) ? 'OFF' : 'ON',
                $parcelNum[$matchId] ?? 0,
                $matchStaffPunchInCardPhoto, //被匹配到的正式工号的上班卡
            ];
        }

        if (RUNTIME == 'dev') {
            $t = $this->getTranslation("zh");
        } else {
            if (isCountry("TH")) {
                $t = $this->getTranslation("en");
            } else {
                $t = $this->getTranslation("en");
            }
        }

        $header    = [
            $t->_('independent_contractor_id'),//个人代理ID
            $t->_('main_account'),//主账号
            $t->_('independent_contractor_name'),//个人代理姓名
            $t->_('individual_contractor_job_title_name'),//个人代理职位
            $t->_('independent_contractor_region'),//个人代理大区
            $t->_('independent_contractor_piece'),//个人代理片区
            $t->_('independent_contractor_store'),//个人代理网点
            $t->_('att_staff_identity'),      //身份证号
            $t->_('ic_start_coordinate'),//上班打卡坐标
            $t->_('store_coordinate'),//网点坐标
            $t->_('ic_is_support'),//是否支援
            $t->_('ic_parcel_num'),//揽派件
            $t->_('attendance_time'),         //打卡时间
            $t->_('att_punch_in_image'),      //打卡扫描照片
            $t->_('independent_contractor_attachment'),//个人代理底片
            $t->_('att_staff_image_url'),     //正式员工底片
            $t->_('att_staff_no'),            //正式员工工号
            $t->_('att_staff_name'),          //正式员工姓名
            $t->_('att_staff_job_title'),     //正式员工职位
            $t->_('att_staff_region'),     //正式员工大区
            $t->_('att_staff_piece'),     //正式员工片区
            $t->_('att_staff_store'),     //正式员工网点
            $t->_('att_staff_workday'),     //正式员工休息日
            $t->_('att_staff_parcel_num'),     //正式员工揽派件
            $t->_('att_match_staff_punch_in_photo'),     //正式员工上班卡
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data, genFileName('detected_agent_data'));

        //标题：{日期} 疑似作弊外协账号名单
        //正文：{日期} 在外协员工打卡环节，稽查到以下外协账号使用者面部特征，与公司既有正式员工相匹配，请核实是否有虚假外协账号作弊情况，名单见附件。
        $title    = $t->_('att_agent_email_title', ['detected_at' => $lastDate]);
        $content  = $t->_('att_agent_email_content', ['detected_at' => $lastDate]);
        $content  .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here')) ;

        //获取发送邮件列表
        $model = new SettingEnvServer();
        $email = $model->getSetVal('agent_attendance_punch_card_email');
        $emailList = !empty($email) ? explode(',', $email) : [];
        if (empty($emailList)) {
            $this->logger->write_log('sendEmailForPunchCardAgentAction, 收件人不能为空:'. $emailList, 'notice');
        }
        $sendEmail = Mail::send($emailList, $title, $content);

        if ($sendEmail) {
            echo ' send success!';
            $this->logger->write_log("sendEmailForPunchCardAgentAction success!", "notice");
            return true;
        } else {
            $this->logger->write_log('sendEmailForPunchCardAgentAction, 发送邮件失败:'. $emailList, 'notice');
            return false;
        }
    }

    /**
     * @description 发送邮件
     * 内容
     *  标题：{日期} 疑似作弊个人代理账号名单
     *  正文：{日期}因稽查到以下个人代理账号，有非本人操作记录，疑似有作弊行为。请核实作弊情况是否真实，若有系统误判，请及时与产品部门申诉。
     *
     * 取值说明：
     *  {日期}：取当日日期，格式：2023年1月13日
     *
     * 通知对象：泰国Network QC邮件组、产品邮件组
     * 发送时间：当日04:30AM
     *
     * 表头：
     *  1. 外协工号 ｜ 2. 外协身份证号｜ 3. 账号留存底片 ｜ 4. 打卡扫描照片 ｜ 5. 违规时间
     *
     * php app/cli.php attendance sendEmailForRegularInspectAgent
     */
    public function sendEmailForRegularInspectAgentAction()
    {
        //个人外协
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->join(HrStaffInfoModel::class, "b.staff_info_id = s.staff_info_id", 'b');
        $builder->where('s.created_at >= :start_time:', ['start_time' => date("Y-m-d 00:00:00", strtotime("-1 day"))]);
        $builder->andWhere('s.created_at < :end_time:', ['end_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.state = :state: and s.type = :type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE,
        ]);
        $builder->inWhere('b.hire_type', HrStaffInfoModel::$agentTypeTogether);
        $list = $builder->getQuery()->execute()->toArray();

        $lastDate = date('Y-m-d', strtotime('-1 day'));
        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            echo "无数据",PHP_EOL;
            return false;
        }
        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id in({staff_ids:array})",
            "bind" => [
                "staff_ids" => $staffIds,
            ],
            "columns" => "staff_info_id,name,identity,mobile,mobile_company,company_name_ef,sys_store_id",
        ])->toArray();
        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');

        $data = [];
        foreach ($list as $item) {
            $identity        = $staffInfoArr[$item['staff_info_id']]['identity'] ?? '';
            //$name            = $staffInfoArr[$item['staff_info_id']]['name'] ?? '';
            $main_account = StaffRepository::getMasterStaffIdBySubStaff($item['staff_info_id'])?:null;
            $data[] = [
                $item['staff_info_id'],
                $main_account,
                $identity,
                $item['os_face_image_source_path'],
                $item['os_submit_face_image_path'],
                date("Y-m-d H:i:s", strtotime($item['created_at'])),
                //$name,
            ];
        }

        if (RUNTIME == 'dev') {
            $t = $this->getTranslation("zh");
        } else {
            if (isCountry("TH")) {
                $t = $this->getTranslation("en");
            } else {
                $t = $this->getTranslation("en");
            }
        }

        $header = [
            $t->_('independent_contractor_id'),                //个人代理ID
            $t->_('main_account'),                              //主账号
            $t->_('att_staff_identity'),                       //身份证号
            $t->_('att_staff_account_image_url'),              //账号留存底片
            $t->_('att_punch_in_image'),                       //打卡扫描照片
            $t->_('att_created_at'),                           //违规时间
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data, genFileName('detected_agent_data_cycle'));

        //标题：{日期} 疑似作弊个人代理账号名单
        //正文：{日期} 因稽查到以下个人代理账号，有非本人操作记录，疑似有作弊行为。请核实作弊情况是否真实，若有系统误判，请及时与产品部门申诉。
        $title    = $t->_('att_agent_list_email_title', ['detected_at' => $lastDate]);
        $content  = $t->_('att_agent_list_email_content', ['detected_at' => $lastDate]);
        $content  .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here')) ;

        //获取发送邮件列表
        $model = new SettingEnvServer();
        $email = $model->getSetVal('agent_attendance_person_email');
        $emailList = !empty($email) ? explode(',', $email) : [];
        if (empty($emailList)) {
            $this->logger->write_log('sendEmailForRegularInspectAgentAction, 收件人不能为空:'. $emailList, 'notice');
        }
        $sendEmail = Mail::send($emailList, $title, $content);

        if ($sendEmail) {
            echo ' send success!';
            $this->logger->write_log("sendEmailForRegularInspectAgentAction success!", "notice");
            return true;
        } else {
            $this->logger->write_log('sendEmailForRegularInspectAgentAction, 发送邮件失败:'. $emailList, 'notice');
            return false;
        }
    }

    /**
     * @description 发送邮件
     * 内容
     *  标题：{日期} 疑似人脸重复账号名单
     *  正文：{日期}因稽查到以下员工账号，与当前在职员工人脸重复。请核实情况是否真实，若有系统误判，请及时与相关部门申诉。
     * 点击链接下载名单附件。
     *
     * 取值说明：
     *  {日期}：取前一日日期，格式：2024-04-13
     *
     * 通知对象：泰国HR邮件组
     * 发送时间：于第二日04:30AM
     *
     * 表头：
     *  1. 个人代理ID ｜ 2. 身份证号 ｜ 3. 提交照片 ｜ 4. 正式员工底片 ｜ 5. 正式员工工号 ｜ 6. 录入底片时间
     *
     * php app/cli.php attendance sendEmailForSaveAttachmentAgent
     */
    public function sendEmailForSaveAttachmentAgentAction()
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("*");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->where('s.created_at >= :start_time:', ['start_time' => date("Y-m-d 00:00:00", strtotime("-1 day"))]);
        $builder->andWhere('s.created_at < :end_time:', ['end_time' => date("Y-m-d 00:00:00")]);
        $builder->andWhere('s.state = :state: and s.type = :type:', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_COURIER_ATTACHMENT,
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        $lastDate = date('Y-m-d', strtotime('-1 day'));

        $staffIds = array_column($list, 'staff_info_id');
        if (empty($staffIds)) {
            echo "无数据",PHP_EOL;
            return false;
        }

        //匹配正式员工id
        $allStaffIds   = $staffIds;
        $matchStaffIds = array_column($list, 'match_staff_info_id');
        $matchStaffIds = array_diff($matchStaffIds, [null]);
        if ($matchStaffIds) {
            $allStaffIds = array_merge($staffIds, $matchStaffIds);
        }
        $allStaffIds = array_values(array_unique($allStaffIds));
        $staffInfo = HrStaffInfoModel::find([
            "staff_info_id in({staff_ids:array})",
            "bind" => [
                "staff_ids" => $allStaffIds,
            ],
            "columns" => "staff_info_id,name,identity,mobile,mobile_company,company_name_ef,sys_store_id, job_title",
        ])->toArray();
        $staffInfoArr = array_column($staffInfo, null, 'staff_info_id');
        //职位
        $jobTitles = HrJobTitleModel::find([
            'columns' => 'id, job_name',
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids' => array_column($staffInfo,'job_title')],
        ])->toArray();
        $jobTitles = empty($jobTitles) ? [] : array_column($jobTitles, 'job_name' ,'id');
        //大区片区等
        $storeIds = array_column($staffInfo, 'sys_store_id');
        $storeIds = array_diff($storeIds,['-1']);
        $storeIds = array_values(array_unique($storeIds));
        $storeInfo = [];
        if(!empty($storeIds)){
            $storeServer = new SysStoreServer($this->lang, $this->timezone);
            $storeInfo = $storeServer->batchStorePieceRegion($storeIds);
        }
        $data = [];
        foreach ($list as $item) {
            $staffId      = $item['staff_info_id'];
            $matchId      = $item['match_staff_info_id'];
            $staffStoreId = $staffInfoArr[$staffId]['sys_store_id'] ?? '';
            $matchStoreId = $staffInfoArr[$matchId]['sys_store_id'] ?? '';
            $identity     = $staffInfoArr[$staffId]['identity'] ?? '';
            $name         = $staffInfoArr[$staffId]['name'] ?? '';
            $main_account = StaffRepository::getMasterStaffIdBySubStaff($item['staff_info_id'])?:null;
            $data[] = [
                $item['staff_info_id'],
                $main_account,
                $name,
                $jobTitles[$staffInfoArr[$staffId]['job_title']] ?? '',
                $storeInfo[$staffStoreId]['region_name'] ?? '',
                $storeInfo[$staffStoreId]['piece_name'] ?? '',
                $storeInfo[$staffStoreId]['store_name'] ?? '',
                $identity,
                $item['os_submit_face_image_path'],
                $item['work_attendance_path'],
                $matchId,
                $staffInfoArr[$matchId]['name'] ?? '',
                $jobTitles[$staffInfoArr[$matchId]['job_title'] ?? ''] ?? '',
                $storeInfo[$matchStoreId]['region_name'] ?? '',
                $storeInfo[$matchStoreId]['piece_name'] ?? '',
                $storeInfo[$matchStoreId]['store_name'] ?? '',
                date("Y-m-d H:i:s", strtotime($item['created_at'])),
            ];
        }

        if (RUNTIME == 'dev') {
            $t = $this->getTranslation("en");
        } else {
            if (isCountry("TH")) {
                $t = $this->getTranslation("en");
            } else {
                $t = $this->getTranslation("en");
            }
        }

        $header = [
            $t->_('ID'),                           //个人代理ID
            $t->_('main_account'),                //主账号
            $t->_('independent_contractor_name'),//个人代理姓名
            $t->_('individual_contractor_job_title_name'),//个人代理职位
            $t->_('independent_contractor_region'),//个人代理大区
            $t->_('independent_contractor_piece'),//个人代理片区
            $t->_('independent_contractor_store'),//个人代理网点
            $t->_('att_staff_identity'),           //身份证号
            $t->_('att_attachment'),               //底片照片
            $t->_('att_staff_image_url'),          //正式员工底片
            $t->_('att_staff_no'),                 //正式员工工号
            $t->_('att_staff_name'),          //正式员工姓名
            $t->_('att_staff_job_title'),     //正式员工职位
            $t->_('att_staff_region'),     //正式员工大区
            $t->_('att_staff_piece'),     //正式员工片区
            $t->_('att_staff_store'),     //正式员工网点
            $t->_('save_attachment_time'),         //录入底片时间
        ];
        $file_name = (new OssHelper)->exportExcel($header, $data, genFileName('detected_agent_save_data'));

        //标题：{日期} 疑似人脸重复账号名单
        //正文：{日期}因稽查到以下员工账号，与当前在职员工人脸重复。请核实情况是否真实，若有系统误判，请及时与相关部门申诉。
        $title    = $t->_('att_agent_attachment_email_title', ['detected_at' => $lastDate]);
        $content  = $t->_('att_agent_attachment_email_content', ['detected_at' => $lastDate]);
        $content  .= sprintf("<a href='%s'>  %s  </a>", $file_name['data'], $t->_('click_here')) ;

        //获取发送邮件列表
        $model = new SettingEnvServer();
        $email = $model->getSetVal('agent_attendance_save_attachment_email');
        $emailList = !empty($email) ? explode(',', $email) : [];
        if (empty($emailList)) {
            $this->logger->write_log('sendEmailForSaveAttachmentAgentAction, 收件人不能为空:'. $emailList, 'notice');
        }
        $sendEmail = Mail::send($emailList, $title, $content);

        if ($sendEmail) {
            echo ' send success!';
            $this->logger->write_log("sendEmailForSaveAttachmentAgentAction success!", "notice");
            return true;
        } else {
            $this->logger->write_log('sendEmailForSaveAttachmentAgentAction, 发送邮件失败:'. $emailList, 'notice');
            return false;
        }
    }


    //是否支援 1，2 有这字段
    public function supportInfo($date, $staffIds){
        $data = HrStaffApplySupportStoreModel::find([
            'columns' => 'id, staff_info_id, sub_staff_info_id',
            'conditions' => '(staff_info_id in ({ids:array}) or sub_staff_info_id in ({ids:array})) and employment_begin_date <= :date_at: 
                            and employment_end_date >= :date_at: and status = 2',
            'bind' => ['ids' => $staffIds, 'date_at' => $date],
        ])->toArray();
        return $data;
    }


    //打卡坐标信息
    public function getAttendanceCoordinate($date, $staffIds){
        $data = StaffWorkAttendance::find([
            'columns' => 'id, staff_info_id,started_staff_lat,started_staff_lng',
            'conditions' => 'attendance_date = :date_at: and staff_info_id in ({ids:array})',
            'bind' => ['date_at' => $date, 'ids' => $staffIds],
        ])->toArray();
        $data = empty($data) ? [] : array_column($data, null, 'staff_info_id');
        return $data;
    }

    //轮休信息
    public function workdayInfo($date, $staffIds){
        $data = HrStaffWorkDayModel::find([
            'columns' => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at = :date_at:',
            'bind' => ['ids' => $staffIds, 'date_at' => $date],
        ])->toArray();
        $data = empty($data) ? [] : array_column($data, 'staff_info_id');
        return $data;
    }



    //客服人员下班打卡 通知 客服系统 注销电话用  只有 泰国马来菲律宾开这个任务 一直跑
    public function syncCustomerAction()
    {
        $envPrefix = 'prod';
        if (in_array(RUNTIME, ['dev','test'])) {
            $envPrefix = 'dev';
        }
        if (in_array(RUNTIME, ['tra'])) {
            $envPrefix = 'tra';
        }
        $redis = $this->getDI()->get('redisLib');
        while ($redis_data = $redis->rpop(AttendanceServer::SYNC_CUSTOMER_KEY)) {
            try {
                if (!empty($redis_data)) {
                    $this->logger->write_log("syncCustomer param {$redis_data} " , 'info');
                    $param = json_decode($redis_data, true);
                    //停职用户登录后
                    $client = new RestClient('chat_buddy');
                    $result = $client->execute(RestClient::METHOD_POST,"/forward/webhook/handle/flashhr/{$envPrefix}/callback",$param);
                    $this->logger->write_log("syncCustomer {$redis_data} result " . json_encode($result) , 'info');
                    //掉用失败
                    if ($result['code'] === 1 || is_null($result['code'])) {
                        $this->logger->write_log("syncCustomer 同步失败 接口返回 ", 'notice');
                        $redis->lpush(AttendanceServer::SYNC_CUSTOMER_KEY, $redis_data);
                    }

                } else {
                    sleep(1);
                }
            } catch (\Exception $e) {
                $this->logger->write_log(__CLASS__.__METHOD__.' exception:'.$e->getMessage());
                echo date('Y-m-d H:i:s').__METHOD__.$e->getMessage().PHP_EOL;
                $redis->lpush(AttendanceServer::SYNC_CUSTOMER_KEY, $redis_data);
                break;
            }
        }
        sleep(5);
    }

    /**
     * @description 异步同步主/子账号同步底片
     * php app/cli.php attendance asyncTransferAttachment
     */
    public function asyncTransferAttachmentAction()
    {
        $redis = $this->getDI()->get('redisLib');
        $server = new AttendanceServer($this->lang, $this->timezone);

        while ($redis_data = $redis->rpop(AttendanceServer::REDIS_ASYNC_TRANSFER_ATTENDANCE_ATTACHMENT)) {
            try {
                if (!empty($redis_data)) {
                    $this->logger->write_log(sprintf("[asyncTransferAttachmentAction task][%s]", $redis_data), 'info');

                    $data = json_decode($redis_data, true);
                    $server->consumeAttendanceAttachment($data);
                } else {
                    sleep(1);
                }
            } catch (\Exception $e) {
                $this->logger->write_log(__CLASS__.__METHOD__.' exception:'.$e->getMessage());
                echo date('Y-m-d H:i:s').__METHOD__.$e->getMessage().PHP_EOL;
                break;
            }
        }
        sleep(5);
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同
     *
     * php app/cli.php attendance sendOsStaffContractMessage
     */
    public function sendOsStaffContractMessageAction()
    {
        $redis  = $this->getDI()->get('redisLib');
        $server = new AttendanceServer($this->lang, $this->timezone);

        while ($redis_data = $redis->rpop(AttendanceServer::REDIS_SEND_OS_STAFF_CONTRACT_KEY)) {
            try {
                if (!empty($redis_data)) {
                    $this->logger->write_log(sprintf("[sendOsStaffContractMessageAction task][%s]", $redis_data),
                        'info');

                    $data = json_decode($redis_data, true);
                    $server->sendOsStaffContractMessage($data);
                } else {
                    sleep(1);
                }
            } catch (\Exception $e) {
                $this->logger->write_log(__CLASS__ . __METHOD__ . ' exception:' . $e->getMessage());
                echo date('Y-m-d H:i:s') . __METHOD__ . $e->getMessage() . PHP_EOL;
                break;
            }
        }
        sleep(5);
    }

    /**
     * 上班卡按钮记录消费
     * @return void
     */
    public function popPunchInAction()
    {
        $sever = new PunchInServer();
        $sever->popData();
    }

    /**
     * 清除31天前的记录
     * @return void
     */
    public function deletePunchInLogAction()
    {
        //保留30天
        $date = date('Y-m-d',strtotime("-31 day"));
        $sever = new PunchInServer();
        $sever->deleteLog($date);
    }

    public function testPunchInAction()
    {
        $sever = new PunchInServer();
        $sever->pushData(['staff_id'=>56780]);
    }


}