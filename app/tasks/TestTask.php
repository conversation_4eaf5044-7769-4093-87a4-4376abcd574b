<?php


use App\Country\Tools;
use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Mail;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Modules\Ph\Server\OvertimeExtendServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Server\AiServer;
use FlashExpress\bi\App\Server\ApprovalFinderServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\HrStaffContractServer;
use FlashExpress\bi\App\Server\HcStatisticServer;
use FlashExpress\bi\App\Server\JobtransferServer;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\Osm\OutsourcingOrderServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StatisticsAttendanceServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;
use FlashExpress\bi\App\Traits\ConditionsConfigTrait;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftMiddleDateModel;


use JsonRPC\Client as JsonRPC_Client;

class TestTask extends \BaseTask
{
    use ConditionsConfigTrait;

    public function aaaaAction()
    {
        $a = (new \FlashExpress\bi\App\Modules\Ph\Server\ProbationServer($this->lang,
                    $this->timezone))->getCountAuditStatus(119175);
        print_r($a);die;
    }
    public function t1Action()
    {
        $server = new \FlashExpress\bi\App\Controllers\AttendanceController();
        //数据查询记录轮数
        $data = FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel::find([
            'conditions' => "attendance_date = '2024-10-16' and type = 4 and live_score is null",
        ]);
        foreach ($data as $d) {
            $faceCompareScore = $server->live_check($d->os_submit_face_image_path);
            $d->live_score = $faceCompareScore;
            $d->save();
        }
    }

    /**
     * @Single
     * @return void
     */
    public function helloAction()
    {
        echo 'common';
    }


    public function aaAction(){
        $s = new OvertimeExtendServer('zh',1,1);

        $ss =  $s->attendance_num('2024-01-01',['37', '16'],['PH44011901']);
        dd($ss);



        $cache = $this->getDI()->get('redisLib');
        $cache->set('abc', 56780, 3600);
        dd(21);
        $data = curlJsonRpc($this->config->api->api_send_sms, curlJsonRpcStr('09233232323', 'สวัสดีค่ะ ติดต่อจากบริษัท แฟลช เอ็กซ์เพรส จำกัด นะคะ ทางเราได้รับข้อมูลการสมัครงาน แต่ไม่สามารถติดต่อท่านได้ กรุณาติดต่อกลับ เบอร์โทรศัพท์(0645871617) \n\nหรือ แอด line:\nhttps://line.me/ti/p/47Xvh6y8Sv?fbclid=IwZXh0bgNhZW0CMTAAAR1FBPlMjNihwmdZj5-MCmSvopBCeyv2Hf1JYM94YoD6r5K6jy8CeJKHvD8_aem_AVeZiI3qZbijJBTBIfAYwO8gGzqLWMY2RV1jTe5gCUxdlMsgHOIbw58im7_ydVI6qFGjGKzZ7udoknt7Dh10jqVu','backyard',1));

dd($data);
    }

    public function aabAction(){
        $model                  = \FlashExpress\bi\App\Models\backyard\AdvanceFuelModel::findFirst(16);
        $a = (new \FlashExpress\bi\App\Server\AdvanceFuelServer())->sendNoticePush($model->toArray(), '112233112233112233');
        print_r($a);
    }
    /**
     *
     * @return void
     */
    public function agentAlertAction()
    {

        $staff = HrStaffInfoModel::find(['hire_type = 13'])->toArray();
        $staff_info_ids = array_column($staff, 'staff_info_id');
        //读主库
        $needStaff = HrStaffContractModel::find([
            'conditions' => 'staff_id in ({staff_ids:array}) and contract_is_need=1 and contract_is_deleted=0 and contract_status in ({contract_status:array})',
            'bind'       => [
                'contract_status' => [
                    enums::CONTRACT_STATUS_SIGNATURE,
                    enums::CONTRACT_STATUS_UNSIGNED,
                    enums::CONTRACT_STATUS_REFUSE,
                ],
                'staff_ids'       => $staff_info_ids,
            ],
            'columns'    => 'staff_id',
            'group'      => 'staff_id',
        ])->toArray();
        echo 'num:'.count($needStaff).PHP_EOL;
        if (!empty($needStaff)) {
            foreach ($needStaff as $staff) {
                HrStaffContractServer::getInstance($this->lang,
                    $this->timezone)->syncContractSignToJava($staff['staff_id'],0);
            }
        }
    }

    public function sstAction(){

        $s = new \FlashExpress\bi\App\Server\AttendanceBusinessServer('zh','+08:00');
        $a = $s->getGeocoding('-6.13564064','106.79293636');
        dd($a);

        $result = [];
        $delay_msg_info = [2,3,4,5,6];
        $staff_id = '333';
        $redis_obj       = $this->getDI()->get('redisLib');

        $curr_time = date('Y-m-d H:i:s');
        $curr_date = substr($curr_time, 0, 10);

        if(!empty($delay_msg_info) ){
            if(count($result) == 1){
                $result = array_merge($result, [$delay_msg_info[0]]);
            }
            if(count($result) == 0){
                $delay_msg_cache_key = sprintf('3d3dd3s3333%s', $staff_id);
                $cache_delay_data    = $redis_obj->get($delay_msg_cache_key);
                $cache_time             = strtotime($curr_date.' 23:59:59') - strtotime($curr_time);
                if (empty($cache_delay_data)) {
                    //缓存里没有 代表没看过 则加到给到查看的结果集里
                    $result = $delay_msg_info;
                    //然后存到缓存 每次存一个
                    $redis_obj->set($delay_msg_cache_key, json_encode([$result[0]]), $cache_time);
                } else {
                    //有缓存 看是否有新的消息进来
                    $cache_delay_data = json_decode($cache_delay_data, true);
                    if ($new = array_diff($delay_msg_info, $cache_delay_data)) {
                        $result= array_values($new);
                        //有就把新的给到查看的结果集里
                        //然后把全量的再存到缓存
                        $redis_obj->set($delay_msg_cache_key, json_encode(array_merge($cache_delay_data,[$result[0]])), $cache_time);
                    }
                }
            }
        }
        dd($result);
    }


    public function addOffAction(){
        $a = [
            ['staff_info_id'=> '120329' , 'before'=>'2023-07-04','after'=>'2023-07-16'],

        ];

        $str = '';
        foreach ($a as $item) {
            $staff_info_id = $item['staff_info_id'];
            $date          = $item['before'];

            // 获取指定日期是星期几
            $dayOfWeek = date("N", strtotime($date));
            // 计算出指定日期所在周的周一和周日的日期
            $monday      = date("Y-m-d", strtotime("-" . ($dayOfWeek - 1) . " days", strtotime($date)));
            $sunday      = date("Y-m-d", strtotime("+" . (7 - $dayOfWeek) . " days", strtotime($date)));
            $sql = "staff_info_id = $staff_info_id and date_at >= '{$monday}' and date_at <= '{$sunday}'";

            $offInfo = HrStaffWorkDayModel::findFirst($sql);

            if($offInfo){
                $str .= "staff_info_id:".$staff_info_id.' '.$monday.' '.$sunday .' 已设置休息日:'.$offInfo->date_at .PHP_EOL;
                continue;
            }

            $date2 = $item['after'];
            // 获取指定日期是星期几
            $dayOfWeek2 = date("N", strtotime($date2));
            // 计算出指定日期所在周的周一和周日的日期
            $monday2      = date("Y-m-d", strtotime("-" . ($dayOfWeek2 - 1) . " days", strtotime($date2)));
            $sunday2      = date("Y-m-d", strtotime("+" . (7 - $dayOfWeek2) . " days", strtotime($date2)));
            $sql2 = "staff_info_id = $staff_info_id and date_at >= '{$monday2}' and date_at <= '{$sunday2}'";


            $issetOffInfo = HrStaffWorkDayModel::count($sql2);
            if($issetOffInfo == 0){
                $str .= $sql2 .' '.  $date2 .'当周 未设置休息日'.PHP_EOL;
                continue;
            }

            $sql3 = "staff_info_id = $staff_info_id and date_at = '$date2'";

            $offInfo2 = HrStaffWorkDayModel::findFirst($sql);
            if($offInfo2){
                $str .= $sql3 .' '.'已被设置为休息日'.PHP_EOL;
                continue;
            }

            $off = new HrStaffWorkDayModel();
            $off->create(['staff_info_id'=>$staff_info_id,'date_at'=>$date2,'month'=>substr($date2,0,7),'type'=>2,'remark'=>'email','operator'=>0,'staff_ph_id'=>0]);
            ;
        }
        echo $str;
        $this->getDI()->get('logger')->write_log(['addOffMY'=>$str], 'info');
    }


    public function attendance_rateAction($params){


        $s= new OvertimeExtendServer('en','+08:00');

        $res = $s->attendance_rate($params[0],explode(',',$params[1]),$params[2]);
        var_dump($res);die;
    }

    public function punchAction()
    {
        $params['order_id'] = 256825;
        $params['company_id'] = 11;
        $result = (new OutsourcingOrderServer($this->lang))->getOptionalStaffList($params);
        var_dump($result);die;

        ini_set('memory_limit', '-1');

        $db = $this->getDI()->get("db");

        $sql = "select  staff_info_id,max(`attendance_at`) as attendance_at  from fix_punch_out_staff  group by staff_info_id ";
        $data = $db->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $i = 0;
        $error  = [];
        foreach ($data as $datum) {
            $staff_info_id = $datum['staff_info_id'];

            //支援账号 查一下志愿表
            if($staff_info_id >= 3000000){
                $supportInfo = \FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");
                if($supportInfo){
                    $master_staff_info_id = $supportInfo->staff_info_id;
                    $attendanceData = StaffWorkAttendance::findFirst("attendance_date = '2022-11-19' and staff_info_id = '$master_staff_info_id'");
                    if(!empty($attendanceData)){
                        if($attendanceData->end_state != 1){
                            $i++;
                            $attendanceData->end_state  = 1;
                            $attendanceData->end_at  = $datum['attendance_at'];
                            $attendanceData->save();
                        }
                    }else{
                       $error[] = $master_staff_info_id;
                    }
                }
            }

            $attendanceData = StaffWorkAttendance::findFirst("attendance_date = '2022-11-19' and staff_info_id = '$staff_info_id'");
            if(!empty($attendanceData)){
                if($attendanceData->end_state != 1){
                    $i++;
                    $attendanceData->end_state  = 1;
                    $attendanceData->end_at  = $datum['attendance_at'];
                    $attendanceData->save();
                }
            }else{
                $error[] = $staff_info_id;
            }
        }

        $this->getDI()->get('logger')->write_log(['action'=>'fix attendance  data','error_staff_info_id'=>$error,'success'=>$i], 'notice');
        echo $i;
    }
    public function fix0Action()
    {
        ini_set('memory_limit', '-1');

        $sql = "select  *  from fix_punch_out_staff ";
        $data = $this->getDI()->get('db')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $db = $this->getDI()->get("db");
        foreach($data as $datum){
            $s_t  = gmdate('Y-m-d H:i:s',strtotime($datum['date_at']));
            $sql =  "update  fix_punch_out_staff  set attendance_at = '$s_t' where id = ".$datum['id'];
            $db->execute($sql);
        }

        die;
    }



    public function fix1Action()
    {
        ini_set('memory_limit', '-1');

        $sql = "select  *  from fix_attendance_log_file ";
        $data = $this->getDI()->get('db')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        foreach($data as $v){
            $query = parse_url($v['request_uri'])['query'];
            $q = [];
            parse_str($query,$q);
            $time = date('Y-m-d H:i:s',strtotime($v['timestamp']));
            $clientid = $q['clientid'];
            $t['time'] = $time;
            $t['clientid'] = $clientid;
            $r[] = $t;
        }

        $s = array_chunk($r,500);
        foreach ($s as $v){
            $insertRst = (new BaseRepository())->batch_insert('fix_attendance_data', $v);
        }
        die;
    }


    public function fix2Action()
    {
        ini_set('memory_limit', '-1');

        $db = $this->getDI()->get("db");
        $sql = "select  clientid,min(`time`) as start_at  from fix_attendance_data group by clientid ";
        $data = $db->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $i = 0;
        foreach ($data as $datum) {

            $clientid = $datum['clientid'];
            $sql = "select * from staff_work_attendance where `attendance_date` = '2022-10-13' and started_clientid = '$clientid'";
            $data_1 = $this->getDI()->get('db')->query($sql);
            $data_1 = $data_1->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $this->getDI()->get('logger')->write_log('fix attendance init data :' . json_encode($data_1), 'info');

            if(empty($data_1)){
               continue;
            }
            $t = current($data_1);
            if(!empty($t['shift_start'])){
                $a = date('Y-m-d H:i:s',strtotime($t['started_at'])+7*3600);
                $b = date('Y-m-d H:i:s',strtotime('2022-10-13 '.$t['shift_start']));

                if($a < $b){
                    continue;
                }
            }
            $s_t  = gmdate('Y-m-d H:i:s',strtotime($datum['start_at']));

            $sql =  "update  staff_work_attendance  set started_at = '$s_t' where `attendance_date` = '2022-10-13' and started_clientid = '$clientid'";
            $db->execute($sql);
            $i++;
        }
        $this->getDI()->get('logger')->write_log('fix attendance  data num :' . $i, 'notice');
        echo $i;
    }

    public function fix3Action()
    {
        ini_set('memory_limit', '-1');
        $db = $this->getDI()->get("db");
        $ot = "SELECT `staff_id` , `start_time`  FROM `hr_overtime` WHERE `date_at`  = '2022-10-13'  and state = 2";
        $ot_data = $db->query($ot)->fetchAll(\Phalcon\Db::FETCH_ASSOC);;
        $ot_data = array_column($ot_data,'start_time','staff_id');

        $db = $this->getDI()->get("db");
        $sql = "select  staff_info_id,min(`time`) as ne_time  from fix_attendance_data_staff_v2 where  staff_info_id  > 0 group by staff_info_id ";
        $data = $db->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $i = 0;
        foreach ($data as $datum) {

            $staff_info_id = $datum['staff_info_id'];
            $sql = "select * from staff_work_attendance where `attendance_date` = '2022-10-13' and staff_info_id = '$staff_info_id'";
            $data_1 = $this->getDI()->get('db')->query($sql);
            $data_1 = $data_1->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $this->getDI()->get('logger')->write_log('fix attendance init data :' . json_encode($data_1), 'info');

            if(empty($data_1)){
                continue;
            }
            $t = current($data_1);
            if(!empty($t['shift_start'])){
                $a = date('Y-m-d H:i:s',strtotime($t['started_at'])+7*3600);
                $b = $ot_data[$staff_info_id] ?? date('Y-m-d H:i:s', strtotime('2022-10-13 '.$t['shift_start']));
                if($a < $b){
                    continue;
                }
            }
            $s_t  = gmdate('Y-m-d H:i:s',strtotime($datum['ne_time']));

            $sql =  "update  staff_work_attendance  set started_at = '$s_t' where `attendance_date` = '2022-10-13' and staff_info_id = '$staff_info_id'";
            $this->getDI()->get('logger')->write_log('fix attendance init data sql :' . $sql, 'info');
            $db->execute($sql);
            $i++;
        }
        $this->getDI()->get('logger')->write_log('fix3Action attendance  data num :' . $i, 'notice');
        echo $i;
    }

    public function abcAction(){
       $a =  <<<EOF
       [{"staff_info_ids_str":122312,"staff_users":[{"id":122312}],"message_title":"【Absent from work without reasonable excuses or prior approval from Superior】Muhamad Azizul bin Razali's warning-has received 2 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1779' width='100%' height='85%'></iframe>","add_userid":"123651","top":1,"category":7,"warning_id":"1779"},
{"staff_info_ids_str":122312,"staff_users":[{"id":122312}],"message_title":"【Absent from work without reasonable excuses or prior approval from Superior】Muhamad Azizul bin Razali's warning-has received 1 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1778' width='100%' height='85%'></iframe>","add_userid":"123651","top":1,"category":7,"warning_id":"1778"},
{"staff_info_ids_str":"124445","staff_users":[{"id":"124445"}],"message_title":"【Absent from work without reasonable excuses or prior approval from Superior】Yusri bin Wahab 's warning-has received 1 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1775' width='100%' height='85%'></iframe>","add_userid":"87148","top":1,"category":7,"warning_id":"1775"},
{"staff_info_ids_str":"122615","staff_users":[{"id":"122615"}],"message_title":"【Violation of Company's policies and procedures】Mohamad Zulhanif bin Omar's warning-has received 1 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1776' width='100%' height='85%'></iframe>","add_userid":"87148","top":1,"category":7,"warning_id":"1776"},
{"staff_info_ids_str":"120065","staff_users":[{"id":"120065"}],"message_title":"【Violation of Company's policies and procedures】Anuar bin Ayob's warning-has received 2 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1777' width='100%' height='85%'></iframe>","add_userid":"87148","top":1,"category":7,"warning_id":"1777"},
{"staff_info_ids_str":"119873","staff_users":[{"id":"119873"}],"message_title":"【Violation of Company's policies and procedures】Azuan Ariff bin Azmi's warning-has received 2 warnings","message_content":"<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='https://hcm-api.flashexpress.my//Datatmp/warning_message?warning_id=1773' width='100%' height='85%'></iframe>","add_userid":"87148","top":1,"category":7,"warning_id":"1773"}]
EOF;
        $db = $this->getDI()->get('db');
       $staff = json_decode($a,true);
       return;
        foreach ($staff as $param) {
            $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-result:' . json_encode($res), 'info');

            if ($res && $res['result']['code'] == 1) {
                $kitId    = $res['result']['data'][0];

               $r =  $db->updateAsDict("message_warning", [
                    "kit_id" => $kitId,
                ],
                    "id = " . $param['warning_id']
                );

                var_dump($kitId,$param['warning_id'],$r);
            }
        }
    }

    public function bcd(object $a){
        echo 234;
    }


    public function work_dayAction()
    {

        $approvers = AuditApprovalModel::find([

            'columns'    => "approval_id",
            'limit'    => "10",
        ]);

        foreach ($approvers as $item){
            $staff_info_id[] = $item->approval_id;
        }
        echo count($staff_info_id);die;
        $day = "'" . implode("','", $day) . "'";
        $sql = "select  a.`staff_info_id` ,a.`leave_start_time`  from  staff_audit  as a  left join hr_staff_info as i on a.staff_info_id = i.staff_info_id where  i.`week_working_day`  = 6 and  a.leave_type =15 and a.leave_start_time in ($day)";
        $data = $this->getDI()->get('db')->query($sql);

        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $work_day_model = new HrStaffWorkDayModel();

        $i = 0;
        if ($data) {
            foreach ($data as $da) {
                $work_day_model_clone = clone $work_day_model;

                $date_at = date('Y-m-d', strtotime($da['leave_start_time']));
                $info = $work_day_model::findFirst([
                                                       'conditions' => "staff_info_id = :staff_info_id: and date_at = :date_at:",
                                                       'bind' => [
                                                           'staff_info_id' => $da['staff_info_id'],
                                                           'date_at' => $date_at,
                                                       ],
                                                   ]);
                if (empty($info)) {
                    //轮休表
                    $i++;
                    $w_d_item = [];
                    $w_d_item['staff_info_id'] = $da['staff_info_id'];
                    $w_d_item['month'] = date('Y-m', strtotime($date_at));
                    $w_d_item['date_at'] = $date_at;
                    $w_d_item['operator'] = 10000;
                    $work_day_model_clone->create($w_d_item);
                }
            }
        }
        echo $i;
    }

    public function msgAction($param){
    }

    public function cccAction(){
        try {
            $m = new \FlashExpress\bi\App\Server\StaffServer('en');
            $p = '{"staff_id":1212122,"formal":"2s","sys_store_id":"PH12010C01","position_category":["1","2"],"state":"1","leave_date":"","staff_car_type":"Tricycle","hire_date":"2022-1-15","name":"123321","mobile":"12345678918","company_name_ef":"aaaa","staff_car_no":"121212"}';
            $aa = json_decode($p,true);

            $re = $m->createCooperator($aa);
        }catch (Exception $e){
            echo $e->getMessage();
        }

    }


    public function  test_to_mailAction(){
    	//测试发送邮件
	    $sendEmail = \FlashExpress\bi\App\library\Mail::send(['<EMAIL>'], 'test title', 'test content');


	    print_r($sendEmail);exit();
    }
    function mainAction()
    {
       //2 加密数据
       $body = [
            'EDC' => [
                'MID' => '00000000',
                'TID' => '00000000',
                "EdcTransID" => "*********",
            ],
            'Card' => [
                'MaxCardNo' => '2190103291',
                'Customer' => '5100000672',
            ],
        ];
        $rep = $this->getDI()->get('hprose')->AES256GCM_Encrypt_API(json_encode(($body)));
        $rep = json_decode($rep, true);
        $repAES256GCM_Encrypt_API = $this->soapXMLToArray('AES256GCM_Encrypt_API',$rep['data'])?? [];
        print_r($repAES256GCM_Encrypt_API);
        echo PHP_EOL;
        // //3 访问回传数据
        $body = $repAES256GCM_Encrypt_API;
        $rep = $this->getDI()->get('hprose')->Check_Balance_API("",$body);
        $rep = json_decode($rep, true);
        print_r($rep);

    }

    public function clsAction()
    {
        $personinfoServer =
        new \FlashExpress\bi\App\Server\PersoninfoServer($this->lang, $this->timezone);

        var_dump(

            $personinfoServer->on_job_need([
                'staff_id' => 300467,
            ])
        );

    }

    function aAction()
    {
        $rep = $this->getDI()->get('hprose')->Generate_JWT_Token();
        $rep = json_decode($rep, true);
        print_r($this->soapXMLToArray('Generate_JWT_Token',$rep['data']));
    }
    function bAction()
    {
        $oil_numbers = ['2190103289','2190103291'];
        foreach ($oil_numbers as $key => $val) {
            $this->getPTOliCardBalance($val);
        }
        echo 'all ok => '.PHP_EOL;
    }
    function cAction()
    {
         //1 获取TOKEN
         $rep = $this->getDI()->get('hprose')->Generate_JWT_Token();
         $rep = json_decode($rep, true);
         $head = $this->soapXMLToArray('Generate_JWT_Token',$rep['data']);
         print_r($head);
         echo PHP_EOL;
         //2 加密数据
         $body = [
             'EDC' => [
                 'MID' => '00000000',
                 'TID' => '00000000',
                 "EdcTransID" => "*********",
             ],
             'Card' => [
                 'MaxCardNo' => '2190103291',
                 'Customer' => '5100000672',
             ],

         ];
         $rep = $this->getDI()->get('hprose')->AES256GCM_Encrypt_API(json_encode(($body)));
         $rep = json_decode($rep, true);
         $repAES256GCM_Encrypt_API = $this->soapXMLToArray('AES256GCM_Encrypt_API',$rep['data'])?? [];
         print_r($repAES256GCM_Encrypt_API);
         echo PHP_EOL;
         // //3 访问回传数据
         $body = $repAES256GCM_Encrypt_API;
         $rep = $this->getDI()->get('hprose')->Card_Statement_API($head,$body);
         $rep = json_decode($rep, true);
         $soapCard_Statement_API = $this->soapXMLToArray('Card_Statement_API',$rep['data']);
         print_r($soapCard_Statement_API);
         echo PHP_EOL;
         //4 解密回传数据
         $rep = $this->getDI()->get('hprose')->AES256GCM_Decrypt_API($soapCard_Statement_API);
         $rep = json_decode($rep, true);
         $soapAES256GCM_Decrypt_API = $this->soapXMLToArray('AES256GCM_Decrypt_API',$rep['data']);
         print_r($soapAES256GCM_Decrypt_API);
         echo PHP_EOL;
         $this->getDI()->get('logger')->write_log(json_decode($soapAES256GCM_Decrypt_API,true),'info');
         $soapAES256GCM_Decrypt_API = json_decode($soapAES256GCM_Decrypt_API,true);
    }
    public function getPTOliCardBalance($oil_number = '2190103289'){
        //1 获取TOKEN
        $rep = $this->getDI()->get('hprose')->Generate_JWT_Token();
        $rep = json_decode($rep, true);
        $head = $this->soapXMLToArray('Generate_JWT_Token',$rep['data']);
        print_r($head);
        echo PHP_EOL;
        //2 加密数据
        $body = [
            'EDC' => [
                'MID' => '00000000',
                'TID' => '00000000',
                "EdcTransID" => "*********",
            ],
            'Card' => [
                'MaxCardNo' => $oil_number,
                'Customer' => '5100000672',
            ],

        ];
        $rep = $this->getDI()->get('hprose')->AES256GCM_Encrypt_API(json_encode(($body)));
        $rep = json_decode($rep, true);
        $repAES256GCM_Encrypt_API = $this->soapXMLToArray('AES256GCM_Encrypt_API',$rep['data'])?? [];
        print_r($repAES256GCM_Encrypt_API);
        echo PHP_EOL;
        // //3 访问回传数据
        $body = $repAES256GCM_Encrypt_API;
        $rep = $this->getDI()->get('hprose')->Check_Balance_API($head,$body);
        $rep = json_decode($rep, true);
        $soapCheck_Balance_API = $this->soapXMLToArray('Check_Balance_API',$rep['data']);
        print_r($soapCheck_Balance_API);
        echo PHP_EOL;
        //4 解密回传数据
        $rep = $this->getDI()->get('hprose')->AES256GCM_Decrypt_API($soapCheck_Balance_API);
        $rep = json_decode($rep, true);
        $soapAES256GCM_Decrypt_API = $this->soapXMLToArray('AES256GCM_Decrypt_API',$rep['data']);
        print_r($soapAES256GCM_Decrypt_API);
        echo PHP_EOL;
        $this->getDI()->get('logger')->write_log(json_decode($soapAES256GCM_Decrypt_API,true),'info');
        $soapAES256GCM_Decrypt_API = json_decode($soapAES256GCM_Decrypt_API,true);
        // echo PHP_EOL;
        if($soapAES256GCM_Decrypt_API['Status']['RespCode'] == '00'){
            $this->getDI()->get('db')->updateAsDict(
                'vehicle_info',
                [
                    'balance' => bcmul($soapAES256GCM_Decrypt_API['Data']['Balance'],100),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => 'oil_number = ?',
                    'bind' => [$soapAES256GCM_Decrypt_API['Data']['MaxCardNo']],
                ]
            );
            echo $oil_number. ' => ok'.PHP_EOL;
        }
    }
    function tAction()
    {
        //{"EDC":{"MID":"00000000","TID":"00000000","EdcTransID":"*********"},"Card":{"MaxCardNo":"2190103289","Customer":"5100000672"}}
        $body = [
            'EDC' => [
                'MID' => '00000000',
                'TID' => '00000000',
                "EdcTransID" => "*********",
            ],
            'Card' => [
                'MaxCardNo' => '2190103289',
                'Customer' => '5100000672',
            ],

        ];
        $rep = $this->getDI()->get('hprose')->AES256GCM_Encrypt_API(json_encode(($body)));
        $rep = json_decode($rep, true);
        $repAES256GCM_Encrypt_API = $this->soapXMLToArray('AES256GCM_Encrypt_API',$rep['data'])?? [];
        print_r($repAES256GCM_Encrypt_API);
        echo PHP_EOL;
    }


    public function testSoapXmlToAction()
    {
        $funcTion = 'Generate_JWT_Token';
        $xmlStr = '<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><Generate_JWT_TokenResponse xmlns="http://tempuri.org/"><Generate_JWT_TokenResult>eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJiYmNkYTIzYi1hZGE3LTQ4Y2ItYThiMy03MmEzYzM1MzhlMjAiLCJ1c3IiOiJkZDIyZTFmMS0zOTkzLTQzNmYtOGJhYi1kYTA0ZjU0NTk1NTEiLCJyb2xlIjoiQWRtaW4iLCJpYXQiOiIyMDE5MTAyMjIxNTQ1NDYxNCIsImV4cCI6IjIwMTkxMDIyMjE1OTU0NjE0In0.l2xqH7ecG0hEMlKEUUKtJMT5xXUZ_gtjOXRjb-Hpk0s8GKS9yDzuq2zAzd5-uuuMcwy1yoyD4SvwXF411QhzmQ</Generate_JWT_TokenResult></Generate_JWT_TokenResponse></soap:Body></soap:Envelope>';
        // 将形如<soapenv:Envelope 替换为：<soapenvEnvelope
        $xmlStringnew = preg_replace('|<([/\w]+)(:)|m','<$1',$xmlStr);
        //将形如： xsi:type 替换为xsitype
        $xmlString = preg_replace('|(\w+)(:)(\w+=\")|m','$1$3',$xmlStringnew);
        libxml_disable_entity_loader(true);
        $array = json_decode(json_encode(simplexml_load_string($xmlString, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        print_r($array['soapBody'][$funcTion.'Response'][$funcTion.'Result']);
    }

    public function  idsAction()
    {
        $typeStr = getIdsStr(enums::$audit_type);
        echo $typeStr;
    }

    public function jsonAction()
    {
        $str = '
        ';
        // $question_all = json_decode($str ?? [],true);
        // $phpPath = APP_PATH.'/messages/aa.php';
        // $phpContent = '<?php $question_all = ' . var_export($question_all,true) . ';';
        // file_put_contents($phpPath, $phpContent);
        // $path = APP_PATH . "/messages/aa.php";
        // require $path;
        // for ($i=0; $i < 10000; $i++) {
        //     $question_list = $question_all['question'] ?? [];
        //     echo count($question_list) ?? 0;
        // }

        $question_all = json_decode($str ?? [],true);
        $stime = microtime(true);
        $question_all = serialize($question_all);
        for ($i=0; $i < 100000; $i++) {
            count(unserialize($question_all));
        }
        $etime = microtime(true);
        echo "serialize  :", ($etime - $stime) ,'<br/>';

        $stime = microtime(true);
        for ($i=0; $i < 100000; $i++) {
            $question_all = json_decode($str ?? [],true);
            count($question_all);
        }
        $etime = microtime(true);
        echo "serialize  :", ($etime - $stime) ,'<br/>';
    }



    public function feishuAction()
    {
        $body = [
            'EDC' => [
                'MID' => '00000000',
                'TID' => '00000000',
                "EdcTransID" => "*********",
            ],
            'Card' => [
                'MaxCardNo' => '2190103291',
                'Customer' => '5100000672',
            ],
        ];
//        $this->getDI()->get('logger')->write_log($body,'error');
        $this->getDI()->get('logger')->sendNotic($body,'error');
    }

    //批量补卡操作 指定工号 日期区间
    //https://l8bx01gcjr.feishu.cn/docs/doccnroJb6fO4PvQW9zgJbdDwve
    public function attendanceAction($param){
        try{
            $staff_id = $param[0];
            $start_date = $param[1];
            $end_date= $param[2];

            $step_date = $start_date;
            $model = new StaffWorkAttendance();
            $staff_model = new StaffRepository();
            $shift_data = $staff_model->get_staff_shift($staff_id);
            if(empty($shift_data))
                die('没有班次信息'.$staff_id);
            //员工信息
            $staff_info = $staff_model->getStaffPosition($staff_id);
            if(empty($staff_info))
                die('没有员工信息'.$staff_id);



            //新增逻辑 获取 员工 对应区间有没有 off或者 ph 有的话不给补
            $off_sql = "select stat_date,OFF,PH from attendance_data_v2 where staff_info_id = {$staff_id} and stat_date between '{$start_date}' and '{$end_date}' and (OFF = 10 or PH = 10)";
            $off_data = $this->getDI()->get('db_rby')->query($off_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $off_date = array();
            if(!empty($off_data)){
                $off_date = array_column($off_data,'stat_date');
            }

            $add_hour = $this->getDI()['config']['application']['add_hour'];

            while ($step_date <= $end_date){
                //如果有 off 或者 ph  就不给补了
                if(!empty($off_date) && in_array($step_date,$off_date)){
                    $step_date = date('Y-m-d',strtotime("{$step_date} +1 day"));
                    continue;
                }


                //检查考勤数据
                $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$step_date}'");
                if(!empty($att_info) && !empty($att_info->started_at) && !empty($att_info->end_at)){
                    $step_date = date('Y-m-d',strtotime("{$step_date} +1 day"));
                    continue;
                }

                //班次
                $shift_start = $shift_data['start'];
                $shift_end = $shift_data['end'];
                //如果补卡日期 小于 班次生效日期  取旧班次
                if(!empty($shift_data['effective_date']) && $shift_data['effective_date'] > $step_date){
                    $shift_start = empty($shift_data['last_start']) ? null : $shift_data['last_start'];
                    $shift_end = empty($shift_data['last_end']) ? null : $shift_data['last_end'];
                }
                if(!empty($att_info)){
                    if(empty($att_info->started_at)){
                        $start_tmp = strtotime("{$step_date} {$shift_start}") - $add_hour * 3600;
                        $att_info->started_at = date("Y-m-d H:i:s" ,$start_tmp);
                        $att_info->started_state = 5;
                        $att_info->started_remark = 'by_tool_task';
                        $att_info->updated_at = gmdate('Y-m-d H:i:s');
                        $att_info->update();
                    }

                    if(empty($att_info->end_at)){
                        $end_tmp = strtotime("{$step_date} {$shift_end}") - $add_hour * 3600;
                        if($shift_start > $shift_end)
                            $end_tmp = $end_tmp + 24 * 3600;
                        $att_info->end_at = date('Y-m-d H:i:s',$end_tmp);
                        $att_info->end_state = 5;
                        $att_info->end_remark = 'by_tool_task';
                        $att_info->updated_at = gmdate('Y-m-d H:i:s');
                        $att_info->update();
                    }

                }else{
                    $insert = array();

                    //组装数据
                    $insert['staff_info_id'] = $staff_id;
                    $insert['organization_type'] = $staff_info['organization_type'];
                    $insert['organization_id'] = $staff_info['organization_type'] == 1 ? $staff_info['sys_store_id'] : $staff_info['sys_department_id'];
                    $insert['attendance_date'] = $step_date;
                    $insert['shift_start'] = $shift_start;
                    $insert['shift_end'] = $shift_end;
                    $start_tmp = strtotime("{$step_date} {$shift_start}") - $add_hour * 3600;
                    $end_tmp = strtotime("{$step_date} {$shift_end}") - $add_hour * 3600;
                    if($start_tmp > $end_tmp)
                        $end_tmp = $end_tmp + 24 * 3600;

                    $insert['started_at'] = date('Y-m-d H:i:s',$start_tmp);
                    $insert['started_state'] = 5;
                    $insert['started_remark'] = 'by_tool_task';
                    $insert['end_at'] = date('Y-m-d H:i:s',$end_tmp);
                    $insert['end_state'] = 5;
                    $insert['end_remark'] = 'by_tool_task';
                    $clone_m = clone $model;
                    $clone_m->create($insert);
                }
                $step_date = date('Y-m-d',strtotime("{$step_date} +1 day"));
            }
            $this->logger->write_log(" 考勤 补数据 {$staff_id} {$start_date} {$end_date} 完成",'info');
        }catch (\Exception $e){
            echo $e->getMessage();
        }

    }

    public function logAction()
    {
        var_dump(1);
        $this->getDI()->get('logger')->write_log('测试 logger 国家编码 ');
        var_dump(2);
    }


    public function test_wmsAction(){
        $url = env('api_wms_order');
        $param['xxx'] = 'xxxx';
        $param = '{"mchId":"phceshi","nonceStr":1626932324,"lang":"zh-CN","warehouseId":"17","orderSn":"as60f8f75699e88","nodeSn":"222","status":"1","type":"1","consigneeName":"นางสาว ยุวดี แซ่เบ๊","consigneePhone":"38967450784","postalCode":"10310","consigneeAddress":"อาคารฟอรั่มทาวเวอร์ ชั้น 36 ห้วยขวาง ห้วยขวาง กทม","goodsStatus":"normal","remark":"Header Office;Hdhjdjdhdjjdjhdjdjd","channelSource":"backyard","province":"","city":"","district":""}';

        $good = '[{"i":1,"barCode":"FEX00167","goodsName":"","specification":"","num":"1","price":"","remark":"[17573-นางสาว ยุวดี แซ่เบ๊-General Manager Assistant]"}]';
//        var_dump(json_decode($good));exit;
        $param = json_decode($param,true);
        $good = json_decode($good,true);
        $param['nonceStr'] = time();
        $param['goods'] = $good;

        $resPost = httpPostFun($url, $param, null, env('wms_pwd'));

        echo $url;
        var_dump($resPost);



    }

    public function audit_attAction($param){
        try{
            if(empty($param[0]) || empty(intval($param[0])))
                die('少id');
            $id = intval($param[0]);

            $audit_info = \FlashExpress\bi\App\Models\backyard\StaffAuditModel::findFirst($id);

            if(empty($audit_info))
                die('没找到申请');

            if($audit_info->audit_type != 1)
                die('不是补卡申请');

            //新需求 回写 班次到打卡表 徐华伟
            $staff_model = new StaffRepository();
            $shift_data  = $staff_model->get_staff_shift($audit_info->staff_info_id);
            $shift_start = $shift_end = null;
            $shift_id = 0;

            if (!empty($shift_data)) {
                $shift_start = $shift_data['start'];
                $shift_end   = $shift_data['end'];
                $shift_id    = $shift_data['shift_id'];

                //如果补卡日期 小于 班次生效日期  取旧班次
                if (!empty($shift_data['effective_date']) && $shift_data['effective_date'] > $audit_info->attendance_date) {
                    $shift_start = empty($shift_data['last_start']) ? null : $shift_data['last_start'];
                    $shift_end   = empty($shift_data['last_end']) ? null : $shift_data['last_end'];
                    $shift_id    = empty($shift_data['last_shift_id']) ? 0 : $shift_data['last_shift_id'];

                }
            }
            $updateData = [
                'attendance_type'   => $audit_info->attendance_type,
                'attendance_date'   => $audit_info->attendance_date,
                'reissue_card_date' => $audit_info->reissue_card_date,
                'staff_info_id'     => $audit_info->staff_info_id,
                'shift_start'       => $shift_start,
                'shift_end'         => $shift_end,
                'shift_id'          => $shift_id,

            ];
            $audit_re = new \FlashExpress\bi\App\Repository\AuditRepository($this->lang);
            $flag = $audit_re->editWorkAttendance($updateData);

            $this->logger->write_log(" 考勤 补卡修数据 {$audit_info->staff_info_id} {$audit_info->attendance_date} {$audit_info->reissue_card_date} ".json_encode($flag),'info');
        }catch (\Exception $e){
            echo "考勤 补卡修数据 {$param[0]} ". $e->getMessage();
            exit;
        }

    }

    public function getuuidAction()
    {
        if (class_exists('Ramsey\Uuid\Provider\Node\RandomNodeProvider')) {
            $nodeProvider = new RandomNodeProvider();
            $newFlowId    = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1,16000))->toString();
        } else {
            $newFlowId    = (new WorkflowServer($this->lang, $this->timezone))->getRandomId();
        }
        echo $newFlowId;
    }

    public function requestAction()
    {
        //(new \FlashExpress\bi\App\Server\AdjustRoleServer($this->lang, $this->timezone))->checkBranchCashier('TH01010101');

        $ret = (new \FlashExpress\bi\App\Server\AdjustRoleServer($this->lang, $this->timezone))->setStaffRoles(23774, 20508, "0,1");
        var_dump($ret['result']);
    }


    public function new_attendanceAction($param){
        try{
            $date = $param[0];

            $staff_att = StaffWorkAttendance::find("attendance_date <= '{$date}' and (started_at is null or end_at is not null)")->toArray();
            if(empty($staff_att))
                die('没找到 符合条件的数据');

            $staff_ids = array_column($staff_att,'staff_info_id');
            $this->logger->write_log(" 考勤 补数据 名单 ".json_encode($staff_ids),'info');
            foreach ($staff_ids as $staff_id){
                $staff_model = new StaffRepository();
                $shift_data = $staff_model->get_staff_shift($staff_id);
                if(empty($shift_data)){
                    echo '没有班次信息'.$staff_id;
                    continue;

                }
                //员工信息
                $staff_info = $staff_model->getStaffPosition($staff_id);
                if(empty($staff_info)){
                    echo '没有员工信息'.$staff_id;
                    continue;
                }

                $add_hour = $this->getDI()['config']['application']['add_hour'];

                //检查考勤数据
                $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");

                //班次
                $shift_start = $shift_data['start'];
                $shift_end = $shift_data['end'];

                if(!empty($att_info)){
                    if(empty($att_info->started_at)){
                        $start_tmp = strtotime("{$date} {$shift_start}") - $add_hour * 3600;
                        $att_info->started_at = date("Y-m-d H:i:s" ,$start_tmp);
                        $att_info->started_state = 5;
                        $att_info->started_remark = 'by_tool_task';
                        $att_info->updated_at = gmdate('Y-m-d H:i:s');
                        $att_info->update();
                    }

                    if(empty($att_info->end_at)){
                        $end_tmp = strtotime("{$date} {$shift_end}") - $add_hour * 3600;
                        if($shift_start > $shift_end)
                            $end_tmp = $end_tmp + 24 * 3600;
                        $att_info->end_at = date('Y-m-d H:i:s',$end_tmp);
                        $att_info->end_state = 5;
                        $att_info->end_remark = 'by_tool_task';
                        $att_info->updated_at = gmdate('Y-m-d H:i:s');
                        $att_info->update();
                    }

                }
                $this->logger->write_log(" 考勤 补数据 {$staff_id} 完成",'info');
            }

        }catch (\Exception $e){
            echo $e->getMessage();
        }
    }

    public function xxxAction()
    {
        $mode = (new \FlashExpress\bi\App\Server\AiServer($this->lang, $this->timezone));

        $aa = $mode->aiImageIdenfication('https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1630722868-a2400b913bec484986e542ccfe260f77.jpg');
var_dump($aa);
        //$mode->genToken('thai-driving-licence-ocr');
    }



    public function testAction()
    {
        (new \FlashExpress\bi\App\Server\PayrollFileServer($this->lang, $this->timezone))->getIcPeriodInfoFromFbi(['staff_info_id' => 2000040, 'period_id' => 55]);

//        $params     = [];
//        $params['lang'] = $this->lang;
//        $stock_data = (new \FlashExpress\bi\App\Repository\BaseRepository())->getDataFromWms(env("api_wms_goodsStock"), $params);
//        var_dump($stock_data);die;
//        $params = [];
//        $params['mchId'] = env('wms_mchId');
//        $params['nonceStr'] = time();
//        var_dump(env("api_wms_url") . "/open/getWarehouseList");
//        $resPost = httpPostFun(env("api_wms_url") . "/open/getWarehouseList", $params, null, env('wms_pwd'));
//        var_dump($resPost);
        var_dump(

            (new \FlashExpress\bi\App\Server\AssetServer($this->lang, $this->timezone))->httpPostFunToWMS(json_decode('{
	"staff_info_id": "59760",
	"staff_name": "\u0e19\u0e32\u0e07\u0e2a\u0e32\u0e27 \u0e1b\u0e23\u0e31\u0e0a\u0e1e\u0e23 \u0e40\u0e1c\u0e48\u0e32\u0e1e\u0e07\u0e29\u0e4c",
	"organization_name": "Header Office",
	"audit_id": "asp_4",
	"status": 2,
	"modify_assets_num": [{
		"id": "5",
		"nums": "1"
	}],
	"reject_reason": "",
	"apply_staff_id": "59760"
}', true), json_decode('{
	"organization_name": "Header Office"
}', true))
        );

    }

    //10-30 早上上班卡数据
    public function att_thAction(){
        $sql = "select staff_info_id, min(created_at) start_at  from staff_work_face_verify_record 
                where `attendance_date`  = '2021-10-30'  and created_at > '2021-10-29 23:50:00' and `created_at`  < '2021-10-30 01:10:00' 
                and success_enabled in (-1,-2,-3) GROUP BY `staff_info_id` ";
        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $model = new StaffWorkAttendance();
        foreach ($data as $v){
            $staff_id = $v['staff_info_id'];
            //检查考勤数据
            $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '2021-10-30'");

            if(!empty($att_info)){//更新最早失败信息
                $att_info->started_at = $v['start_at'];
                $att_info->started_state = 5;
                $att_info->started_remark = 'by_tool_task';
                $att_info->updated_at = gmdate('Y-m-d H:i:s');
                $att_info->update();
            }else{//insert
                $insert = array();
                $staff_model = new StaffRepository();
                $shift_data = $staff_model->get_staff_shift($staff_id);
                if(empty($shift_data)){
                    echo '没有班次信息'.$staff_id;
                    continue;
                }
                //员工信息
                $staff_info = $staff_model->getStaffPosition($staff_id);
                if(empty($staff_info)){
                    echo '没有员工信息'.$staff_id;
                    continue;
                }
                $shift_start = $shift_data['start'];
                $shift_end   = $shift_data['end'];

                //组装数据
                $insert['staff_info_id'] = $staff_id;
                $insert['organization_type'] = $staff_info['organization_type'];
                $insert['organization_id'] = $staff_info['organization_type'] == 1 ? $staff_info['sys_store_id'] : $staff_info['sys_department_id'];
                $insert['attendance_date'] = '2021-10-30';
                $insert['shift_start'] = $shift_start;
                $insert['shift_end'] = $shift_end;
                $insert['started_at'] = $v['start_at'];
                $insert['started_state'] = 5;
                $insert['started_remark'] = 'by_tool_task';
                $clone_m = clone $model;
                $clone_m->create($insert);

            }
            $this->logger->write_log(" 考勤 补数据 {$staff_id} 完成",'info');
        }

    }


    //马来 10月份 去掉处罚提醒
    public function att_myAction(){
        try{
            ini_set('memory_limit', '-1');
            $staff_att = StaffWorkAttendance::find([
                'conditions' => "attendance_date between '2021-10-01' and '2021-10-24' ",
                'columns' => 'id, staff_info_id,attendance_date ',

            ])->toArray();
            if(empty($staff_att))
                die('没找到 符合条件的数据');

            $staff_ids = array_column($staff_att,'staff_info_id');
            $this->logger->write_log(" 马来 考勤 补数据 名单 ".json_encode($staff_ids),'info');
            $db = $this->getDI()->get('db');
            foreach ($staff_att as $v){
                $staff_id = $v['staff_info_id'];
                $attendance_date = $v['attendance_date'];
                $staff_model = new StaffRepository();
                $shift_data = $staff_model->get_staff_shift($staff_id);
                if(empty($shift_data)){
                    echo '没有班次信息'.$staff_id;
                    continue;

                }

                $add_hour = $this->getDI()['config']['application']['add_hour'];

                //班次
                $shift_start = $shift_data['start'];
                $shift_end = $shift_data['end'];

                //查询 有没有请假
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('a.staff_info_id');
                $builder->from(['a' => StaffAuditModel::class]);
                $builder->leftJoin(StaffAuditLeaveSplitModel::class, 's.staff_info_id = a.staff_info_id and  (a.audit_id = s.audit_id or (s.audit_id = 0 and a.leave_type = 15 and date(a.leave_start_time) = s.date_at))', 's');
                $builder->andWhere("a.audit_type = 2");
                $builder->andWhere("a.status = 2");
                $builder->andWhere("s.date_at = '{$attendance_date}'");
                $builder->andWhere("a.staff_info_id = '{$staff_id}'");
                $check = $builder->getQuery()->execute()->toArray();
                if(!empty($check))
                    continue;

                //查询有没有 请假
                $ot_check = HrOvertimeModel::findFirst("staff_id = {$staff_id} and date_at = '{$attendance_date}' and state in (1,2)");
                if(!empty($ot_check) && !empty($ot_check->overtime_id))
                    continue;

                $start_tmp = strtotime("{$v['attendance_date']} {$shift_start}") - $add_hour * 3600;
                $started_at = date("Y-m-d H:i:s" ,$start_tmp);

                $end_tmp = strtotime("{$v['attendance_date']} {$shift_end}") - $add_hour * 3600;
                if($shift_start > $shift_end)
                    $end_tmp = $end_tmp + 24 * 3600;
                $end_at = date('Y-m-d H:i:s',$end_tmp);

                $sql = "update staff_work_attendance set started_at = '{$started_at}',end_at = '{$end_at}' 
                        where id = {$v['id']}";
                $flag = $db->execute($sql);

                $this->logger->write_log(" 考勤 补数据 {$staff_id} {$attendance_date} {$flag}",'info');
            }

        }catch (\Exception $e){
            echo $e->getMessage();
        }
    }

    //菲律宾 未回款 补卡脚本
    public function att_phAction($param){
        //日期
        $date = '2024-09-10';
        $sql = "select  staff_info_id, click_time,attendance_type, concat(staff_info_id,'-',attendance_type) as u_key from fix_click_20240910";
        //测试工号
        if (!empty($param[0])) {
            $sql .= " where staff_info_id = {$param[0]}";
        }
        $data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($data)) {
            echo '没数据';
            return;
        }
        $staffIds = array_column($data, 'staff_info_id');
        $staffIds = array_values(array_unique($staffIds));
        $data     = array_column($data, null, 'u_key');


        //取班次
        $shiftData = HrStaffShiftMiddleDateModel::find([
            'conditions' => 'staff_info_id in ({ids:array}) and shift_date = :date_at:',
            'bind'       => ['ids' => $staffIds, 'date_at' => $date],
        ])->toArray();
        $shiftData = empty($shiftData) ? [] : array_column($shiftData, null, 'staff_info_id');

        //员工信息
        $staffInfos = HrStaffInfoModel::find([
            'columns' => 'staff_info_id, job_title,sys_store_id, sys_department_id',
            'conditions' => 'staff_info_id in ({ids:array}) and (sys_store_id != :store_staff: or job_title in ({jobs:array})) ',
            'bind' => ['ids' => $staffIds, 'store_staff' => '-1', 'jobs' => [79,269]],
        ])->toArray();
        $staffInfos = empty($staffInfos) ? [] : array_column($staffInfos, null, 'staff_info_id');

        $attendanceData = StaffWorkAttendance::find([
            'conditions' => 'attendance_date = :date_at: and staff_info_id in ({ids:array})',
            'bind'       => ['date_at' => $date, 'ids' => $staffIds],
        ]);

        //已经有记录的 update
        $updateIds = [];
        if (!empty($attendanceData->toArray())) {
            foreach ($attendanceData as $attInfo) {
                $staffId = $attInfo->staff_info_id;
                //上班
                $key1 = "{$staffId}-1";
                if (!empty($data[$key1]['click_time'])) {
                    //上班 取最早
                    $startTime = $data[$key1]['click_time'];
                    if(!empty($attInfo->started_at) && $data[$key1]['click_time'] > $attInfo->started_at){
                        $startTime = $attInfo->started_at;
                    }
                    $attInfo->started_at     = $startTime;
                    $attInfo->started_state  = 5;
                    $attInfo->started_remark = 'by_tool_task';

                }
                //下班
                $key2 = "{$staffId}-2";
                if (!empty($data[$key2]['click_time'])) {
                    $endTime = $data[$key2]['click_time'];
                    if(!empty($attInfo->end_at) && $data[$key2]['click_time'] < $attInfo->end_at){
                        $endTime = $attInfo->end_at;
                    }
                    $attInfo->end_at     = $endTime;
                    $attInfo->end_state  = 5;
                    $attInfo->end_remark = 'by_tool_task';
                }
                $flag = $attInfo->update();
                $this->logger->write_log(" 考勤 补数据 update {$staffId} {$date} {$flag}", 'info');
                //排除掉操作完的工号
                $updateIds[] = $staffId;
            }
        }

        if(!empty($updateIds)){
            $staffIds = array_values(array_diff($staffIds, $updateIds));
        }

        //剩下没有记录的 insert
        if (empty($staffIds)) {
            echo '跑完了';
            return;
        }

        foreach ($staffIds as $id) {
            $insert      = [];
            $staff_info  = $staffInfos[$id] ?? [];
            if (empty($staff_info)) {
                echo $id . '没信息';
                continue;
            }

            $model       = new StaffWorkAttendance();
            $shift_start = $shiftData[$id]['shift_start'] ?? '';
            $shift_end   = $shiftData[$id]['shift_end'] ?? '';

            //组装数据
            $insert['staff_info_id']     = $id;
            $insert['organization_type'] = $staff_info['sys_store_id'] == '-1' ? 2 : 1;
            $insert['organization_id']   = $staff_info['sys_store_id'] == '-1' ? $staff_info['sys_department_id'] : $staff_info['sys_store_id'];
            $insert['attendance_date']   = $date;
            $insert['job_title']         = $staff_info['job_title'];
            $insert['shift_start']       = $shift_start;
            $insert['shift_end']         = $shift_end;
            $insert['shift_id']          = $shiftData[$id]['shift_id'] ?? 0;
            //上班
            $key1 = "{$id}-1";
            if (!empty($data[$key1]['click_time'])) {
                $insert['started_at']     = $data[$key1]['click_time'];
                $insert['started_state']  = 5;
                $insert['started_remark'] = 'by_tool_task';
            }
            //下班
            $key2 = "{$id}-2";
            if (!empty($data[$key2]['click_time'])) {
                $insert['end_at']     = $data[$key2]['click_time'];
                $insert['end_state']  = 5;
                $insert['end_remark'] = 'by_tool_task';
            }
            $flag = $model->create($insert);
            $this->logger->write_log(" 考勤 补数据 insert {$id} {$date} {$flag}", 'info');
        }
        echo '跑完了';
        return ;
    }

    //菲律宾 灾难 补请假
    public function leaveAction($param){
        $staff_id = intval($param[0]);
        $date_at = $param[1];
        $leave_type = intval($param[2]);

        //判断如果存在假期 跳过
        $sql = "select date_at
                from staff_audit_leave_split s
                join staff_audit a on s.staff_info_id = a.staff_info_id and  (a.audit_id = s.audit_id or (s.audit_id = 0 and a.leave_type = 15 and date(a.`leave_start_time`) = s.date_at))                
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 
                and a.status = 2 
                and s.date_at = '{$date_at}'";
        $data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($data))
            die("存在请假 {$staff_id} {$date_at}");


        //补记录
        $insetData = [
            'staff_info_id'    => $staff_id,
            'leave_type'       => $leave_type,
            'leave_start_time' => date('Y-m-d 09:00:00',strtotime($date_at)),
            'leave_start_type' => 1,
            'leave_end_time'   => date('Y-m-d 18:00:00',strtotime($date_at)),
            'leave_end_type'   => 2,
            'audit_reason'     => 'by tool task',
            'status'           => 2,
            'audit_type'       => enums::$audit_type['LE'],
            'leave_day'        => 1,
            'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
        ];
        $model = new StaffAuditModel();
        $model->create($insetData);

        $audit_id = $model->audit_id;

        $split_insert['audit_id'] = $audit_id;
        $split_insert['staff_info_id'] = $staff_id;
        $split_insert['date_at'] = $date_at;
        $split_insert['type'] = 0;
        $split_insert['year_at'] = date('Y',strtotime($date_at));

        $model = new StaffAuditLeaveSplitModel();
        $flag = $model->create($split_insert);

        $this->logger->write_log("考勤 补请假 {$staff_id} {$date_at} {$flag}",'info');

    }


    public function ascAction($args = [])
    {
        $staffInfoId = $args[0] ?? 0;

        //分组
        $groups = AuditListEnums::getAuditFilterGroup();
        $auditTypeList = AuditListEnums::getAllAuditTypes();

        //获取全部的待审批数据
        $conditions = [
            'state' => [enums::APPROVAL_STATUS_PENDING],
            'audit_type' => $auditTypeList,
        ];

        $pageNum = 1;
        $pageSize = 1000;
        $countData = [];
        $repo = new AuditlistRepository($this->lang, $this->timezone);

        while ($pendingList = $repo
            ->getAuditApprovalList($staffInfoId, $conditions, 'biz_value,biz_type', AuditListEnums::LIST_SORT_ASC, $pageNum, $pageSize)) {
            if (empty($pendingList)) {
                break;
            }
            foreach ($pendingList as $item) {
                $bizType  = $item['biz_type'];
                $bizValue = $item['biz_value'];

                // 如果还没有该 biz_type 的统计项，则初始化一个空数组
                if (!isset($countData[$bizType])) {
                    $countData[$bizType] = [];
                }

                // 使用数组的键来保证 biz_value 的唯一性
                $countData[$bizType][$bizValue] = true;
            }
            if (count($pendingList) < $pageSize) {
                break;
            }
            $pageNum += 1;
        }
        print_r($countData);
        die;
    }

	public function testLockAction(){
        $key = 'test_lock';
        $redis = $this->getDI()->get("redisLib");

        $rs = $redis->set($key, 1, array('nx', 'ex' => 20));//锁10分钟
        if(!$rs)
            die('task is running');

        echo 'done';
    }

    public function extinguisherAction()
    {
	    $sql = 'select et.id,ebi.asset_code,ebi.longitude,ebi.latitude,ebi.coordinate,ebi.weight,ebi.photo_url,ebi.operator_id,ebi.deleted     from extinguisher_task as et join extinguisher_base_info as ebi on et.extinguisher_info_id = ebi.id where et.status = 2';
        $taskInfo = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);


        foreach ($taskInfo as $one)  {

            $extinguisherBaseJson['asset_code'] = $one['asset_code'];
            $extinguisherBaseJson['longitude']  = $one['longitude'];
            $extinguisherBaseJson['latitude']   = $one['latitude'];
            $extinguisherBaseJson['coordinate'] = $one['coordinate'];
            $extinguisherBaseJson['weight']     = $one['weight'];
            $extinguisherBaseJson['photo_url']  = $one['photo_url'];
            $extinguisherBaseJson['operator_id']= $one['operator_id'];
            $extinguisherBaseJson['deleted']    = $one['deleted'];

            $endInfo['extinguisher_base_json']= json_encode($extinguisherBaseJson, JSON_UNESCAPED_UNICODE);
            $res = $this->getDI()->get('db')->updateAsDict(
                'extinguisher_task',
                $endInfo,
                'id = '. $one['id']
            );
            if($res) {
                echo $one['id'] . ': success'. PHP_EOL;
            }
        }
    }

    public function messageAction()
    {
        $staff_info_id = 90284;
        $id = time() . $staff_info_id . rand(1000000, 9999999);
        $param['staff_users'] = array($staff_info_id);//数组 多个员工id
        $param['message_title'] = 'test';
        $param['message_content'] = 'test';
        $param['staff_info_ids_str'] = $staff_info_id;
        $param['id'] = $id;
        $param['category'] = -1;

        $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        var_dump($res);
    }

    public function aaaAction()
    {
       $st =  '[{"staff_users":[{"id":"214946"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104004395892, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"214946","category":33,"category_code":5},
{"staff_users":[{"id":"137891"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104007730590, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"137891","category":33,"category_code":5},
{"staff_users":[{"id":"140825"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104016954187, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"140825","category":33,"category_code":5},
{"staff_users":[{"id":"196881"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104019593101, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"196881","category":33,"category_code":5},
{"staff_users":[{"id":"180386"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104021706766, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"180386","category":33,"category_code":5},
{"staff_users":[{"id":"213153"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104025185543, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"213153","category":33,"category_code":5},
{"staff_users":[{"id":"213605"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104030257603, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"213605","category":33,"category_code":5},
{"staff_users":[{"id":"215422"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104032797485, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"215422","category":33,"category_code":5},
{"staff_users":[{"id":"214874"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104039339432, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"214874","category":33,"category_code":5},
{"staff_users":[{"id":"136054"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104045563900, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"136054","category":33,"category_code":5},
{"staff_users":[{"id":"212809"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104042757037, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"212809","category":33,"category_code":5},
{"staff_users":[{"id":"215709"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104052363248, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"215709","category":33,"category_code":5},
{"staff_users":[{"id":"188994"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104053355370, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"188994","category":33,"category_code":5},
{"staff_users":[{"id":"228305"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104061769564, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"228305","category":33,"category_code":5},
{"staff_users":[{"id":"150968"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104068890773, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"150968","category":33,"category_code":5},
{"staff_users":[{"id":"230191"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104063563864, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"230191","category":33,"category_code":5},
{"staff_users":[{"id":"232098"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104073078179, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"232098","category":33,"category_code":5},
{"staff_users":[{"id":"212311"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104071835165, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"212311","category":33,"category_code":5},
{"staff_users":[{"id":"213451"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104083334560, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"213451","category":33,"category_code":5},
{"staff_users":[{"id":"228486"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104086340413, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"228486","category":33,"category_code":5},
{"staff_users":[{"id":"230284"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104081851927, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"230284","category":33,"category_code":5},
{"staff_users":[{"id":"179672"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104097990395, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"179672","category":33,"category_code":5},
{"staff_users":[{"id":"231819"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104097081193, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"231819","category":33,"category_code":5},
{"staff_users":[{"id":"173515"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104104780069, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"173515","category":33,"category_code":5},
{"staff_users":[{"id":"126870"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104102466188, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"126870","category":33,"category_code":5},
{"staff_users":[{"id":"166686"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104119675965, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"166686","category":33,"category_code":5},
{"staff_users":[{"id":"141040"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104119917214, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"141040","category":33,"category_code":5},
{"staff_users":[{"id":"139111"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104129691406, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"139111","category":33,"category_code":5},
{"staff_users":[{"id":"191270"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104127201658, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"191270","category":33,"category_code":5},
{"staff_users":[{"id":"171003"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070104125138289, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"171003","category":33,"category_code":5},
{"staff_users":[{"id":"170965"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110000610624, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"170965","category":33,"category_code":5},
{"staff_users":[{"id":"175428"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110016623668, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"175428","category":33,"category_code":5},
{"staff_users":[{"id":"167187"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110019492441, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"167187","category":33,"category_code":5},
{"staff_users":[{"id":"175430"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110019253636, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"175430","category":33,"category_code":5},
{"staff_users":[{"id":"154055"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110029500053, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"154055","category":33,"category_code":5},
{"staff_users":[{"id":"178635"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110029119205, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"178635","category":33,"category_code":5},
{"staff_users":[{"id":"167568"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110033787063, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"167568","category":33,"category_code":5},
{"staff_users":[{"id":"212996"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110037952851, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"212996","category":33,"category_code":5},
{"staff_users":[{"id":"153845"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110032683988, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"153845","category":33,"category_code":5},
{"staff_users":[{"id":"226429"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110044683237, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"226429","category":33,"category_code":5},
{"staff_users":[{"id":"214638"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110049077703, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"214638","category":33,"category_code":5},
{"staff_users":[{"id":"176597"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110055051524, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"176597","category":33,"category_code":5},
{"staff_users":[{"id":"189101"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110055877642, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"189101","category":33,"category_code":5},
{"staff_users":[{"id":"232799"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110052609652, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"232799","category":33,"category_code":5},
{"staff_users":[{"id":"236485"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110067947918, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"236485","category":33,"category_code":5},
{"staff_users":[{"id":"226172"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110065532279, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"226172","category":33,"category_code":5},
{"staff_users":[{"id":"236627"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110076368152, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"236627","category":33,"category_code":5},
{"staff_users":[{"id":"236841"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110074201714, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"236841","category":33,"category_code":5},
{"staff_users":[{"id":"236671"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110074067786, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"236671","category":33,"category_code":5},
{"staff_users":[{"id":"236823"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110085331168, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"236823","category":33,"category_code":5},
{"staff_users":[{"id":"212270"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110089796004, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"212270","category":33,"category_code":5},
{"staff_users":[{"id":"155749"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110090997854, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"155749","category":33,"category_code":5},
{"staff_users":[{"id":"235857"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110090775264, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"235857","category":33,"category_code":5},
{"staff_users":[{"id":"217704"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110099749966, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"217704","category":33,"category_code":5},
{"staff_users":[{"id":"189357"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110104736986, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"189357","category":33,"category_code":5},
{"staff_users":[{"id":"222963"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110101084129, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"222963","category":33,"category_code":5},
{"staff_users":[{"id":"189644"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110116919554, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 09:00 - 18:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"189644","category":33,"category_code":5},
{"staff_users":[{"id":"192808"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110112064529, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"192808","category":33,"category_code":5},
{"staff_users":[{"id":"169943"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110120669397, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"169943","category":33,"category_code":5},
{"staff_users":[{"id":"203778"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110127248870, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"203778","category":33,"category_code":5},
{"staff_users":[{"id":"166014"}],"message_title":"Give Support notice","message_content":"<div style=\'font-size: 30px\'>Approval Number:SASS202411070110125984856, support branch NAG_SP, support date is 2024-11-07-2024-11-07, support shift is 04:00 - 13:00, please punch in at the support branch during the support period.  </div>","staff_info_ids_str":"166014","category":33,"category_code":5}]';
        $st = (json_decode($st,true));
        foreach ($st as $item) {
            $item['message_content'] = str_replace("<div style='font-size: 30px'>","<div style=\'font-size: 30px\'>",$item['message_content']);
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($item);
            $res = $bi_rpc->execute();

       }
    }



    public function tetaAction(){
        $staff_info['department_id'] = 105;
        $ids = [];
        $query = $this->modelsManager->createQuery('SELECT b.staff_info_id FROM FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel b where b.staff_info_id IN (
                    SELECT a.staff_info_id FROM FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel a WHERE a.department_id = :department:
                    and a.deleted = 0 and a.type = 1
                ) and position_category = :hrbp:');

        $hrbp = $query->execute([
                                    'hrbp'       => 68,
                                    'department' => $staff_info['department_id'],
                                ])->toArray();
        if (!empty($hrbp)) {
            $ids = array_merge($ids, array_column($hrbp, 'staff_info_id'));
        } else {
            //获取部门的部门链
            $department  = SysDepartmentModel::findFirst($staff_info['department_id']);
            $ancestry_v3 = isset($department->ancestry_v3) ? explode('/', $department->ancestry_v3) : [];
            //获取是否为包含子部门的管辖范围 hrbp
            if (!empty($ancestry_v3)) {
                $query = $this->modelsManager->createQuery('SELECT b.staff_info_id FROM FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel b where b.staff_info_id IN (
                    SELECT a.staff_info_id FROM FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel a WHERE  a.department_id in (' . implode(',', $ancestry_v3) . ')
                    and a.deleted = 0 and a.type = 1 and a.is_include_sub = 1
                ) and position_category = :hrbp:');

                $hrbp = $query->execute([
                                            'hrbp' => 68,
                                        ])->toArray();
                if (!empty($hrbp)) {
                    $ids = array_merge($ids, array_column($hrbp, 'staff_info_id'));
                }
            }
        }

        print_r($ids);exit();
    }


    /**
     * @description:修复 OT 抄送数据  泰国
     * @param null
     * @return: 
     * @author: L.J
     * @time: 2023/2/8 22:48
     */
    public function repair_ccAction()
    {
        //查询 2023-01-01 之后的抄送数据
        $list = AuditCCModel::find([
            'conditions' => ' biz_type = :type: and created_at >= :created_at: and deleted = 0 ',
            'bind'       => [
                'type'       => \FlashExpress\bi\App\Enums\AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'created_at' => '2023-01-01 00:00:00',
            ],
        ])->toArray();
        if (empty($list)) {
            exit('没有抄送数据');
        }
        $staff_info_ids  = array_column($list, 'submitter_id');
        $staff_info_data = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_info_ids:array})',
            'bind'       => [
                'staff_info_ids' => $staff_info_ids,
            ],
            'columns'    => 'staff_info_id,node_department_id,sys_store_id',
        ])->toArray();
        $staff_info_data = array_column($staff_info_data, null, 'staff_info_id');
        $WorkflowServer  = (new WorkflowServer($this->lang, $this->timezone));
        $insert_data     = [];

        foreach ($list as $v) {
            $staff = $staff_info_data[$v['submitter_id']];
            if (empty($staff)) {
                continue;
            }
            $audit_hrbp_cc = $WorkflowServer->findHRBPFromCache($staff['node_department_id'],
                ['store_id' => $staff['sys_store_id']]);
            $audit_hrbp_cc = explode(',', $audit_hrbp_cc);
            $audit_hrbp_cc  = array_diff($audit_hrbp_cc, [$v['cc_staff_id']]);


            //如果当前抄送人在 hrbp 里 认为 hrbp 没有找错
            if (empty($audit_hrbp_cc)){
                continue;
            }

            foreach ($audit_hrbp_cc as $hrbp_id) {
                //插入 hrbp
                $insert_data[] = [
                    'biz_type'     => $v['biz_type'],
                    'biz_value'    => $v['biz_value'],
                    'flow_node_id' => $v['flow_node_id'],
                    'submitter_id' => $v['submitter_id'],
                    'cc_staff_id'  => $hrbp_id,
                    'state'        => $v['state'],
                    'summary'      => $v['summary'],
                    'is_read'      => 2,
                    'created_at'   => $v['created_at'],
                    'update_at'    => '2023-02-08 00:00:00',
                ];
            }

        }
        echo '--总数 '.count($insert_data);
        $insert_data = array_chunk($insert_data, 1000);

        foreach ($insert_data as $insert_data_v) {
            echo '--'.(new BaseRepository())->batch_insert('audit_cc', $insert_data_v);
        }
    }

    public function edit_ot_type_of_summaryAction(){
        (new \FlashExpress\bi\App\Repository\AuditApplyRepository())->editOtTypeOfSummary(2120050,118681,1);
    }


    //单元测试代码
    public function ttAction()
    {
        $jobtransferObj=Tools::reBuildCountryInstance(new JobtransferServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
//        print_r($jobtransferObj->getGraduallyDeptManager(4, 4, '2023-08-24'));die;
        print_r($jobtransferObj->getafterManagerIdInfo('TH01060113', 32, 13, '2023-08-27'));die;
    }

    public function pushAction($params){

        $staff_info_id = $params[0] ?? 0;
        $silence = $params[1] ?? CommonEnums::PUSH_MESSAGE_TYPE_NORMAL;

        $data = [
            "staff_info_id" => $staff_info_id,  //上级id
            "src" => "backyard",      //1:'kit'; 2:'backyard','c';
            "message_title" => '获取红点数',  //标题
            "message_content" => '获取红点数内容',//内容
            "message_scheme" => '', //地址
            'silence' => $silence,
        ];

        //rpc调用
        $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
        $ret->setParams($data);
        $result = $ret->execute();

        dd($result);
    }
    public function twoStaffCheckAction()
    {
        $filePath = BASE_PATH.'/public/2_staff_face.xlsx';

        //获取Excel数据
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $t = $this->getTranslation('zh');

        foreach ($excelData as $k => $v) {
            if($k == 0){
                $header = $v;
                $header[] = '工号1';
                $header[] = '工号1职位';
                $header[] = '工号1编制类型';
                $header[] = '工号1在职状态';
                $header[] = '工号1雇佣类型';
                $header[] = '工号1是否为子账号';
                $header[] = '工号1关联主账号';
                $header[] = '工号1关联主账号在职状态';
                $header[] = '工号2';
                $header[] = '工号职位';
                $header[] = '工号2编制类型';
                $header[] = '工号2在职状态';
                $header[] = '工号2雇佣类型';
                $header[] = '工号2是否为子账号';
                $header[] = '工号2关联主账号';
                $header[] = '工号2关联主账号在职状态';
                $header[] = '工号1底片';
                $header[] = '工号2底片';
                $header[] = '是否是同一人';
                continue;
            }
            $tmpStaff = [$staff1,$staff2] = explode('|',$v[0]);
            $supportStaffInfo = HrStaffApplySupportStoreModel::find([
                'conditions' => 'sub_staff_info_id in ({sub_staff_info_id:array}) ',
                'bind' =>['sub_staff_info_id' => $tmpStaff ],
            ]);
            $staff1MasterStaff = $staff2MasterStaff = '';
            $staff1MasterStaffState = $staff2MasterStaffState = '';
            if ($supportStaffInfo) {
                $supp              = array_column($supportStaffInfo->toArray(), 'staff_info_id', 'sub_staff_info_id');
                $staff1MasterStaff = $supp[$staff1] ?? '';
                $staff2MasterStaff = $supp[$staff2] ?? '';
                $masterStaffInfos  = $staffServer->getStaffInfoList(array_values($supp));
                $masterStaffInfos  = array_column($masterStaffInfos, null, 'staff_info_id');
                if (!empty($masterStaffInfos[$staff1MasterStaff])) {
                    $staff1MasterStaffState = $masterStaffInfos[$staff1MasterStaff]['state'] == 1 && $masterStaffInfos[$staff1MasterStaff]['wait_leave_state'] == 1 ? 4 : $masterStaffInfos[$staff1MasterStaff]['state'];
                    $staff1MasterStaffState = $t->_('hris_working_state_' . $staff1MasterStaffState);
                }
                if (!empty($masterStaffInfos[$staff2MasterStaff])) {
                    $staff2MasterStaffState = $masterStaffInfos[$staff2MasterStaff]['state'] == 1 && $masterStaffInfos[$staff2MasterStaff]['wait_leave_state'] == 1 ? 4 : $masterStaffInfos[$staff2MasterStaff]['state'];
                    $staff2MasterStaffState = $t->_('hris_working_state_' . $staff2MasterStaffState);
                }
            }
            $staffInfos = $staffServer->getStaffInfoList($tmpStaff);
            $staff1state = $staffInfos[$staff1]['state'] == 1 &&  $staffInfos[$staff1]['wait_leave_state'] == 1 ? 4:$staffInfos[$staff1]['state'];
            $staff2state = $staffInfos[$staff2]['state'] == 1 &&  $staffInfos[$staff2]['wait_leave_state'] == 1 ? 4:$staffInfos[$staff2]['state'];
            $v[] = $staff1;
            $v[] = $staffInfos[$staff1]['job_name'];
            $v[] = $staffInfos[$staff1]['formal'];
            $v[] = $t->_('hris_working_state_'.$staff1state);
            $v[] = $staffInfos[$staff1]['hire_type'] ? $t->_('hire_type_'.$staffInfos[$staff1]['hire_type']) :'';
            $v[] = $staffInfos[$staff1]['is_sub_staff'];
            $v[] = $staff1MasterStaff;
            $v[] = $staff1MasterStaffState;
            $v[] = $staff2;
            $v[] = $staffInfos[$staff2]['job_name'];
            $v[] = $staffInfos[$staff2]['formal'];
            $v[] = $t->_('hris_working_state_'.$staff2state);
            $v[] = $staffInfos[$staff2]['hire_type'] ? $t->_('hire_type_'.$staffInfos[$staff2]['hire_type']) :'';
            $v[] = $staffInfos[$staff2]['is_sub_staff'];
            $v[] = $staff2MasterStaff;
            $v[] = $staff2MasterStaffState;
            [$staff1Face,$staff2Face,$checkRes] = $this->checkIsSameStaff($staffInfos[$staff1],$staffInfos[$staff2]);
            $v[] = $staff1Face;
            $v[] = $staff2Face;
            $v[] = $checkRes;
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('2_staff_face'));
        $this->logger->write_log(['人脸比对'=>$file_name],'alert');
    }

    protected function checkIsSameStaff($staff1Info,$staff2Info){
        $staff1 = $staff1Info['staff_info_id'];
        $verifyServer = new \FlashExpress\bi\App\Server\AttendanceImageVerifyServer('zh','+07:00');

        [$viewCurrentUrl1, $currentUrl1] = $this->getFaceImage($staff1Info);
        [$viewCurrentUrl2, $currentUrl2] = $this->getFaceImage($staff2Info);
        if (empty($currentUrl1) || empty($currentUrl2)) {
            return [$viewCurrentUrl1, $viewCurrentUrl2, '底片不全'];
        }
        $res = $verifyServer->internalAiInterFace($staff1, $currentUrl1, $currentUrl2);

        if ($res['score'] == 100) {
            return [$viewCurrentUrl1, $viewCurrentUrl2, '是'];
        }
        return[$viewCurrentUrl1,$viewCurrentUrl2,'否'];
    }

    public function getFaceImage($staffInfo)
    {
        $staff = $staffInfo['staff_info_id'];
        if($staffInfo['hire_type'] == 12){
            $res =  $this->hireType12FaceMap($staff);
            return [$res,$res];
        }
        $verifyServer = new \FlashExpress\bi\App\Server\AttendanceImageVerifyServer('zh','+07:00');

        $staffWorkAttendanceRepository = new StaffWorkAttendanceRepository();
        //获取底片
        $currentStaffFaceImagePath = $staffWorkAttendanceRepository->getStaffFaceImage($staff);
        if(empty($currentStaffFaceImagePath)){
            return ['',''];
        }
        //[4]人脸比对
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $currentStaffFaceImagePath['path'];
        $res = $verifyServer->formatOssUrl($format, 1);
        return [env("img_prefix"). $format['path'],$res];

    }

    public function hireType12FaceMap($staff_id)
    {
        $arr =  [
            '4786853'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731903890-eed472ac44f944539bad434a055e12dd.jpg?Expires=1732428966&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=U3reJHcjkwix90%2B12o%2F%2BxESeFGg%3D',
            '4689873'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728272456-c2e296e7f5d24557a93095e797c12d32.jpg?Expires=1732428966&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=t%2FFcdjwlWUgtoKRdvG8Ugc0iyLk%3D',
            '4677215'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1725770830-0f72b5a1adff4636a9369de0b3a8932b.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=5%2FzrcxqE7zTzmnDI0Um21lgePBg%3D',
            '4305237'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1711708109-bd89085c42014283a1799ee81a33b36e.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=V5EMuiHz83sMUTj99BleDi9LUD4%3D',
            '4178754'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1699931145-a3fc96bd61554338adc6b6f41d40aa44.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Iq%2Fjs0nquCCT4DGfa9KDISC0qoQ%3D',
            '4047554'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1702911691-0f7c7195c81147c59b4b84f54e57328a.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=LD02fPfRK%2BUljCplZ8EDRbqc2vo%3D',
            '4060776'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1703560861-bb988d5b289f4ac8bea00e83fbabec21.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=mx3eJG52z8m0w04Uo7RQJvv%2B80E%3D',
            '4127091'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1705554674-5ecaffce6b114c7c94b35f4dc1e198ae.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=kxtltn2o8SPrSr6eLdv6zXLKeRw%3D',
            '4258412'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1710685818-76ce11c353cc494590937f2340d776f0.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=923t5Hf3UsNt4y6dhz9CocvUHhI%3D',
            '4125948'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1705677261-7d7f904f58d84707baa47da5a771750a.jpg?Expires=1732428967&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=IYFA1UnE%2BCvlXgGxq0VvoEVDjrw%3D',
            '4357682'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1706763343-b83271cd1fc34e8b9504c680dc6c15e1.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=2qyPjlWjCsZWZG6r8btqd6JfzhI%3D',
            '4203036'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1706919515-442b1604640a453b9b8bef773d7c1dda.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=knvYPqAeoZ83OidOQp06xibUmfg%3D',
            '4169153'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1707231282-175448968af84f0890914cee3dc83575.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=OV%2Ft1iIElcZlZysVyCY3%2B2LUh%2Bk%3D',
            '4170549'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1707273233-fa69391780e94c488e12bb30e8daaac3.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=NWmkwu0qHjDdpB4h8odzwWiUVw0%3D',
            '4310462'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1708423437-1378d3dbe59e4176b3fce441e3a47894.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=o1e1xT0hUP%2FU7cNPWW8qDiuDe1s%3D',
            '4193989'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1708653093-b214cbdb2d3b4c388208af2531a0584a.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=82NBXrQQdmx2c5vVVWgpJhSu0dw%3D',
            '4272173'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1712136230-d2378917a5494e3b831daf18464811b3.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=KDZ1%2F8opOS0H7C9m3zHMSLSjYdM%3D',
            '4608229'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1712411059-12a2e377cc0f4c2d9c8854d8b19cc4b1.jpg?Expires=1732428968&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=%2BqR18IhdWu1ZsLOacuS%2BqlR5xJo%3D',
            '4281168'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1712578282-5613e1ff19ad40db8978116c47bbce19.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=DmNyZG4O5L4jakY%2B%2FUEkuPSe050%3D',
            '4354812'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1712801351-d4d5bab963ba4dd994bff3a9e8bb734a.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=qm1fNzbXtktzKtxZ%2FrNKBCMh4kk%3D',
            '4294751'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1713420660-7ed2046ab4854d1ca975bc13cb77cc29.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=xOKjrgjCWE%2B8s0orTDN%2BcKTcFEo%3D',
            '4404779'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1714315287-2b8ead3301d14283bfeed6f37b323e63.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=wcgJk3EyFpZxTg2jDjdFq%2BvW8gw%3D',
            '4326631'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1714459808-9561970c084b45b2a598f3723a174911.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=G0u3367HWbFrUsIs2LzV5%2FwO690%3D',
            '4317778'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1714619491-7926dc94f68b481a9d9fbfe386f8133c.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=1gPRnI4Hi%2BIacucByCh%2FvWqlF0A%3D',
            '4551775'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1714621229-c3cb9bed143d452fa29388144ddbe07d.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=aZ4ey%2BxdFFJOTPrK1%2F5%2F7t5c9F4%3D',
            '4317778'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1714620271-c00dd41785c6401b9b18b1ba88d6c42e.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=esjs70V%2BAaYUVg%2BF0FHko%2FB90OA%3D',
            '4328776'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1715152074-f29d526813d047afbd59eb86f14c0e42.jpg?Expires=1732428969&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=bQEg9DCHmf3KI%2F0KFHfMoRzm6Vw%3D',
            '4348651'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1715655920-0dccc59c58a347a8bba5caa25f81d59e.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=gRujU%2Fx9m1Q7uLqrT7W4ayhzUog%3D',
            '4343584'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1715682931-1e9d59413e864e359ac51c2b238d570a.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=yZghsL53cutrUxR101BDPr65fEQ%3D',
            '4343600'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1715742979-b40dd065b17e4ed5a58b343b6ea1c62a.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=5ma%2B5Ay5I7hsR5hwAeQnaUvPVxE%3D',
            '4348521'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1715776455-4780d64a1fa9428282371dc61f57208c.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=z86rFaPLMeb2bwyt4LBCfgmRpYA%3D',
            '4348679'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1715906592-5a470d4e53e44f54aa10cd721ef18d39.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=QAExmcFWgK7QI7tFZE3KDHVmLUo%3D',
            '4356347'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1716187326-9454f989ae4d4124bc705875090fe6f2.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=eywgeH4y3MgW%2Fw5G6Qkxfe%2FIF00%3D',
            '4454371'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1716520917-8764ae11f50d4d6da40482f702db5f85.jpg?Expires=1732428970&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=A5JctZkXRzZqzsu7070uX92IhSk%3D',
            '4371823'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1716772948-0fc2632e519149538b1a445124ab5dc2.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=iPEqb9nZdJ6jcDwzT4Qee0qSev8%3D',
            '4754565'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1716954210-e15e0950f2d0422e81e8814b018c08aa.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=9hzNzUhpqEXxrTxcMr6utkFoVgc%3D',
            '4404745'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1717334097-dee3c3c110fb4a72a7f70a296fca22f2.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=uj4gD0MrzNTLxkd5OO%2FFgei9AyE%3D',
            '4406632'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1717500477-952de322cbcc44e7a96fa31d94d0f60b.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=XL7wrJSeHxu8Ss8SqXCbNOuEiVE%3D',
            '4413935'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1717813271-e71eba7cd661478397da847dbd095d72.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=18qdOfVqjK3Zs%2FCLdiFahKjawNo%3D',
            '4425424'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1717907816-d48b7a7d03d942298ed984f9baf36690.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Kb3Ws1IHXHD3HMkJeNtP7if%2BJ7A%3D',
            '4426976'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1718242607-f587e7a595fe49908cd2e117a0e9d81e.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=nu%2F5dhzsBXYhusk2YUrbaz%2F1JA0%3D',
            '4440519'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1718549980-7c002841b6564d08baa5a5f1021ec557.jpg?Expires=1732428971&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=w%2BzUOyfhGI3XG6xoswlNPwrxLZ4%3D',
            '4472032'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1719928819-5018ff20e39d4eddb52ba51936bbfc9d.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=ILg6GA0qyNTWIkND%2BNa4r3SIEcA%3D',
            '4477590'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1720184533-0d02422619ec471c9ac21b72721f4e82.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=oElR8a2FfcKN9O%2BYW42ojL%2BU3Zg%3D',
            '4652029'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1720486989-dfe16ebdc92d4f7eb8317ffe9ae63d19.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=DVYaWgBgG1heVnHMALgGXZUVJAk%3D',
            '4502366'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1721178763-ba92edcc720a44be9ed2f1cd58ed02e3.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Z63hCZ6OZGAziW%2Fn%2BEFfk61WsiA%3D',
            '4784811'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731805957-b136052fb2ed44228d5979db6a6916b9.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=cQjp3eKFR360aZGrJ4e4YbX5RwY%3D',
            '4527221'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1722310748-327a35add0474a37801cd0405fbf8f35.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=KJDS0fZEwRFg1804984I13YxxAc%3D',
            '4532520'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1722418294-bd2a7b4cbc57438bae2f62b0dba31ea3.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=0zFMLW1upK5eBWjgtxp6S%2FRrGw0%3D',
            '4546158'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1723077615-1cc79526bb8c4f3cbbf06252f36ed5e1.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=gdEfPCFq6wKZfXt6J4GvGr38F3o%3D',
            '4548280'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1723101399-1351c4b53afb4e348f6b6b75349f6b29.jpg?Expires=1732428972&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=771WxNKU4NuwH7fi3SjRWnhuoDY%3D',
            '4559254'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1723689390-4421732729664944a64f9701d96ef645.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=SFotCimjGeDzk2TjhZhRNrK59bg%3D',
            '4579486'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1724376414-fddef1005afd4bd39e17b31812c6698c.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=nugLR%2B0ceqEXqdOCwuzXZwYmC8c%3D',
            '4603629'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1725245433-ed0ac03dc464400b9adbbc1bfe27a55b.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=C%2FCrztyp5OYhmIneUo6KAz5KV5I%3D',
            '4608149'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1725353745-11c2de81e4f1410cb8a8d83f48c2ddc3.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=cRoO%2BrbbXDOZrya2vXDUnYn99oo%3D',
            '4619951'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1725594022-437fa9a9b041471f8b4abb229a137e41.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=fUU090F1lINcoZ4DXL19wH91XEU%3D',
            '4630956'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1725893499-ec3f168765b3486f8f9bd15807420acb.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=jINrI5s3RRNep7EeUp9U7Ywcoc4%3D',
            '4686294'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727921624-70b41ff3a0084f1da18659ace363b4ec.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=DsAdkPJM0CYqje4YlCVrw3bV97o%3D',
            '4653631'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1726843991-4cc7e4ea06444ecf8f572da63f60a4bd.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=WBU84t3TKiXEeS169E5lWIzKmsw%3D',
            '4661778'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727227451-00ae85930ed54786a9b72cc3195bd103.jpg?Expires=1732428973&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=6eduOJB5f8IJ%2FIiIIPMS4BxXtkg%3D',
            '4675134'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727406961-b72ea75a91b34872b0660e247496727d.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=QGvyQDGgAsH7NGUJrJS2XzPP8p8%3D',
            '4664150'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727417138-aed837b4c05c4a528dd57ef9b60e7d46.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=0WNxFzOqukDY3lkauk0ga1C%2FWrM%3D',
            '4665574'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727488476-764c6ee319504c5a902a3f07b2930432.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=BVAbC1YNEfAXTbXN1X6JJVt3KAY%3D',
            '4665588'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727489839-54a74a6b2f094ce89da2b76fbdce4d77.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=hvwlpWvHVkUr7oPJ5n1fbPewbxg%3D',
            '4677121'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727699770-cf330350b9574280a54662bbb1bbc951.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=hobO%2FE3d7fhVgOyrJUlfBiiOorM%3D',
            '4670127'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727750850-4e951c0b87554f33852175ab798591cd.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=dc0xWzlY8Kp%2BYdZgP07FvrdoD44%3D',
            '4677148'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727832783-a60d93d37ede4050810b8cbb322c6b6a.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=WADCO18dJHN3pXlZ1%2FmF5eXFeqs%3D',
            '4677345'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1727923580-a93a4f261c4f4b72a9e99c99502a213f.jpg?Expires=1732428974&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=tOCFoZOJ4tJTf%2F43jV2OQwJ7R5M%3D',
            '4688082'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728016345-bb1b4c7409b940f7801905cc636b758d.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=WpJGQEIgZ%2BPcrXYFtKdPFkEBGUY%3D',
            '4686376'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1728095414-e204f14f6841491a992e81b12e22308d.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=s1%2FsubhPPL3T8ybsn%2FXiRqK97p4%3D',
            '4686172'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728105325-c9efcfd8685e47e097417a89205dd702.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Gc9p2%2BmeI7en9E%2F74VqYQ1M2cfU%3D',
            '4780866'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1731635886-bb1eedf2a9614e5d907de62f7be53800.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=n4LruICITn13j7A4U52UJOpthug%3D',
            '4691516'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728272632-50a179f7f6e14c208019fd886c7276a2.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=4hwYyhUcx%2BHWPMaZKm%2Fy90x%2BlRw%3D',
            '4693563'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728371863-c2a0539301a74d3683c235c059040a8e.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=%2BYlcgxtsk2GVxqP%2BWDKsiEX97Xw%3D',
            '4693501'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728380960-a6580677c5d3410f8390a3a5dbf9b84b.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=V6eIl1x2v5B7W8Bj7TS873rec1w%3D',
            '4701890'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728394220-34a2f69c1348416c8ee0a2b1e4d2d1d4.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=JjHxpF1f4FwD0hMvEEb64nEF%2Bdw%3D',
            '4708833'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1728716858-0c6238cdab254a798b59c96081e244cf.jpg?Expires=1732428975&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=F8kJvN%2BtuZ9torl5%2BzMTwyStL1c%3D',
            '4701757'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728784362-7480ed0c5158453aa7af00c30b027f91.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=SMfkYaHYYJa4J68viwmjji88oks%3D',
            '4707280'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1728833180-87dfcff73d064acbae7c0c6a2de2f4b3.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=mjHWDPWrJhUaVBW86Qy%2Bvry6uDU%3D',
            '4709006'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1729083793-4ebbfecca5dd42048c2b91ff7f4ceabb.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=1tgSbE8bHFdhHaycGrvwSQUO79U%3D',
            '4707278'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729138088-d7142a326f614133965eeff0d8958219.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=1CwM81F7LuCvhHCg03HSew8Ekhk%3D',
            '4707282'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729132897-369bbbba052a4d3db5770ab8790a8b4d.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=soGOEwl4hIqYSUPz6cjC5GJnYqI%3D',
            '4710517'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729218146-41b49bd609c84852a7ea0250bc4b1296.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=PU33S33bktZNQfCgPmPdU7MVQgI%3D',
            '4711874'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729253817-54c230a079d746e79cafc9343e748094.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=gUvkHSynytuZJuMkOYGiDIYuRwI%3D',
            '4721168'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729354458-fb040177c6fc44efbb2c0b39e311df09.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=byf%2B9Cif%2FeZ8FbrgjsTqRamKpqo%3D',
            '4720992'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1729433883-183a134601c54400b8a9382b40cc8185.jpg?Expires=1732428976&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=8ydZxp0LBsyRx3n9mQyFsu5%2FWII%3D',
            '4720984'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1729512257-e49c26c7bfcc4b5cbe6b5ef2aace466b.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=qECiyqHp1xWN6WcSedw6hibQcJs%3D',
            '4738974'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730135199-bfb9d03315ab4a7eafde952ae9e04e07.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=D0DaR2h65oAvER0PCjp16%2BrUFAU%3D',
            '4738937'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730103777-4aec9df27e72428e948a458c473677e1.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=MQ5UOggjIxuIooKXwrm97TrVo2A%3D',
            '4735808'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730163919-b24686706dfb4220b243189f0d996bdb.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=WjXlOVH%2FdmYNkcH5z803cAY%2F5lc%3D',
            '4737316'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730217471-ec003fc323a4403c931699d5c7fb3fc7.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=bOcgM0IkQD0F3eeFRZTuCEbOGp0%3D',
            '4745508'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730654790-69bd683251ad4fa2825511a965859dbe.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=LUEcO9pAbWDoQaV1J24TgPpf%2Ff4%3D',
            '4738922'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730341716-75f4a4a340914ea3ac20c7af09e76985.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=tFGljQ2IbB%2Btb7KtxT%2BDVpF0P3k%3D',
            '4740722'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730377567-a4b3dadc6016494d8c65f51f0340285c.jpg?Expires=1732428977&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=VDggepyOGZZgz14OgJwY03mCBSU%3D',
            '4745511'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730381526-89062d2136044a689f74fdac68446368.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=fdtJ%2BPBS7je8GVY0un4gvV7RUG0%3D',
            '4745501'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1730440397-9951158d0be848af82e42fd2087e57b7.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=AyDeztMPaGwmE7eEoPO5Nh%2Fx9Sw%3D',
            '4745481'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730557332-32b48b530e0742c5bca61d28f624c611.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=qaJep1qBHkt7x6LyMKTB53yftOw%3D',
            '4744067'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730602177-c99c023f6eeb407da6af407e79729bca.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=CaXVnoblTCPYbfx99Y%2F0ZpJv2Uw%3D',
            '4744054'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730603800-6d48875d22fe498a8ff2eb292cbbd382.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=V7zUoJbp%2FdEDvuJgsSg%2BB%2B3eo5k%3D',
            '4747214'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730786787-85ac71dc17ea494c9b9b396d6d732fc4.jpg?Expires=1732428978&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=2JyVpHr2ZKzvozE4ipUzyGisb4g%3D',
            '4749035'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730853644-3adca8bfa7d144e583eb0b3eab562a3f.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=VblwzX%2BtU9HIdwpTbE6v%2BjnR%2FV8%3D',
            '4749135'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730863413-519b3f74248c4612bc3f9d1e3b6f537b.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=uOXImMpgNC5fhU7c1cT38S2Y2pA%3D',
            '4750848'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730943030-296ae58a46f543d79ce9fdbc3a408118.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Ltn3uGqWr1lR1iTwv2AxJNbevYw%3D',
            '4750854'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730949168-b5455ee2cb434685b1be7d7a00aad826.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=SBuPidLBKHIes%2BobYrQgs7tO5%2BI%3D',
            '4750909'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1730958911-058ae896b5124aebbd4662c7532320dd.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=8KcA8YzQwLis2SMPpigEMkamdwU%3D',
            '4752710'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731031788-53b66721facf4030b1b55d4dd409e690.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=tmk7%2FAs9bUjydImZtfgOGbioMxA%3D',
            '4707278'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731040016-4e27710b292941fb944d27889834cb71.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=pTZfXlizIoNsBMqdnQDWFIQ2LX4%3D',
            '4754530'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731089174-c5fc3f61cdc840adbe1f6005e670efac.jpg?Expires=1732428979&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=pGfyK%2BKkTIY1dygMPSb5awUEZlY%3D',
            '4756061'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731212385-84e4a766ef604db5a4a76d5d840c6c1b.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=hcsvAEwfbFjiSZLyZHyvfutFx5s%3D',
            '4757895'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731288894-9e451872cff74930ab4715a927ab4049.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=B3i679ZEnQDxsq9Z0L%2B8Y3Ojzxo%3D',
            '4773860'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1731342338-cb117e496720418c99912350e25d60be.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=BWTaZqY8LNTMcbPp%2B0sFvuyAnio%3D',
            '4778760'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/idPhoto/1731302342-bb35804dd9374134ada05b649b01db8c.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=sM7257xU1RONNDu04ZJGTxdOuIk%3D',
            '4773903'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731332958-adbbbe8301da49faa1bbabfb656459fa.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=%2B%2FCMXyzodrHT6TP3QbGm498H040%3D',
            '4773886'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731379481-d41e0eb7215b4f6f801cf54427a05b3b.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=47l8uVUVV%2F35chE2xyE1QcrYOuk%3D',
            '4780979'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731548706-2c90a5c9dd444932816922bdcf94dfe7.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=tdzcFgEL0x0JfP%2BIMbzBnchV7Uw%3D',
            '4778868'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731550673-75825a0dda5c439585654d1bc312007d.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=azszacmkzr22THG0QKKOI620jzs%3D',
            '4780972'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731551737-47275e40061a42d69d9b7296d9b6348d.jpg?Expires=1732428980&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=f13CjMODajYEMKgS6KKZ9JhFNAw%3D',
            '4796193'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1732031178-fd86091490da40b394e32ae59d32f20b.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Nr%2B7rHoa%2FeDk6dHE6lPXFWe4xOc%3D',
            '4780974'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731643674-d77e7b80128f48b1808e258c7eec67b3.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=Tq9TuoH5pXAqdMg35fkFS%2F3XEI0%3D',
            '4786694'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731652537-50048df1295e4e3fb8e4e1563a1f47c0.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=fL3bU6uvXzg6x%2FOkLVZ1TjuV7hI%3D',
            '4786860'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731730143-dd055bae608b4bff8b81400c03e541d6.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=YnEfJf0oRgVLM6PYjn2fgdIV%2Bgc%3D',
            '4783001'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731735885-71b78117d33c4d319667efe6cbf5b95d.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=bSP9J6Fk65prFaqKGvmwnZOTXco%3D',
            '4786799'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1731892424-aae97021dc1e4d7891b0c62e98c5510e.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=dhvkZxo2l4TEqGftZk0drb66rz8%3D',
            '4790841'=>'https://cd-store-th-pro.oss-ap-southeast-1.aliyuncs.com/certificationAudit/1732069539-c3624d918f44459bad7460ca94854982.jpg?Expires=1732428981&OSSAccessKeyId=LTAI5tRzCXd6ALJNhqHsMEaF&Signature=OnDVsEJPeTeBxmHJKwlvCHCiWuU%3D',
            ];

        if(!empty($arr[$staff_id])){
            $offer_attachment_path = sys_get_temp_dir() . "/" .$staff_id. md5( time()).'face' . '.jpg';
            file_put_contents($offer_attachment_path,file_get_contents($arr[$staff_id]));
            $upload_result = OssHelper::uploadFile($offer_attachment_path, false);
            return $upload_result['object_url'];
        }
        return '';
    }


    public function icCheckAction()
    {

        $t= $this->getTranslation('zh');
        $sql = "select i.staff_info_id,a.work_attendance_path from hr_staff_info as i  inner join staff_work_attendance_attachment as a on i.`staff_info_id`  = a.`staff_info_id` and a.`deleted`  = 0 where i.state = 1 and i.`hire_type`  = 13 and i.is_sub_staff = 0";
        $staffList = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $server = new AttendanceServer('zh-CN',$this->timezone);
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $source      = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
        $countryCode = $server->getCountryCode($source);
        $groupIdList = $server->getRegionList();
        $returnData = [];
        $header[] = 'IC工号';
        $header[] = 'IC底片';
        $header[] = '匹配到正式员工工号';
        $header[] = '匹配到正式员工底片';
        $header[] = '匹配到正式员工在职状态';
        foreach ($staffList as $item) {
            $imageUrl    = $item['work_attendance_path'];
            $staffInfoId = $item['staff_info_id'];
            //拼接图片地址
            $format['bucket'] = $this->config->application->oss_bucket;
            $format['path']   = $imageUrl;
            if (RUNTIME == 'pro') {
                $flag = 1;
            }
            $url = $server->format_oss($format, $flag ?? 0);

            //生成唯一ID
            $nodeProvider = new RandomNodeProvider();
            $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

            $params = [
                "url"           => $url,
                "request_id"    => $uuid,
                "country"       => $countryCode,
                "group_id_list" => $groupIdList,
            ];
            $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $staffInfoId);
            if (empty($result)) {
                $this->logger->write_log(['ic稽查请求超时'=>$params]);
                break;
            }
            $matchStaffImageUrl = '';
            $matchStaffInfoId   = '';
            $matchStaffState    = '';
            //score字段大于等于93分，flash_hr_exist = true
            if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist']) { //发现比对的问题
                //获取匹配到的员工底片
                $matchStaffInfoId   = $result['result']['person_id'];
                $matchStaffImageUrl = $server->get_face_img($matchStaffInfoId);
                $matchStaffInfo =  $staffServer->getStaffById($matchStaffInfoId);
                $matchStaffState = $matchStaffInfo['state'] == 1 && $matchStaffInfo['wait_leave_state'] == 1 ? 4 : $matchStaffInfo['state'];
                $matchStaffState = $t->_('hris_working_state_' . $matchStaffState);
            }
            $tmpItem = [];
            $tmpItem[] = $item['staff_info_id'];
            $tmpItem[] = env('img_prefix').$item['work_attendance_path'];
            $tmpItem[] = $matchStaffInfoId;
            $tmpItem[] = $matchStaffImageUrl;
            $tmpItem[] = $matchStaffState;
            $returnData[] = $tmpItem;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('2_staff_face'));
        $this->logger->write_log(['ic稽查'=>$file_name],'alert');
    }


    public function staffCheck4Action()
    {
        $filePath = BASE_PATH.'/public/check_face.xlsx';

        //获取Excel数据
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            3 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];

        foreach ($excelData as $k => $v) {

            if($k == 0){
                $header = $v;
                $header[] = '工号1';
                $header[] = '工号1上班卡照片';
                $header[] = '工号1上班卡设备';
                $header[] = '工号1下班卡照片';
                $header[] = '工号1下班卡设备';
                $header[] = '工号2';
                $header[] = '工号2上班卡照片';
                $header[] = '工号2上班卡设备';
                $header[] = '工号2下班卡照片';
                $header[] = '工号2下班卡设备';
                $header[] = '工号3';
                $header[] = '工号3上班卡照片';
                $header[] = '工号3上班卡设备';
                $header[] = '工号3下班卡照片';
                $header[] = '工号3下班卡设备';
                continue;
            }
            $date = trim($v[0],"'");


            $tmpStaff = [$staff1,$staff2] = explode('|',$v[1]);
            $staff3 = '';
            if(!empty($v[2])){
                $staff3 = $v[2];
                $tmpStaff[] = $staff3;
            }
            $attendanceInfo = \FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel::find([
                'conditions' => 'staff_info_id in ({staff_info_id:array}) and attendance_date = :attendance_date: ',
                'bind' =>['staff_info_id' => $tmpStaff ,'attendance_date' => $date],
            ])->toArray();
            $attendanceInfo = array_column($attendanceInfo,null,'staff_info_id');
            $v[] = $staff1;
            $v[] = $attendanceInfo[$staff1]['started_path']? env('img_prefix').$attendanceInfo[$staff1]['started_path'] : '';
            $v[] = $attendanceInfo[$staff1]['started_clientid']."\t";
            $v[] = $attendanceInfo[$staff1]['end_path']? env('img_prefix').$attendanceInfo[$staff1]['end_path'] : '';
            $v[] = $attendanceInfo[$staff1]['end_clientid']."\t";;
            $v[] = $staff2;
            $v[] = $attendanceInfo[$staff2]['started_path']? env('img_prefix').$attendanceInfo[$staff2]['started_path'] : '';
            $v[] = $attendanceInfo[$staff2]['started_clientid']."\t";;
            $v[] = $attendanceInfo[$staff2]['end_path']? env('img_prefix').$attendanceInfo[$staff2]['end_path'] : '';
            $v[] = $attendanceInfo[$staff2]['end_clientid']."\t";;
            $v[] = $staff3;
            $v[] = $attendanceInfo[$staff3]['started_path']? env('img_prefix').$attendanceInfo[$staff3]['started_path'] : '';
            $v[] = $attendanceInfo[$staff3]['started_clientid']."\t";;
            $v[] = $attendanceInfo[$staff3]['end_path']? env('img_prefix').$attendanceInfo[$staff3]['end_path'] : '';
            $v[] = $attendanceInfo[$staff3]['end_clientid']."\t";;
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('3_staff_face'));
        $this->logger->write_log(['打卡信息'=>$file_name],'alert');
    }


    public function checkFormalAction($params)
    {
        $max_staff_info_id = $params[0]??0;

        $t= $this->getTranslation('zh');
        $sql = "select i.staff_info_id,a.work_attendance_path from hr_staff_info as i  inner join staff_work_attendance_attachment as a on i.`staff_info_id`  = a.`staff_info_id` and a.`deleted`  = 0 where i.state = 1 and  i.is_sub_staff = 0 and i.staff_info_id > $max_staff_info_id order by i.staff_info_id asc limit 15000";
        $staffList = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $server = new AttendanceServer('zh-CN',$this->timezone);
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $source      = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
        $countryCode = $server->getCountryCode($source);
        $groupIdList = $server->getRegionList();
        $returnData = [];
        $header[] = '工号';
        $header[] = '底片';
        $header[] = '匹配到其他员工工号';
        $header[] = '匹配到其他员工底片';
        $header[] = '匹配到其他员工在职状态';
        foreach ($staffList as $item) {
            $imageUrl    = $item['work_attendance_path'];
            $staffInfoId = $item['staff_info_id'];
            //拼接图片地址
            $format['bucket'] = $this->config->application->oss_bucket;
            $format['path']   = $imageUrl;
            if (RUNTIME == 'pro') {
                $flag = 1;
            }
            $url = $server->format_oss($format, $flag ?? 0);

            //生成唯一ID
            $nodeProvider = new RandomNodeProvider();
            $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

            $params = [
                "url"           => $url,
                "request_id"    => $uuid,
                "country"       => $countryCode,
                "group_id_list" => $groupIdList,
            ];
            $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $staffInfoId);
            if (empty($result)) {
                $this->logger->write_log(['正式员工稽查异常'=>$params]);
                break;
            }
            $matchStaffImageUrl = '';
            $matchStaffInfoId   = '';
            $matchStaffState    = '';
            //score字段大于等于93分，flash_hr_exist = true
            if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist'] && $result['result']['person_id'] != $item['staff_info_id']) { //发现比对的问题
                //获取匹配到的员工底片
                $matchStaffInfoId   = $result['result']['person_id'];
                $matchStaffImageUrl = $server->get_face_img($matchStaffInfoId);
                $matchStaffInfo =  $staffServer->getStaffById($matchStaffInfoId);
                $matchStaffState = $matchStaffInfo['state'] == 1 && $matchStaffInfo['wait_leave_state'] == 1 ? 4 : $matchStaffInfo['state'];
                $matchStaffState = $t->_('hris_working_state_' . $matchStaffState);
            }
            $tmpItem = [];
            $tmpItem[] = $item['staff_info_id'];
            $tmpItem[] = env('img_prefix').$item['work_attendance_path'];
            $tmpItem[] = $matchStaffInfoId;
            $tmpItem[] = $matchStaffImageUrl;
            $tmpItem[] = $matchStaffState;
            $returnData[] = $tmpItem;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('formal_staff_face'));
        $this->logger->write_log(['全员稽查'=>$file_name,'max_staff_info_id'=>$max_staff_info_id],'alert');
    }


    /**
     * ic作弊
     * @return void
     * @throws \FlashExpress\bi\App\library\Exception\ValidationException
     */
    public function staffCheck5Action()
    {
        $filePath = BASE_PATH.'/public/2025-01-ic.xlsx';
        $t= $this->getTranslation('zh');
        //获取Excel数据
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            2 => \Vtiful\Kernel\Excel::TYPE_STRING,
            3 => \Vtiful\Kernel\Excel::TYPE_STRING,
            4 => \Vtiful\Kernel\Excel::TYPE_STRING,
            5 => \Vtiful\Kernel\Excel::TYPE_STRING,
            6 => \Vtiful\Kernel\Excel::TYPE_STRING,
            7 => \Vtiful\Kernel\Excel::TYPE_STRING,
            8 => \Vtiful\Kernel\Excel::TYPE_STRING,
            9 => \Vtiful\Kernel\Excel::TYPE_STRING,
            10 => \Vtiful\Kernel\Excel::TYPE_STRING,
            11 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $storeServer = (new SysStoreServer());
        foreach ($excelData as $k => $v) {
            if($k == 0){
                $header = $v;
                $header[] = '上班卡是否是同一人';
                $header[] = '差异值';
                $header[] = '下班卡是否是同一人';
                $header[] = '差异值';
                $header[] = '作弊提交图片与工号2上班卡是否是同一人';
                $header[] = '差异值';
                $header[] = '作弊提交图片与工号2下班卡是否是同一人';
                $header[] = '差异值';
                $header[] = '工号1编制类型';
                $header[] = '工号1在职状态';
                $header[] = '工号1雇佣类型';
                $header[] = '工号1部门';
                $header[] = '工号1职位';
                $header[] = '工号1网点';
                $header[] = '工号1片区';
                $header[] = '工号1大区';
                $header[] = '工号2编制类型';
                $header[] = '工号2在职状态';
                $header[] = '工号2雇佣类型';
                $header[] = '工号2部门';
                $header[] = '工号2职位';
                $header[] = '工号2网点';
                $header[] = '工号2片区';
                $header[] = '工号2大区';
                continue;
            }
            $checkFace = explode(',',$v[2])[0];
            [$res,$distance] = $this->checkStaffByUrlV2('',$v[0],$v[4],$v[9]);
            $v[] = $res;
            $v[] = $distance;

            [$res,$distance] = $this->checkStaffByUrlV2('',$v[0],$v[6],$v[11]);
            $v[] = $res;
            $v[] = $distance;

            [$res,$distance]= $this->checkStaffByUrlV2('',$v[0],$checkFace,$v[9]);
            $v[] = $res;
            $v[] = $distance;

            [$res,$distance] = $this->checkStaffByUrlV2('',$v[0],$checkFace,$v[11]);
            $v[] = $res;
            $v[] = $distance;

            $tmpStaff = [$staff1, $staff2] = [$v[0],$v[7]];


            $staffInfos  = $staffServer->getStaffInfoList($tmpStaff);
            $staff1state = $staffInfos[$staff1]['state'] == 1 && $staffInfos[$staff1]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff1]['state'];
            $v[]         = $staffInfos[$staff1]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff1state);
            $v[]         = $staffInfos[$staff1]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff1]['hire_type']) : '';
            $v[]         = $staffInfos[$staff1]['department_name'];
            $v[]         = $staffInfos[$staff1]['job_name'];
            $v[]         = $staffInfos[$staff1]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff1]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];

            $staff2state = $staffInfos[$staff2]['state'] == 1 && $staffInfos[$staff2]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff2]['state'];
            $v[]         = $staffInfos[$staff2]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff2state);
            $v[]         = $staffInfos[$staff2]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff2]['hire_type']) : '';
            $v[]         = $staffInfos[$staff2]['department_name'];
            $v[]         = $staffInfos[$staff2]['job_name'];
            $v[]         = $staffInfos[$staff2]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff2]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('4_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['ic-formal打卡信息'=>$file_name],'alert');
    }

    protected function checkStaffByUrl($date,$staff1, $currentUrl1, $currentUrl2)
    {
        $verifyServer = new \FlashExpress\bi\App\Server\AttendanceImageVerifyServer('zh','+07:00');

        if (empty($currentUrl1) || empty($currentUrl2)) {
            return '照片不全';
        }
        $res = $verifyServer->internalAiInterFace($staff1, $currentUrl1, $currentUrl2);

        if ($res['score'] == 100) {
            return '是';
        }
        return'否';

    }

    protected function checkStaffByUrlV2($date,$staff1, $currentUrl1, $currentUrl2)
    {
        $verifyServer = new \FlashExpress\bi\App\Server\AttendanceImageVerifyServer('zh','+07:00');

        if (empty($currentUrl1) || empty($currentUrl2)) {
            return ['照片不全',''];
        }
        $res = $verifyServer->internalAiInterFace($staff1, $currentUrl1, $currentUrl2);
        if ($res['score'] == 100) {
            if ($res['distance'] <= 0.7) {
                return ['是', $res['distance']];
            } elseif ($res['distance'] <= 1.2) {
                return ['疑似', $res['distance']];
            } else {
                return ['可能不是', $res['distance']];
            }
        }
        return ['否', $res['distance']??''];
    }

    public function staffCheck123Action()
    {
        $filePath = BASE_PATH.'/public/123_check_face.xlsx';

        //获取Excel数据
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            2 => \Vtiful\Kernel\Excel::TYPE_STRING,
            3 => \Vtiful\Kernel\Excel::TYPE_STRING,
            4 => \Vtiful\Kernel\Excel::TYPE_STRING,
            5 => \Vtiful\Kernel\Excel::TYPE_STRING,
            6 => \Vtiful\Kernel\Excel::TYPE_STRING,
            7 => \Vtiful\Kernel\Excel::TYPE_STRING,
            8 => \Vtiful\Kernel\Excel::TYPE_STRING,
            9 => \Vtiful\Kernel\Excel::TYPE_STRING,
            10 => \Vtiful\Kernel\Excel::TYPE_STRING,
            11 => \Vtiful\Kernel\Excel::TYPE_STRING,
            12 => \Vtiful\Kernel\Excel::TYPE_STRING,
            13 => \Vtiful\Kernel\Excel::TYPE_STRING,
            14 => \Vtiful\Kernel\Excel::TYPE_STRING,
            15 => \Vtiful\Kernel\Excel::TYPE_STRING,
            16 => \Vtiful\Kernel\Excel::TYPE_STRING,
            17 => \Vtiful\Kernel\Excel::TYPE_STRING,
            18 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];

        foreach ($excelData as $k => $v) {
            if($k == 0){
                $header = $v;
                $header[] = '工号1、工号2上班卡是否是同一人';
                $header[] = '工号1、工号2下班卡是否是同一人';
                $header[] = '工号1、工号3上班卡是否是同一人';
                $header[] = '工号1、工号3下班卡是否是同一人';
                continue;
            }
            $v[0] = date('Y-m-d',$v[0]);
            $v[4] = $this->map($v[0],$v[3],$v[4]);
            $v[] = $this->checkStaffByUrl($v[0],$v[3],$v[4],$v[9]);
            $v[] = $this->checkStaffByUrl($v[0],$v[3],$v[6],$v[11]);
            $v[] = $this->checkStaffByUrl($v[0],$v[3],$v[4],$v[14]);
            $v[] = $this->checkStaffByUrl($v[0],$v[3],$v[6],$v[16]);
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('4_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['ic-formal打卡信息'=>$file_name],'alert');
    }

    protected function map($date,$staff_id,$url)
    {
        $offer_attachment_path = sys_get_temp_dir() . "/" .$staff_id. md5( time()).rand(10,10000).'face' . '.jpg';
        file_put_contents($offer_attachment_path,file_get_contents($url));
        $upload_result = OssHelper::uploadFile($offer_attachment_path, false);
        return $upload_result['object_url'];

    }

    public function checkStaffAction()
    {
        $dateList = \FlashExpress\bi\App\library\DateHelper::DateRange(strtotime('2024-12-01'),
            strtotime('2024-12-19'));
        $excelDataTmp = [
            '129057|2000111',
            '132907|119069',
            '195927|2000972',
            '332544|119695',
            '399657|168597',
            '441288|210013',
            '448076|211138',
            '456700|228648',
            '456701|155399',
            '464410|245144',
            '464421|189298',
            '2000117|2000362',
        ];
        $excelData[]  = ['日期', '待处理工号'];
        foreach ($excelDataTmp as $excelDatum) {
            foreach ($dateList as $date) {
                $excelData[] = [$date, $excelDatum];
            }
        }
        $returnData = [];
        $t= $this->getTranslation('zh');
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $storeServer = (new SysStoreServer());
        foreach ($excelData as $k => $v) {
            if ($k == 0) {
                $header   = $v;
                $header[] = '工号1';
                $header[] = '工号1编制类型';
                $header[] = '工号1在职状态';
                $header[] = '工号1雇佣类型';
                $header[] = '工号1部门';
                $header[] = '工号1职位';
                $header[] = '工号1网点';
                $header[] = '工号1片区';
                $header[] = '工号1大区';
                $header[] = '工号2';
                $header[] = '工号2编制类型';
                $header[] = '工号2在职状态';
                $header[] = '工号2雇佣类型';
                $header[] = '工号2部门';
                $header[] = '工号2职位';
                $header[] = '工号2网点';
                $header[] = '工号2片区';
                $header[] = '工号2大区';
                $header[] = '工号1上班卡照片';
                $header[] = '工号2上班卡照片';
                $header[] = '上班是否同一个人脸';
                $header[] = '差异值';
                $header[] = '工号1上班卡设备';
                $header[] = '工号2上班卡设备';
                $header[] = '工号1下班卡照片';
                $header[] = '工号2下班卡照片';
                $header[] = '工号1下班卡设备';
                $header[] = '工号2下班卡设备';
                $header[] = '下班是否同一个人脸';
                $header[] = '差异值';
                continue;
            }
            $date = trim($v[0], "'");

            $tmpStaff = [$staff1, $staff2] = explode('|', $v[1]);

            $attendanceInfo = \FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel::find([
                'conditions' => 'staff_info_id in ({staff_info_id:array}) and attendance_date = :attendance_date: ',
                'bind'       => ['staff_info_id' => $tmpStaff, 'attendance_date' => $date],
            ])->toArray();
            $attendanceInfo = array_column($attendanceInfo, null, 'staff_info_id');

            $v[]         = $staff1;
            $staffInfos  = $staffServer->getStaffInfoList($tmpStaff);
            $staff1state = $staffInfos[$staff1]['state'] == 1 && $staffInfos[$staff1]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff1]['state'];
            $v[]         = $staffInfos[$staff1]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff1state);
            $v[]         = $staffInfos[$staff1]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff1]['hire_type']) : '';
            $v[]         = $staffInfos[$staff1]['department_name'];
            $v[]         = $staffInfos[$staff1]['job_name'];
            $v[]         = $staffInfos[$staff1]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff1]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];

            $staff2state = $staffInfos[$staff2]['state'] == 1 && $staffInfos[$staff2]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff2]['state'];
            $v[]         = $staff2;
            $v[]         = $staffInfos[$staff2]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff2state);
            $v[]         = $staffInfos[$staff2]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff2]['hire_type']) : '';
            $v[]         = $staffInfos[$staff2]['department_name'];
            $v[]         = $staffInfos[$staff2]['job_name'];
            $v[]         = $staffInfos[$staff2]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff2]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];

            if (empty($attendanceInfo)) {
                $returnData[] = $v;
                continue;
            }
            $s_1_in       = $attendanceInfo[$staff1]['started_path'] ? env('img_prefix') . $attendanceInfo[$staff1]['started_path'] : '';
            $s_1_out      = $attendanceInfo[$staff1]['end_path'] ? env('img_prefix') . $attendanceInfo[$staff1]['end_path'] : '';
            $s_2_in       = $attendanceInfo[$staff2]['started_path'] ? env('img_prefix') . $attendanceInfo[$staff2]['started_path'] : '';
            $s_2_out      = $attendanceInfo[$staff2]['end_path'] ? env('img_prefix') . $attendanceInfo[$staff2]['end_path'] : '';
            $v[]          = $s_1_in;
            $v[]          = $s_2_in;
            [$res,$distance]          = $this->checkStaffByUrlV2($v[0], $staff1, $s_1_in, $s_2_in);
            $v[] = $res;
            $v[] = $distance;
            $v[]          = $attendanceInfo[$staff1]['started_clientid'] . "\t";
            $v[]          = $attendanceInfo[$staff2]['started_clientid'] . "\t";
            $v[]          = $s_1_out;
            $v[]          = $s_2_out;
            $v[]          = $attendanceInfo[$staff1]['end_clientid'] . "\t";
            $v[]          = $attendanceInfo[$staff2]['end_clientid'] . "\t";
            [$res,$distance]          =  $this->checkStaffByUrlV2($v[0], $staff1, $s_1_out, $s_2_out);
            $v[] = $res;
            $v[] = $distance;
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('3_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['打卡信息加比对' => $file_name], 'alert');
    }


    public function addInfo1Action($params)
    {

        $filePath = BASE_PATH . "/public/{$params[0]}.xlsx";

        //获取Excel数据
        $setColumnType = [
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            7 => \Vtiful\Kernel\Excel::TYPE_STRING,
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);

        $returnData = [];
        $t= $this->getTranslation('zh');
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $storeServer = (new SysStoreServer());
        foreach ($excelData as $k => $v) {
            if ($k == 0) {
                $header   = $v;
                $header[] = '工号1编制类型';
                $header[] = '工号1在职状态';
                $header[] = '工号1雇佣类型';
                $header[] = '工号1部门';
                $header[] = '工号1职位';
                $header[] = '工号1网点';
                $header[] = '工号1片区';
                $header[] = '工号1大区';
                $header[] = '工号2编制类型';
                $header[] = '工号2在职状态';
                $header[] = '工号2雇佣类型';
                $header[] = '工号2部门';
                $header[] = '工号2职位';
                $header[] = '工号2网点';
                $header[] = '工号2片区';
                $header[] = '工号2大区';
                continue;
            }

            $tmpStaff = [$staff1, $staff2] = [$v[0],$v[7]];


            $staffInfos  = $staffServer->getStaffInfoList($tmpStaff);
            $staff1state = $staffInfos[$staff1]['state'] == 1 && $staffInfos[$staff1]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff1]['state'];
            $v[]         = $staffInfos[$staff1]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff1state);
            $v[]         = $staffInfos[$staff1]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff1]['hire_type']) : '';
            $v[]         = $staffInfos[$staff1]['department_name'];
            $v[]         = $staffInfos[$staff1]['job_name'];
            $v[]         = $staffInfos[$staff1]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff1]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];

            $staff2state = $staffInfos[$staff2]['state'] == 1 && $staffInfos[$staff2]['wait_leave_state'] == 1 ? 4 : $staffInfos[$staff2]['state'];
            $v[]         = $staffInfos[$staff2]['formal'];
            $v[]         = $t->_('hris_working_state_' . $staff2state);
            $v[]         = $staffInfos[$staff2]['hire_type'] ? $t->_('hire_type_' . $staffInfos[$staff2]['hire_type']) : '';
            $v[]         = $staffInfos[$staff2]['department_name'];
            $v[]         = $staffInfos[$staff2]['job_name'];
            $v[]         = $staffInfos[$staff2]['store_name'];
            $storeInfo = $storeServer->getStorePieceAndRegionInfo($staffInfos[$staff2]['sys_store_id']);
            $v[]         = $storeInfo['piece_name'];
            $v[]         = $storeInfo['region_name'];
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('3_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['打卡信息加比对' => $file_name], 'alert');
    }

    public function uploadImgAction()
    {
        $filePath = BASE_PATH.'/public/4640775.xlsx';

        //获取Excel数据
        $setColumnType = [
        ];
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $excelData = $baseService->getExcelData($filePath,$setColumnType);
        $returnData = $header = [];

        foreach ($excelData as $k => $v) {
            if($k == 0){
                $header = $v;
                $header[] = '新地址';
                continue;
            }
            if(empty($v[0])){
                break;
            }
            $v[3] = $this->map($v[1],$v[0],$v[2]);
            $returnData[] = $v;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('4_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['ic-formal打卡信息'=>$file_name],'alert');
    }

    public function checkFaceAction()
    {
        $filePath = BASE_PATH.'/public/4640775.xlsx';

        $t= $this->getTranslation('zh');
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
        ];
        $staffList = $baseService->getExcelData($filePath,$setColumnType);

        $server = new AttendanceServer('zh-CN',$this->timezone);
        $staffServer = new \FlashExpress\bi\App\Server\StaffServer();
        $source      = AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT;
        $countryCode = $server->getCountryCode($source);
        $groupIdList = $server->getRegionList();
        $returnData = [];

        foreach ($staffList as $key => $item) {
            if($key == 0 ){
                $header = $item;
                $header[] = '匹配到其他员工工号';
                $header[] = '匹配到其他员工底片';
                $header[] = '匹配到其他员工在职状态';
                continue;
            }
            if(empty($item[0])){
                break;
            }
            $url    = $item[2];
            $staffInfoId = $item[0];

            //生成唯一ID
            $nodeProvider = new RandomNodeProvider();
            $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

            $params = [
                "url"           => $url,
                "request_id"    => $uuid,
                "country"       => $countryCode,
                "group_id_list" => $groupIdList,
            ];
            $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $staffInfoId);
            if (empty($result)) {
                $this->logger->write_log(['正式员工稽查异常'=>$params]);
                break;
            }
            $matchStaffImageUrl = '';
            $matchStaffInfoId   = '';
            $matchStaffState    = '';
            //score字段大于等于93分，flash_hr_exist = true
            if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist'] && $result['result']['person_id'] != $item['staff_info_id']) { //发现比对的问题
                //获取匹配到的员工底片
                $matchStaffInfoId   = $result['result']['person_id'];
                $matchStaffImageUrl = $server->get_face_img($matchStaffInfoId);
                $matchStaffInfo =  $staffServer->getStaffById($matchStaffInfoId);
                $matchStaffState = $matchStaffInfo['state'] == 1 && $matchStaffInfo['wait_leave_state'] == 1 ? 4 : $matchStaffInfo['state'];
                $matchStaffState = $t->_('hris_working_state_' . $matchStaffState);
            }
            $item[1] = date('Y-m-d',$item[1]);
            $item[3] = $matchStaffInfoId;
            $item[4] = $matchStaffImageUrl;
            $item[5] = $matchStaffState;
            $returnData[] = $item;
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('formal_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['fileCheck'=>$file_name],'alert');
    }

    public function checkFace2Action()
    {
        $filePath = BASE_PATH.'/public/4640775.xlsx';

        $t= $this->getTranslation('zh');
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,
            1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
        ];
        $staffList = $baseService->getExcelData($filePath,$setColumnType);

        $server = new AttendanceServer('zh-CN',$this->timezone);

        $returnData = [];

        foreach ($staffList as $key => $item) {
            if($key == 0 ){
                $header = $item;
                $header[] = '同网点其他员工工号';
                $header[] = '同网点其他员工打卡照片';
                $header[] = '打卡照片是否为同一个人';
                $header[] = '差异值';
                continue;
            }
            if(empty($item[0])){
                break;
            }
            $item[1] = $date = date('Y-m-d',$item[1]);
            $attendanceInfoList = \FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel::find([
                'conditions' => 'organization_id = "TH67020103" and attendance_date = :attendance_date: ',
                'bind'       => ['attendance_date' => $date],
            ])->toArray();
            $url    = $item[2];
            $staffInfoId = $item[0];


            foreach ($attendanceInfoList as $value) {
                $_item = $item;
                $imageUrl    = $value['started_path'];
                $_staffInfoId = $value['staff_info_id'];
                //拼接图片地址
                $format['bucket'] = $this->config->application->oss_bucket;
                $format['path']   = $imageUrl;
                $url2 = $server->format_oss($format, $flag ?? 0);
                [$res,$distance]          =  $this->checkStaffByUrlV2('', $staffInfoId, $url, $url2);

                $_item[3] = $_staffInfoId;
                $_item[4] = $url2;
                $_item[5] = $res;
                $_item[6] = $distance;
                $returnData[] = $_item;
            }
        }
        $file_name = (new OssHelper)->exportSvc($header, $returnData, genFileName('formal_staff_face'));
        var_dump($file_name);
        $this->logger->write_log(['checkFace2Action'=>$file_name],'alert');
    }

    public function addOtAction()
    {
        $filePath = BASE_PATH.'/public/th-ot.xlsx';

        $t= $this->getTranslation('zh');
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $setColumnType = [
            0 => \Vtiful\Kernel\Excel::TYPE_INT,
            1 => \Vtiful\Kernel\Excel::TYPE_INT,
            2 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
        ];
        $otList = $baseService->getExcelData($filePath,$setColumnType);
        $ot_item = [];
        foreach ($otList as $key => $item) {
            if($key == 0){
                continue;
            }

            if(empty($item[0])){
                break;
            }
            $date_at = date('Y-m-d',$item[2]);
            $staffId = $item[0];
            $shiftInfo = HrStaffShiftMiddleDateModel::findFirst([
                'columns' => 'shift_date as date_at,shift_start as start,shift_end as [end],shift_id,shift_type',
                'conditions' => 'staff_info_id = :staff_id: and shift_date = :date_at: and deleted = 0',
                'bind' => ['staff_id' => $staffId,'date_at' => $date_at],
            ]);
            if (empty($shiftInfo)) {
                $shiftInfo = new HrStaffShiftMiddleDateModel();
                $shiftInfo->start = '09:00';
            }
            $start_time = date('Y-m-d H:i:s',strtotime($date_at .' '.$shiftInfo->start));
            $duration = $item[1];

            $staffInfo = \FlashExpress\bi\App\Models\backyard\HrStaffTransferModel::findFirst(
                [
                    'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                    'bind'       => ['staff_id' => $staffId,'date_at' => $date_at],
                ]
            );
            $type = $item[4];
            $ot_item[] = [
                'staff_id'      => $staffId,
                'type'          => $type,
                'hire_type'     => $staffInfo->hire_type ?? null,
                'start_time'    => $start_time,
                'end_time'      => date('Y-m-d H:i:s', strtotime($start_time) + ($duration * 3600)),
                'reason'        => 'system auto add',
                'reject_reason' => '',
                'job_title'     => $staffInfo->job_title ?? null,
                'sys_store_id'  => $staffInfo->store_id ?? null,
                'state'         => 2,
                'duration'      => $duration,
                'date_at'       => $date_at,
            ];

        }
        (new HrOvertimeModel())->batch_insert($ot_item, BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
    }
    public function checkLeaveAction()
    {
        $filePathBefore = BASE_PATH.'/public/before.xlsx';
        $filePathAfter = BASE_PATH.'/public/after.xlsx';

        $t= $this->getTranslation('zh');
        $baseService = new \FlashExpress\bi\App\Server\BaseServer();
        $setColumnType = [];
        $beforeList = $baseService->getExcelData($filePathBefore,$setColumnType);
        $beforeList = array_column($beforeList, null, 0);
        $afterList = $baseService->getExcelData($filePathAfter,$setColumnType);
        $result = [];
        $staffInfoStaffRepository = new StaffRepository('zh');
        $i = 0;
        $header = ['id','工号','修改后额度','修改后剩余额度','使用额度','修改前额度','修改后展示额度','修改前展示额度','差额','增加份额','created_at','失效日期','失效状态','入职日期','姓名','部门','职位','网点','在职状态'];
        foreach ($afterList as $key => $item) {
            if($key == 0){
                continue;
            }
            $i ++;
            if(empty($item[0])){
                break;
            }
            $resultItem= [];
            $resultItem[] = $item[0];
            $resultItem[] = $item[1];
            $resultItem[] = $item[5];//after freeze_days
            $resultItem[] = round($item[5] - $item[7],5);
            $resultItem[] = $item[7];
            $id = $item[0];
            $beforeInfo = $beforeList[$id];
            $resultItem[] = $beforeInfo[5];//before freeze_days
            $resultItem[] =  half_num($item[5]);//show after freeze_days
            $resultItem[] =  half_num($beforeInfo[5]);//show after freeze_days
            $resultItem[] =  half_num($item[5]) - half_num($beforeInfo[5]);
            $resultItem[] =  round($item[5] - $beforeInfo[5],5);
            $resultItem[] = $item[8];

            $staffInfo = $staffInfoStaffRepository->getStaffInfoAllOne( $item[1]);
            $invalidDate         = date('Y-m-d',
                strtotime("{$staffInfo['hire_date']} +{$item[3]} year +89 day"));//失效日期 是结束日期 +89天 算上当天是90天
            $resultItem[] = $invalidDate;
            if('2025-05-31' > $invalidDate){
                $resultItem[] = '已失效';
            }else{
                $resultItem[] = '未失效';
            }
            $resultItem[] =  date('Y-m-d',strtotime($staffInfo['hire_date']));
            $resultItem[] =  $staffInfo['name'];
            $resultItem[] =  $staffInfo['department_name'];
            $resultItem[] =  $staffInfo['job_title_name'];
            $resultItem[] =  $staffInfo['store_name'];
            $staff1state  = $staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1 ? 4 : $staffInfo['state'];
            $resultItem[] = $t->_('hris_working_state_' . $staff1state);
            $result[] = $resultItem;
            if($i > 5){
               // break;
            }
        }
        $file_name = (new OssHelper)->exportSvc($header, $result, genFileName('checkLeaveAction'));
        var_dump($file_name);
        $this->logger->write_log(['checkLeaveAction'=>$file_name],'alert');
    }

}