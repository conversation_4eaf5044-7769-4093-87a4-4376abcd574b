<?php

use App\Country\Tools;
use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\JobTransferSpecialModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\JobTransferConfirmServer;
use FlashExpress\bi\App\Server\JobTransferMessageServer;
use FlashExpress\bi\App\Server\JobtransferServer;
use FlashExpress\bi\App\Server\JobTransferV2Server;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\PushServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Traits\FactoryTrait;

class JobtransferTask extends BaseTask
{
    use FactoryTrait;

    /**
     * 转岗
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function syncjobtransferAction($args)
    {
        
        $jobTransferStaffIds = [];
        if (isset($args[0]) && $args[0]) {
            $jobTransferStaffIds = $args[0];
        }

        try {
            $objPublic        = new PublicRepository();
            $jobtransferObj=Tools::reBuildCountryInstance(new JobtransferServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
            $appServer        = new ApprovalServer($this->lang, $this->timezone);
            $date             = date("Y-m-d");
            $tomorrow         = date("Y-m-d", strtotime("+1 day"));
            $tomorrowData     = $jobtransferObj->getJobtransferList([
                "after_date" => $tomorrow,
            ]);
            $toThree         = date("Y-m-d", strtotime("+3 day"));
            $toThreeData = $jobtransferObj->getJobtransferList([
                                                                        "after_date" => $toThree,
                                                                    ]);
            if (!$tomorrowData) {
                $this->getDI()->get('logger')->write_log("明天没有需要转岗的员工:" . $date,"info");
            }
            if (!$toThreeData) {
                $this->getDI()->get('logger')->write_log("3天后的那一天没有需要转岗的员工:" . $toThree,"info");
            }

            $blackList = (new JobtransferServer($this->lang,$this->timezone))->getBlackList();
            $tomorrowData = array_merge($tomorrowData,$toThreeData);

            if ($tomorrowData) {
                $workflowObj = new WorkflowServer($this->lang, $this->timezone);
                $baseServer = new \FlashExpress\bi\App\Server\BaseServer();
                $staffIds = array_column($tomorrowData, 'current_manager_id');
                $staffLang = (new StaffServer())->getBatchStaffLanguage($staffIds);
                foreach ($tomorrowData as $k => $v) {

                    //获取语言
                    $lang = $staffLang[$v['current_manager_id']] ?? '';

                    //转岗申请人 ผู้ยื่นขอ
                    //转岗后HRBP  HRBP หลังโอนย้าย
                    //内容：您好，您为{工号}{转岗员工姓名}提交的{转岗日期}转岗申请{审批编号}，由{转岗前部门}{转岗前网点}{转岗前职位}转为{转岗后部门}{转岗后网点}{转岗后职位}的转岗申请即将到期，请检查员工的未回款项、工作完成情况。

                    $replaceArea = [
                        '{staff_id}',
                        '{staff_name}',
                        '{after_date}',
                        '{serial_no}',
                        '{current_department_name}',
                        '{current_store_name}',
                        '{current_position_name}',
                        '{after_department_name}',
                        '{after_store_name}',
                        '{after_position_name}',
                    ];
                    $replaceData = [
                        $v['staff_id'],
                        $v['staff_name'],
                        $v['after_date'],
                        $v['serial_no'],
                        $v['current_department_name'],
                        $v['current_store_name'],
                        $v['current_position_name'],
                        $v['after_department_name'],
                        $v['after_store_name'],
                        $v['after_position_name'],
                    ];

                    $hrbp = $workflowObj->findHRBP($v['current_department_id'], ["store_id" => $v['current_store_id']]);
                    if($hrbp) {
                        $hrbp = explode(',', $hrbp);
                        $hrbpStaffLang = (new StaffServer())->getBatchStaffLanguage($hrbp);
                        foreach ($hrbp as $val) {
                            //过滤指定hrbp
                            if(!empty($blackList) && in_array($val,$blackList)){
                                $this->getDI()->get('logger')->write_log("过滤 hrbp 工号:".$val." ，不推送消息", "info");
                                continue;
                            }
                            $hrbpLang = $hrbpStaffLang[$val] ?? '';
                            $remindMsageContentHrbp = str_replace($replaceArea, $replaceData, $baseServer->getTranslation($hrbpLang)->_('job_transfer_handover'));

                            $pushSuccessParam = [
                                'staff_info_id'   => $val,
                                'message_title'   => $baseServer->getTranslation($hrbpLang)->_('job_transfer_handover_title'),
                                'message_content' => $remindMsageContentHrbp,
                                'type'            => 18,
                            ];
                            $msgRes           = $objPublic->pushAndSendMessageToSubmitter($pushSuccessParam);
                            $this->getDI()->get('logger')->write_log("明天或者 3 天后需要转岗的员工推送 hrbp:".$val." ，推送结果:" . $msgRes, "info");
                        }
                    }
                    $remindMsageContent = str_replace($replaceArea, $replaceData, $baseServer->getTranslation($lang)->_('job_transfer_handover'));

                    $checkErrorParam    = [
                        'staff_info_id'   => $v["current_manager_id"],
                        'message_title'   => $baseServer->getTranslation($lang)->_('job_transfer_handover_title'),
                        'message_content' => $remindMsageContent,
                        'type'            => 18,
                    ];
                    $msgRes = $objPublic->pushAndSendMessageToSubmitter($checkErrorParam);
                    $this->getDI()->get('logger')->write_log("明天或者 3 天后需要转岗的员工推送直线上级".$v["current_manager_id"]."，推送结果:" . $msgRes,"info");
                }
            }

            if (empty($jobTransferStaffIds)) { //自动转岗脚本逻辑

                //昕哲需求：
                //转岗日期已经到了，还未审批完成
                //则自动驳回，转岗状态为未转岗
                $overtimeData = $jobtransferObj->getJobtransferList([
                    "after_date"    => $date,
                    "approval_state"=> enums::$audit_status['panding']
                ]);
                if (isset($overtimeData) && !empty($overtimeData) && is_array($overtimeData)) {

                    $logger = $this->getDI()->get('logger');
                    //昕哲需求
                    //到转岗日还待审批的数据，全部系统自动驳回
                    foreach ($overtimeData as $transfer) {

                        $logger->write_log("系统已经自动驳回: {$transfer['submitter_id']}申请的，{$transfer['staff_id']}的转岗申请。", "info");

                        ////操作记录需要的数据
                        if ($transfer['data_source'] == 1) {
                            $type = enums::$audit_type['TF'];
                        } else {
                            $type = enums::$audit_type['TFOA'];
                        }
                        $appServer->reject($transfer['id'],
                            $type,
                            'The next approval node does not agree before the transfer date, and the system automatically reject',
                            10000,
                            ['super' => 1]
                        );
                    }
                }

                $data = $jobtransferObj->getJobtransferList([
                    "after_date" => $date,
                ]);
            } else { //手动补转岗的逻辑
                $data = $jobtransferObj->getJobtransferList([
                    "staff_ids" => $jobTransferStaffIds,
                ]);
            }

            if (!$data) {
                $this->getDI()->get('logger')->write_log("今天没有需要转岗的员工:" . $date,"info");
                return false;
            }
            //立即转岗
            $jobtransferObj->doJobTransfer(['data' => $data]);

            echo "转岗完毕" ,PHP_EOL;
        } catch (Exception $e) {
            echo "转岗同步数据出现异常：" . $e->getMessage();
            $this->getDI()->get('logger')->write_log("转岗同步数据出现异常" . $e->getMessage(),"error");
        }
    }

    /**
     * 发送消息
     */
    public function pushAction()
    {
        try {
            echo "开始执行转岗审批提醒推送任务", PHP_EOL;

            //获取待审批列表
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('group_concat(jt.serial_no) as serial_no, aa.approval_id');
            $builder->from(['jt' => JobTransferModel::class]);
            $builder->join(AuditApprovalModel::class, 'aa.biz_value = jt.id and aa.biz_type = 20', 'aa');
            $builder->where('aa.state = 1 and aa.deleted = 0');
            $builder->groupBy('aa.approval_id');
            $pushList = $builder->getQuery()->execute()->toArray();

            if (empty($pushList)) {
                //无待审批
                $this->getDI()->get('logger')->write_log("pushAction：无push数据 日期:" . date("Y-m-d") ,"info");
                echo "pushAction：无push数据 日期:" . date("Y-m-d");
                return false;
            }

            foreach ($pushList as $v) {
                echo sprintf("审批人 %s, 待审批序列号 %s", $v['approval_id'], $v['serial_no']), PHP_EOL;
            }
            $pushList = array_column($pushList, 'serial_no', 'approval_id');

            foreach ($pushList as $staff_id => $value) {
                //获取接收人的语言环境
                $staffAccount = StaffAccountModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: and equipment_type in ({equipment_types:array})',
                    'bind' => ['staff_id' => $staff_id, 'equipment_types' => [LangEnums::$equipment_type['kit'], LangEnums::$equipment_type['backyard']]],
                    'order' => 'updated_at desc',
                ]);
                if (isset($staffAccount->accept_language) && $staffAccount->accept_language) {
                    if (in_array($staffAccount->accept_language, ['th', 'th-CN'])) {
                        $this->lang = 'th';
                    } else {
                        $this->lang = $staffAccount->accept_language ?? 'en';
                    }
                } else {
                    $this->lang = 'en';
                }
                echo "审批人 {$staff_id} 推送语言为：" . $this->lang, PHP_EOL;

                //拼接模板
                $t = $this->getTranslation();
                $content = $t->_('job_transfer_notice');
                $title   = $t->_('job_transfer_title');
                $batch   = $t->_('job_transfer_batch'); //批量转刚刚
                $state   = $t->_('audit_status.1'); //待审批
                $oa_url = 'https://oa.flashexpress.com/#/dashboard';

                $jtt_oa_url = SettingEnvModel::findFirst("code = 'job_transfer_task_oa_url'");
                if ($jtt_oa_url && $jtt_oa_url->set_val) {
                    $oa_url = $jtt_oa_url->set_val;
                }
                //拼接模板
                $content .= $oa_url." <br /><br />";

                $serialList = isset($value) && $value ? explode(',', $value) : [];
                if (count($serialList) == 0) {
                    continue;
                }

                foreach ($serialList as $serial) {
                    $content .= sprintf("%s - %s - %s<br />", $batch, $serial, $state);
                }

                //发送消息
                $data = [
                    "staff_info_id"     =>$staff_id,    //提交人ID
                    "title"             => $title . "(". count($serialList) .")",
                    "category"          => 18,          //类别
                    "content"           => $content,    //内容
                ];
                echo "消息内容 :" . json_encode($data) , PHP_EOL;
                $this->getDI()->get('logger')->write_log("转岗审批提醒，请求参数:" . json_encode($data),"info");
                $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
                $fle_rpc->setParams($data);
                $fle_return = $fle_rpc->execute();
                $this->getDI()->get('logger')->write_log("转岗审批提醒，推送结果:" . json_encode($fle_return),"info");
            }

            echo "发送完毕", PHP_EOL;
        } catch (\Exception $e) {
            echo "转岗审批提醒出现异常：" . $e->getMessage() . $e->getTraceAsString();
            $this->getDI()->get('logger')->write_log("转岗审批提醒出现异常" . $e->getMessage() . $e->getTraceAsString(),"notice");
        }
    }

    /**
     * @description 异步插入待确认日志，以在审批详情-审批流中展示待确认节点
     * php app/cli.php jobtransfer async_job_transfer
     */
    public function async_job_transferAction()
    {
        $redis = $this->getDI()->get('redisLib');
        $apply      = new ApplyRepository();
        $server     = new WorkflowServer($this->lang, $this->timezone);
        while ($redis_data = $redis->rpop(RedisEnums::REDIS_ASYNC_JOB_TRANSFER)) {
            try {
                if (empty($redis_data)) {
                    exit('未有同步的数据');
                }

                sleep(2);
                $this->logger->write_log([
                    'function'  => 'async_job_transferAction',
                    'message'   => '异步写入待确认log',
                    'redis_key' => RedisEnums::REDIS_ASYNC_JOB_TRANSFER,
                    'params'    => $redis_data,
                ],'info');
                echo $redis_data . PHP_EOL;

                $params     = json_decode($redis_data, true);
                $requestObj = $apply->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $params['audit_id']);

                //保存日志
                $server->saveAuditLog($requestObj, $params['staff_info_id'], enums::WF_ACTION_CONFIRM_PENDING, null);

            } catch (\Exception $e) {
                $this->logger->write_log(__CLASS__.__METHOD__.' exception:'.$e->getMessage());
                echo date('Y-m-d H:i:s').__METHOD__.$e->getMessage().PHP_EOL;
                continue;
            }
        }
    }

    /**
     * @description 定时转岗V2
     * @param $args
     * @return false
     * php app/cli.php jobtransfer sync_job_transferV2
     */
    public function sync_job_transferV2Action($args)
    {
        $jobTransferStaffIds = [];
        if (isset($args[0]) && $args[0]) {
            $jobTransferStaffIds = $args[0];
        }

        try {
            $jobTransferServer = Tools::reBuildCountryInstance(new JobTransferV2Server($this->lang, $this->timezone),
                [$this->lang, $this->timezone]);
            $date              = date("Y-m-d");

            $specialData = JobTransferSpecialModel::find([
                'conditions' => 'status = :status: and after_date = :after_date:',
                'bind' => [
                    'status'     => enums::APPROVAL_STATUS_PENDING,
                    'after_date' => $date,
                ],
            ])->toArray();
            if (empty($jobTransferStaffIds)) { //自动转岗脚本逻辑
                //转岗日期已经到了，还未审批完成
                //则自动审批超时，转岗状态为未转岗
                $this->time_out($date);

                //超时特殊批量转岗
                if (!empty($specialData)) {
                    $this->time_out_special($specialData);
                }
            }
            $data = $jobTransferServer->getJobtransferList([
                "after_date" => $date,
            ]);
            if (!$data) {
                $this->logger->write_log("今天没有需要转岗的员工:" . $date,"info");
                echo "今天没有需要转岗的员工:" . $date;
                return false;
            }
            //立即转岗
            $jobTransferServer->doJobTransfer(['data' => $data]);

            echo "转岗完毕" ,PHP_EOL;
        } catch (Exception $e) {
            echo "转岗同步数据出现异常：" . $e->getMessage() . $e->getTraceAsString();
            $this->logger->write_log("转岗同步数据出现异常" . $e->getMessage(),"error");
        }
    }

    /**
     * 超时处理未完成的审批
     * @param $date
     * @return bool
     * @throws ReflectionException
     */
    public function time_out($date)
    {
        $jobTransferServer = Tools::reBuildCountryInstance(new JobTransferV2Server($this->lang, $this->timezone),
            [$this->lang, $this->timezone]);
        $overtimeList = $jobTransferServer->getJobtransferList([
            "after_date"     => $date,
            "approval_state" => enums::APPROVAL_STATUS_PENDING,
        ]);
        if (empty($overtimeList)) {
            $this->logger->write_log("今天没有需要超时的员工:" . $date,"info");
            echo "今天({$date})没有需要超时的员工",PHP_EOL;
            return false;
        }
        //超时关闭
        $appServer     = new ApprovalServer($this->lang, $this->timezone);
        $confirmServer = new JobTransferConfirmServer();
        $auditApply    = new ApplyRepository();
        foreach ($overtimeList as $transfer) {
            if ($transfer['type'] == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                continue;
            }
            $detailInfo = JobTransferModel::findFirst($transfer['id']);
            $request = $auditApply->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $transfer['id']);
            $isSecondStageAudit = $confirmServer->isSecondStageAudit($request);

            if ($transfer['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM && !$isSecondStageAudit) {
                $jobTransferServer->processStateToFinal($detailInfo, enums::APPROVAL_STATUS_TIMEOUT, enums::SYSTEM_STAFF_ID);
            } else {
                $appServer->timeOut($transfer['id'],AuditListEnums::APPROVAL_TYPE_JT);
            }
            $this->logger->write_log("系统已经自动超时: {$transfer['submitter_id']}申请的，{$transfer['staff_id']}的转岗申请。", "info");
        }
        return true;
    }

    /**
     * 特殊批量转岗超时关闭
     * @param $data
     * @return void
     */
    private function time_out_special($data)
    {
        //超时关闭
        $appServer = new ApprovalServer($this->lang, $this->timezone);
        foreach ($data as $item) {
            $appServer->timeOut($item['id'], AuditListEnums::APPROVAL_TYPE_JT_SPECIAL);
            $log = sprintf('系统已经自动超时(特殊批量转岗): {%s}申请的，{%s}的转岗申请。', $item['submitter_id'],
                $item['serial_no']);
            $this->logger->write_log($log, "info");
            echo $log, PHP_EOL;
        }
    }

    public function send_job_transfer_special_messageAction()
    {
        $date        = date("Y-m-d");
        $specialData = JobTransferSpecialModel::find([
            'conditions' => 'status = :status: and after_date = :after_date:',
            'bind'       => [
                'status'     => enums::APPROVAL_STATUS_APPROVAL,
                'after_date' => $date,
            ],
            'columns' => 'submitter_id,group_concat(batch_code) as batch_code',
            'group'      => 'submitter_id',
        ])->toArray();

        $addHour     = $this->config->application->add_hour;
        $staffServer = new StaffServer();
        foreach ($specialData as $value) {

            $lang = $staffServer->getLanguage($value['submitter_id']);
            if (empty($value['batch_code'])) {
                continue;
            }
            $jobTransferList = JobTransferSpecialModel::find([
                'conditions' => 'batch_code in({batch_code:array})',
                'bind'       => [
                    'batch_code'     => explode(',', $value['batch_code']),
                ],
                'columns' => 'batch_code,created_at,after_date',
            ])->toArray();

            $contentList = [];
            foreach ($jobTransferList as $item) {
                $successNo     = JobTransferModel::count([
                    'conditions' => 'batch_code = :batch_code: and state = :state: and after_date = :after_date:',
                    'bind'       => [
                        'batch_code' => $item['batch_code'],
                        'state'      => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
                        'after_date' => $date,
                    ],
                ]);
                $failureNo     = JobTransferModel::count([
                    'conditions' => 'batch_code = :batch_code: and state = :state: and after_date = :after_date:',
                    'bind'       => [
                        'batch_code' => $item['batch_code'],
                        'state'      => JobTransferModel::JOBTRANSFER_STATE_TRANSFERE_ERR,
                        'after_date' => $date,
                    ],
                ]);
                $totalNo       = JobTransferModel::count([
                    'conditions' => 'batch_code = :batch_code:',
                    'bind'       => [
                        'batch_code' => $item['batch_code'],
                    ],
                ]);
                $contentList[] = $this->getTranslation($lang)->_('job_transfer.notice_special_transfer_content_row', [
                    'created_at'        => date('Y-m-d H:i:s', strtotime($item['created_at']) + $addHour * 3600),
                    'after_date'        => $item['after_date'],
                    'success_no'        => $successNo ?? 0,
                    'failure_no'        => $failureNo ?? 0,
                    'staff_total_count' => $totalNo ?? 0,
                ]);
            }
            $content         = join('<br/>', $contentList);
            $checkErrorParam = [
                'staff_info_id'   => $value['submitter_id'],
                'message_title'   => $this->getTranslation($lang)->_('job_transfer.notice_special_transfer'),
                'message_content' => $this->getTranslation($lang)->_('job_transfer.notice_special_transfer_content',
                    ['content' => $content]),
                'path'            => 'message_list',
            ];
            PushServer::getInstance($lang, $this->timezone)->pushAndSendMessageToSubmitter($checkErrorParam, $lang);
        }
    }

    public function renewAction($args)
    {
        ini_set('memory_limit', '-1');
        $jobTransferStaffIds = [];
        if (isset($args[0]) && $args[0]) {
            $jobTransferStaffIds = $args[0];
        }

        if (RUNTIME != 'pro') {
            $conditions = 'state = 1 and approval_state = 1 and created_at <= :created_at:';
            $bind['created_at'] = date('Y-m-d 16:00:00', strtotime('-1 day'));
        } else {
            $conditions = 'state = 1 and approval_state = 1 and workflow_role_name = \'job_transfer_new\'';
        }
        if (!empty($jobTransferStaffIds)) {
            $conditions .= ' and staff_id in({staff_ids:array})';
            $bind['staff_ids'] = explode(',', $jobTransferStaffIds);
        }
        $info = JobTransferModel::find([
            'conditions' => $conditions,
            'bind' => $bind ?? [],
        ]);
        
        if (empty($info->toArray())) {
            return;
        }
        $server = $this->class_factory("JobTransferV2Server", $this->lang, $this->timezone);
        $app    = new ApprovalServer($this->lang, $this->timezone);

        foreach ($info as $item) {
            //$db = JobTransferModel::beginTransaction($this);
            try {
                $item->after_manager_id = null;
                $item->approval_state_stage_one = enums::APPROVAL_STATUS_PENDING;
                $item->approval_state_stage_two = enums::APPROVAL_STATUS_PENDING;
                $item->data_source = 1; //所有来源强制刷成by
                $item->save();

                $extend = $server->getParseNodeParams($item->id, $item->submitter_id);
                $params = [
                    'audit_id'     => $item->id,
                    'audit_type'   => AuditListEnums::APPROVAL_TYPE_JT,
                    'submitter_id' => $item->submitter_id,
                    'extend'       => $extend,
                ];
                $app->renew($params);

                //$db->commit();
            } catch (\FlashExpress\bi\App\library\Exception\ValidationException $ve) {
                //$db->rollback();
                echo $ve->getMessage().$ve->getTraceAsString();die;
            }
        }
    }

    /**
     * 发送邮件（转岗后，发送合同失败）
     * php app/cli.php jobtransfer sendJobTransferContractFailEmail
     * @return void
     */
    public function sendJobTransferContractFailEmailAction($args)
    {
        if (empty($args[0])) {
            $date = date('Y-m-d', strtotime('-1 day'));
        } else {
            $date = $args[0];
        }
        echo 'sendJobTransferContractFailEmail start', PHP_EOL;
        $fileName = sprintf('List of failed transfer contracts_%s.xlsx', date('Y-m-d'));
        $header = [
            'ID',
            'Name',
            'Company before transfer',
            'Department before transfer',
            'Area before transfer',
            'District before transfer',
            'Branch before transfer',
            'Job before transfer',
            'Hiring type before transfer',
            'Company after transfer',
            'Department after transfer',
            'Area after transfer',
            'District after transfer',
            'Branch after transfer',
            'Job after transfer',
            'Hiring type after transfer',
            'Actual transfer date',
            'Type of contract that failed to send',
        ];
        $jobTransferData = (new JobTransferV2Server($this->lang, $this->timezone))->generateSendContractFailData($date);
        if (empty($jobTransferData)) {
            $message = sprintf('sendJobTransferContractFailEmail %s , data is empty', $date);
            $this->logger->write_log($message, 'info');
            echo $message, PHP_EOL;
            return;
        }
        $config = [
            'path' => sys_get_temp_dir(),
        ];
        $excel = new \Vtiful\Kernel\Excel($config);
        $fileObject = $excel->fileName($fileName);
        $filePath = $fileObject->header($header)->data($jobTransferData)->output();

        $settingEnv = (new SettingEnvServer())->listByCode(['failed_transfer_contract_email', 'failed_transfer_contract_cc_email']);
        if (!empty($settingEnv)) {
            $settingEnvList = array_column($settingEnv, 'set_val', 'code');
            $settingEnvList['failed_transfer_contract_email'] = explode(',', $settingEnvList['failed_transfer_contract_email']);
            $settingEnvList['failed_transfer_contract_cc_email'] = explode(',', $settingEnvList['failed_transfer_contract_cc_email']);
        }
        $title = 'List of failed transfer contracts';
        $email_content = 'Dear all, 
          <br/>
          <br/>
          Attached is the list of employees whose job transfer contract failed to be sent because the current contract is not effective. Please deal with it as soon as possible.
          <br/>
          <br/>
          Thank you!';

        if (empty($settingEnvList['failed_transfer_contract_email'])) {
            $message = 'sendJobTransferContractFailEmail send to user empty!';
            $this->logger->write_log($message, 'info');
            echo $message, PHP_EOL;
            return;
        }
        $ret   = (new MailServer())->send_mail($settingEnvList['failed_transfer_contract_email'], $title, $email_content, $filePath, $fileName, $settingEnvList['failed_transfer_contract_cc_email']);
        $this->logger->write_log("sendJobTransferContractFailEmail sendMail 发送邮件结束 toUsers:" . implode(',',
                $settingEnvList['failed_transfer_contract_email']) . ', email_content:' . $email_content . ',result:' . $ret, 'info');
        echo "sendJobTransferContractFailEmail send email done!";
    }
}