<?php
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Server\PenaltyAppealServer;

class PenaltyAppealTask extends RocketMqBaseTask
{
    public function initialize()
    {
        $this->tq = "penalty-appeal-add";
        parent::initialize();
    }

    /**
     *  消费 Kit 申诉 创建by审批流
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        $log = $this->getDI()->get('logger');
        try {
            $msgBody = base64_decode($msgBody);
            $log->write_log('PenaltyAppealTask processOneMsg ' . $msgBody,
                'info');
            $messageBody = json_decode($msgBody, true);
            if (empty($messageBody)) {
                return false;
            }
            if (!empty($messageBody['data']) && !empty($messageBody['handleType']) && $messageBody['handleType'] == RocketMQ::TAG_KIT_ADD_APPEAL) {
                $data = (new PenaltyAppealServer($this->lang, $this->timezone))->create($messageBody['data']);
                if (!empty($data['data']['penalty_appeal_id'])) {
                    $log->write_log('PenaltyAppealTask processOneMsg 处理成功 ' . json_encode($data,
                            JSON_UNESCAPED_UNICODE), 'info');
                    $params = [
                        'penalty_appeal_id' => $data['data']['penalty_appeal_id'],
                        'penalty_id'        => $messageBody['data']['penalty_id'] ?? 0,
                    ];
                    $rmq    = new RocketMQ('penalty-appeal-success');
                    $rmq->setHandleType(RocketMQ::TAG_BY_APPEAL_CREATED_SUCCESS);
                    $rid = $rmq->sendToMsg($params, 30);
                    $log->write_log('penalty-appeal-success rid:' . $rid . 'data:' . json_encode($params,
                            JSON_UNESCAPED_UNICODE), $rid ? 'info' : 'error');
                    return true;
                } else {
                    $log->write_log('PenaltyAppealTask processOneMsg 异常 ' . $data['msg'], 'error');
                    return false;
                }
            }
            return true;
        } catch (\Exception $e) {
            $log->write_log('PenaltyAppealTask processOneMsg 异常 ' . $e->getMessage(), 'error');
            return false;
        }
    }
}