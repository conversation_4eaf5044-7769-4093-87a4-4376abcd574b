<?php
/**
 * Author: Bruce
 * Date  : 2021-11-21 17:12
 * Description:
 */

namespace FlashExpress\bi\App\Models\backyard;


class ExtinguisherOperationLogModel extends BackyardBaseModel
{
    protected $table_name = 'extinguisher_operation_log';

    private $id;
    private $extinguisher_id;
    private $content;
    private $type;
    private $operator_id;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @param $extinguisherId
     */
    public function setExtinguisherId($extinguisherId): void
    {
        $this->extinguisher_id  = $extinguisherId;
    }

    /**
     * @param $content
     */
    public function setContent($content): void
    {
        $this->content = $content;
    }

    /**
     * @param $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    /**
     * @param $operatorId
     */
    public function setOperatorId($operatorId): void
    {
        $this->operator_id = $operatorId;
    }
}