<?php


namespace FlashExpress\bi\App\Models\backyard;


class InteriorStaffsShoppingCartModel extends BackyardBaseModel
{
    protected $table_name = 'interior_staffs_shopping_cart';

    protected function relations()
    {
        $this->belongsTo(
            "goods_sku_id",
            InteriorGoodsSkuModel::class,
            "id"
        );
    }

    public function getGoodsSkuInfo($parameters = null)
    {
        $data = $this->getRelated(InteriorGoodsSkuModel::class, $parameters);
        return $data;
    }
}