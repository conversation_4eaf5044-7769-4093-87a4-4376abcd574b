<?php

namespace FlashExpress\bi\App\Models\backyard;

/**
 * 员工薪资税表
 */
class StaffSalaryTaxModel extends BackyardBaseModel
{
    protected $table_name = 'staff_salary_tax';


    //marital枚举 婚姻状况 1单身 2已婚 3Divorce离异 4Child Relief领养儿童
    const SINGLE = 1;
    const MARRIED = 2;
    const DIVORCE = 3;
    const RELIEF = 4;
    public static $maritalEnum = [
        self::SINGLE => 'single',
        self::MARRIED => 'married',
        self::DIVORCE => 'divorce',
    ];

    //是否 枚举
    const YES = 1;
    const NO = 2;

}