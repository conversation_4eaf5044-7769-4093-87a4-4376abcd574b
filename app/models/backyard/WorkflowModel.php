<?php


namespace FlashExpress\bi\App\Models\backyard;


use FlashExpress\bi\App\Enums\AuditListEnums;

class WorkflowModel extends BackyardBaseModel
{
    protected $table_name = 'workflow';

    const WORKFLOW_MANUAL = 0; //手动维护审批流
    const WORKFLOW_VISUALIZE = 1; //可视化界面维护审批流

    const WORKFLOW_NEXT_STAGE_FLOW_ID = 0; //只有一个审批阶段

    /**
     * @description 获取
     * @param $audit_type
     * @param $workflow_id
     * @return mixed
     */
    public function getWorkflowModel($audit_type, $workflow_id = '')
    {
        $conditions = 'type = 1 and relate_type = :type: ';
        $bind       = ['type' => $audit_type];

        //只配置部分审批流的审批，可以传workflow_id进来
        if (!empty($workflow_id) && in_array($audit_type, AuditListEnums::getPartialVisualizeApprovalType())) {
            $conditions          .= " and id = :workflow_id: ";
            $bind['workflow_id'] = $workflow_id;
        }
        return self::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id asc',
        ]);
    }

    /**
     * 获取审批流详情
     * @param $workflow_id
     * @return mixed
     */
    public static function getWorkflowInfo($workflow_id)
    {
        //获取下一个阶段不为空的那个ID
        return WorkflowModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $workflow_id,
            ]
        ]);
    }
}