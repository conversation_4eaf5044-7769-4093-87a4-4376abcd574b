<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/30/23
 * Time: 2:39 PM
 */



namespace FlashExpress\bi\App\Models\backyard;

/**
 * by 活动记录表
 */
class ActivityRecordModel extends BackyardBaseModel
{
    //跟 发送活动的枚举 一样 activityEnums
    const CODE_BIRTHDAY = 'birthday';
    const CODE_ANNIVERSARY = 'anniversary';
    const CODE_FASTING = 'fasting';//开斋节
    const CODE_SONGGAN = 'songgan';//宋干节

    const ALERT_YET = 1;//还没弹出公告
    const ALERT_HAD = 2;//已经弹出公告


    protected $table_name = 'activity_record';
}