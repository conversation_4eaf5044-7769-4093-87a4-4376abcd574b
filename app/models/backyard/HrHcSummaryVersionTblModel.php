<?php


namespace FlashExpress\bi\App\Models\backyard;


class HrHcSummaryVersionTblModel extends BackyardBaseModel
{
    const TASK_STATE_AWAIT_IMPLEMENTATION = 0; //待执行
    const TASK_STATE_FINISHED = 1;             //已完成

    const DATA_TYPE_INCOMPLETE = 0; //未完成数据
    const DATA_TYPE_COMPLETED = 1;  //已完成数据
    const DATA_TYPE_DISCARD = 2;    //丢弃数据

    const SAVE_VERSIONS_MAX_NUM = 3; //最多保存几个版本的数据

    protected $table_name = 'hr_hc_summary_version_tbl';
    public $cache_redis_key = 'lock_hc_general_hc_budget_dept';  //redis  的锁 key
}