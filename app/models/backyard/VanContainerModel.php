<?php

namespace FlashExpress\bi\App\Models\backyard;

class Van<PERSON><PERSON>rModel extends BackyardBaseModel
{
    protected     $table_name = 'van_container';


    const HAVE_ONE = 1;//有车厢
    const HAVE_NO_ONE = 2;//没车厢

    public static $typeList = [
        self::HAVE_ONE    => 'have_container',
        self::HAVE_NO_ONE => 'have_no_container',
    ];


    const SOURCE_TYPE_WIN = 1;//win hr 消息链接 添加 完善信息入口
    const SOURCE_TYPE_BY = 2;//by 自己申请入口
    const SOURCE_TYPE_HCM = 3;//hcm 代申请入口
    const SOURCE_TYPE_HCM_LIST = 4; //HCM无车厢名单

    public static $sourceTypeList = [
        self::SOURCE_TYPE_WIN      => 'source_win',
        self::SOURCE_TYPE_BY       => 'source_by',
        self::SOURCE_TYPE_HCM      => 'source_hcm_audit',
        self::SOURCE_TYPE_HCM_LIST => 'source_hcm_list',
    ];

    /**
     * 是否提交过完善车厢请求
     * @param $staff_info_id
     * @param bool $is_sub_staff 是否子账号 true=子账号，false=主账号
     * @return bool
     */
    public static function hasSubmitVanContainer($staff_info_id, bool $is_sub_staff = false): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }
        $queryStaffIds[] = $staff_info_id;
        if ($is_sub_staff) {
            $subStaffInfo = HrStaffApplySupportStoreModel::findFirst([
                'conditions' => 'sub_staff_info_id = :sub_staff_info_id:',
                'bind' => [
                    'sub_staff_info_id' => $staff_info_id
                ]
            ]);
            if (!empty($subStaffInfo)) {
                $queryStaffIds[] = $subStaffInfo->staff_info_id;
            }
        }

        $vanContainerInfo = self::findFirst([
            'conditions' => 'staff_info_id in ({staff_info_id:array})',
            'bind'       => [
                'staff_info_id' => $queryStaffIds
            ]
        ]);
        return !empty($vanContainerInfo);
    }

}