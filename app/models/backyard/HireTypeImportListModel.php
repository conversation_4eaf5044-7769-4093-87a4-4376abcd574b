<?php


namespace FlashExpress\bi\App\Models\backyard;


class HireTypeImportListModel extends BackyardBaseModel
{
    protected $table_name = 'hire_type_import_list';

    const DATA_TYPE_IC = 1;
    const DATA_TYPE_LNT = 2;

    const IMPORT_STATE_SUCCESS = 1; //成功
    const IMPORT_STATE_FAIL = 2;  //失败

    const STATUS_EMPLOYED       = 1;//已入职
    const STATUS_TO_BE_EMPLOYED = 2;//待入职
    const STATUS_NOT_EMPLOYED   = 3;//未入职

    const SEND_OFFER_PENDING = 0; //待发送
    const SEND_OFFER_HAS_SEND = 1; //已发送
    const SEND_OFFER_TIMED = 2; //定时待发送

    const DATA_SOURCE_WIN_HR = 1; //winhr来源
    const DATA_SOURCE_BY     = 2; //By来源

}