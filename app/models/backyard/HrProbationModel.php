<?php

namespace FlashExpress\bi\App\Models\backyard;

class HrProbationModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation';
    public const STATUS_PROBATION = 1; //试用期
    public const STATUS_PASS      = 2; //已通过
    public const STATUS_NOT_PASS  = 3; //未通过
    public const STATUS_FORMAL    = 4; //已转正

    /**
     * 1第一次评估，2第二次评估
     */
    const CUR_LEVEL_FIRST = 1;
    const CUR_LEVEL_SECOND = 2;
    /**
     * first_audit_status 第一次评估状态 1“待发起”2“评估中”3“已完成”4“已超时”
     * 待发起 在没有生成hr_probation时候给列表使用
     */
    const FIRST_AUDIT_STATUS_WAIT = 1;
    const FIRST_AUDIT_STATUS_RUN = 2;
    const FIRST_AUDIT_STATUS_DONE = 3;
    const FIRST_AUDIT_STATUS_TIMEOUT = 4;
    /**
     * first_status 第一次评估结果 0 未通过  1 通过 (当前评估最后一个人评分决定是否通过)
     */
    const FIRST_STATUS_NOT_PASS = 0;
    const FIRST_STATUS_PASS = 1;

    /**
     * second_audit_status 第二次评估状态 1“待发起”2“评估中”3“已完成”4“已超时”
     * 待发起 在没有生成hr_probation时候给列表使用
     */
    const SECOND_AUDIT_STATUS_WAIT = 1;
    const SECOND_AUDIT_STATUS_RUN = 2;
    const SECOND_AUDIT_STATUS_DONE = 3;
    const SECOND_AUDIT_STATUS_TIMEOUT = 4;
    /**
     * second_status 第二次评估结果 0 未通过  1 通过(当前评估最后一个人评分决定是否通过)
     */
    const SECOND_STATUS_NOT_PASS = 0;
    const SECOND_STATUS_PASS = 1;

    /**
     * 待处理
     */
    const AUDIT_STATUS_PENDING  = 1;

    /**
     * 超时关闭
     */
    const AUDIT_STATUS_TIMEOUT  = 3;

    /**
     * 是系统直接操作的
     */
    const IS_SYSTEM = 1;
    const IS_NOT_SYSTEM = 0;
    
    // 是否激活过 默认没激活过
    const IS_ACTIVE_DEFAULT = 0;
    // 激活过
    const IS_ACTIVE_YES = 1;
    
    // 结果通知 1=已通知
    const RESULT_NOTIFICATION_YES = 1;

    /**
     * 转正评估要走那个流程类别，默认1，1=正式员工,2=非一线，3=合同工，4=合同工三期
     */
    const PROBATION_CHANNEL_TYPE_FORMAL = 1; // 正式员工
    const PROBATION_CHANNEL_TYPE_NON_FRONTLINE = 2; // 非一线
    const PROBATION_CHANNEL_TYPE_CONTRACT = 3;
    const PROBATION_CHANNEL_TYPE_CONTRACT_V3 = 4;
}