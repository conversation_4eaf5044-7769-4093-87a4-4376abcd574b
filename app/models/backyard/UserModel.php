<?php
namespace FlashExpress\bi\App\Models\backyard;

use League\OAuth2\Server\Entities\UserEntityInterface;

/**
 * <AUTHOR> <<EMAIL>>
 * Class UserModel
 * @property string username
 * @property string password
 * @property string scope
 * @property int id
 * @package App\Models
 */
class UserModel extends BackyardBaseModel implements UserEntityInterface
{

    public function initialize()
    {
        parent::initialize();
        $this->setSource('user');
    }

    /**
     * @return mixed|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     */
    public function getIdentifier()
    {
        return $this->u_id;
    }
}
