<?php
namespace FlashExpress\bi\App\Models\oa;

/**
 * 员工盘点资产记录表
 * Class MaterialInventoryCheckStaffAssetsModel
 * @package App\Modules\Material\Models
 */
class MaterialInventoryCheckStaffAssetsModel extends OaBaseModel
{
    protected $table_name = 'material_inventory_check_staff_assets';
    /**
     * 所属盘点单ID
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $inventory_check_id;
    /**
     * 员工工号
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $staff_id;
    /**
     * 员工盘点任务表ID
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $staff_rel_id;
    /**
     * 资产商品ID
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $assets_goods_id;
    /**
     * 申请资产ID（assets_info表ID）
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $assets_info_id;
    /**
     * 1 公共资产 0个人资产
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $is_public;
    /**
     * 是否多项资产，0否，1是
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $is_batch;
    /**
     * 资产名称（英文）
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $goods_name_en;
    /**
     * 资产名称（泰文）
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $goods_name_th;
    /**
     * 资产名称（中文）
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $goods_name_zh;
    /**
     * barcode
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $bar_code;
    /**
     * 资产编码
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $asset_code;
    /**
     * sn编码
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $sn_code;
    /**
     * 修正后的sn编码
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $revise_sn_code;
    /**
     * 没盘到的反馈原因
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     */
    public $reason;
    /**
     * 盘点类型0初始化，1盘到，2没盘到，3手动新增，4手动盘点，5扫码盘点，6扫码新增，7多项资产确认
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $type;
    /**
     * 多项资产系统中记录的数量
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $sys_asset_num;
    /**
     * 多项资产员工手里实际的数量
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $actual_asset_num;
    /**
     * 手动新增资产数量
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|integer
     */
    public $asset_num;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|datetime
     */
    public $created_at;
    /**
     * @var \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|datetime
     */
    public $updated_at;
}
