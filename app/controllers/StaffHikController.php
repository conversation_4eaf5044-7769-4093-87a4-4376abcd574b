<?php
/**
 * Author: Bruce
 * Date  : 2023-12-01 14:16
 * Description:
 */

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\StaffHikServer;


class StaffHikController extends Controllers\ControllerBase
{
    protected $paramIn;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * hik人脸信息列表
     */
    public function getStaffListAction()
    {
        //[1]入参校验
        $paramIn     = $this->paramIn;
        $validations = [
            "staff_keyword" => "StrLenGeLe:0,100",
            'page_num'      => "Required|IntGe:0",
            'page_size'     => "Required|IntGe:0",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        $returnArr = (new StaffHikServer($this->lang, $this->timezone))->getStaffList($paramIn);
        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 编辑人脸
     */
    public function editFaceAction()
    {
        //[1]入参校验
        $paramIn     = $this->paramIn;
        $validations = [
            'staff_info_id' => "Required|IntGe:0",
            'face_img'      => "Required|StrLenGeLe:1,500",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        $returnArr = (new StaffHikServer($this->lang, $this->timezone))->editFaceUseLock($paramIn);
        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 人脸详情
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function detailAction()
    {
        //[1]入参校验
        $paramIn     = $this->paramIn;
        $validations = [
            'staff_info_id' => "Required|IntGe:0",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        $returnArr = (new StaffHikServer($this->lang, $this->timezone))->detail($paramIn);
        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}