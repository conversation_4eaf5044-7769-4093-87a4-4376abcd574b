<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\OsStaffServer;
use FlashExpress\bi\App\library\enums;
use Exception;

class OsController extends Controllers\ControllerBase
{
    protected $os;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 添加外协员工申请
     * @param int    job_id             外协职位
     * @param string employment_date    雇佣日期
     * @param int    employment_days    雇佣天数
     * @param int    shift_id           班次ID
     * @param int    demend_num         申请人数
     * @param string reason             申请原因
     * @return json
     */
    public function addOsStaffAction()
    {
        //[1]入参校验
        $paramIn                               = $this->paramIn;
        $paramIn['param']                      = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
        $paramIn['staff_id']                   = $this->userinfo['staff_id'];
        $paramIn['organization_id']            = $this->userinfo['organization_id'];
        $paramIn['organization_type']          = $this->userinfo['organization_type'];
        $logger                                = $this->getDI()->get('logger');

        $hubOsRoles = UC('outsourcingStaff')['hubOsRoles'];


        //[2]数据验证
        if (!isset($paramIn['os_type']) ||
            empty($paramIn['os_type']) ||
            !in_array($paramIn['os_type'], [
                enums::$os_staff_type['normal'],
                enums::$os_staff_type['long_term'],
                enums::$os_staff_type['motorcade'],
            ])) {
                throw new ValidationException("'os_type' invalid input");
        }

        switch ($paramIn['os_type']) {
            case enums::$os_staff_type['normal']:
                //hub 外协工单，分拨经理 提交申请校验规则
                if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:".enums::$job_title['outsource'].",".enums::$job_title['security_outsource']."|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntIn:1",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:98,111,473,13,452,110,271,1000|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:1,7",
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:"."'demend_num' invalid input",
                    ];
                }

                break;
            case enums::$os_staff_type['long_term']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:271,98,473|>>>:".$this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:90,365|>>>:".$this->getTranslation()->_('err_msg_more_days'),
                    "demend_num"      => "Required|IntGeLe:1,50|>>>:"."'demend_num' invalid input",
                ];
                break;
            case enums::$os_staff_type['motorcade']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:110|>>>:".$this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:1,7",
                    "demend_num"      => "Required|IntGeLe:1,100|>>>:"."'demend_num' invalid input",
                ];
                break;
            default:
                $validation_sub = [];
                break;
        }

        //[3]数据验证
        //短期外协的可选雇用日期为从申请日期的第二天起+7天
        //长期外协的可选雇用日期为从申请日期的第二天起+90日
        $dateFrom = date("Y-m-d", time() + 86400);
        $dateTo   = $paramIn['os_type'] == enums::$os_staff_type['normal']
            ? date("Y-m-d", time() + 8 * 86400)
            : date("Y-m-d", time() + 91 * 86400);

        $validations = [
            "employment_date" => "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:".$this->getTranslation()->_('err_msg_invalid_date'),
            "shift_id"        => "Required|Int",
            "reason_type"     => "Required|IntGeLe:0,8",
        ];

        //hub 外协工单，分拨经理 提交申请校验规则,古铜
        if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
            //今日或次日
            $dateFrom                       = date("Y-m-d");
            $dateTo                         = date("Y-m-d", strtotime("+6 day"));
            $validations["employment_date"] = "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:".$this->getTranslation()->_('today_tomorrow_date_error');
            $validations["company_id"]      = "Required|Int|>>>:'company_id' invalid input";
        }


        if (in_array($paramIn['reason_type'], [0, 2])) { //离职或其他原因需验证reason字段[原因备注字段]
            $valid       = [
                "reason" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1019'),
            ];
            $validations = array_merge($validations, $valid);
        }
        $this->validateCheck($this->paramIn, array_merge($validations, $validation_sub));


        //[4]业务处理
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new OsStaffServer($this->lang, $this->timezone))->addOsStaff($paramIn);
        }, 'addOsStaff_'.md5(json_encode($paramIn)), 300, false);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }

        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 更新加班车
     * @param int       audit_id        审批ID
     * @param int       status          审批状态
     * @param string    reject_reason   驳回原因
     * @param int       approval_data   审批数据
     * @return string
     */
    public function updateOsStaffAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;
        $logger               = $this->getDI()->get('logger');

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($this->paramIn, $validations);

        $osStaffServer = Tools::reBuildCountryInstance(new OsStaffServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $returnArr = $osStaffServer->updateOsStaffUseLock($paramIn);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取外协员工信息 跟前端确认页面入口都已经下掉
     */
    public function getOsStaffDetailAction()
    {
        //[1]入参校验
        $paramIn   = $this->paramIn;
        $logger    = $this->getDI()->get('logger');
        $validations = [
            "audit_id"      => "Required|Int",
        ];
        $this->validateCheck($this->paramIn, $validations);

        //[2]业务处理
        try{
            //[2]获取outsourcing staff审批详情
            $osDetail = (new OsStaffServer($this->lang, $this->timezone))->getOsStaffDetail(['id' => $paramIn['audit_id']]);
            if (empty($osDetail)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }

            //[3]获取outsourcing staff订单详情
            $ac = new ApiClient('hr_rpc', '', 'hr_outsourcing_detail', $this->lang);
            $ac->setParams([
                'serial_no' => $osDetail['serial_no']
            ]);
            $ret = $ac->execute();

            if ($ret['result']) {
                $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsStaffDetailInfo($ret['result'], $osDetail);

                //[3]数据返回
                $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
            } else {
                $logger->write_log("OsController:getOsStaffDetail:rpc-hr_outsourcing_detail:req:" . json_encode($osDetail) .":response:". json_encode($ret));
                $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }
        } catch (\Exception $e) {
            $logger->write_log("OsController:getOsStaffDetail:" . $e->getMessage(),'info');
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取班次、申请职位、申请原因列表；所属部门；
     */
    public function getShiftListAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //[2]业务处理
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getRequestList($paramIn);
        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 班次列表
     * @return void
     */
    public function getShiftListV2Action()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $job_title       = $this->processingDefault($paramIn, 'job_id', 2);
        $employment_date = $this->processingDefault($paramIn, 'employment_date');

        $list = (new OsStaffServer($this->lang, $this->timezone))->getShiftList($job_title);
        $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $list]]));
    }

    /**
     * 用户短期已经申请的短期外协记录；
     */
    public function shortTermApplyAction()
    {
        //[1]入参校验
        $organization_id  = $this->userinfo['organization_id'];

        //[2]业务处理
        try{
            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->shortTermApplyS($organization_id);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:shortTermApplyAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 获取可申请职位下拉列表
     */
    public function getOsJobTitleListAction()
    {
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;

            $validations = [
                "type"      => "Required|IntIn:1,2,3",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsJobTitleList($paramIn);
            return  $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:shortTermApplyAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * @description: 提供本月累计外协快递员使用情况
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/21 15:45
     */
    public function getThisMonthOsHappeningAction()
    {
        //[1]传入参数
        $paramIn                      = $this->paramIn;
        $paramIn['userinfo']          = $this->userinfo;
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $validations                  = [
            "job_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3008'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        //判断并返回数据
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getThisMonthOsHappening($paramIn);
        return $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
    }

    /**
     * @description: 提供3 天的外协使用风险提示
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/22 15:59
     */
    public function getOsRiskPromptAction(){
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;
            $paramIn['organization_id'] = $this->userinfo['organization_id'];
            $paramIn['organization_type'] = $this->userinfo['organization_type'];
            $validations = [
                "job_id"      => "Required|Int|>>>:" . $this->getTranslation()->_('3008'),
            ];
            $this->validateCheck($this->paramIn, $validations);
            //判断并返回数据
            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsRiskPrompt($paramIn);
            return  $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:getOsRiskPromptAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取距离班次的时长
     * @return void|null
     */
    public function getDurationByShiftIdAction()
    {
        $paramIn              = $this->paramIn;
        $paramIn['user_info'] = $this->userinfo;

        $validations = [
            "shift_id"        => "Required|Int",
            "employment_date" => "Required|Date",
        ];
        $this->validateCheck($this->paramIn, $validations);
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getDurationByShiftId($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }


    //根据 职位和 雇佣模式 返回对应的 外协类型 泰国和马来用
    public function getHireOsTypeAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;

        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->osType($paramIn);

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    public function updatePeopleNumAction()
    {
        //[1]入参校验
        $paramIn              = $this->paramIn;
        $paramIn['staff_id']  = $this->userinfo['staff_id'];
        $paramIn['name']  = $this->userinfo['name'];
        $paramIn['positions'] = $this->userinfo['positions'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['param']['position_category']  = $this->userinfo['positions'];
        $paramIn['param']['store_id']  = $this->userinfo['organization_id'];
        $paramIn['userinfo']  = $this->userinfo;

        $validations = [
            "audit_id"         => "Required|Int",
            "out_company_list" => "Required|Arr",
        ];
        $this->validateCheck($this->paramIn, $validations);

        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->updatePeopleNumUseLock($paramIn);
        return $this->jsonReturn($returnArr);


    }

}

