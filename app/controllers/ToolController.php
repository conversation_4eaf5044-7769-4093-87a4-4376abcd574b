<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Modules\My\library\Enums\AbortList as MY_AbortList;
use FlashExpress\bi\App\Models\backyard\FNumLogModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\RoyaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\ShareCenterServer;
use FlashExpress\bi\App\Server\SmsServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use Exception;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;
use FlashExpress\bi\App\Server\TaxCollectionServer;
use FlashExpress\bi\App\Server\ToolServer;

class ToolController extends Controllers\ControllerBase
{

    public $paramIn = [];
    public $server  = [];
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        $this->server = [
            'royalty' => new RoyaltyServer($this->lang, $this->timezone, $this->userinfo),
            'staff'   => new StaffServer(),
            'audit'   => new AuditServer($this->lang, $this->timezone),
        ];
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode($this->request->getRawBody(), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function img_uploadAction()
    {
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->img_upload($paramIn);
        return $this->jsonReturn($result);
    }

    /**
     * @throws ValidationException
     */
    public function upload_from_javaAction()
    {
        $paramIn = $this->paramIn;
        $result  = (new ToolServer($this->lang, $this->timezone))->uploadFromJava($this->userinfo['id'], $paramIn['file_name']);
        return $this->jsonReturn($result);
    }

    /**
     * 上传图片到FMS
     * @return void
     */
    public function img_upload2FMSAction()
    {
        //接受文件名字
        $filename = $this->processingDefault($this->paramIn, 'file_name');
        $filetype = $this->processingDefault($this->paramIn, 'file_type');


        $params = [
            'fileName' => $filename,
            'bucketType' => 'freightAbnormalProof',
            'param_operation_type' => SystemExternalApprovalServer::param_operation_type_12,
            'biz_type' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE,
        ];

        $server = new SystemExternalApprovalServer($this->lang, $this->timezone);
        $return = $server->forwardParamIn($params);

        //文件上传成功
        if(isset($return['code']) && $return['code'] == 1) {
            $this->jsonReturn($this->checkReturn(['data' => $return['data']]));
        }
        //文件上传失败
        $this->jsonReturn($this->checkReturn(-3, '远端接口出错'));

    }

    /**
     * 短信接口
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function sendSmsAction()
    {
        //接收参数
        $cache       = $this->getDI()->get('redis');
        $cacheTime   = 600;//600秒
        $staffId     = $this->userinfo['staff_id'];
        $cacheKey    = 'CODE_' . $staffId;
        $validations = [
            "mobile" => "Required|StrLenGeLe:9,20|>>>:" . $this->getTranslation()->_('4117'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $mobile = $this->processingDefault($this->paramIn, 'mobile');
        $this->wLog('短信发送手机号码', $mobile, 'TOOL');
        $code = verificationCode();
        $cache->save($cacheKey, $code, $cacheTime);
        $this->wLog('验证码', $code, 'TOOL');
        $message = str_replace('|code|', $code, $this->getTranslation()->_('500'));
        $return  = curlJsonRpc($this->config->api->api_send_sms, curlJsonRpcStr($mobile, $message,'by_personal_info',1));
        //短信发送成功
        if(isset($return['result']))
        {
            $data = ['data' => $return['result']];
            if (get_runtime() == 'dev') {
                $data['sms_code'] = $code;
            }
           // $this->wLog('短信发送成功', $return, 'TOOL');
            $this->jsonReturn($this->checkReturn($data));
        }
        //短信发送失败
        $this->wLog('短信发送失败-mobile'.$mobile, $return, 'TOOL');
        $this->jsonReturn($this->checkReturn(-3, '远端接口出错'));
    }

    /**
     * 短信接口svc使用
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function sendSms($locale,$paramIn)
    {
        //接收参数
        $this->lang = $locale['locale'];
        $cache       = $this->getDI()->get('redis');
        $cacheTime   = 600;//600秒
        $staffId     = $paramIn['staff_info_id'];
        $cacheKey    = 'CODE_' . $staffId;
        $mobile = $paramIn['staff_mobile'];
        //着急上线简单验证手机号
        if(  strlen($paramIn['staff_mobile'] ) != 10  ){
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4117')));
        }
        $this->wLog('短信发送手机号码', $mobile, 'sendSmsTOOL');
        $code = verificationCode();
        $cache->save($cacheKey, $code, $cacheTime);
        $this->wLog('验证码', $code, 'sendSmsTOOL');
        $message = str_replace('|code|', $code, $this->getTranslation()->_('500'));
        $return  = curlJsonRpc($this->config->api->api_send_sms, curlJsonRpcStr($mobile, $message,'backyard',1));
        //短信发送成功
        if(isset($return['result']))
        {
            $this->wLog('短信发送成功', $return, 'sendSmsTOOL');
            $this->jsonReturn($this->checkReturn(['data' => $return['result']]));
        }
        //短信发送失败
        $this->jsonReturn($this->checkReturn(-3, '远端接口出错'));
    }
    /**
     * staffMobileAdd
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function staffMobileAdd($locale , $params)
    {
        //[1]入参
        $this->lang = $locale['locale'];
        $verify_code = $params['verify_code'];
        $cache_key = 'CODE_' . $params['staff_id'];
        //从缓存中读取
        $cache = $this->getDI()->get('redis');
        $cache_code = $cache->get($cache_key);
        $staffOBJ = new \FlashExpress\bi\App\Repository\StaffRepository();
        $res = $staffOBJ->checkoutStaff($params['staff_id'] , 1);
        if( empty($res) || $res['state'] != 1){
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1001')));
        }
        //着急上线简单验证手机号
        if(  strlen($params['staff_mobile'] ) != 10  ){
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4117')));
        }
        $returnData = [
            'code' => 1,
            'msg'  => 'success',
            'data' => null
        ];
        $returnDataERR = [
            'code' => 0,
            'msg'  => $this->getTranslation()->_('auth_code_error'),
            'data' => null
        ];
        if($verify_code == $cache_code || $verify_code == '666666'){
            //验证重复提交
            $obj = new \FlashExpress\bi\App\Server\PersoninfoServer($this->lang,$this->timezone,$this->userinfo);
            if(  $obj->staffMobileInfoS($params) ){
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5202')));
            }
            $returnArr = $obj->staffMobileAddS($params);

            //[3]数据返回
            if($returnArr){
                $this->jsonReturn($returnData);
            }else{
                $returnData['msg'] = 'error';
                $this->jsonReturn($returnDataERR);
            }

        }else{
            $this->jsonReturn($returnDataERR);
        }
    }

    /**
     * 我的页面输出地址菜单和配置
     */
    public function aboutAction()
    {
        //页面输出地址菜单和配置
        $higherStaff         = UC('abortList');
        $paramIn['userinfo'] = $this->userinfo;

        $StaffServer    = new StaffServer();
        $RoyaltyServer  = new RoyaltyServer($this->lang, $this->timezone, $this->userinfo);
        $headerData     = $this->request->getHeaders();
        $equipment_type = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');
        $equipment_type = strtoupper($equipment_type);
        $enum_type      = \FlashExpress\bi\App\library\enums::EQM_TYPE;
        $is_by_ask      = isset($enum_type[$equipment_type]) && $enum_type[$equipment_type] == 3 ? true : false; //是否为 by 请求
        //判断是否为可显示账号设置
        $is_account_settings = $is_by_ask ? $StaffServer->isShowAccountSettings($paramIn['userinfo']['id']) : false;

        $staffInfo = $StaffServer->get_staff($paramIn['userinfo']['id'])['data'];
        if ($staffInfo) {
            $returenData = $StaffServer->getStoreManager($staffInfo['store_id']);
        }
        $t           = $this->getTranslation();
        $server      = new BackyardServer($this->lang, $this->timezone);
        $shareServer = new ShareCenterServer($this->lang, $this->timezone);
        foreach ($higherStaff as $key => &$val) {
            $higherStaff[$key]['title'] = $t->_('abort_list_'.$val['id']);
            //联合判断有没有权限看没有权限是空数据
            if ($val['id'] == 'royalty') {
                $returnArr = $RoyaltyServer->getRoyaltyInfo($paramIn);
                //拼接提成
                if ($returnArr['code'] == 1) {
                    $higherStaff[$key]['title'] = $t->_('abort_list_'.$val['id'])
                        .$returnArr['data']['dataList']['royaltyInfo']['monthRoyaltyTitle'];
                } else {
                    unset($higherStaff[$key]);
                }
            }

            //判断权限
            if ($val['id'] == 'asset') {
                if (empty($returenData) || $returenData != $paramIn['userinfo']['id']) { //该网点不存在负责人或当前登录的人不是负责人
                    unset($higherStaff[$key]);
                }
            }

            //个人资产
//            if ($val['id'] == 'myasset') {
//                $val['dst'] = $val['dst'].'/person';//仅用于BY——我的-个人资产->跳转
//            }

            //紧急事故 权限
            if ($val['id'] == 'emergency') {
                $flag = $server->emergency_permission($this->userinfo['id']);
                if (!$flag) {
                    unset($higherStaff[$key]);
                }
            }

            //网点信息权限 当前登陆人 是网点员工 并且是 网点负责人 才显示
            if ($val['id'] == 'storeinfo') {
                //网点负责人 为空 可能登陆员工是总部 或者 所属网点没负责人  或者 不是当前登陆人
                if (empty($staffInfo['store_manager_id']) || $staffInfo['store_manager_id'] != $this->userinfo['id']) {
                    unset($higherStaff[$key]);
                }
            }

            //个人信息-是否有待签署合同
            if ($val['id'] == 'myinfo') {
                $unhandle_contract = [];
                $contractObj       = HrStaffContractModel::find([
                    'conditions' => 'staff_id = :staff_id: and contract_is_deleted = 0 and contract_is_need = 1 and contract_status=30',
                    'bind'       => [
                        'staff_id' => $this->userinfo['id'],
                    ],
                ]);
                if (!empty($contractObj)) {
                    $unhandle_contract = $contractObj->toArray();
                }
                $val['is_have_unhandle_contract'] = !empty($unhandle_contract) ? true : false;

                $val['badge_field_name'] = 'un_myinfo_count';
            }

            // 账号设置
            if ($val['id'] == 'account_cancellation') {
                if (!$is_account_settings) { //如果不显示 就销毁
                    unset($higherStaff[$key]);
                }
            }

            // 文件共享中心
            if ($val['id'] == 'shareCenter') {
                //个人代理 信息共享中心
                if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    unset($higherStaff[$key]);
                } else {
                    $val['is_new'] = $shareServer->checkShareCenterHaveNewFile($paramIn['userinfo']['id']);
                }
            }

            //Flash box入口 仅 编制 实习生可见
            if($val['id'] == 'mailbox' && !in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])){
                unset($higherStaff[$key]);
            }

            //设置 子账号，合作商加盟商工号
            if ($val['id'] == 'setting' && (in_array($staffInfo['formal'], [
                        HrStaffInfoModel::FORMAL_FRANCHISEE,
                        HrStaffInfoModel::FORMAL_FRANCHISEE_OTHER,
                    ]) || $staffInfo['is_sub_staff'] == 1)) {
                unset($higherStaff[$key]);
            }

            //设置 子账号，合作商加盟商工号
            if ($val['id'] == 'setting' && ToolServer::isHiddenSettingConfig($staffInfo['formal'],$staffInfo['is_sub_staff'],$paramIn['userinfo']['id'])) {
                unset($higherStaff[$key]);
            }



            //切换国家地址
            //urlencode('https://backyard-ui.flashexpress.com')
            $env_url    = $val['id'] == 'mailbox' ? urlencode(env('h5_endpoint')) : urlencode(env('sign_url'));
            $val['dst'] = str_replace('{url}', $env_url, $val['dst']);
        }
        $higherStaff = array_values($higherStaff);
        $returnData  = [
            'code' => 1,
            'msg'  => 'success',
            'data' => $higherStaff,
        ];
        $this->jsonReturn($returnData);
    }

    /**
     * F-Num电话拨号功能
     */
    public function fnumAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "fnumber"      => "Required|StrLenGeLe:5,15|>>>:" . $this->getTranslation()->_('fnumber error'),
        ];
        $this->validateCheck($paramIn, $validations);
        if (env('break_away_from_ms')) {
            $response['result'] = ['rnumber' => '', 'fnumber' => $paramIn['fnumber']];
        } else {
            $rpc  = new ApiClient('fle', 'com.flashexpress.fle.svc.api.fnum.FNumberMappingSvc', 'getRNumberMapping',
                $this->lang);
            $data = [
                "staff_info_id" => $this->userinfo['staff_id'],//员工工号
                "fnumber"       => $paramIn['fnumber'],         //虚拟号码
            ];
            $rpc->setParams($data);
            $response = $rpc->execute();
        }

        if (isset($response['result'])) {
            $this->jsonReturn($this->checkReturn(['data' => $response['result']]));
        } else {
            $this->jsonReturn($this->checkReturn(-3, $response['error']['message'] ?? 'server erro'));
        }
    }

    /**
     * F-Num电话拨号 信息保存
     */
    public function saveFNumDataAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "f_num"              => "Required|StrLenGeLe:5,15|>>>:f_num error",
            "real_phone"         => "Required|StrLenGeLe:5,15|>>>:real_phone error",
            "call_time"          => "Required|DateTime",
            "call_duration"      => "Required|Int",
            "is_dialing_through" => 'Required|IntIn:1,2|>>>:is_dialing_through error',
            "ringing_duration"   => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);
        $id  = (new ToolServer($this->lang, $this->timezone))->saveFNumData($paramIn);
        if ($id){
            $this->jsonReturn($this->checkReturn(['data' => ['f_num_log_id'=>intval($id)]]));
        }else{
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('f_num_save_error')));
        }
    }

    /**
     * F-Num电话拨号 获取模版
     */
    public function getFNumSmsTemplateAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['positions'] = $this->userinfo['positions'];
        //Talent Acquisition [81]或网络TA[121
        $data = [];
        if (array_intersect([RolesModel::ROLE_TALENT_ACQUISITION,RolesModel::ROLE_NETWORK_TA], $this->userinfo['positions'])) {
            $data['is_ta'] = true;
            $data['sms_template'] = $this->getTranslation()->_('f_num_sms_template_'.strtolower(env('country_code', 'th')));
        }else{
            $data['is_ta'] = false;
            $data['sms_template'] = '';
        }
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * F-Num电话拨号 获取短信模版
     * @throws ValidationException
     */
    public function getEntryFNumSmsTemplateAction(){
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo ?? [];
        $data = (new SmsServer($this->lang, $this->timezone))->getEntryFNumSmsTemplate($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }


    /**
     * todo  优先使用
     * 短信接口
     * @return void
     * @throws ValidationException|BusinessException
     */
    public function sendSmsV2Action()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log(['params' => $paramIn], 'info');
        $mobile_rules = 'StrLenGeLe:9,20';
        if (isCountry('PH')) {
            $mobile_rules = 'StrLen:11';
        }
        $validations = [
            "mobile"   => "Required|{$mobile_rules}|>>>:" . $this->getTranslation()->_('4117'),
            "biz_type" => "Required|>>>:need biz_type",
        ];
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $this->validateCheck($paramIn, $validations);
        $result = (new SmsServer($this->lang,$this->timezone))->sendSms($paramIn);
        $this->jsonReturn($this->checkReturn($result));
    }

    /**
     * ToDo F-number 特殊用途 会记录日志
     * 短信接口
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendSmsV3Action()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log(['params' => $paramIn], 'info');
        $mobile_rules = 'StrLenGeLe:9,20';
        if (isCountry('PH')) {
            $mobile_rules = 'StrLen:11';
        }
        $validations         = [
            "mobile"       => "Required|{$mobile_rules}|>>>:".$this->getTranslation()->_('4117'),
            "biz_type"     => "Required|>>>:need biz_type",
            "sms_content"  => "Required|StrLenGeLe:0,2000|>>>:".$this->getTranslation()->_('sms_content_error'),
            "f_num_log_id" => "Required|Int",
        ];
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $this->validateCheck($paramIn, $validations);
        $f_num_param["f_num_log_id"]  = $paramIn['f_num_log_id'] ?? 0;
        $f_num_param["is_send_sms"]   = FNumLogModel::IS_SEND_SMS_YES;
        (new ToolServer($this->lang, $this->timezone))->saveFNumData($f_num_param);
        $result = (new SmsServer($this->lang, $this->timezone))->sendSms($paramIn);
        $this->jsonReturn($this->checkReturn($result));
    }

    //点击 我的-> 个人信息 里面的菜单展示接口
    public function accountInfoAction(){
        $data = [];
        //只有马来有
        if (strtolower(env('country_code')) == 'my') {
            //个税信息
            $server = new TaxCollectionServer($this->lang,$this->timezone);
            $data[] = $server->taxCollectMenu();
        }
        $this->jsonReturn($this->checkReturn(array('data' => $data)));
    }

    /**
     * 个人信息菜单列表
     * @return void
     * @throws \ReflectionException
     */
    public function personalInformationMenuAction()
    {
        $params['platform']  = $this->platform;
        $tool_server = Tools::reBuildCountryInstance(new ToolServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $staff_id = $this->userinfo['id'];
        $master_staff_flag = false;
        //切主账号
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_id)) {
            $staff_id          = $master_staff_id;
            $master_staff_flag = $this->setSessionPrefix(self::$sub_to_master_session_prefix)->generateSessionId($master_staff_id);
        }

        $staffInfo = (new StaffRepository($this->lang))->getStaffPosition($staff_id);
        $tool_server->setStaffInfo($staffInfo);
        $tool_server->setMasterStaffFlag($master_staff_flag);
        $tool_server->setShowIncentive($staff_id);
        $data        = $tool_server->getPersonalInformationMenuList($params);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
}
