<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Server\AfterAttendanceServer;
use FlashExpress\bi\App\Server\AttendanceImageVerifyServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer;
use FlashExpress\bi\App\Server\FaceCompareServer;
use FlashExpress\bi\App\Server\OssFileServer;
use FlashExpress\bi\App\Repository\BySettingRepository;

use FlashExpress\bi\App\Server\PdpaServer;
use FlashExpress\bi\App\Server\QueueMQServer;
use FlashExpress\bi\App\Server\ResignServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffFaceBlacklistServer;
use TencentCloud\Cvm\V20170312\CvmClient;
use TencentCloud\Cvm\V20170312\Models\DescribeZonesRequest;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Credential;
use FlashExpress\bi\App\Server\AttendanceBusinessServer;
use FlashExpress\bi\App\Repository\StaffRepository;



class AttendanceController extends Controllers\ControllerBase
{

    protected $paramIn;
    protected $server;

    //腾讯云 密钥
    protected $tencent_url;
    protected $tencent_id;
    protected $tencent_key;
    protected $tencent_host;

    protected $crypt = 'TC3-HMAC-SHA256';

    //ai 数据部门密钥
    protected $ai_Key = "64b8ceefb7abccb3e5fe75a7d1bc939ed41bb416c175f606aa1b438ae352";

    //oss 密钥
    protected $oss_id = 'LTAI7MMCu3h4oaGD';

    protected $score = '80';//人脸匹配 分数界定值
    public static $face_verify_error_key = 'face_verify_error';
    public static $switch_two_hour = 'face_two_hour';

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);

        $this->tencent_url = env('tencent_url', 'https://iai.ap-singapore.tencentcloudapi.com');
        $this->tencent_id = env('tencent_id', 'AKIDQmyvhEAihSFpL8tXGgdmlXE8TbUDlIdm');
        $this->tencent_key = env('tencent_key', '50umTpcDwRkpm15ucX5B7o6ORFqBUjBd');
        $this->tencent_host = env('tencent_host', 'iai.ap-singapore.tencentcloudapi.com');

    }

    public function onConstruct()
    {
        parent::onConstruct();

    }


    //获取考勤信息 客户端用 打卡日期和打卡类型
    public function get_attendance_infoAction()
    {

        try{
            $param['client_id'] = $this->request->get('clientid');
            $param['lat'] = $this->request->get('lat');
            $param['lng'] = $this->request->get('lng');
            $param['attendance_category'] = $this->request->get('attendance_category');
            $param['user_info'] = $this->userinfo;
            $param['platform'] = $this->platform;

            //获取该员工 该天考勤
            $server = new AttendanceServer($this->lang, $this->timezone);
            $info = $server->attendance_info($param);

            //时区设置 放方法前面 不好用 这个要注意
            if(!empty($info['start_data'])){
                date_default_timezone_set('UTC');
                $info['started_at'] = strtotime($info['start_data']);
            }

            if(!empty($info['end_data'])){
                date_default_timezone_set('UTC');
                $info['end_at'] = strtotime($info['end_data']);
            }
            //客户端建议 改成布尔
            $info['is_on_business'] = !empty($info['is_on_business']);
            //有外出申请的 则为外出申请
            if ($info['is_go_out']) {
                $info['is_on_business'] = false;
            }
            //如果 客户端没有传当前坐标 返回null
            if (empty($info['store_lat'])) {
                $info['store_lat'] = null;
            }
            if (empty($info['store_lng'])) {
                $info['store_lng'] = null;
            }
            if (empty($info['store_id'])) {
                $info['store_id'] = null;
            }

            //新增是否同意pdad字段
            $is_agree_pdpa = (new PdpaServer())->getIsAgreePdPa($this->userinfo['id']);
            $info['is_agree_pdpa'] = $is_agree_pdpa == 1;//是否同意pada，已经同意true，不同意false

            //新增 静默活体接口 名单
//            $list = $server->get_live_list();
//            if (is_bool($list)) {
//                $info['is_name_list'] = $list;
//            } else {
//                $info['is_name_list'] = in_array($this->userinfo['id'], $list);
//            }
            $info['is_name_list'] = true;//只眨眼

            //生成验收用
            if(isCountry('TH') && in_array($this->userinfo['id'],explode(',',env('qa_field_punch_staff_ids')))) {
                $info['field_punch'] = true;
            }
            $info['staff_info_id'] = intval($this->userinfo['id']);

            $this->logger->write_log('attendance_info '.$this->userinfo['id'].json_encode($info).json_encode($param),'info');
            $this->jsonReturn(array('code' => 1, 'message' => 'success', 'data' => $info));
        }catch (ValidationException $e){
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }catch (\Exception $e){
            $this->logger->write_log('attendance_info ' . $e->getMessage() . $e->getTraceAsString());
            $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));
        }
    }

    /**
     * Notes: 缺卡提醒、迟到早退惩罚提醒
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function malformedAction()
    {
        $param                        = [];
        $param['attendance_date']     = $this->request->get('attendance_date');
        $param['attendance_category'] = $this->request->get('attendance_category');
        $server                       = new AttendanceServer($this->lang, $this->timezone);
        $param['user_info']           = $this->userinfo;
        $result                       = $server->malformed($param);
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }


    //刷脸打卡 保存接口
    public function create_attendanceAction()
    {
        //X-FLE-EQUIPMENT-TYPE
        $headerData = $this->request->getHeaders();
        $equipment  = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');

        $param = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $param['equipment'] = strtoupper($equipment);
        //增加拦截验证
        $setVal = (new SettingEnvServer())->getSetVal('punch_out_switch');
        if(!empty($setVal) && $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF){
            $paramsIn['is_skip']     = 1;
            $paramsIn['platform']    = $this->platform;
            $paramsIn['shift_type']  = $param['shift_type'] ?? 0;
            $paramsIn['shift_index'] = $param['shift_index'] ?? 0;
            $userInfo                = $this->userinfo;
            $checkPunchOutServer     = Tools::reBuildCountryInstance(new CheckPunchOutServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
            $check                    = $checkPunchOutServer->check_staff_punch_out($userInfo, $paramsIn);
            $this->logger->write_log("attendance_check_punch_out {$this->userinfo['id']} " . json_encode($check, JSON_UNESCAPED_UNICODE), 'info');
            if(!empty($check) && $check['code'] == '-3'){
                return $this->jsonReturn($check);
            }
        }
        try{
            $server = Tools::reBuildCountryInstance(new AttendanceServer($this->lang,$this->timezone),[$this->lang,$this->timezone]);
            //如果是支援员工提醒用子账号操作
            $boolSupportStaff = $server->checkIsSupportStaff($this->userinfo['id'],$param['attendance_date']);
            if($boolSupportStaff) {
                $this->jsonReturn(['code' => 100, 'message' => $this->getTranslation()->_('staff_support_period_remind'), 'data' => null]);
            }
            //保存打卡
            $flag = $server->save_attendance($param);
            $this->logger->write_log("create_attendance {$this->userinfo['id']} " . json_encode($flag),'info');
            if(is_array($flag)){
                http_response_code(422);
                $this->jsonReturn($flag);
            }

            if ($flag) {
                $this->jsonReturn(['code' => 1, 'message' => 'success', 'data' => null]);
            }

            $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));
        } catch (BusinessException|ValidationException $bve) {
            $this->jsonReturn($this->checkReturn(-3, $bve->getMessage()));
        } catch (\Exception $e){
            throw $e;
        }

    }

    /**
     * 人脸比对失败后的提示
     * @return mixed
     */
    protected function getVerifyFailMessage()
    {
        $this->logger->write_log(['getVerifyFailMessage'=>['lang'=>$this->lang,'staff_info'=>$this->userinfo]],'info');
        $server = (new FaceCompareServer($this->lang, $this->timezone));
        return $server->getVerifyFailMessage($this->userinfo['id']);
    }


    //人脸照片对比接口
    public function image_verifyAction()
    {
        $setting_model = new BySettingRepository($this->lang);
        $ai_key = $setting_model->get_settingFromCache('ai_secret');
        $this->ai_Key = empty($ai_key) ? $this->ai_Key : $ai_key;

        $staff_id                = $this->userinfo['id'];
        $headerData              = $this->request->getHeaders();
        $this->paramIn['device'] = $this->processingDefault($headerData, 'User-Agent');
        $user_info               = $this->userinfo;
        $resultCode              = ErrCode::SUCCESS;
        $message                 = "";

        try {
            //获取 是否 ai 活体检测名单人员
            $server = new AttendanceServer($this->lang, $this->timezone);
            $list = $server->get_live_list();
            if((is_bool($list) && $list === true) || (!is_bool($list) && !empty($list) && in_array($staff_id,$list))){
                //人脸识别之前 需要 做活体检测 如果失败 不继续人脸对比
                $return_score = $this->live_check($this->paramIn['click_url']);
                $alive_score = $setting_model->get_settingFromCache('alive_score');
                $this->logger->write_log("live_check_{$staff_id} {$return_score} " . json_encode($this->paramIn),'info');

                //保存 ai 日志记录表
                $this->paramIn['success_enabled'] = intval($return_score * 100);
                $server->add_face_log($user_info,5,$this->paramIn);

                if($return_score >= 0 && $alive_score > $return_score){//非活体
                    http_response_code(422);
                    throw new ValidationException($this->getVerifyFailMessage(), ErrCode::AI_IMAGE_VERIFY_ERROR);
                }
            }

            //默认 人脸识别 接口 腾讯 如果配置为空 走百分比
            $attendance_type = $setting_model->get_setting('attendance_type');
            if (empty($attendance_type)) {
                $attendance_type = $this->get_percent();
            }

            if (empty($attendance_type) || $attendance_type == 1) {
                $type = 'tencent';//腾讯
            } else if ($attendance_type == 3) {
                $type = 'ai';//ai数据部门
            } else {
                $type = 'ali';//阿里
            }

            //如果 ai 服务器问题已经暴露 切换腾讯
            $count_line = $setting_model->get_setting('attendance_face_top');;//这个 改成可配置 出现错误次数峰值
            $redis = $this->getDI()->get('redisLib');
            $error_num = $redis->get(self::$face_verify_error_key);
            $switch_tow_hour = $redis->get(self::$switch_two_hour);
            if($error_num > $count_line || !empty($switch_tow_hour)){
                $attendance_type = 1;
                $type = 'tencent';
            }

            $function = $type.'_interface';

            $flag = 0;//是否拼接内网图片地址
            if ($type == 'ai' && RUNTIME == 'pro') {
                $flag = 1;
            }

            //马来 子账号打卡要同步主账号 写redis
            if(isCountry('MY')){
                $old_url = (new AttendanceImageVerifyServer($this->lang, $this->timezone))->getStaffFaceImage($staff_id, (bool)$flag);
            }else{
                $old_url = $server->get_face_img($this->userinfo['id'], $flag);
            }

            $current_url = $this->paramIn['click_url'];//本次打卡照片地址
            $source_url = empty($this->paramIn['source_url']) ? '' : $this->paramIn['source_url'];//如果有值 跟click 一样
            //打卡图片为空
            if(empty($current_url)){
                http_response_code(422);
                throw new ValidationException($this->getTranslation()->_('miss_args').' click_url',
                    ErrCode::AI_IMAGE_VERIFY_MISS_ARGS);
            }
            //拼接图片 全路径
            $format['bucket'] = $this->config->application->oss_bucket;//固定
            $format['path'] = $current_url;
            $current_url = $server->format_oss($format,$flag);
            $isDoneAnalyzeFace = false;
            //如果没有底片 跳过匹配照片 新增需求 没有底片校验是否戴口罩
            if(empty($old_url)){
                if(empty($source_url)){
                    http_response_code(422);
                    throw new ValidationException($this->getTranslation()->_('miss_args').' source_url',
                        ErrCode::AI_IMAGE_VERIFY_MISS_ARGS);
                }
                $switch = $setting_model->get_setting('ai_mask_switch');//口罩开关

                //如果是外协并且是快递员职位，则需要在全网进行人脸检索
                $attendanceServer = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
                if (isCountry('MY') && $attendanceServer->outsourceStaffPunchField($staff_id)) { //在马来白名单中，直接返回
                    //把马来在白名单中的外协员工的case，从下面的条件中分离出来。需要保存底片。
                    //https://flashexpress.feishu.cn/docx/X0uLdTQ07oytlIxuQZnc52W3nQh

                } else if (isCountry(['MY','TH','PH']) && $this->paramIn['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON &&
                    $server->checkStaffInfo($staff_id)) {
                    $staffAttendanceImage = StaffWorkAttendanceAttachmentModel::findFirst([
                        "staff_info_id = :staff_info_id:",
                        "bind" => [
                            "staff_info_id" => $staff_id
                        ]
                    ]);
                    if (empty($staffAttendanceImage)) {
                        $groupIdList = $server->getRegionList();
                        $params = [
                            'source_url'    => $source_url,
                            'store_id'      => $user_info['organization_id'],
                            'staff_id'      => $staff_id,
                            'group_id_list' => $groupIdList,
                            'source'        => AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_ATTENDANCE,
                        ];
                        //图片质量检测
                        $server->setValidateFaceWithMskTips();
                        $server->analyzeFace($source_url, $staff_id);
                        $isDoneAnalyzeFace = true;
                        //没有底片，这是首次打卡，需要在保存底片之前进行人脸的比对
                        $server->searchRepeatFace($params);
                    }
                } else if ($switch && $switch == 1) {
                    //口罩分数
                    $mask_base_score = $setting_model->get_setting('ai_mask_score');
                    $mask_score = $this->mask_verify($this->paramIn['click_url']);
                    $this->paramIn['success_enabled'] = $mask_score;
                    $server->add_face_log($user_info,6,$this->paramIn);
                    if($mask_score > 0 && $mask_base_score > 0 && $mask_score >= $mask_base_score){
                        http_response_code(423);
                        throw new ValidationException($this->getTranslation()->_('mask_notice'),
                            ErrCode::AI_IMAGE_VERIFY_MASK_EXIST);
                    }
                }
                $isCheckFaceBlackList = (new StaffFaceBlacklistServer())->isCheckFaceBlackList($staff_id);
                if($isCheckFaceBlackList){
                    //图片质量检测
                    if (empty($isDoneAnalyzeFace)) {
                        $server->setValidateFaceWithMskTips();
                        $server->analyzeFace($source_url, $staff_id);
                    }
                    $this->logger->write_log(["dealFaceBlackListLogic"=>[$staff_id,$source_url]],'info');
                    //处理黑名单底片逻辑
                    $server->dealFaceBlackListLogic($staff_id, $source_url);
                }

                //保存底片
                $insert['staff_info_id'] = $this->userinfo['id'];
                $insert['work_attendance_path'] = $source_url;
                $insert['work_attendance_bucket'] = $this->config->application->oss_bucket;
                $server->save_face_img($insert);

                $score = $this->score;
                $attendance_type = 4;
            }else{

                //上班打卡
                //如果是外协并且是快递员职位，则需要在所属大区进行人脸检索
                $attendanceServer = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
                if (isCountry('MY') && $attendanceServer->outsourceStaffPunchField($staff_id)) { //在马来白名单中，直接返回
                    //在白名单中不校验人脸

                    $this->jsonReturn(array('code' => ErrCode::SUCCESS,'message' => "success", 'data' => null));
                } else if (isCountry(['MY','TH','PH']) && $this->paramIn['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON &&
                    $server->checkStaffInfo($staff_id)) {
                    $groupIdList = $server->getRegionList($user_info['organization_id']);
                    $params = [
                        'source_url'    => $this->paramIn['click_url'],
                        'store_id'      => $user_info['organization_id'],
                        'staff_id'      => $staff_id,
                        'group_id_list' => $groupIdList,
                        'source'        => AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_ATTENDANCE,
                    ];
                    //图片质量检测
                    $server->analyzeFace($this->paramIn['click_url'], $staff_id);
                    //有底片，这是非首次打卡
                    $server->searchRepeatFace($params);
                }

                //匹配照片
                $score_arr = $this->$function($old_url, $current_url);
                $score = $score_arr['score'];
                $http_code = $score_arr['http_code'];
                //如果 是 ai  并且 500+ 切 腾讯
                if($type == 'ai' && $http_code >= 500){
                    //记录 ai 失败日志
                    $this->paramIn['success_enabled'] = -3;
                    $server->add_face_log($user_info,$attendance_type,$this->paramIn);
                    //失败数量递增 并且写延时队列
                    $s = (new QueueMQServer($this->lang,$this->timezone));
                    $data = [
                        "staff_info_id" => $staff_id,
                        "src"  => "backyard",
                    ];
                    $s->sendToMsg($data,5 * 60,'attendance-fail');
                    $redis = $this->getDI()->get('redisLib');
                    $redis->incr(self::$face_verify_error_key);

                    //如果 已经达到峰值 配置2小时实效切换策略
                    $num = $redis->get(self::$face_verify_error_key);
                    if($num > $count_line){
                        $is_changed = $redis->get(self::$switch_two_hour);
                        if(empty($is_changed))
                            $redis->set(self::$switch_two_hour, 1, 2 * 60 * 60); //2小时 切换腾讯开关
                        $this->logger->write_log("face_switch_changed！！！！！！！ ");//已经启动 切换腾讯 2 让ai 检查问题 2小时之后切回来
                    }

                    $this->logger->write_log("image_verify_failed_{$staff_id} {$http_code} {$score}_{$function} odlURL:{$old_url} currentUrl:{$current_url}",'info');

                    $attendance_type = 1;
                    $type = 'tencent';
                    $function = $type.'_interface';
                    $format['bucket'] = $this->config->application->oss_bucket;//固定
                    $format['path'] = $this->paramIn['click_url'];
                    $current_url = $server->format_oss($format);
                    $old_url = $server->get_face_img($this->userinfo['id']);
                    $score_arr = $this->$function($old_url, $current_url);
                    $score = $score_arr['score'];
                }
            }
            $this->logger->write_log("image_verify_{$staff_id} {$score}_{$function} odlURL:{$old_url} currentUrl:{$current_url}" . json_encode($this->paramIn),'info');
            //ai组 需要数据跑用例 记录下对比后的日志 staff_work_face_verify_record 保存

            $this->paramIn['success_enabled'] = intval($score > $this->score);
            $server->add_face_log($user_info,$attendance_type,$this->paramIn);

            if($score < $this->score){//不匹配
                http_response_code(422);
                throw new ValidationException($this->getVerifyFailMessage(),
                    ErrCode::AI_IMAGE_VERIFY_ERROR);
            }
        } catch (ValidationException $e) {
            $resultCode = $e->getCode();
            $message = $e->getMessage();
            $this->logger->write_log("image_verify_failed_{$staff_id} code $resultCode message {$e->getMessage()}",'info');
        }

        $this->jsonReturn(array('code' => $resultCode ?? ErrCode::SUCCESS,'message' => $message ?? "success", 'data' => null));
    }

    /**
     * @description 周期性校验外协员工是否本人
     * 检验周期 每2个小时
     *
     * @return string
     * @throws ValidationException
     */
    public function image_verify_cyclicalAction()
    {
        //获取参数
        $headerData           = $this->request->getHeaders();
        $device               = $this->processingDefault($headerData, 'User-Agent');
        $user_info            = $this->userinfo;
        $staffId              = $this->userinfo['id'];
        $paramIn              = $this->paramIn;
        $paramIn['user_info'] = $user_info;

        $attServer         = new AttendanceServer($this->lang, $this->timezone);
        $attServer         = Tools::reBuildCountryInstance($attServer,[$this->lang, $this->timezone]);
        $settingModel      = new BySettingRepository($this->lang);
        $aiKey             = $settingModel->get_settingFromCache('ai_secret');
        $this->ai_Key      = empty($aiKey) ? $this->ai_Key : $aiKey;
        $paramIn['device'] = $device;

        try {
            if(isCountry('MY')){
                $attendanceInfo = $attServer->newAttendanceInfo($paramIn);
            }else{
                $attendanceInfo = $attServer->attendance_info($paramIn);
            }

            $getBrushFaceStaffInfo = $attServer->getBrushFaceStaffInfoFromCache($staffId, $attendanceInfo['attendance_date']);
            if (!$getBrushFaceStaffInfo['is_check']) { //员工身份不符合
                //如果身份不符合直接退出，不报错
                throw new ValidationException('success', ErrCode::SUCCESS);
            }
            //戴口罩一直提示
            $attServer->setValidateFaceWithMskTips();
            try {
                //图片质量检测
                $attServer->analyzeFace($paramIn['source_url'], $staffId);
            } catch (ValidationException $ev) {
                http_response_code(ErrCode::IMAGE_VERIFY_TRY_AGAIN_HTTP);
                throw new ValidationException($ev->getMessage(),
                    ErrCode::IMAGE_VERIFY_TRY_AGAIN);
            }

            $isLive    = true;
            if (isCountry('TH') || isCountry('PH')) {
                $list = $attServer->get_live_list();
                if ($list === true || (is_array($list) && in_array($staffId, $list))) {
                    //人脸比对之前需要做活体检测
                    $faceCompareScore = $this->live_check($paramIn['source_url']);
                    //人脸比对基础分数
                    $aliveBaseScore = $settingModel->get_setting('ic_cycle_alive_score');
                    $this->logger->write_log(sprintf('[image_verify_cyclical] live check staff id: {%s}, score: {%s}, params in %s',
                        $staffId, $faceCompareScore, json_encode($paramIn)), 'info');
                    $isLive    = $faceCompareScore >= 0 && $faceCompareScore >= $aliveBaseScore;//活体
                }
            }

            //获取底片
            $faceImageUrl = $attServer->getOutsourcingFaceImg([
                'staff_info_id' => $staffId,
                'hire_type'     => $getBrushFaceStaffInfo['staff_info']['hire_type'],
            ]);
            //需要校验的图片
            $format['bucket'] = $this->config->application->oss_bucket;
            $format['path']   = $paramIn['source_url'];
            $sourceUrl        = $attServer->format_oss($format);
            //人脸比对
            $score_arr = ['http_code' => 0];
            $faceImageUrl && $score_arr = $this->ai_interface($faceImageUrl, $sourceUrl);

            if (isCountry('TH') || isCountry('PH')) {
                $type = in_array($getBrushFaceStaffInfo['staff_info']['hire_type'], HrStaffInfoModel::$agentTypeTogether)
                    ? StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE
                    : StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS;

                //如果是个人代理 并且 验证失败 泰国新增逻辑 增加次数判断
                if ($type == StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE && (empty($score_arr['score']) || $score_arr['score'] == 0 || !$isLive)) {
                    //根据失败次数 看是否需要再次尝试
                    $attServer->checkFailTimes($staffId, $attendanceInfo['attendance_date']);
                }
            } else {
                $type = StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS;
            }

            //保存信息
            $result = $attServer->addStaffWorkDetectFaceRecord([
                'organization_id'           => $user_info['organization_id'],
                'staff_info_id'             => $staffId,
                'score_arr'                 => $score_arr,
                'os_submit_face_image_path' => $sourceUrl,   //外网
                'os_face_image_source_path' => $faceImageUrl,// 外网照片
                'source_url'                => $paramIn['source_url'],
                'type'                      => $type,
                'live_score'                => $faceCompareScore ?? null,
                'attendance_date'           => $attendanceInfo['attendance_date'],
            ]);
        } catch (ValidationException $e) {
            $message    = $e->getMessage();
            $returnCode = $e->getCode();
            if($returnCode == ErrCode::IMAGE_VERIFY_TRY_AGAIN){
                throw $e;
            }
        }

        $this->jsonReturn([
            'code'    => $returnCode ?? ErrCode::SUCCESS,
            'message' => $message ?? 'success',
            'data'    => null,
        ]);
    }


    /**
     * @description 获取下次侦测时间、当前时间
     */
    public function getNextDetectTimeAction()
    {
        //获取参数
        $staffId = $this->userinfo['id'];
        $result  = [
            'is_need_verify' => false,
        ];
        $param['user_info'] = $this->userinfo;

        try {
            $attServer             = new AttendanceServer($this->lang, $this->timezone);
            $attendanceInfo        = $attServer->attendance_info($param);
            $getBrushFaceStaffInfo = $attServer->getBrushFaceStaffInfoFromCache($staffId, $attendanceInfo['attendance_date']);

            if (!$getBrushFaceStaffInfo['is_check']) { //员工身份不符合
                throw new ValidationException('success', ErrCode::SUCCESS);
            }

            if ($attServer->checkIsNeedVerify($staffId, $attendanceInfo['attendance_date'])) {
                $result['is_need_verify'] = true;
            } else {
                $result['is_need_verify'] = false;
            }
        } catch (ValidationException $ve) {
            $returnCode = $ve->getCode();
            $message    = $ve->getMessage();
        }
        $this->logger->write_log(['getNextDetectTime' => $result, 'staff_info' => $this->userinfo['id']], 'info');

        $this->jsonReturn([
            'code'    => $returnCode ?? ErrCode::SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $result,
        ]);
    }

    /**
     * 腾讯 人脸对比接口
     * @param $old_url --底片
     * @param $new_url --新照的照片
     */
    protected function tencent_interface($old_url,$new_url)
    {
        try{
            //照片比对
            date_default_timezone_set('UTC');//零时区 与腾讯服务器 不能相差5分钟以上
            $secretId = $this->tencent_id;
            $secretKey = $this->tencent_key;
            $host = $this->tencent_host;
            $tmp = time();
            $date = date('Y-m-d',$tmp);
            $server = 'iai';
            $action = 'CompareFace';
            $version = '2020-03-03';
            $region = 'ap-singapore';
            //底片
            $param['UrlA'] = $old_url;
            //$param['UrlA'] = 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workAttendanceSource/1552988191-2328143d07394b5895abee035c242aac.jpg';
            //app 人脸图片
            $param['UrlB'] = $new_url;
            //$param['UrlB'] = 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workAttendanceSource/1553004855-f9863113e10242ed81f9a12a7121b302.jpg';
            $param = str_replace("\\/", "/", json_encode($param));

            //生成 签名 认证信息
            $signature = $this->sign($secretKey,$host,$server,$tmp,$param);
            $auth = $this->crypt." Credential={$secretId}/{$date}/{$server}/tc3_request, SignedHeaders=content-type;host, Signature={$signature}";
            $header[] = "Authorization: ".$auth;
            $header[] = "Host: {$host}";
            $header[] = "Content-Type: application/json";
            $header[] = "X-TC-Action: {$action}";
            $header[] = "X-TC-Timestamp: " . $tmp;
            $header[] = "X-TC-Version: " . $version;
            $header[] = "X-TC-Region: " . $region;

            $res = $this->httpPost($this->tencent_url, $param, $header ,null,5) ;//{"Response":{"Score":8.954370498657227,"FaceModelVersion":"2.0","RequestId":"963b7486-86eb-43bb-9af9-cffebb7194d5"}}
            $this->logger->write_log("tencent_interface ".json_encode($param) . ' ' . $res['response'],'info');
            $http_code = $res['code_status'];
            $res = json_decode($res['response'],true);
            if(!empty($res) && !empty($res['Response']['Score'])){
                return array('score' => $res['Response']['Score'], 'http_code' => $http_code);
            }else{
                $this->getDI()->get('logger')->write_log("图片对比失败 ".json_encode($res),'info');
                return array('score' => 0, 'http_code' => $http_code);
            }
        }catch (Exception $e){
            $this->getDI()->get('logger')->write_log("图片对比失败 ".$e->getMessage(),'info');
            return array('score' => 0, 'http_code' => 508);
        }
    }

    //阿里人脸对比接口 参考文档 https://help.aliyun.com/knowledge_detail/53535.html
    protected function ali_interface()
    {

    }


    //AI接口
    /**
     * https://l8bx01gcjr.feishu.cn/docs/doccnFvgc9rQMjLaT39VKaxlYhh#y4lsEe
     *SDK
     * java
     * String staffInfoId = "17331";
    String secret_key = "64b8ceefb7abccb3e5fe75a7d1bc939ed41bb416c175f606aa1b438ae352";
    Long currentTimeMillis = System.currentTimeMillis();
    String timeStamp = String.valueOf(currentTimeMillis);
    String toToken = "POST" + "\n" + "/v1/compare-face" + "\n" + timeStamp + "\n" + staffInfoId;
    String token = Base64.getEncoder().encodeToString(
    Hashing.hmacSha1(secret_key.getBytes()).hashString(toToken, StandardCharsets.UTF_8).asBytes()
    );
     *
     *
     *python
     * def gen_token(staff_info_id, secret_key):
    timestamp = int(time.time() * 1000)
    to_token = "POST" + "\n" + "/v1/compare-face" + "\n" + str(timestamp) + "\n" + str(staff_info_id)
    digest = hmac.new(secret_key.encode('utf-8'), to_token.encode('utf-8'), digestmod=hashlib.sha1).digest()
    token = base64.b64encode(digest).decode('utf-8')
    v = str(timestamp) + "_" + str(staff_info_id) + "_" + str(token)
     *
    1605012224358_10290_UBICpUimz2nIbNBbWyalw5KR2Bk=
     *
     * @param $old_url
     * @param $new_url
     */
    protected function ai_interface($old_url,$new_url)
    {
        $user_info = $this->userinfo;
        $staff_id = $user_info['id'];

        $setting_model = new BySettingRepository($this->lang);
        $host = $setting_model->get_setting('ai_face_host');

        $tmp = intval(microtime(true) * 1000);

        $str_token = "POST" . "\n" . "/v1/compare-face" . "\n" . $tmp . "\n" . $staff_id;
        $secretKey = $this->ai_Key;

        $t = hash_hmac("sha1",$str_token,$secretKey,true);
        $token = "{$tmp}_{$staff_id}_" . base64_encode($t);

        $header[] = "X-FLE-Token: " . $token;
        $header[] = "Content-type: application/x-www-form-urlencoded";
        $header[] = "Accept: application/json";

        //底片
        //$old_url = 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workAttendanceSource/1552988191-2328143d07394b5895abee035c242aac.jpg';
        //$new_url = 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workAttendanceSource/1553004855-f9863113e10242ed81f9a12a7121b302.jpg';
        $old_url = urlencode(str_replace("\\/", "/", $old_url));
        $new_url = urlencode(str_replace("\\/", "/", $new_url));

        $param = "urlA={$old_url}&urlB={$new_url}";
        $res = $this->httpPost($host, $param, $header,null,5) ;//{"requestId":"c9119376-ce73-44b4-9804-2305d0b06ae6","result":false,"distance":2.109908103942871}
        $http_code = $res['code_status'];//如果是0 和其他已知的非200 code 需要切流量
        $this->logger->write_log("ai_interface {$http_code} ".$param.'  ' . $res['response'],'info');
        $res = json_decode($res['response'],true);


        if (!empty($res) && !empty($res['result'])) {
            return ['score' => 100, 'http_code' => $http_code, 'distance' => $res['distance'] ?? null];
        }
        return ['score' => 0, 'http_code' => $http_code, 'distance' => null];
    }

    //签名 加密 参考文档 https://cloud.tencent.com/document/product/867/32802
    protected function sign($secretKey,$host,$service,$timestamp,$param)
    {
        $algorithm = $this->crypt;//固定加密算法
        // step 1: build canonical request string
        $httpRequestMethod = "POST";
        $canonicalUri = "/";
        $canonicalQueryString = "";
        $canonicalHeaders = "content-type:application/json\n"."host:".$host."\n";
        $signedHeaders = "content-type;host";
        $hashedRequestPayload = hash("SHA256", $param);
        $canonicalRequest = $httpRequestMethod."\n"
            .$canonicalUri."\n"
            .$canonicalQueryString."\n"
            .$canonicalHeaders."\n"
            .$signedHeaders."\n"
            .$hashedRequestPayload;

        // step 2: build string to sign
        $date = gmdate("Y-m-d", $timestamp);
        $credentialScope = $date."/".$service."/tc3_request";
        $hashedCanonicalRequest = hash("SHA256", $canonicalRequest);
        $stringToSign = $algorithm."\n"
            .$timestamp."\n"
            .$credentialScope."\n"
            .$hashedCanonicalRequest;

        // step 3: sign string
        $secretDate = hash_hmac("SHA256", $date, "TC3".$secretKey, true);
        $secretService = hash_hmac("SHA256", $service, $secretDate, true);
        $secretSigning = hash_hmac("SHA256", "tc3_request", $secretService, true);
        $signature = hash_hmac("SHA256", $stringToSign, $secretSigning);
        return $signature;

        // step 4: build authorization
        $authorization = $algorithm
            ." Credential=".$secretId."/".$credentialScope
            .", SignedHeaders=content-type;host, Signature=".$signature;

        $curl = "curl -X POST https://".$host
            .' -H "Authorization: '.$authorization.'"'
            .' -H "Content-Type: application/json"'
            .' -H "Host: '.$host.'"'
            .' -H "X-TC-Action: '.$action.'"'
            .' -H "X-TC-Timestamp: '.$timestamp.'"'
            .' -H "X-TC-Version: '.$version.'"'
            .' -H "X-TC-Region: '.$region.'"'
            ." -d '".$param."'";
        echo $curl.PHP_EOL;
    }


    //组合文件 url 地址

    /**
     * java 返回示例 暂时没用 获取oss推送地址 需要走java 统一接口
     * code = 1;
    data =     {
    "access_key_id" = LTAI7MMCu3h4oaGD;
    "bucket_name" = "fle-staging-asset-internal";
    "content_type" = "image/jpeg";
    date = "Wed, 08 Jul 2020 08:44:07 GMT";
    endpoint = "https://oss-ap-southeast-1.aliyuncs.com";
    method = PUT;
    "object_key" = "staffMileageWorkRecord/SMI-1594197847-40e50b4d800a47fd89d49736a5fb29bd.jpg";
    "object_url" = "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/staffMileageWorkRecord/SMI-1594197847-40e50b4d800a47fd89d49736a5fb29bd.jpg";
    "object_url_th" = "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/staffMileageWorkRecord/SMI-1594197847-40e50b4d800a47fd89d49736a5fb29bd.jpg?x-oss-process=style/th";
    "object_url_x3" = "https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/staffMileageWorkRecord/SMI-1594197847-40e50b4d800a47fd89d49736a5fb29bd.jpg?x-oss-process=style/x3";
    signature = "bYAqio/k/VMVSRyP1eFfZ8/VTTA=";
     *
     * fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/
    };
    message = success;
     */
    public function format_urlAction()
    {
        try{
            $oss_server = new OssFileServer($this->lang);
            $file_name = $this->request->get('fileName');//原图 source.jpg  对比图 duty.jpg
            if(empty($file_name) || !in_array($file_name,array('source.jpg','duty.jpg')))
                $this->jsonReturn( $this->checkReturn(-3, 'miss parameter'));

            $res = $oss_server->oss_put(1,$file_name);
            if($res['status'] != 200)
                $this->jsonReturn( $this->checkReturn(-3, 'server error'));

            $info = $res['header']['oss-requestheaders'];
            /**
             * ["oss-requestheaders"]=>
            array(5) {
            ["Accept-Encoding"]=>
            string(0) ""
            ["Content-Type"]=>
            string(10) "image/jpeg"
            ["Date"]=>
            string(29) "Wed, 08 Jul 2020 12:36:51 GMT"
            ["Host"]=>
            string(42) "ser-oa-pro.oss-ap-southeast-1.aliyuncs.com"
            ["Authorization"]=>
            string(57) "OSS LTAI4GDstV4E9awWTDgnz7RV:SplczlRCMXDjTuL1znDmP/Qfy2U="
            }
             */
            $Authorization = explode(':', $info['Authorization']);
            $data['signature'] = $Authorization[1];
            $data['access_key_id'] = $res['data']['sid'];
            $data['bucket_name'] = $res['data']['bucket'];
            $data['endpoint'] = $res['data']['point'];
            $data['object_key'] = $res['data']['file_name'];
            $data['content_type'] = $info['Content-Type'];
            $data['date'] = $info['Date'];
            $data['method'] = $res['header']['info']['method'];
            $data['object_url'] = $res['header']['info']['url'];

            if($file_name == 'source.jpg'){//原图需要保存底片地址  staff_work_attendance_attachment
                $insert['staff_info_id'] = $this->userinfo['id'];
                $insert['work_attendance_path'] = $data['object_key'];
                $insert['work_attendance_bucket'] = $data['bucket_name'];
                $att_server = new AttendanceServer($this->lang,$this->timezone);
                $att_server->save_face_img($insert);
            }

            $this->jsonReturn( array('code' => 1, 'message' => 'success','data' => $data));
        }catch (\Exception $e){
            $this->logger->write_log($e->getMessage());
            return $this->jsonReturn(array('code' => -1, 'message' => 'failed', 'data' => null));
        }

    }

    //由于 所有文件 需要调用java 统一接口 暂时新封装一个 图片推送接口 获取url 和签名 返回客户端

    /**
     * 底片、人脸上传
     * @return void
     * @throws Exception
     */
    public function format_url_fleAction()
    {
        $server = Tools::reBuildCountryInstance(new AttendanceServer($this->lang, $this->timezone),[$this->lang, $this->timezone]);
        $result = $server->faceFormatUrlFle($this->userinfo['id'], $this->request->get('fileName'));
        $data['code'] = 1;
        $data['message'] = 'success';
        $data['data'] = $result['result'];
        die(json_encode($data,JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES+JSON_PRETTY_PRINT));

    }


    //出差打卡 审核
    public function audit_atbAction()
    {
        $param     = $this->paramIn;
        $user_info = $this->userinfo;

        $validations = [
            "status"   => "Required|Required|IntIn:1,2,3,4|>>>:".$this->getTranslation()->_('miss_args'),
            "audit_id" => "Required|Required|Int|>>>:".$this->getTranslation()->_('miss_args'),
        ];
        //如果驳回，验证驳回理由
        if ($param['status'] == 3) {
            $validations['reject_reason'] = "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('1020');
        }
        $this->validateCheck($param, $validations);

        $server = new AttendanceBusinessServer($this->lang, $this->timezone);
        $return = $server->update_statusUseLock($param, $user_info);

        //[3]成功数据返回
        $this->jsonReturn($return);
    }


    //对接服务 占比 腾讯 90% ai 10%
    protected function get_percent()
    {
        //10 满分 {"1":9,"3":1}
        $setting_model = new BySettingRepository($this->lang);
        $face_percent = $setting_model->get_setting('face_percent');
        if(empty($face_percent))
            $face_percent = '{"1":9,"3":1}';
        $face_percent = json_decode($face_percent);
        $weight = 0;
        $rand_data = array();
        foreach ($face_percent as $type => $p) {
            $weight += $p;
            for ($i = 0; $i < $p; $i++) {
                $rand_data[] = $type;
            }
        }
        $use = rand(0, $weight -1);
        $one = $rand_data[$use];
        return $one;
    }

    /**
     * @description ai 活体检测接口
     * @param string $url 人脸图片
     * @return float
     */
    public function live_check($url)
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path'] = $url;
        $flag = 0;//是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $url = $server->format_oss($format,$flag);
        $user_info = $this->userinfo;
        $staff_id = $user_info['id'];

        $setting_model = new BySettingRepository($this->lang);
        $host = $setting_model->get_settingFromCache('ai_live_host');

        $tmp = intval(microtime(true) * 1000);

        $str_token = "POST" . "\n" . "/v1/detect-live-face" . "\n" . $tmp . "\n" . $staff_id;
        $secretKey = $this->ai_Key;
        $t = hash_hmac("sha1",$str_token,$secretKey,true);
        $token = "{$tmp}_{$staff_id}_" . base64_encode($t);

        $header[] = "X-FLE-Token: " . $token;
        $param = "url={$url}";
        $res = $this->httpPost($host, $param, $header,null,5) ;//{"requestId":"c9119376-ce73-44b4-9804-2305d0b06ae6","result":false,"distance":2.109908103942871}
        $this->logger->write_log("live_check_ai ".$param.'  ' . $res['response'],'info');
        $http_code = $res['code_status'];
        $res = json_decode($res['response'],true);

        /**
         * array(3) {
        ["request_id"]=>
        string(36) "758a6d58-aeb7-4478-86b5-4b417700a375"
        ["result"]=>
        array(1) {
        ["score"]=>
        float(0.99720937013626)
        }
        ["status"]=>
        string(2) "OK"
        }
         *
         * {"error":{"code":"INTERNAL_SERVER_ERROR","message":"Upstream error: expected value at line 1 column 1"},"request_id":"01872189-3a66-48e3-ac56-683582f3c68d","status":"ERROR"}

         */
        //如果 没有返回  算未知错误 存-3
        if (empty($res) || $http_code == 0 || $http_code >= 500) {
            return -0.03;
        }


        if(!empty($res['result']) && !empty($res['result']['score'])){//调用成功
            return floatval($res['result']['score']);
        }
        if (!empty($res['error'])) {
            return $this->ai_code_format($res['error'], enums::AI_INTERFACE_2);
        }

        return 0.00;
    }


    //口罩识别
    public function mask_verify($url)
    {
        $server = new AttendanceServer($this->lang, $this->timezone);
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path'] = $url;
        $flag = 0;//是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $url = $server->format_oss($format,$flag);
        $user_info = $this->userinfo;
        $staff_id = $user_info['id'];

        $setting_model = new BySettingRepository($this->lang);
        $host = $setting_model->get_setting('ai_mask_host');

        $tmp = intval(microtime(true) * 1000);

        $str_token = "POST" . "\n" . "/v1/analyze-face" . "\n" . $tmp . "\n" . $staff_id;
        $secretKey = $this->ai_Key;
        $t = hash_hmac("sha1",$str_token,$secretKey,true);
        $token = "{$tmp}_{$staff_id}_" . base64_encode($t);

        $header[] = "X-FLE-Token: " . $token;
        $param = "url={$url}&face_attributes=mask&max_face_num=1";
        $res = $this->httpPost($host, $param, $header,null,5) ;//{"requestId":"c9119376-ce73-44b4-9804-2305d0b06ae6","result":false,"distance":2.109908103942871}
        $this->logger->write_log("mask_check_ai ".$param.'  ' . $res['response'],'info');
        $http_code = $res['code_status'];
        $res = json_decode($res['response'],true);

        //$res 返回数据结构样例
        //{
        //	"request_id": "6900e298-1eff-4b59-a462-f41e36201437",
        //	"result": {
        //		"face_list": [{
        //			"location": {
        //				"height": 714,
        //				"left": 601,
        //				"rotation": 4,
        //				"top": 275,
        //				"width": 509
        //			},
        //			"mask": {
        //				"score": 99
        //			}
        //		}],
        //		"face_num": 1
        //	},
        //	"status": "OK"
        //}

        //如果 没有返回  算未知错误 存-3
        if (empty($res) || $http_code == 0 || $http_code >= 500) {
            return -3;
        }


        if(!empty($res['result']) && !empty($res['result']['face_list'][0]['mask']['score'])){//调用成功 ai 那边说就这个层级 做成服务产品 不能改 封了800层
            return floatval($res['result']['face_list'][0]['mask']['score']);
        }
        if (!empty($res['error'])) {
            return $this->ai_code_format($res['error'], enums::AI_INTERFACE_3);
        }


        return 0;
    }



    /**
     * http POST 请求
     * @param $url
     * @param $param
     * @param null $header 如果带头信息 返回http状态和响应json 字符串  如果传null 返回json解析后的数组(不带状态码)
     * @param null $pwd
     * @param int $timeout
     * @return bool|mixed|string
     */
    private function httpPost($url, $param, $header = null,$pwd = null, $timeout = 10)
    {
        $curl = curl_init();


        $data = $param;

        $log_data = is_array($param) ? json_encode($param) : $param;

        if(!isset($header)){
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . $this->lang;
            $escape = true;
        }

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, true); // post
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // post data
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        $responseText = curl_exec($curl);
        if (curl_errno($curl)) {
            $responseText .= "error: ". curl_error($curl);
            $this->getDI()->get('logger')->write_log("url:".$url. " http_post_error {$log_data} " . curl_error($curl),'error');
        }
        //http 状态码
        $return['code_status'] = curl_getinfo($curl,CURLINFO_HTTP_CODE);
        curl_close($curl);


        $this->getDI()->get('logger')->write_log("url ".$url. " http_post {$return['code_status']} {$log_data} response {$responseText} " .json_encode($header),'info');
        if(isset($escape)){
            $responseText = json_decode($responseText,true);
            return $responseText;
        }

        $return['response'] = $responseText;
        return $return;
    }

    /**
     * 早退拦截提示
     * @throws ValidationException|\ReflectionException
     */
    public function check_Leave_earlyAction()
    {
        $paramsIn['attendance_date'] = $this->request->get('attendance_date');//考勤日期
        $paramsIn['shift_index']          = $this->request->get('shift_index') ?? 0;
        $userInfo                         = $this->userinfo;
        try {
            $checkPunchOutServer = Tools::reBuildCountryInstance(new CheckPunchOutServer($this->lang, $this->timezone),
                [$this->lang, $this->timezone]);
            $checkPunchOutServer->check_early_off($userInfo, $paramsIn['attendance_date'],
                $paramsIn['shift_index'], 0);
            return $this->jsonReturn($this->checkReturn(1));
        } catch (BusinessException|ValidationException $e) {
            $this->logger->write_log("check_Leave_early {$this->userinfo['id']} " . $e->getMessage(), 'info');
            return $this->jsonReturn($this->checkReturn([
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'data' => (object)[],
            ]));
        }
    }

    /**
     * 下班打卡公共拦截接口
     * @throws ValidationException
     */
    public function check_punch_outAction()
    {
        $paramsIn['is_skip']     = $this->request->get('is_skip');
        $paramsIn['platform']    = $this->platform;
        $paramsIn['shift_type']  = $this->request->get('shift_type') ?? 0;
        $paramsIn['shift_index'] = $this->request->get('shift_index') ?? 0;
        $userInfo                = $this->userinfo;
        $checkPunchOutServer     = Tools::reBuildCountryInstance(new CheckPunchOutServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $data                    = $checkPunchOutServer->check_staff_punch_out($userInfo, $paramsIn);
        $this->logger->write_log(" check_punch_out {$this->userinfo['id']} " . json_encode($data,
                JSON_UNESCAPED_UNICODE), 'info');
        return $this->jsonReturn($data);
    }

    /**
     * @param $error_data
     * @param int $type 接口业务类型
     * @return float
     */
    protected function ai_code_format($error_data,$type)
    {
        $enum_data = enums::$ai_error_num;
        $code = $error_data['code'];
        $score = -3;
        if (!empty($enum_data[$code])) {
            $score = $enum_data[$code];
        }

        //静默 不是百分制 需要扩大100 所以先缩小100 适应上面的代码
        if ($type == enums::AI_INTERFACE_2) {
            return $score / 100;
        }

        return $score;
    }

    public function ai_interfaceAction()
    {
        $url1 = $this->paramIn['url1'];
        $url2 = $this->paramIn['url2'];
        $res = $this->ai_interface($url1,$url2);
        return $this->ajax_fle_return('', 1, $res);
    }

    /**
     * 上班卡后的提示信息
     * @throws Exception
     */
    public function after_attendance_noticeAction()
    {

        $params = $this->userinfo;
        $service = new AfterAttendanceServer($this->lang,$this->timezone);
        $data = $service->afterPunchInNotice($params);
        $res['speed'] = $data?:(object)[];
        return $this->ajax_fle_return('', 1, $res);
    }


}