<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Server\DepartmentServer;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;


class CourierEquipmentController extends \FlashExpress\bi\App\Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode($this->request->getRawBody(),true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function addAction()
    {
        $this->jsonReturn($this->checkReturn(['data'=>[]]));
    }


}
