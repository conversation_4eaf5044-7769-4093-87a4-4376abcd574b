<?php

namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\QuickOfferServer;

class QuickOfferController extends Controllers\ControllerBase
{

    public function enumAction()
    {
        $quickOfferServer =  Tools::reBuildCountryInstance((new QuickOfferServer($this->lang, $this->timezone)),[$this->lang, $this->timezone]);
        $result             = $quickOfferServer->enums();
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    public function jdListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "type" => "Required|Int|>>>:[type] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);

        $quickOfferServer = Tools::reBuildCountryInstance((new QuickOfferServer($this->lang, $this->timezone)),[$this->lang, $this->timezone]);
        $result             = $quickOfferServer->getJDList($paramIn['type']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * 简历提交接口
     * @return void
     * @throws BusinessException
     */
    public function submitAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "quick_offer_id" => "Int|>>>:[quick_offer_id] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        //[3]业务处理
        $result = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->submitUseLock($paramIn);
        //[4]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 暂存接口
     * @return void
     * @throws BusinessException
     */
    public function saveAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]数据验证
        $validations = [
            "quick_offer_id" => "Int|>>>:[quick_offer_id] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[3]业务处理
        $result = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->save($paramIn);
        //[4]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * quick offer列表
     */
    public function listAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "page_num"  => "Required|Int|>>>:[page_num] parameter is wrong",
            "page_size" => "Required|Int|>>>:[page_size] parameter is wrong",
            "type"      => "Required|Int|>>>:[type] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $result = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->list($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * @return void
     * @throws BusinessException
     */
    public function deleteAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "quick_offer_id" => "Required|Int|>>>:[quick_offer_id] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);

        $result = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->delete($paramIn['quick_offer_id']);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data'=>$result]));
    }

    /**
     * 获取详情
     * @return void
     * @throws BusinessException
     */
    public function detailAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "quick_offer_id" => "Required|Int|>>>:[quick_offer_id] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        $result = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->detail($paramIn['quick_offer_id']);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }


    /**
     * 获取hc关联网点
     */
    public function getStoreListAction()
    {
        //[1]入参
        $paramIn  = $this->paramIn;
        //[2]数据验证
        $validations = [
            "job_id" => "Required|Int|>>>:[job_id] parameter is wrong",
            "type" => "Required|Int|>>>:[type] parameter is wrong",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $result        = Tools::reBuildCountryInstance(new QuickOfferServer($this->lang, $this->timezone),[$this->lang, $this->timezone])->getStoreList($paramIn);
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}