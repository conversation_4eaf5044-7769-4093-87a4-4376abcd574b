<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers\ControllerBase;
use FlashExpress\bi\App\Server\CriminalServer;
use Exception;

class CriminalController extends ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn)){
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }


    /**
     *
     * 详情
     *
     */
    public function infoAction()
    {
        try {

            $result = (new CriminalServer($this->lang, null))->info($this->userinfo['id']);

            $this->jsonReturn(self::checkReturn(['data' => $result]));
        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log("criminal_info " . json_encode([
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Message' => $e->getMessage(),
                    'Err_Code' => $e->getCode()
                ], JSON_UNESCAPED_UNICODE), 'error');

            $this->jsonReturn(self::checkReturn(-1));
        }

    }

    /**
     *
     *
     */
    public function sysInfoAction()
    {
        try {

            $result = (new CriminalServer($this->lang, null))->CriminalStatus();


            $this->jsonReturn(self::checkReturn(['data' => $result]));


        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log("criminal_info " . json_encode([
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Message' => $e->getMessage(),
                    'Err_Code' => $e->getCode()
                ], JSON_UNESCAPED_UNICODE), 'error');

            $this->jsonReturn(self::checkReturn(-1));
        }

    }

    /**
     *
     * 编辑接口
     *
     */
    public function editAction()
    {
        try {
            $record_status = $this->paramIn['record_status']; // 犯罪记录状态
            $is_has_criminal = $this->paramIn['is_has_criminal']; // 是否有犯罪记录

            $criminals = $this->paramIn['criminals']; // 犯罪记录

            $inspection_certificate = $this->paramIn['inspection_certificate'] ?? ""; // 检查证明
            if (!$inspection_certificate) {

                throw new \Dotenv\Exception\ValidationException($this->getTranslation()->_('miss_args'));
            }

            (new CriminalServer($this->lang, null)) -> edit([
                'staff_id' => $this->userinfo['id'],
                'record_status' => $record_status,
                'is_has_criminal' => $is_has_criminal,
                'criminals' => $criminals,
                'inspection_certificate' => json_encode($inspection_certificate, JSON_UNESCAPED_UNICODE),
                'userinfo_id' => $this->userinfo['id']
            ]);
            $this->jsonReturn(self::checkReturn(1));
        } catch (\Dotenv\Exception\ValidationException $e) {
            $this->getDI()->get('logger')->write_log("criminal_edit " . json_encode([
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Message' => $e->getMessage(),
                    'Err_Code' => $e->getCode(),
                    'data' => $this->paramIn
                ], JSON_UNESCAPED_UNICODE), 'info');

            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("criminal_edit " . json_encode([
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Message' => $e->getMessage(),
                    'Err_Code' => $e->getCode()
                ], JSON_UNESCAPED_UNICODE), 'error');
            $this->jsonReturn(self::checkReturn(-1));
        }
    }

    /**
     *
     * 是否弹出 界面
     *
     */
    public function isPopAction()
    {
        try {
            $staffId = $this->request->get('staff_info_id');
            $isPop = (new CriminalServer($this->lang, null))->isPop($staffId);

            $this->jsonReturn(self::checkReturn(['data' => ['is_pop' => $isPop]]));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("criminal_is_pop " . json_encode([
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Message' => $e->getMessage(),
                    'Err_Code' => $e->getCode()
                ], JSON_UNESCAPED_UNICODE), 'error');
            $this->jsonReturn(self::checkReturn(-1));
        }
    }

    // 个人整改书签字保存接口
    public function sign_criminalAction()
    {
        $param = $this->paramIn;
        $param['staff_id'] = $this->userinfo['staff_id'];
        $criminal = new \FlashExpress\bi\App\Server\CriminalServer($this->lang);
        $criminal->signCriminal($param);
        $this->jsonReturn(self::checkReturn(1));
    }

    /**
     *
     * 个人行为警告书
     * 拒绝签字
     *
     */
    public function refusalSignCriminalAction()
    {
        $param = $this->paramIn;
        $param['staff_id'] = $this->userinfo['staff_id'];
        $criminal = new \FlashExpress\bi\App\Server\CriminalServer($this->lang);
        $criminal->refusalSign($param);

        $this->jsonReturn(self::checkReturn(1));
    }


}