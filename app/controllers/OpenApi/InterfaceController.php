<?php
namespace FlashExpress\bi\App\Controllers\OpenApi;

use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\library\ErrCode;

class InterfaceController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 员工贷风控数据源
     */
    public function staff_loanAction()
    {
        $paramIn = $this->paramIn;
        try {
            $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
            $result = $attendanceServer->staffLoansData($paramIn['staff_id'], $paramIn['month'] ?? '', $paramIn['days'] ?? [], $paramIn['start_date'] ?? '', $paramIn['end_date'] ?? '');
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result['data']);
        } catch (\Exception $e) {
            $this->logger->write_log($e->getMessage(),'info');
            return $this->returnJson(ErrCode::ERROR,$e->getMessage(),[]);
        }
    }


    /**
     * 员工贷风控数据  员工注册
     */
    public function staff_loan_registerAction()
    {
        $paramIn = $this->paramIn;
        try {
            $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
            $result = $attendanceServer->staffInfo($paramIn['staff_id']);
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result['data']);
        } catch (\Exception $e) {
            $this->logger->write_log($e->getMessage(),'info');
            return $this->returnJson(ErrCode::ERROR,$e->getMessage(),[]);
        }
    }

}
