<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\DriverBlackListServer;

class DriverBlackListController extends Controllers\ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * @description: 审批
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/21 16:55
     */
    public function updateApprovalAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "audit_id" => "Required|Int|>>>:[audit_id] parameter is wrong",
            "status"   => "Required|Int|>>>:[status] parameter is wrong",
            "reason"   => "Str|StrLenLe:800|>>>:[reason] length exceeds maximum 800",
        ];
        $this->validateCheck($paramIn, $validations);

        $server               = (new DriverBlackListServer($this->lang, $this->timezone));
        $param['operator_id'] = $this->userinfo['staff_id'];                  //这是操作人
        $param['biz_type']    = enums::$audit_type['DRIVER_BLACKLIST'];       //类型
        $param['reason']      = $paramIn['reason'] ?? '';                     //备注
        $param['status']      = $paramIn['status'];                           //类型
        $param['id']          = $paramIn['audit_id'];                         //id

        $result = $server->setLockConf(20)->updateApproveUseLock($param);
        $this->jsonReturn($result);
    }


}
