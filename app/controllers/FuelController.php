<?php
namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\FuelService;
use Exception;

class FuelController extends Controllers\ControllerBase
{



    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }


    /**
     *
     * 开始, 结束 汇报用车接口
    https://yapi.flashexpress.pub/project/93/interface/api/21602*
     *
     */
    public function reportDriveAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, ['report_scenes' => 'Required|StrIn:start,end']);
        $fuelServer = new FuelService($this->lang, $this->timezone);
        if ($params['report_scenes'] == FuelService::SCENES_START) {
            $this->validateCheck($params, [
                'start_drive_lat' => 'Required|FloatGeLe:-180,180',
                'start_drive_lng' => 'Required|FloatGeLe:-180,180',
                'start_drive_place' => 'Required|StrLenGe:1',
                'start_drive_mileage' => 'Required|FloatGt:0',
                'start_drive_mileage_img' => 'Required|ArrLen:1',
                'start_drive_place_img' => 'Required|ArrLen:1',
                'drive_reason' => 'Required|StrLenGe:1',
            ]);
        } else {
            $fuelReport = $fuelServer->getLastFuelReport($this->userinfo['id']);
            $this->validateCheck($params, [
                'end_drive_lat' => 'Required|FloatGeLe:-180,180',
                'end_drive_lng' => 'Required|FloatGeLe:-180,180',
                'end_drive_place' => 'Required|StrLenGe:1',
                'end_drive_mileage' => 'Required|FloatGe:' . ($fuelReport ? $fuelReport->start_drive_mileage : 0) . "|>>>:" . $this->getTranslation()->_('mileage_notice'),
                'end_drive_mileage_img' => 'Required|ArrLen:1',
                'end_drive_place_img' => 'Required|ArrLen:1'
            ]);
        }
        $fuelServer->setLockConf(10)->fuelReportUseLock($this->userinfo['id'], $params);
        $this->jsonReturn($this->checkReturn([]));
    }


    /**
     *
     * 修改审批状态
    https://yapi.flashexpress.pub/project/93/interface/api/21610*
     *
     */
    public function updateFuelAction()
    {
        try {
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"           => "Required|Int",
                "audit_id"           => "Required|Int",
                "status"           => "Required|IntIn:" . enums::$audit_status['approved'].",".enums::$audit_status['dismissed']."," . enums::$audit_status['revoked'] . "|>>>:status is not invalid",
                "reject_reason"      => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
            ];
            $this->validateCheck($paramIn, $validations);

            (new FuelService($this->lang, $this->timezone))->updateFuelApprove(
                $paramIn['audit_id'],
                $paramIn['staff_id'],
                $paramIn['status'],
                $paramIn['reject_reason']);

        } catch (\Exception $e) {

            $this->getDI()->get("logger")->write_log("fuel_approve_error "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");

            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $this->jsonReturn($this->checkReturn([]));
    }


    public function buttonStateAction()
    {
        try {
            $status = (new FuelService($this->lang, $this->timezone))->buttonSwitch($this->userinfo['id']);
            $this->jsonReturn($this->checkReturn(['data' => $status]));
        } catch (\Exception $e) {

            $this->getDI()->get("logger")->write_log("fuel_button "
                . $e->getFile()
                . " line " . $e->getLine()
                . " message " . $e->getMessage()
                . " trace " . $e->getTraceAsString(), "error");

            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }

    }



}