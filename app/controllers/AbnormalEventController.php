<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\AbnormalExpenseServer;
use FlashExpress\bi\App\Server\SystemExternalApprovalServer;

class AbnormalEventController extends ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();

        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);

    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取费用类型
     */
    public function getExpenseTypeAction()
    {
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        $paramIn['externalStaffId'] = $this->userinfo['staff_id']; //这是操作人
        $paramIn['role'] = $this->userinfo['positions'];
        try {
            $abnormalExpenseServer = (new AbnormalExpenseServer($this->lang, $this->timezone));
            $result = $abnormalExpenseServer->getExpenseType($paramIn);
            $this->jsonReturn($result);
        } catch (\Exception $e) {
            $this->logger->write_log('getExpenseTypeAction:'.$e->getMessage(), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * 获取出车凭证详情
     */
    public function getProofInfoAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['param_operation_staff_id'] = $this->userinfo['staff_id']; //这是操作人
        $abnormalExpenseServer = (new AbnormalExpenseServer($this->lang, $this->timezone));
        $result = $abnormalExpenseServer->getProofInfo($paramIn);
        $this->jsonReturn($result);
    }

    /**
     * @description:添加申请
     */
    public function addApplyAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;

        try {
            $server = (new SystemExternalApprovalServer($this->lang, $this->timezone));
            $redisKey = "lock_add_abnormal_event_" . $this->userinfo['staff_id'];

            $parameters = [
                'biz_type' => AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE,
                'param_operation_type' => SystemExternalApprovalServer::param_operation_type_10,
                'freightAbnormalType' => intval($paramIn['freightAbnormalType']),
                'externalStaffId' => $this->userinfo['staff_id'],
                'freightAbnormalDetailList' => [[
                    'proofId' => $paramIn['proofId'],
                    'proofUrl' => $paramIn['proofUrl'],
                    'fee' => intval($paramIn['fee'] * 100),
                    'remark' => $paramIn['remark'],
                ]],
            ];

            //[2]业务处理
            $result = $this->atomicLock(function () use ($parameters, $server) {
                return $server->forwardParamIn($parameters);
            }, $redisKey, 5, false);

            if ($result === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            $this->jsonReturn($result);
        } catch (\Exception $e) {
            $this->logger->write_log('addApplyAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

    /**
     * @description: 审核接口
     */
    public function updateAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['operator_id'] = $this->userinfo['staff_id']; //这是操作人
        try {
            //[2]数据验证
            $validations = [
                "serial_no"  => "Required",
                "audit_id" => "Required",
                "status" => "Required",
            ];
            $this->validateCheck($paramIn, $validations);

            $server = (new AbnormalExpenseServer($this->lang, $this->timezone));
            $redisKey = "lock_update_abnormal_event_" . $this->userinfo['staff_id'];

            //[2]业务处理
            $result = $this->atomicLock(function () use ($paramIn, $server) {
                return $server->updateApprove($paramIn);
            }, $redisKey, 5, false);

            if ($result === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
            }

            $this->jsonReturn($result);
        } catch (\Exception $e) {
            $this->logger->write_log('updateAction:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }
}