<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\oa\MaterialPackageStockModel;

class WmsPlanServer extends BaseServer
{
    public $timezone;

    /**
     * WmsPlanServer constructor.
     * @param string $lang 当前语言包
     * @param string $timezone 默认时区
     */
    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 获取网点包材数据统计中表中获取当前网点、当前barcode前一天（统计日期）的结余库存数量
     * @param object $plan_task_info 库存盘点计划任务详情对象
     * @return |null
     */
    public function getReferenceStockNumber($plan_task_info)
    {
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $stock_info = MaterialPackageStockModel::findFirst([
            'columns' => 'surplus_number',
            'conditions' => 'store_id = :store_id: and barcode = :barcode: and statistical_date = :statistical_date:',
            'bind' => ['store_id' => $plan_task_info->store_id, 'barcode' => $plan_task_info->barcode, 'statistical_date' => $yesterday]
        ]);
        return !empty($stock_info) ? $stock_info->surplus_number : null;
    }
}
