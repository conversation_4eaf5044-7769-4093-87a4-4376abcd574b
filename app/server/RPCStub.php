<?php


namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Traits\FactoryTrait;
use Phalcon\Mvc\User\Component;

/**
 * RPC stub Class
 * Class RPCServer
 * @package FlashExpress\bi\App\Server
 */
class RPCStub extends Component
{
    use FactoryTrait;
    private $timezone;

    public function __construct($timezone)
    {
        $this->timezone = $timezone;
    }

    /**
     * @param $locale
     * @param $params
     * @return array
     */
    public function get_staff_shift_info($locale, $params): array
    {
        $date_at     = empty($params['date_at']) ? date('Y-m-d') : $params['date_at'];
        $shiftServer =  Tools::reBuildCountryInstance(new HrShiftServer());
        $shiftInfo   = $shiftServer->getShiftInfos($params['staff_info_id'], [$date_at]);
        $shift_start = $shiftInfo[$date_at]['start'] ?? '09:00';
        $shift_end   = $shiftInfo[$date_at]['end'] ?? '18:00';

        $shiftStartTime  = date('Y-m-d H:i:s',strtotime($date_at . ' '.$shift_start));
        $shiftEndTime  = date('Y-m-d H:i:s',strtotime($date_at . ' '.$shift_end));

        return ['shift_start' => $shift_start, 'shift_end' => $shift_end,'shift_start_date_time'=>$shiftStartTime,'shift_end_date_time'=>$shiftEndTime];
    }


    /**
     * @param $locale
     * @param $params
     * @return array
     */
    public function addHcBudget($locale, $params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->addHcBudget($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return array
     */
    public function getHcBudgetDetail($locale, $params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getHcBudgetDetail($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return mixed
     */
    public function getHcBudgetList($locale, $params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getHcBudgetList($params);
    }

    /**
     * 同部门 同职位 同网点是否有重复申请
     * @param $locale
     * @param $params
     * @return array
     */
    public function getHcBudgetDetailRepeat($locale,$params) {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getHcBudgetDetailRepeat($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return
     */
    public function auditHcBudget($locale, $params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->audit($params);
    }

    /**
     * @description 获取预算人数(已废弃)
     * @param $locale
     * @param $params
     * @return mixed
     */
    public function staffing($locale,$params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getStaffing($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return mixed
     */
    public function getHcBudgetListByDep($locale,$params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getHcBudgetListByDep($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return mixed
     */
    public function getHcBudgetListByStore($locale,$params)
    {
        $hcbs = new HcBudgetServer($locale['locale']);

        return $hcbs->getHcBudgetListByStore($params);
    }

    /**
     * ai 识别身份证信息 只调用api接口 返回识别信息
     * @param $locale
     * @param $params
     * @return array
     */
    public function AiIdCardAuditPost($locale,$params) {
        $model = new PersoninfoServer($locale['locale'],$this->timezone);

        return $model->ai_id_card_ocr_post($params['id_card_url']);
    }

    /**
     * ai 识别身份证信息 返回对比结果
     * @param $locale
     * @param $params
     * @return mixed
     * @throws \ReflectionException
     */
    public function AiIdCardAudit($locale,$params) {
        $model = Tools::reBuildCountryInstance(new PersoninfoServer($locale['locale'], $this->timezone), [$locale['locale'], $this->timezone]);

        return $model->ai_id_card_audit($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return mixed
     * @throws \ReflectionException
     */
    public function AiIdCardSubmit($locale,$params) {
        $model = Tools::reBuildCountryInstance(new PersoninfoServer($locale['locale'], $this->timezone), [$locale['locale'], $this->timezone]);

        return $model->ai_id_card_submit($params);
    }

    /**
     * @param $locale
     * @param $params
     * @return array
     */
    public function checkCanReinstate($locale,$params)
    {
        try {
            $server = $this->class_factory('ReinstatementServer',$locale['locale'],$this->timezone);
            [$result,$msg] = $server->checkCanApply($params['staff_info_id'], false);

            return ['result' => $result,'msg'=>$msg];
        }catch (BusinessException $e ){
            return ['result' => false,'msg'=>$e->getMessage()];
        }catch (\Exception $e){
            $this->logger->write_log($e->getMessage());
            return ['result' => false,'msg'=>$e->getMessage()];
        }

    }

    /**
     * 停职变在职 发送消息  停职原因是旷工和未回款
     * @param $locale
     * @param $params
     * @return mixed
     * @throws \ReflectionException
     */
    public function suspendedReinstatementSendMsg($locale, $params) {
        $server = Tools::reBuildCountryInstance(new ReinstatementServer($locale['locale'], $this->timezone), [$locale['locale'], $this->timezone]);
        $result = $server->suspendedReinstatementSendMsg($params);
        return $result;
    }

    /**
     * @param $locale
     * @param $params
     * @return array
     */
    public function apply_for_reemployment($locale, $params)
    {
        try {
            $server = Tools::reBuildCountryInstance(new ReemploymentServer($locale['locale'], $this->timezone), [$locale['locale'], $this->timezone]);
            $result = $server->addRequest($params);
            return ['code' => 1, 'data' => $result, 'msg' => 'ok'];
        } catch (\Exception $e) {
            return ['code' => 0, 'data' => '', 'msg' => $e->getMessage()];
        }
    }

    /**
     * 加班车审批状态枚举
     * @param $locale
     * @return array
     */
    public function fleet_audit_status_enums($locale): array
    {
        $fleetServer = new FleetServer($locale['locale'], $this->timezone);
        $info        = $fleetServer->getAuditStateEnums();
        return $fleetServer->checkReturn(['data' => $info]);
    }

    /**
     * 加班车 详情
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function get_fleet_detail($locale, $params): array
    {
        $fleetServer = new FleetServer($locale['locale'], $this->timezone);
        $info        = $fleetServer->getFleetDetailForSvc($params['fleet_audit_id']);

        return $fleetServer->checkReturn(['data' => $info]);
    }

    /**
     * 加班车列表
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function get_fleet_list($locale, $params): array
    {
        $server = new FleetServer($locale['locale'], $this->timezone);
        $this->logger->write_log(['get_fleet_list'=>$params],'info');
        $data   = $server->getFleetListForSvc($params);
        return $server->checkReturn(['data' => $data]);
    }

    /**
     * 获取管辖网点
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function get_staff_manage_store($locale, $params): array
    {
        $server = new StaffServer($locale['locale'], $this->timezone);
        $this->logger->write_log(['get_fleet_list' => $params], 'info');
        $data = $server->getStaffManageStore($params['staff_info_id']);
        return $server->checkReturn(['data' => $data]);
    }
    /**
     * 人脸比对配置信息
     * @param $locale
     * @param $params
     * @return array
     */
    public function facial_compare_config($locale, $params): array
    {
        $server = new FacialVerifyServer($locale['locale']);
        $data = $server->getConfig($params['staff_info_id']);
        $this->logger->write_log(['FacialVerifyServer_config'=>$data],'info');
        return $server->checkReturn(['data' => $data]);
    }

    /**
     * 人脸比对保存底片
     * @param $locale
     * @param $params
     * @return array
     * @throws Exception
     */
    public function save_face_negatives($locale, $params)
    {
        $this->logger->write_log(['save_face_negatives'=>$params], 'info');

        $server = new AttendanceServer($locale['locale'], $this->timezone);
        $source_url = $params['face_negatives_path'];

        //保存底片
        $insert['staff_info_id']          = $params['staff_info_id'];
        $insert['work_attendance_path']   = $source_url;
        $insert['work_attendance_bucket'] = $this->config->application->oss_bucket;

        $isCheckFaceBlackList = (new StaffFaceBlacklistServer())->isCheckFaceBlackList($insert['staff_info_id']);

        $switch = (new SettingEnvServer())->getSetVal('ai_mask_switch');

        if ($switch == CommonEnums::SWITCH_OPENED) {
            try {
                //图片质量检测
                $server->setValidateFaceWithMskTips();
                $server->analyzeFace($source_url, $params['staff_info_id'], false);
            } catch (ValidationException $e) {
                $this->logger->write_log(['save_face_negatives_msg_1' => $e->getMessage(),'params'=>$params], 'info');
                return $server->checkReturn(['code' => $e->getCode(), 'msg' => $e->getMessage()]);
            }
        }

        if ($isCheckFaceBlackList) {
            try {
                //处理黑名单底片逻辑
                $server->dealFaceBlackListLogic($params['staff_info_id'], $source_url, false);
            } catch (ValidationException $e) {
                $this->logger->write_log(['save_face_negatives_msg_2' => $e->getMessage(),'params'=>$params], 'info');
                return $server->checkReturn(['code' => $e->getCode(), 'msg' => $e->getMessage()]);
            }
        }
        $result = $server->save_face_img($insert);
        if (isCountry('TH') || isCountry('PH')) {
            $server = new AttendanceImageVerifyServer($locale['locale'], $this->timezone);
            $server->saveFaceNegativesValidation($params['staff_info_id'],$source_url);
        }

        return $server->checkReturn(['data'=>$result]);
    }

    /**
     * 清除session缓存
     * @param $locale
     * @param $params
     * @return mixed
     */
    public function delete_staff_session_cache($locale, $params)
    {
        $server = new LoginServer();
        return $server->deleteStaffSessionCache($params['staff_id']);
    }

    /**
     * kit人脸比对配置信息
     * @param $locale
     * @param $params
     * @return array
     */
    public function kit_facial_compare_config($locale, $params): array
    {
        $server = new FacialVerifyServer($locale['locale']);
        $data = $server->getKitConfig($params['staff_info_id']);
        return $server->checkReturn(['data' => $data]);
    }

    /**
     * 20364【TH|MY|PH】线路管理新增字段
     * @param $locale
     * @param $params
     * @return array
     */
    public function get_staff_info_by_identity($locale, $params): array
    {
        $staffServer = new StaffServer();
        $data        = $staffServer->getStaffInfoByIdentity($params['identity']);
        return $staffServer->checkReturn(['data' => $data]);
    }

    /**
     * th
     * 正式员工人脸黑名单离职复制hc
     * @param $locale
     * @param $params
     * @return array
     */
    public function face_blacklist_hit_copy_hc($locale, $params): array
    {
        $server = new StaffFaceBlacklistServer();
        $data        = $server->copyHcUseLock($params['staff_info_id']);
        return $server->checkReturn(['data' => $data]);

    }


}