<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrBlackListModel;
use FlashExpress\bi\App\Models\backyard\HrDataBackupModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOperationModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtentModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\QuickOfferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Repository\HrJDRepository;
use FlashExpress\bi\App\Repository\QuickOfferRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use Exception;


class QuickOfferServer extends BaseServer
{


    protected $notRequiredField = [];//非必填字段

    protected static $resume_is_new = false;
    protected static $resume_source = 5;//by quick offer

    //不需要 驾照正面的岗位
    protected $noNeedDriverLicenseJd = [];
    //需要 驾照正面的岗位
    protected $needDriverLicenseJd = [];


    const TYPE_IC = 1;   //个人代理
    const TYPE_RE = 2;   //普通员工
    const TYPE_MONTH = 3;//月薪制合同工

    protected static $type_setting_key_map = [
        self::TYPE_IC    => 'individual_contractor_jobids',
        self::TYPE_RE    => 'employee_jobids',
        self::TYPE_MONTH => 'monthly_employee_jobids',
    ];

    protected $operate_staff_id;
    protected $old_resume_staff_id = 0;

    protected function setOperateStaffId($staff_id): bool
    {
        $this->operate_staff_id = $staff_id;
        return true;
    }
    protected function getOperateStaffId()
    {
        return $this->operate_staff_id;
    }

    protected function setOldResumeStaffId($old_staff_id): bool
    {
        $this->old_resume_staff_id = $old_staff_id;
        return true;
    }

    protected function getOldResumeStaffId()
    {
        return $this->old_resume_staff_id;
    }


    /**
     * 必填字段验证
     * @return string[]
     */
    protected function getValidationsField($jd_id): array
    {
        return array_merge($this->getDefaultValidationsField(), $this->getOtherValidationsFieldByJD($jd_id));
    }


    /**
     * 默认必填字段
     * @return array
     */
    protected function getDefaultValidationsField(): array
    {
        return [];
    }


    /**
     * 特定职位需要补充的字段
     * @param $jd_id
     * @return array|string[]
     */
    protected function getOtherValidationsFieldByJD($jd_id): array
    {
        $result = [];
        if (!in_array($jd_id, $this->noNeedDriverLicenseJd)) {
            $result["driver_license_front"] = "StrLenGeLe:1,255|>>>:driver_license_front error";
        }
        return $result;
    }

    protected function fullParamInData($paramIn)
    {
        return $paramIn;
    }


    /**
     * 验证是否已经完善全部必填字段
     * @param $paramIn
     * @return bool
     */
    protected function validationAndCheckIsPerfect(&$paramIn): bool
    {
        $validations = $this->getValidationsField($paramIn['job_id'] ?? 0);

        $paramIn = $this->fullParamInData($paramIn);

        $is_perfect = true;
        foreach ($validations as $field => $validation) {
            if (empty($paramIn[$field])) {
                $is_perfect = false;
            }
            if (!empty($paramIn[$field])) {
                $this->validateCheck([$field => $paramIn[$field]], [$field => $validation], -3);
            }
        }

        return $is_perfect;
    }

    /**
     * 类型选项
     * @return array
     */
    protected function getType(): array
    {
        $t            = $this->getTranslation();
        $result       = [];
        $country_code = strtolower(env('country_code'));
        $t_key        = 'qo_' . $country_code . '_type_';

        //个人代理
        if ($this->getJDList(self::TYPE_IC)) {
            $result[] = ['value' => self::TYPE_IC, 'label' => $t->_($t_key . self::TYPE_IC)];
        }
        //正式员工
        if ($this->getJDList(self::TYPE_RE)) {
            $result[] = ['value' => self::TYPE_RE, 'label' => $t->_($t_key . self::TYPE_RE)];
        }
        //月薪制合同工
        if ($this->getJDList(self::TYPE_MONTH)) {
            $result[] = ['value' => self::TYPE_MONTH, 'label' => $t->_($t_key . self::TYPE_MONTH)];
        }
        return $result;
    }


    /**
     * 保存 quick offer 表
     * @param $paramIn
     * @param $state
     * @return array
     * @throws BusinessException
     */
    protected function saveQuickOffer($paramIn, $state): array
    {
        if (!empty($paramIn['quick_offer_id'])) {
            $model = QuickOfferModel::findFirstById($paramIn['quick_offer_id']);
            if (in_array($state, [
                    QuickOfferModel::STATE_EDITING,
                    QuickOfferModel::STATE_WAIT_SUBMIT,
                ]) && $model->state == QuickOfferModel::STATE_SUBMITTED) {
                throw new BusinessException($this->getTranslation()->_('4012'));
            }
        } else {
            $model = new QuickOfferModel();
        }
        if (!empty($paramIn['driver_license_front'])) {
            $paramIn['driver_license_front'] = str_replace(env('img_prefix'), '', $paramIn['driver_license_front']);
        }

        if (!empty($paramIn['credentials_img'])) {
            $paramIn['credentials_img'] = str_replace(env('img_prefix'), '', $paramIn['credentials_img']);
        }

        if (!empty($paramIn['vehicle_photo'])) {
            $paramIn['vehicle_photo'] = str_replace(env('img_prefix'), '', $paramIn['vehicle_photo']);
        }
        if (!empty($paramIn['bank_statement'])) {
            $paramIn['bank_statement'] = str_replace(env('img_prefix'), '', $paramIn['bank_statement']);
        }

        if (!empty($paramIn['half_length_photo'])) {
            $paramIn['half_length_photo'] = str_replace(env('img_prefix'), '', $paramIn['half_length_photo']);
        }

        $model->job_id     = $paramIn['job_id'] ?? 0;
        $model->hc_id      = $paramIn['hc_id'] ?? 0;
        $model->first_name = $paramIn['first_name'] ?? '';
        if (!empty($paramIn['name'])) {
            $model->first_name = $paramIn['name'];
        }
        $model->last_name          = $paramIn['last_name'] ?? '';
        $model->store_id           = $paramIn['store_id'] ?? '';
        $model->phone              = $paramIn['phone'] ?? '';
        $model->type               = $paramIn['type'] ?? 0;
        $model->submit_at          = gmdate('Y-m-d H:i:s');
        $model->submitter_staff_id = $paramIn['staff_id'];
        $model->state              = $state;
        $model->resume_id          = $paramIn['resume_id'] ?? 0;
        $model->interview_id       = $paramIn['interview_id'] ?? 0;
        $model->form_data          = json_encode(array_only($paramIn,
            array_merge(array_keys($this->getValidationsField($paramIn['job_id'])), $this->notRequiredField)),
            JSON_UNESCAPED_UNICODE);
        $model->save();
        return ['quick_offer_id' => $model->id];
    }

    protected function getRelationshipList(): array
    {
        $t = $this->getTranslation();
        return [
            ['value' => 1, 'label' => $t->_('relatives_relationship_1')],
            ['value' => 2, 'label' => $t->_('relatives_relationship_2')],
            ['value' => 3, 'label' => $t->_('relatives_relationship_3')],
            ['value' => 4, 'label' => $t->_('relatives_relationship_4')],
            ['value' => 5, 'label' => $t->_('relatives_relationship_5')],
            ['value' => 6, 'label' => $t->_('relatives_relationship_6')],
        ];
    }


    /**
     * 获取静态枚举
     * @return array
     */
    public function enums(): array
    {
        $result['type'] = $this->getType();
        return $result;
    }

    /**
     * 获取jd
     * @param $type
     * @return array
     */
    public function getJDList($type): array
    {
        $key = self::$type_setting_key_map[$type] ?? '';

        $settingServer = new SettingEnvServer();
        $qo_jds        = $settingServer->getSetVal($key, ',');
        if (empty($qo_jds)) {
            return [];
        }
        $jdList = HrJDRepository::getJDList($qo_jds);
        foreach ($jdList as &$item) {
            $item['is_need_driver_license'] = !in_array($item['job_id'], $this->noNeedDriverLicenseJd);
        }
        return $jdList;
    }


    /**
     * 有hc的网点
     * @param $params
     * @return array
     */
    public function getStoreList($params): array
    {
        return (new QuickOfferRepository($this->lang))->getHasHcStoreList($params);
    }

    /**
     * 删除
     * @param int $quick_offer_id
     * @return bool
     * @throws BusinessException
     */
    public function delete(int $quick_offer_id): bool
    {
        $model = QuickOfferModel::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $quick_offer_id],
            ]
        );
        if (empty($model)) {
            throw new BusinessException('this is quick_offer not exists');
        }
        $model->deleted = 1;
        if (!$model->save()) {
            throw new BusinessException('operation failed please try again');
        }
        return true;
    }

    /**
     * 获取列表
     * @param $paramIn
     * @return array
     */
    public function list($paramIn): array
    {
        $pageNum              = $paramIn["page_num"] ?? 1;
        $pageSize             = $paramIn["page_size"] ?? 10;
        $staff_id             = $paramIn['staff_id'];
        $quickOfferRepository = new QuickOfferRepository($this->lang);
        if ($paramIn['type'] == 2) {
            //获取已经提交列表
            return $quickOfferRepository->getSubmittedList($staff_id, $pageNum, $pageSize);
        }
        //获取待提交列表
        return $quickOfferRepository->getWaitSubmitList($staff_id, $pageNum, $pageSize);
    }


    /**
     * 保存
     * @param $paramIn
     * @return array
     * @throws BusinessException
     */
    public function save($paramIn): array
    {
        $is_perfect = $this->validationAndCheckIsPerfect($paramIn);
        return $this->saveQuickOffer($paramIn,
            $is_perfect ? QuickOfferModel::STATE_WAIT_SUBMIT : QuickOfferModel::STATE_EDITING);
    }

    /**
     * @throws BusinessException
     */
    protected function validationBlackList($paramIn)
    {
        $blackListExist = HrBlackListModel::findFirst([
                'conditions' => 'mobile = :mobile: and status  = 1',
                'bind'       => ['mobile' => $paramIn['phone']],
            ]
        );
        if ($blackListExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_003',
                ['mobile' => $paramIn['phone']]));
        }
        if (!empty($paramIn['credentials_num'])) {
            $outsourcingBlackList = (new OutsourcingBlackListServer())->check($paramIn['credentials_num'], 'winhr', false,$this->lang);
            if ($outsourcingBlackList && $outsourcingBlackList['is_black']){
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_008',
                    ['credentials_num' => $paramIn['credentials_num']]));
            }
        }
        $outsourcingBlackList = (new OutsourcingBlackListServer())->check_v1($paramIn['phone'], 'winhr', false,$this->lang);
        if ($outsourcingBlackList && $outsourcingBlackList['is_black']) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_003',
                ['mobile' => $paramIn['phone']]));
        }
    }

    protected function validationStaffInfoMobile($paramIn)
    {
        //19097【PH|WHR&BY】员工状态校验规则变更
        //创建简历时校验开关-N代表关，Y代表开
        $create_resume_verify = (new SettingEnvServer())->getSetValFromCache('create_resume_verify');

        //如果所有国家都走开关，则把 国家判断去掉
        $conditionsSwitch = '';
        if(!empty($create_resume_verify) && strtoupper($create_resume_verify) === 'N' && isCountry('PH')) {
            $conditionsSwitch = 'formal in ({formals:array}) and ';
            $bind['formals'] = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
        }

        $conditions = $conditionsSwitch . " mobile = :mobile: and state in (1,3) and is_sub_staff = 0";

        $bind['mobile']        = $paramIn['phone'];

        $phoneExist = HrStaffInfoModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if ($phoneExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_002', ['mobile' => $paramIn['phone']]));
        }
    }


    /**
     * @throws BusinessException
     */
    protected function validationAndGetHcId($type, $hc_id,&$paramIn)
    {
        $conditions = 'hc_id = :hc_id: and state_code = :state_code: and reason_type in ({reason_type:array})';
        $bind       = [
            'state_code'  => HrHcModel::STATE_RECRUITING,
            'hc_id'       => $hc_id,
            'reason_type' => [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT],
        ];
        //个人代理
        if ($type == QuickOfferServer::TYPE_IC) {
            $conditions        .= " and hire_type = :hire_type:";
            $bind['hire_type'] = HrHcModel::HIRE_TYPE_CONTRACT_LABOUR;
        }
        //普通员工 【quick offer 雇佣类型联动修改点(1,2)】 - 1
        if ($type == QuickOfferServer::TYPE_RE) {
            $bind['hire_type'] = [HrHcModel::HIRE_TYPE_CONTRACT_LABOUR];
            if (isCountry(['MY', 'PH'])) {
                $bind['hire_type'] = [
                    HrHcModel::HIRE_TYPE_CONTRACT_LABOUR,
                    HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS,
                ];
            }
            $conditions .= " and hire_type not in ({hire_type:array})";
        }
        //月薪制
        if ($type == QuickOfferServer::TYPE_MONTH) {
            $conditions        .= " and hire_type = :hire_type:";
            $bind['hire_type'] = HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS;
        }

        $hc = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'priority_id ASC, surplusnumber DESC, approval_completion_time ASC',
        ]);
        if (empty($hc)) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_004'));
        }
        $paramIn['worknode_id'] = $hc->worknode_id;
        return $hc->hc_id;
    }

    /**
     * @throws BusinessException
     */
    protected function validationStartDate($paramIn)
    {
        if ($paramIn['expected_arrivaltime'] < date('Y-m-d') || $paramIn['expected_arrivaltime'] > date('Y-m-d',
                strtotime('+ 7 days'))) {
            throw  new BusinessException($this->getTranslation()->_('qo_error_msg_006'));
        }
    }

    /**
     * @throws BusinessException
     */
    protected function checkAndGetResume($paramIn)
    {
        $t = $this->getTranslation();
        //是否存在于简历库
        $resumeFromPhone = HrResumeModel::findFirst([
            'conditions' => "phone = :phone: and deleted = 0",
            'bind'       => ['phone' => $paramIn['phone']],
        ]);

        if (!empty($paramIn['credentials_num'])) {
            $resumeFromIdCredentialsNum = HrResumeModel::findFirst([
                'conditions' => "credentials_num = :credentials_num: and deleted = 0",
                'bind'       => ['credentials_num' => $paramIn['credentials_num']],
            ]);
        }

        if (!empty($resumeFromPhone) && !empty($resumeFromIdCredentialsNum)) {
            if ($resumeFromPhone->id != $resumeFromIdCredentialsNum->id) {
                throw new BusinessException($t->_('qo_error_msg_005'));
            }
        }
        if (!empty($resumeFromPhone)) {
            $this->checkEntry($resumeFromPhone->id,$t->_('qo_error_msg_002', ['mobile' => $paramIn['phone']]));
            return $resumeFromPhone;
        }

        if (!empty($resumeFromIdCredentialsNum)) {
            $this->checkEntry($resumeFromIdCredentialsNum->id,$t->_('qo_error_msg_009', ['credentials_num' => $paramIn['credentials_num']]));
            return $resumeFromIdCredentialsNum;
        }
        return false;
    }


    /**
     * @throws BusinessException
     */
    protected function checkEntry($resume_id, $notice): bool
    {
        if(empty($resume_id)){
            return false;
        }

        $entryInfo = HrEntryModel::find([
            'conditions' => "resume_id = :resume_id: and staff_id > 0",
            'bind'       => ['resume_id' => $resume_id],
        ])->toArray();

        if (empty($entryInfo)) {
            return true;
        }
        $staff_ids = array_column($entryInfo, 'staff_id');

        //19097【PH|WHR&BY】员工状态校验规则变更
        //创建简历时校验开关-N代表关，Y代表开
        $create_resume_verify = (new SettingEnvServer())->getSetValFromCache('create_resume_verify');

        //如果所有国家都走开关，则把 国家判断去掉
        $bind             = [
            'staff_info_id' => $staff_ids,
        ];
        $conditionsSwitch = '';
        if (!empty($create_resume_verify) && strtoupper($create_resume_verify) === 'N' && isCountry('PH')) {
            $conditionsSwitch = ' and formal in ({formals:array}) ';
            $bind['formals']  = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
        }

        $staffInfo = HrStaffInfoModel::find([
            'columns'    => 'state,staff_info_id,formal',
            'conditions' => "staff_info_id in ({staff_info_id:array})  and is_sub_staff = 0 " . $conditionsSwitch,
            'bind'       => $bind,
            'order'      => 'hire_date desc',
        ])->toArray();

        if (!empty($staffInfo)) {
            foreach ($staffInfo as $item) {
                if (in_array($item['state'], [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION])) {
                    throw new BusinessException($notice);
                }
                //简历存在离职工号 用来走复制简历流程
                if (in_array($item['formal'], [
                        HrStaffInfoModel::FORMAL_1,
                        HrStaffInfoModel::FORMAL_INTERN,
                    ]) && HrStaffInfoModel::STATE_RESIGN == $item['state']) {
                    $this->setOldResumeStaffId($item['staff_info_id']);
                    break;
                }
            }
        }
        return true;
    }


    /**
     * @param $resumeInfo
     * @return HrResumeModel
     * @throws BusinessException
     */
    protected function deleteAndCreateResume($resumeInfo): HrResumeModel
    {
        $this->deleteAndBackupResume($resumeInfo);
        return $this->copyNewResume($resumeInfo);
    }


    /**
     * 删除并备份简历
     * @param $resumeInfo
     * @return bool
     */
    protected function deleteAndBackupResume($resumeInfo): bool
    {
        $operate_staff_id = $this->getOperateStaffId();
        $traceid          = molten_get_traceid();
        $db               = $this->getDI()->get("db");
        //删除到岗确认 hr_entry
        $entry_info = HrEntryModel::find([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => ['resume_id' => $resumeInfo->id],
        ])->toArray();
        $backupData = [];
        if (!empty($entry_info)) {
            $hr_entry_del_sql = "DELETE FROM `hr_entry` WHERE `resume_id` = ?;";
            $db->execute($hr_entry_del_sql, [$resumeInfo->id]);
            $backupData[] = [
                'traceid'       => $traceid,
                'staff_info_id' => $operate_staff_id,
                'table'         => 'hr_entry',
                'content'       => json_encode($entry_info, JSON_FORCE_OBJECT),
            ];
        }
        if (!isCountry('TH')) {
            //删除黑名单
            $hr_black_list = HrBlacklistModel::find([
                'conditions' => 'identity = :identity: or mobile = :mobile:',
                'bind'       => [
                    'identity' => $resumeInfo->credentials_num,
                    'mobile'   => $resumeInfo->phone,
                ],
            ])->toArray();
            if (!empty($hr_black_list)) {
                $hr_black_del_sql = "DELETE FROM `hr_blacklist` WHERE `mobile` = ? or `identity` = ?;";
                $db->execute($hr_black_del_sql, [$resumeInfo->phone, $resumeInfo->credentials_num]);
                $backupData[] = [
                    'traceid'       => $traceid,
                    'staff_info_id' => $operate_staff_id,
                    'table'         => 'hr_blacklist',
                    'content'       => json_encode($hr_black_list, JSON_FORCE_OBJECT),
                ];
            }
        }

        //删除面试offer hr_interview_offer
        $hr_interview_offer = HrInterviewOfferModel::find([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => ['resume_id' => $resumeInfo->id],
        ])->toArray();
        if (!empty($hr_interview_offer)) {
            $hr_interview_offer_del_sql = "DELETE FROM `hr_interview_offer` WHERE `resume_id` = ?;";
            $db->execute($hr_interview_offer_del_sql, [$resumeInfo->id]);
            $backupData[] = [
                'traceid'       => $traceid,
                'staff_info_id' => $operate_staff_id,
                'table'         => 'hr_interview_offer',
                'content'       => json_encode($hr_interview_offer, JSON_FORCE_OBJECT),
            ];
        }

        //删除面试数据 hr_interview
        $hr_interview = HrInterviewModel::find([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => ['resume_id' => $resumeInfo->id],
        ])->toArray();
        if (!empty($hr_interview)) {
            $hr_interview_del_sql = "DELETE FROM `hr_interview` WHERE `resume_id` = ?;";
            $db->execute($hr_interview_del_sql, [$resumeInfo->id]);
            $backupData[] = [
                'traceid'       => $traceid,
                'staff_info_id' => $operate_staff_id,
                'table'         => 'hr_interview',
                'content'       => json_encode($hr_interview, JSON_FORCE_OBJECT),
            ];
        }
        //删除简历 hr_resume
        $hr_resume_del_sql = "DELETE FROM `hr_resume` WHERE `id` = ?;";
        $db->execute($hr_resume_del_sql, [$resumeInfo->id]);
        $backupData[] = [
            'traceid'       => $traceid,
            'staff_info_id' => $operate_staff_id,
            'table'         => 'hr_resume',
            'content'       => json_encode($resumeInfo->toArray(), JSON_FORCE_OBJECT),
        ];
        (new HrDataBackupModel())->batch_insert($backupData,
            BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        return true;
    }

    /**
     * @param $oldResumeInfo
     * @return HrResumeModel
     * @throws BusinessException
     */
    public function copyNewResume($oldResumeInfo): HrResumeModel
    {
        if(empty($oldResumeInfo)){
            throw new BusinessException('CVID not exist');
        }
        $db = $this->getDI()->get("db");
        $params = $oldResumeInfo->toArray();
        $staff_resume_id = $params['id'];

        $oldResumeExtend = HrResumeExtentModel::findFirst(
            [
                'conditions' => "resume_id = :resume_id:",
                'bind' => ['resume_id' => $staff_resume_id]
            ]
        );

        $newResume                           = new HrResumeModel();
        $newResume->work_city_id             = $params['work_city_id'];             //期望工作城市
        $newResume->work_district_id         = $params['work_district_id'];         //期望工作城市
        $newResume->address_id               = $params['address_id'];               //期望工作城市
        $newResume->job_type                 = $params['job_type'];                 //岗位类别
        $newResume->job_id                   = $params['job_id'];                   //职位id
        $newResume->currency                 = $params['currency'];                 //币种
        $newResume->entry_salary             = $params['entry_salary'];             //期望薪资
        $newResume->alternative_job_ids      = $params['alternative_job_ids'];      //备选岗位
        $newResume->expected_arrivaltime     = $params['expected_arrivaltime'];     //预计到岗时间
        $newResume->name                     = $params['name'];                     //姓名
        $newResume->first_name               = $params['first_name'];
        $newResume->last_name                = $params['last_name'];
        $newResume->call_name                = $params['call_name'];
        $newResume->first_name_en            = $params['first_name_en'];
        $newResume->last_name_en             = $params['last_name_en'];
        $newResume->suffix_name              = $params['suffix_name'];
        $newResume->middle_name              = $params['middle_name'];
        $newResume->nationality              = $params['nationality'];                     //国籍
        $newResume->date_birth               = $params['date_birth'];                      //出生日期
        $newResume->permit_number            = $params['permit_number'];                   //工作证号
        $newResume->credentials_category     = $params['credentials_category'];            //证件类型
        $newResume->credentials_num          = $params['credentials_num'];                 //证件号
        $newResume->expiration_date          = $params['expiration_date'];                 //失效日期
        $newResume->cert_place               = $params['cert_place'];                      //发征地
        $newResume->phone_area_code          = $params['phone_area_code'];                 //区号
        $newResume->phone                    = $params['phone'];                           //手机号-联系电话
        $newResume->email                    = $params['email'];                           //邮箱
        $newResume->line_id                  = $params['line_id'];                         //Line ID
        $newResume->register_house_num       = $params['register_house_num'];              //户口所在地门牌号
        $newResume->register_village_num     = $params['register_village_num'];            //register_village_num
        $newResume->register_village         = $params['register_village'];                //户口所在地村庄
        $newResume->register_alley           = $params['register_alley'];                  //户口所在地巷子
        $newResume->register_street          = $params['register_street'];                 //户口所在地街道
        $newResume->register_country         = $params['register_country'];                //户口所在地国家
        $newResume->register_government      = $params['register_government'];             //户口所在地 府
        $newResume->register_city            = $params['register_city'];                   //户口所在地市
        $newResume->register_town            = $params['register_town'];                   //户口所在地镇
        $newResume->register_postcodes       = $params['register_postcodes'];              //户口所在地邮编
        $newResume->register_detail_address  = $params['register_detail_address'];         //户口所在地详情
        $newResume->residence_house_num      = $params['residence_house_num'];             //居住所在地门牌号
        $newResume->residence_village_num    = $params['residence_village_num'];           //居住所在地村号
        $newResume->residence_village        = $params['residence_village'];               //居住所在地村庄
        $newResume->residence_alley          = $params['residence_alley'];                 //居住所在地巷
        $newResume->residence_street         = $params['residence_street'];                //居住所在地街道
        $newResume->residence_country        = $params['residence_country'];               //居住地所在地国家
        $newResume->residence_government     = $params['residence_government'];            //居住所在地 府
        $newResume->residence_city           = $params['residence_city'];                  //居住所在地市
        $newResume->residence_town           = $params['residence_town'];                  //居住所在地镇
        $newResume->residence_postcodes      = $params['residence_postcodes'];             //居住所在地邮编
        $newResume->residence_detail_address = $params['residence_detail_address'];        //居住所在地详情
        $newResume->fit                      = $params['fit'];                             //居住地是否和户口所在地一致? 1=一致;0=不一致
        $newResume->religion                 = $params['religion'];                        //宗教
        $newResume->race                     = $params['race'];                            //种族
        $newResume->fund_num                 = $params['fund_num'];                        //公积金号码
        $newResume->tax_no                   = $params['tax_no'];                          //税号
        $newResume->state_code               = HrResumeModel::STATE_CODE_NO_COMMUNICATION;
        $newResume->recruit_type             = 1;//'招聘类型 1社会招聘 2实习生招聘 3应届生招聘';
        $newResume->hc_id                    = 0;
        $newResume->deleted                  = 0;
        $newResume->source                   = HrResumeModel::SOURCE_BY_QUICK_OFFER;//quick offer
        $newResume->recruit_channel          = $this->getQuickOfferRecruitChannel();
        $newResume->save();

        self::$resume_is_new = true;
        $new_resume_id       = $newResume->id;
        $newResume = $newResume::findFirstById($new_resume_id);
        //保留问卷 把原来问卷中的 resume_id 更新为新的 resume_id
        $db->updateAsDict('hr_survey_question', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);

        //保留家庭信息 把原来家庭信息中的 resume_id 更新为新的 resume_id
        $db->updateAsDict('hr_family', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);

        //保留附件信息 hr_annex
        $db->updateAsDict('hr_annex', ['oss_bucket_key' => $new_resume_id,], 'type = '. HrAnnexModel::TYPE_RESUME ." and oss_bucket_key = '$staff_resume_id' ");

        //保留经济与能力 把原来 resume_id 更新为新的 resume_id 包括电脑能力/车辆驾照信息/教育经历
        $db->updateAsDict('hr_economy_ability', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);

        //保留教育经历 hr_resume_education
        $db->updateAsDict('hr_resume_education', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);

        //保留工作经历 hr_work_experience
        $db->updateAsDict('hr_work_experience', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);

        //保留外语能力 hr_languages
        $db->updateAsDict('hr_languages', ['resume_id' => $new_resume_id,], 'resume_id = ' . $staff_resume_id);
        if ($this->getOldResumeStaffId()) {
            $resumeExtend                  = new HrResumeExtentModel();
            $resumeExtend->old_staff_id = $this->getOldResumeStaffId();
            $resumeExtend->resume_id       = $new_resume_id;

            if ($oldResumeExtend) {
                $resumeExtend->is_have_social_security   = $oldResumeExtend->is_have_social_security;
                $resumeExtend->social_security_num       = $oldResumeExtend->social_security_num;
                $resumeExtend->is_have_medical_insurance = $oldResumeExtend->is_have_medical_insurance;
                $resumeExtend->medical_insurance_num     = $oldResumeExtend->medical_insurance_num;
                $resumeExtend->is_have_fund              = $oldResumeExtend->is_have_fund;
                $resumeExtend->is_have_or_cr             = $oldResumeExtend->is_have_or_cr;
            }
            $resumeExtend->save();
        }
        return $newResume;
    }



    /**
     * 提交
     * @param $paramIn
     * @return bool
     * @throws BusinessException
     */
    public function submit($paramIn): bool
    {

        $this->setOperateStaffId($paramIn['staff_id']);

        $is_perfect = $this->validationAndCheckIsPerfect($paramIn);

        if (!$is_perfect) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_01'));
        }
        //验证开始日期
        $this->validationStartDate($paramIn);

        //验证是否被在职员工使用
        $this->validationStaffInfoMobile($paramIn);
        //验证是否在黑名单
        $this->validationBlackList($paramIn);
        //验证hc
        $hc_id = $this->validationAndGetHcId($paramIn['type'], $paramIn['hc_id'],$paramIn);

        //开启事务
        $db = $this->getDI()->get("db");
        $db->begin();

        $resumeModel      = $this->checkAndGetResume($paramIn);

        $paramIn['hc_id'] = $hc_id;
        //验证面试信息
        $interviewModel = $this->validationInterview($resumeModel, $paramIn);
        if ($interviewModel === true) {
            $db->commit();
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_005'));
        }

        try {

            //非在职留着 新建简历用
            if($resumeModel && $this->getOldResumeStaffId()){
                //执行删除、备份、新建简历
                $resumeModel = $this->deleteAndCreateResume($resumeModel);
                $interviewModel = null;
            }

            //简历信息绑定
            $resumeModel = $this->fullResumeInfo($resumeModel, $paramIn);
            //处理面试信息
            $interviewModel = $this->dealInterview($interviewModel, $resumeModel, $paramIn);
            //更新 quick offer 表
            $paramIn['resume_id']    = $resumeModel->id;
            $paramIn['interview_id'] = $interviewModel->interview_id;
            $this->saveQuickOffer($paramIn, QuickOfferModel::STATE_SUBMITTED);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * @throws BusinessException
     */
    protected function validationInterview($resumeModel, &$paramIn)
    {
        if (empty($resumeModel)) {
            return false;
        }
        $interviewModel = HrInterviewModel::findFirst([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => ['resume_id' => $resumeModel->id],
            'order'      => 'interview_id desc',
        ]);

        if ($interviewModel) {
            //已存在该简历，无需重复操作
            if (HrInterviewModel::INTERVIEW_LSSUED_OFFER == $interviewModel->state) {
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_005'));
            }
            if (HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER == $interviewModel->state) {
                if ($resumeModel->recruit_channel != $this->getQuickOfferRecruitChannel()) {
                    $resumeModel->recruit_channel =  $this->getQuickOfferRecruitChannel();
                    $resumeModel->save();
                    //编辑简历
                    (new LogServer())->addLog([
                        'module_id'       => $resumeModel->id,
                        'module_type'     => HrLogModel::$log_module_type['base_info'],
                        'action'          => HrLogModel::$log_option['modify'],
                        'data_after'      => $paramIn,
                        'current_user_id' => $paramIn['staff_id'],
                    ]);
                }
                return true;
            }
            return $interviewModel;
        }
        return false;
    }

    /**
     * 开启新一轮面试
     * @param $resumeModel
     * @param $paramIn
     * @return array
     */
    protected function createNewInterview($resumeModel, $paramIn): array
    {
        $interviewModel = HrInterviewModel::findFirst([
            'conditions' => 'resume_id = :resume_id: and hc_id = :hc_id:',
            'bind'       => ['resume_id' => $resumeModel->id, 'hc_id' => $resumeModel->hc_id],
            'order'      => 'interview_id desc',
        ]);
        if (empty($interviewModel)) {
            //预约面试
            $interviewModel            = new HrInterviewModel();
            $interviewModel->resume_id = $resumeModel->id;
            $interviewModel->hc_id     = $resumeModel->hc_id;
            $interviewModel->job_id    = $resumeModel->job_id;
        }
        $interviewModel->cancel_type   = 0;
        $interviewModel->cancel_reason = '';
        $interviewModel->state         = HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER;
        $interviewModel->save();

        $interviewSubscribe = HrInterviewSubscribeModel::findFirst([
            'conditions' => 'interview_id = :interview_id: and hc_id = :hc_id:',
            'bind'       => ['interview_id' => $interviewModel->interview_id, 'hc_id' => $resumeModel->hc_id],
            'order'      => 'id desc',
        ]);
        if (empty($interviewSubscribe)) {
            $interviewSubscribe               = new HrInterviewSubscribeModel();
            $interviewSubscribe->interview_id = $interviewModel->interview_id;
            $interviewSubscribe->hc_id        = $resumeModel->hc_id;
        }

        $interviewSubscribe->interview_back_cancel_type   = 0;
        $interviewSubscribe->interview_back_cancel_reason = '';
        $interviewSubscribe->interviewer_id               = $paramIn['staff_id'];
        $interviewSubscribe->interview_time               = gmdate('Y-m-d H:i:s');
        $interviewSubscribe->interview_back               = HrInterviewSubscribeModel::INTERVIEW_BACK_NO;
        $interviewSubscribe->status                       = 1;// 面试完成
        $interviewSubscribe->hr_remark                    = 'quick offer';
        $interviewSubscribe->save();
        //log
        (new LogServer())->addLog([
            'module_id'       => $interviewModel->interview_id,
            'module_type'     => HrLogModel::$log_module_type['interview'],
            'action'          => HrLogModel::$log_option['create'],
            'data_after'      => [],
            'current_user_id' => $paramIn['staff_id'],
        ]);
        return [$interviewModel, $interviewSubscribe];
    }

    protected function getInterviewLevel($interviewModel)
    {
        $count = HrInterviewInfoModel::count([
            'conditions' => 'interview_id = :interview_id:',
            'bind'       => [
                'interview_id' => $interviewModel->interview_id,
            ],
        ]);
        return $count + 1;
    }


    protected function cancelInterview($interviewModel, $paramIn, $flag)
    {
        $interviewSubscribe = HrInterviewSubscribeModel::findFirst([
            'conditions' => 'interview_id = :interview_id:',
            'bind'       => ['interview_id' => $interviewModel->interview_id],
            'order'      => 'id desc',
        ]);
        if ($interviewSubscribe) {
            if ($flag) {
                $interviewSubscribe->hr_remark      = $interviewSubscribe->hr_remark . ' quick offer';
                $interviewSubscribe->interviewer_id = $paramIn['staff_id'];
                $interviewSubscribe->status         = 1;// 未取消
            } else {
                $interviewSubscribe->status = 2;// 取消
            }
            $interviewSubscribe->save();

            $interviewerOperationModel = HrInterviewOperationModel::findFirst([
                'conditions' => 'interview_sub_id = :interview_sub_id:',
                'bind'       => ['interview_sub_id' => $interviewSubscribe->id],
                'order'      => 'id desc',
            ]);
            if (!empty($interviewerOperationModel)) {
                $interviewerOperationModel->hr_remark = $interviewerOperationModel->hr_remark . ' quick offer';
                $interviewerOperationModel->state     = HrInterviewOperationModel::STATE_CANCEL;//面试取消
                $interviewerOperationModel->cancel_at = gmdate('Y-m-d H:i:s');
                $interviewerOperationModel->save();
                $interviewInfoModel = HrInterviewInfoModel::findFirst([
                    'conditions' => 'interview_id = :interview_id:',
                    'bind'       => [
                        'interview_id' => $interviewModel->interview_id,
                    ],
                    'order'      => 'interview_info_id desc',
                ]);
                if ($interviewInfoModel) {
                    $interviewInfoModel->state    = 2;//下一步
                    $interviewInfoModel->evaluate = $interviewInfoModel->evaluate . ' quick offer';
                    $interviewInfoModel->save();
                }
            }
            return $interviewSubscribe;
        }
        return false;
    }


    /**
     * @throws BusinessException
     * @throws Exception
     */
    protected function dealInterview($interviewModel, $resumeModel, $paramIn): ?HrInterviewModel
    {
        if (!empty($interviewModel)) {
            if (in_array($interviewModel->state,
                [HrInterviewModel::WAITING_INTERVIEW, HrInterviewModel::INTERVIEW_IN_THE])) {
                //更换hc
                if ($interviewModel->hc_id != $resumeModel->hc_id) {
                    $interviewModel->state = HrInterviewModel::INTERVIEW_CANCELLED;
                    $interviewModel->save();
                    //取消流程中的面试
                    $this->cancelInterview($interviewModel, $paramIn, false);
                    //新一轮面试
                    [$interviewModel, $interviewSubscribe] = $this->createNewInterview($resumeModel, $paramIn);
                } else {
                    $interviewModel->state = HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER;
                    $interviewModel->save();
                    $interviewSubscribe = $this->cancelInterview($interviewModel, $paramIn, true);
                }
            } elseif (in_array($interviewModel->state,
                [
                    HrInterviewModel::INTERVIEW_REJECTED,
                    HrInterviewModel::INTERVIEW_CANCELLED,
                    HrInterviewModel::INTERVIEW_EMPLOYEED,
                ])) {
                if ($interviewModel->hc_id == $resumeModel->hc_id) {
                    $interviewModel->state = HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER;
                    $interviewModel->save();
                    $interviewSubscribe = HrInterviewSubscribeModel::findFirst([
                        'conditions' => 'interview_id = :interview_id:',
                        'bind'       => ['interview_id' => $interviewModel->interview_id],
                        'order'      => 'id desc',
                    ]);
                    if ($interviewSubscribe) {
                        $interviewSubscribe->hr_remark      = $interviewSubscribe->hr_remark . ' quick offer';
                        $interviewSubscribe->interviewer_id = $paramIn['staff_id'];
                        $interviewSubscribe->status         = 1;// 未取消
                        $interviewSubscribe->save();
                    }
                } else {
                    //新一轮面试
                    [$interviewModel, $interviewSubscribe] = $this->createNewInterview($resumeModel, $paramIn);
                }
            } else {
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_005') . ' -1');
            }
        } else {
            //简历未沟通
            if ($resumeModel->state_code == HrResumeModel::STATE_CODE_NO_COMMUNICATION) {
                //预约面试
                [$interviewModel, $interviewSubscribe] = $this->createNewInterview($resumeModel, $paramIn);
            } else {
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_005') . ' -2');
            }
        }
        $level = $this->getInterviewLevel($interviewModel);

        $interviewerOperationModel                   = new HrInterviewOperationModel();
        $interviewerOperationModel->interview_sub_id = $interviewSubscribe->id;
        $interviewerOperationModel->interview_time   = gmdate('Y-m-d H:i:s');
        $interviewerOperationModel->interviewer_id   = $paramIn['staff_id'];
        $interviewerOperationModel->create_id        = $paramIn['staff_id'];
        $interviewerOperationModel->shop_id          = $paramIn['store_id'];                 //网点
        $interviewerOperationModel->state            = HrInterviewOperationModel::STATE_PASS;//面试通过
        $interviewerOperationModel->hr_remark        = 'quick offer';
        $interviewerOperationModel->save();

        $interviewInfoModel                 = new HrInterviewInfoModel();
        $interviewInfoModel->interview_id   = $interviewModel->interview_id;
        $interviewInfoModel->hc_id          = $interviewModel->hc_id;
        $interviewInfoModel->evaluate       = 'quick offer';
        $interviewInfoModel->state          = 1;
        $interviewInfoModel->level          = $level;
        $interviewInfoModel->interviewer_id = $paramIn['staff_id'];
        $interviewInfoModel->operation_id   = $paramIn['staff_id'];
        $interviewInfoModel->opt_id         = $interviewerOperationModel->id;
        $interviewInfoModel->save();


        $interview_record_model                 = new HrInterviewRecordModel();
        $interview_record_model->interview_id   = $interviewModel->interview_id;
        $interview_record_model->resume_id      = $interviewModel->resume_id;
        $interview_record_model->operator_id    = $paramIn['staff_id'];
        $interview_record_model->state          = HrInterviewRecordModel::STATE_PASS;
        $interview_record_model->interviewer_id = $paramIn['staff_id'];
        $interview_record_model->interview_time = gmdate('Y-m-d H:i:s');
        $interview_record_model->save();
        //log
        (new LogServer())->addLog([
            'module_id'       => $interviewModel->interview_id,
            'module_type'     => HrLogModel::$log_module_type['interview'],
            'module_level'    => $level,
            'action'          => HrLogModel::$log_option['final_pass'],
            'module_status'   => HrLogModel::$log_status['final_pass'],
            'data_after'      => [],
            'current_user_id' => $paramIn['staff_id'],
        ]);

        $resumeModel->is_out       = HrResumeModel::IS_OUT_NOT;
        $resumeModel->state_code   = HrResumeModel::STATE_CODE_COMMUNICATED;
        $resumeModel->filter_state = HrResumeModel::FILTER_STATE_ADD_INTERVIEW;
        $resumeModel->save();
        return $interviewModel;
    }


    protected function getPhoneAreaCode(): int
    {
        if (isCountry('PH')) {
            return 63;
        }
        if (isCountry('TH')) {
            return 66;
        }
        if (isCountry('MY')) {
            return 60;
        }
        return 0;
    }

    protected function getQuickOfferRecruitChannel(): int
    {
        if (isCountry('PH')) {
            return 46;
        }
        if (isCountry('TH')) {
            return 46;
        }
        if (isCountry('MY')) {
            return 26;
        }
        return 0;
    }


    /**
     * 差异化字段绑定
     * @param $resumeModel
     * @param $paramIn
     * @param $is_create
     * @return mixed
     */
    protected function bindResumeData($resumeModel, $paramIn, $is_create)
    {
        return $resumeModel;
    }

    public function nameToUpper($name)
    {
        if (empty($name) || !isCountry('MY')){
            return $name;
        }
        $name = strtoupper(trim($name));
        $specials = array(
            '。'=>'.',
            '，'=>',',
            '、'=>'.',
            '；'=>';',
            '：'=>':',
            '“'=>'"',
            '”'=>'"',
            '’'=>'\'',
            '‘'=>'\'',
            '（'=>'(',
            '）'=>')',
            '【'=>'[',
            '】'=>']',
            '＠'=>'@',
            '？'=>'?',
            '！'=>'!',
            '＃'=>'#',
            '＄'=>'$',
            '％'=>'%',
            '＆'=>'&',
            '＊'=>'*',
            '｜'=>'|',
            '｝'=>'}',
            '｛'=>'{',
            '｀'=>'`',
            '～'=>'~',
            '－'=>'-',
            '＿'=>'_',
            '＝'=>'=',
            '＋'=>'+',
        );

        foreach ($specials as $k => $v) {
            $name = str_replace($k,$v,$name);
        }
        return $name;
    }

    protected function bindOtherResumeData($resumeModel, &$paramIn): bool
    {
        return true;
    }


    /**
     * 手机号不存在，创建简历的逻辑
     * @param $resumeModel
     * @param $paramIn
     * @return HrResumeModel|mixed
     */
    protected function fullResumeInfo($resumeModel, &$paramIn)
    {
        if (empty($resumeModel)) {
            self::$resume_is_new               = true;
            $resumeModel                       = new HrResumeModel();
            $resumeModel->recruit_type         = 1;//'招聘类型 1社会招聘 2实习生招聘 3应届生招聘',
            $resumeModel->hc_id                = 0;
            $resumeModel->deleted              = 0;
            $resumeModel->state_code           = HrResumeModel::STATE_CODE_NO_COMMUNICATION;
            $resumeModel->fit                  = 1;
            $resumeModel->work_city_id         = '';
            $resumeModel->source               = HrResumeModel::SOURCE_BY_QUICK_OFFER;//quick offer

        } else {
            if ($resumeModel->source == HrResumeModel::SOURCE_BY) {
                $resumeModel->approve_state = 2;
            }
        }

        $init_resume_hc_id                 = $resumeModel->hc_id;
        $resumeModel->phone_area_code      = $this->getPhoneAreaCode();
        $resumeModel->hc_id                = $paramIn['hc_id'];
        $resumeModel->phone                = $paramIn['phone'];
        $resumeModel->job_id               = $paramIn['job_id'];
        $resumeModel->expected_arrivaltime = $paramIn['expected_arrivaltime'];
        $resumeModel->reserve_type         = $paramIn['type'] == self::TYPE_IC ? HrResumeModel::RESERVE_TYPE_FORMAL : HrResumeModel::RESERVE_TYPE_AGENT;                                  //预约类型1 正式员工  2 个人代理
        $resumeModel->is_out               = 2;                                                                                                                                           //未淘汰
        $resumeModel->recruit_channel      = $this->getQuickOfferRecruitChannel();                                                                                                        // 招聘渠道
        $resumeModel                       = $this->bindResumeData($resumeModel, $paramIn, self::$resume_is_new);
        $resumeModel->save();
        $resumeModel = HrResumeModel::findFirstById($resumeModel->id);
        if (!empty($paramIn['driver_license_front'])) {
            //驾照正面
            // '0=其他，1=本人半身照，2=身份证正面，3=身份证反面，4=户籍照第一页，5=户籍照应聘者本人信息页，6=兵役服役证明，7=成绩报告单，
//8=清白证明书，9=补充附件，10=驾驶证正面，11=驾驶证反面，12=车辆登记薄，13=车辆照片，14=车辆使用授权，
//15=签名，16=个人简历, 17=残疾证正面，18=残疾证反面, 19=最高学历证书，20=OR附件，21=承诺书，22=授权书';

            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = 10',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['driver_license_front']);

            $paramIn['driver_license_front'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => 10,
            ]);
        }

        $this->bindOtherResumeData($resumeModel, $paramIn);

        //编辑简历
        (new LogServer())->addLog([
            'module_id'       => $resumeModel->id,
            'module_type'     => HrLogModel::$log_module_type['base_info'],
            'action'          => self::$resume_is_new ? HrLogModel::$log_option['create'] : HrLogModel::$log_option['modify'],
            'data_after'      => $paramIn,
            'current_user_id' => $paramIn['staff_id'],
        ]);
        //关联hc
        if ($init_resume_hc_id != $paramIn['hc_id']) {
            (new LogServer())->addLog([
                'module_id'       => $resumeModel->id,
                'module_type'     => HrLogModel::$log_module_type['hc_id'],
                'action'          => HrLogModel::$log_option['relate'],
                'module_level'    => $resumeModel->hc_id,
                'data_after'      => ['hc_id' => $resumeModel->hc_id],
                'current_user_id' => $paramIn['staff_id'],
            ]);
        }
        return $resumeModel;
    }


    /**
     * 获取详情
     * @param $quick_offer_id
     * @return array
     * @throws BusinessException
     */
    public function detail($quick_offer_id): array
    {
        $quickOfferModel = QuickOfferModel::findFirstById($quick_offer_id);
        if (empty($quickOfferModel)) {
            throw new BusinessException($this->getTranslation()->_('message_no_exists'));
        }
        $typeList       = array_column($this->getType(), 'label', 'value');
        $quickOfferInfo = $quickOfferModel->toArray();
        $result         = json_decode($quickOfferInfo['form_data'], true);

        $result['quick_offer_id']         = $quickOfferInfo['id'];
        $result['job_name']               = HrJDRepository::getJobName($result['job_id'] ?? 0);
        $result['store_name']             = SysStoreRepository::getStoreName($result['store_id'] ?? '');
        $result['type_text']              = $typeList[$result['type']] ?? '';
        $result['driver_license_front']   = !empty($result['driver_license_front']) ? $this->getDI()->getConfig()->application['img_prefix'] . $result['driver_license_front'] : '';
        $result['credentials_img']        = !empty($result['credentials_img']) ? $this->getDI()->getConfig()->application['img_prefix'] . $result['credentials_img'] : '';
        $result['type']                   = intval($result['type']);
        $result['is_need_driver_license'] = !empty($this->getOtherValidationsFieldByJD($result['job_id'] ?? 0)['driver_license_front']);
        $this->bindDetailData($result);
        return $result;
    }

    /**
     *
     * @param $result
     * @return void
     * @throws BusinessException
     */
    protected function bindDetailData(&$result)
    {
        $province_obj                        = SysProvinceModel::findFirstByCode($result['residence_government'] ?? '');
        $city_obj                            = SysCityModel::findFirstByCode($result['residence_city'] ?? '');
        $district_obj                        = SysDistrictModel::findFirstByCode($result['residence_town'] ?? '');
        $cityList                            = array_column((new SysServer())->getTotalDictionaryRegionByDictCode('address_country_region'),
            'label', 'value');
        $result['residence_country_text']    = $cityList[$result['residence_country']] ?? '';
        $result['residence_government_text'] = $province_obj->name ?? $result['residence_government'];
        $result['residence_city_text']       = $city_obj->name ?? $result['residence_city'];
        $result['residence_town_text']       = $district_obj->name ?? $result['residence_town'];
    }


}
