<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;

class AttendanceDetectCheatServer extends BaseServer
{

    public function getOsCheatData($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id,s.staff_info_id,s.organization_id,s.attendance_date,s.match_staff_info_id,
            s.os_face_image_source_path,s.os_submit_face_image_path,s.work_attendance_path,s.state,
            s.created_at,s.updated_at
        ");
        $builder->from(['s' => StaffWorkDetectFaceRecordModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = s.staff_info_id', 'hsi');
        $builder->where('s.state = :state: and s.type = :type: ', [
            'state' => StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED,
            'type'  => StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_OS,
        ]);
        if (!empty($params['start_time'])) {
            $builder->andWhere('s.created_at > :start_time:', ['start_time' => $params['start_time']]);
        }
        if (!empty($params['end_time'])) {
            $builder->andWhere('s.created_at <= :end_time:', ['end_time' => $params['end_time']]);
        }
        if (!empty($params['need_hire_type'])) {
            $builder->andWhere('hsi.hire_type = :need_hire_type:', ['need_hire_type' => $params['need_hire_type']]);
        }
        return $builder->getQuery()->execute()->toArray();
    }


    public function syncCrowdSourcingCheatToFMS($date_at): bool
    {
        $params['start_time']     = date('Y-m-d 00:00:00', strtotime($date_at));
        $params['end_time']       = date("Y-m-d 00:00:00", strtotime($params['start_time'] . " +1 day"));
        $params['need_hire_type'] = HrStaffInfoModel::HIRE_TYPE_12;
        $data                     = $this->getOsCheatData($params);
        if (empty($data)) {
            $this->logger->write_log(['syncCrowdSourcingCheatToFMS' => 'nodata'], 'info');
            return true;
        }
        $sendData = [];
        foreach ($data as $datum) {
            $sendData[$datum['staff_info_id']][] = [
                'pic_url'       => $datum['os_submit_face_image_path'],
                'check_face_at' => strtotime(($datum['created_at'])),
            ];
        }
        $api = new RestClient('fms');
        foreach ($sendData as $staff_id => $item) {
            $send_params['external_staff_id'] = $staff_id;
            $send_params['face_pic_list']     = $item;
            $res                              = $api->execute(RestClient::METHOD_POST,
                '/svc/driver/crowdsourcing/task/suspected/false',
                $send_params, ['Accept-Language' => $this->lang]);
            $this->logger->write_log(['send_params' => $send_params, 'res' => $res], 'info');
        }
        return true;
    }
}