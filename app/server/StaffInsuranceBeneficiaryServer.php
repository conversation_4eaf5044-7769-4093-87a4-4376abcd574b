<?php

namespace FlashExpress\bi\App\Server;

use app\enums\InsuranceBeneficiaryEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInsuranceBeneficiaryModel;
use Exception;

class StaffInsuranceBeneficiaryServer extends BaseServer
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * 保险受益人详情
     * @param $params
     * @return array
     */
    public function getInsuranceBeneficiaryDetail($params): array
    {
        $staff_info_id = $params['staff_info_id'];
        $detail = HrStaffInsuranceBeneficiaryModel::findFirst([
            'columns'    => 'staff_info_id,beneficiary_name,beneficiary_identity,beneficiary_mobile,relation',
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 编辑保险受益人信息 有更新 无新增
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function editInsuranceBeneficiary($params): bool
    {
        $staff_info_id        = $params['staff_info']['id'];
        $beneficiary_name     = $this->processingDefault($params, 'beneficiary_name');
        $beneficiary_identity = $this->processingDefault($params, 'beneficiary_identity');
        $beneficiary_mobile   = $this->processingDefault($params, 'beneficiary_mobile');
        $relation             = $this->processingDefault($params, 'relation', $params, 2);

        $detail = $this->getInsuranceBeneficiaryDetail(['staff_info_id' => $staff_info_id]);

        if (empty($detail)) {
            //新增
            $model                       = new HrStaffInsuranceBeneficiaryModel();
            $model->staff_info_id        = $staff_info_id;
            $model->beneficiary_name     = $beneficiary_name;
            $model->beneficiary_identity = $beneficiary_identity;
            $model->beneficiary_mobile   = $beneficiary_mobile;
            $model->relation             = $relation;
            $model->operator             = $staff_info_id;

            if (!$model->save()) {
                $messages = $model->getMessages();
                $message = '';
                foreach ($messages as $message) {
                    $message = '_' . $message;
                }
                $this->getDI()->get('logger')->write_log([
                    'function' => 'editInsuranceBeneficiary',
                    'message'  => '新增保险受益人失败',
                    'params'   => $params,
                    'error'    => $message
                ]);
                throw new Exception('保存失败');
            }
            $this->getDI()->get('logger')->write_log([
                'function' => 'editInsuranceBeneficiary',
                'message'  => '新增保险受益人',
                'params'   => $params,
            ], 'info');

        } else {
            if ($beneficiary_mobile == $detail['beneficiary_mobile']
                && $beneficiary_identity == $detail['beneficiary_identity']
                    && $beneficiary_name == $detail['beneficiary_name']
                    && $relation == $detail['relation']) {
                throw new BusinessException('没有需要保存的数据');
            }
            //变更
            $edit_data = [
                'beneficiary_name'     => $beneficiary_name,
                'beneficiary_identity' => $beneficiary_identity,
                'beneficiary_mobile'   => $beneficiary_mobile,
                'relation'             => $relation,
                'operator'             => $staff_info_id,
            ];
            $result = $this->getDI()->get('db')->updateAsDict('hr_staff_insurance_beneficiary', $edit_data,
                [
                    'conditions' => 'staff_info_id = ?',
                    'bind'       => [$staff_info_id],
                ]
            );

            if (!$result) {
                $this->getDI()->get('logger')->write_log([
                    'function' => 'editInsuranceBeneficiary',
                    'message'  => '编辑保险受益人失败',
                    'params'   => $params,
                ]);
                throw new BusinessException('保存失败');
            }
            $this->getDI()->get('logger')->write_log([
                'function' => 'editInsuranceBeneficiary',
                'message'  => '编辑保险受益人',
                'params'   => $params,
                'before'   => $detail,
            ], 'info');
        }
        return true;
    }

    /**
     * 枚举值
     * @return array[]
     */
    public function sysInfo(): array
    {
        $relation_list = $this->getInsuranceBeneficiaryRelation();
        return [
            'relation_list' => $relation_list,
        ];
    }

    /**
     * 受益人关系
     * @return array
     */
    public function getInsuranceBeneficiaryRelation(): array
    {
        $list = InsuranceBeneficiaryEnums::$relation_list;
        $relation_list = [];
        foreach ($list as $key => $value) {
            $relation_list[] = ['key' => (string)$key, 'value' => $this->t->_($value)];
        }
        return $relation_list;
    }

    /**
     * 保险受益人菜单
     * @param $staff_info_id
     * @return array
     */
    public function insuranceBeneficiaryCollectMenu($staff_info_id): array
    {
        //入口权限：工作所在国家是“马来西亚”的，雇佣类型为正式员工、日薪制特殊合同工、月薪制特殊合同工、时薪制特殊合同工
        if(!$this->verifyStaffInsuranceBeneficiaryCollectMenu($staff_info_id)) {
            return [];
        }

        $result['id']    = 'insurance_beneficiary_info';                //编码
        $result['icon']  = '';                                          //图标
        $result['title'] = $this->t->_('insurance_beneficiary_info');   //菜单名称
        $result['dst']   = env('h5_endpoint') . 'insurance-beneficiary';//h5 url 地址

        $result['state'] = InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_UNSUBMITTED;
        $result['state_text'] = $this->t->_('insurance_beneficiary_submit_state_0');
        if (!empty($this->getInsuranceBeneficiaryDetail(['staff_info_id' => $staff_info_id]))) {
            $result['state'] = InsuranceBeneficiaryEnums::INSURANCE_BENEFICIARY_SUBMITTED;
            $result['state_text'] = $this->t->_('insurance_beneficiary_submit_state_1');
        }
        return $result;
    }

    /**
     * 验证工号是否有保险受益人编辑菜单
     * 工作所在国家是“马来西亚”的，雇佣类型为正式员工、日薪制特殊合同工、月薪制特殊合同工、时薪制特殊合同工
     * @param $staff_info_id
     * @return bool
     */
    public function verifyStaffInsuranceBeneficiaryCollectMenu($staff_info_id): bool
    {
        $staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($staff_info_id, 'staff_info_id,formal,hire_type,working_country');
        if (empty($staff_info)) {
            return false;
        }
        $staff_info = $staff_info->toArray();
        if ($staff_info['working_country'] != HrStaffInfoModel::WORKING_COUNTRY_MY
            || !in_array($staff_info['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
                HrStaffInfoModel::HIRE_TYPE_3,
                HrStaffInfoModel::HIRE_TYPE_4,
            ])) {
            return false;
        }
        return true;
    }

    /**
     * 给未填写保险受益人工号发送消息
     * @return bool
     */
    public function StaffInsuranceBeneficiarySendMessage(): bool
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hr_staff_info.staff_info_id,
                            hr_staff_info.name,
                            hr_staff_info.formal,
                            hr_staff_info.hire_type,
                            hr_staff_info.state,
                            hr_staff_insurance_beneficiary.beneficiary_name,
                            hr_staff_insurance_beneficiary.beneficiary_mobile,
                            hr_staff_insurance_beneficiary.beneficiary_identity,
                            hr_staff_insurance_beneficiary.relation');
        $builder->from(['hr_staff_info' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInsuranceBeneficiaryModel::class, 'hr_staff_info.staff_info_id = hr_staff_insurance_beneficiary.staff_info_id', 'hr_staff_insurance_beneficiary');
        $builder->where('hr_staff_info.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        $builder->andWhere('hr_staff_info.state = :state: ', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('hr_staff_info.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $builder->andWhere(' hr_staff_info.working_country = :working_country:', ['working_country' => HrStaffInfoModel::WORKING_COUNTRY_MY]);
        $builder->inWhere('hr_staff_info.hire_type', [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3, HrStaffInfoModel::HIRE_TYPE_4]);
        $builder->andWhere('hr_staff_insurance_beneficiary.beneficiary_name IS NULL');

        $all_staff_list = $builder->getQuery()->execute()->toArray();

        if(empty($all_staff_list)) {
            //没有要发送的信息的工号
            $this->getDI()->get('logger')->write_log([
                'function' => 'StaffInsuranceBeneficiarySendMessage',
                'message'  => '没有要发送的数据',
            ], 'info');
            return false;
        }

        $list = array_chunk($all_staff_list, 200);
        
        $message_title = $this->getTranslation('en')->_('insurance_beneficiary_message_title');

        foreach ($list as $item => $staff_list) {
            $ids = [];
            $staffIds = [];
            foreach ($staff_list as $key => $value) {
                $ids[] = ['id' => $value['staff_info_id']];
                $staffIds[] = $value['staff_info_id'];
            }

            $this->getDI()->get('logger')->write_log([
                'function' => 'StaffInsuranceBeneficiarySendMessage',
                'message'  => '需要发送消息的工号',
                'staff_info_ids' => $staffIds
            ], 'info');

            $message_param['staff_info_ids_str'] = implode(',', $staffIds);
            $message_param['staff_users']        = $ids;
            $message_param['message_title']      = $message_title;
            $message_param['message_content']    = '';
            $message_param['category']           = MessageEnums::MESSAGE_CATEGORY_INSURANCE_BENEFICIARY;

            $hcm_rpc = new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang);
            $hcm_rpc->setParams($message_param);
            $res = $hcm_rpc->execute();
            if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
                $this->getDI()->get('logger')->write_log([
                    'function' => 'StaffInsuranceBeneficiarySendMessage',
                    'message'  => '消息发送失败',
                    'params'   => $message_param,
                    'result'   => $res,
                ]);
            }
        }
        return true;
    }
}