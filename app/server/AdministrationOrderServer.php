<?php

/**
 * 行政工单主表业务类
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AdministrationLogRepository;
use FlashExpress\bi\App\Repository\AdministrationOrderRepository;
use FlashExpress\bi\App\Repository\AdministrationQuestionTypeRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Db;

class AdministrationOrderServer extends BaseServer
{

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * 新逻辑 验证工号或者职位否有权限
     * @param $staffInfo
     * @return bool
     */
    public function checkDealAdministrationOrderPermission($staffInfo): bool
    {
        $config = (new SettingEnvServer())->listByCode(['administrative_work_order_staff_id','administrative_work_order_job_title']);
        if(empty($config)){
            return false;
        }
        $config = array_column($config,'set_val','code');

        if(!empty($config['administrative_work_order_staff_id']) && in_array($staffInfo['id'],explode(',',$config['administrative_work_order_staff_id']))){
            return  true;
        }
        if(!empty($config['administrative_work_order_job_title']) && in_array($staffInfo['job_title'],explode(',',$config['administrative_work_order_job_title']))){
            return  true;
        }
        return  false;
    }

    /**
     * 行政工单上报接口
     * @param array $paramIn
     * @param array $userInfo
     * @return array
     */
    public function addAdministrationOrder(array $paramIn, array $userInfo)
    {
        // [1] 验证问题类型是否真实存在
        $questionTypes = (new AdministrationQuestionTypeRepository())->getQuestionTypeByParams([
            'is_deleted' => 0,
            'id'         => $paramIn['question_type_id'],
        ], ['id', 't_key', 'remark']);
        if (empty($questionTypes) || !is_array($questionTypes)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "question type id not exist"]);
        }

        // 当不是总部的时候需要进行验证
        if ("-1" != $paramIn['store_id']) {
            // [2] 验证问题所在网点id是否合法。
            $storeRet = (new SysStoreServer())->getStoreInfoByid($paramIn['store_id']);
            if (empty($storeRet) || !is_array($storeRet)) {
                return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "store id not exist"]);
            }
        }

        //网点
        if ($userInfo['organization_type'] == 1) {
            // 申请人部门id
            $paramIn['created_department_id']   = $userInfo['department_id'];
            // 申请人网点
            $paramIn['created_store_id']        = $userInfo['organization_id'];
            // 申请人 网点名称
            $paramIn['created_store_name']      = "";
        } else {
            $paramIn['created_store_id']      = -1;
            $paramIn['created_department_id'] = $userInfo['department_id']; //$userInfo['organization_id'];
            $paramIn['created_store_name']    = "Head Office";
        }

        //泰国时间
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $createTime = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
        // 申请人工号
        $paramIn['created_staff_id'] = $userInfo['id'];
        // 申请人姓名
        $paramIn['created_staff_name'] = $userInfo['name'];
        $paramIn['created_at']         = $createTime;
        $paramIn['updated_at']         = $createTime;
        // 申请人职位id
        $paramIn['created_job_title_id'] = $userInfo['job_title']?:0;
        // 申请人职位名称
        $paramIn['created_job_title_name'] = "";
        // 申请人部门名称
        $paramIn['created_department_name'] = "";
        // 上传照片
        $paramIn['pics'] = !empty($paramIn['pics']) ? json_encode($paramIn['pics'], JSON_UNESCAPED_UNICODE) : "";

        $nums = (new AdministrationOrderRepository())->getNumByDay(gmdate("Y-m-d", time()+$add_hour * 3600));
        $nums++;
        $country_code           = substr(strtoupper(env('country_code', 'Th')), 0, 2);
        $paramIn['order_code']  = $country_code . gmdate("YmdHis", time()+$add_hour * 3600) . str_pad($nums, 4,0, STR_PAD_LEFT);

        if (!empty($paramIn['created_job_title_id'])) {
            $temp = (new StaffRepository())->getStaffInfoById($userInfo['id']);
            if (!empty($temp)) {
                $paramIn['created_job_title_name'] = $temp['job_name'] ?? "";
                if ($paramIn['created_store_id'] != -1) {
                    $paramIn['created_store_name'] = $temp['name'] ?? "";
                }
            }
        }

        if (!empty($paramIn['created_department_id'])) {
            $paramIn['created_department_name'] = (new DepartmentRepository())->getDepartmentNameById($paramIn['created_department_id']);
        }

        $this->getDI()->get('db')->begin();
        // 向行政工单表插入数据
        $lastInsertId = (new AdministrationOrderRepository())->add($paramIn);
        if (empty($lastInsertId)) {
            $this->getDI()->get('db')->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert order error"]);
        }

        $data                       = [];
        $data['order_id']           = $lastInsertId;
        $data['created_staff_id']   = $paramIn['created_staff_id'];
        $data['created_staff_name'] = $userInfo['name'];
        $data['created_type']       = 1;
        $data['created_at']         = $paramIn['created_at'];
        $data['mark']               = $paramIn['info'];
        $data['pics']               = $paramIn['pics'];
        $lastLogId                  = (new AdministrationLogRepository())->add($data);
        if (empty($lastLogId)) {
            $this->getDI()->get('db')->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert log error"]);
        }
        $this->getDI()->get('db')->commit();
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 获取提交人的默认信息
     * @param $userInfo
     * @return array
     */
    public function getDefault($userInfo)
    {
        $data = (new HrStaffInfoServer())->getUserInfoByStaffInfoId($userInfo['id'],
            "id,staff_info_id, sys_store_id,mobile_company");
        if (is_bool($data) && false === $data) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "staff data empty"]);
        }

        $res['mobile'] = $data['mobile_company'] ?? "";;
        $res['sys_store_id']   = "";
        $res['sys_store_name'] = "";

        // 获取网点信息
        if ($data['sys_store_id'] != "-1") {
            $storeObj = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind'       => [
                    'store_id' => $data['sys_store_id'],
                ],
                "columns"    => 'id,name',
            ]);
            // 所属网点
            $res['sys_store_id']   = $data['sys_store_id'];
            $res['sys_store_name'] = (is_object($storeObj) && !empty($storeObj->toArray())) ? $storeObj->name : "";
        }

        if ($data['sys_store_id'] == "-1") { // 总部
            $res['sys_store_id']   = "-1";
            $res['sys_store_name'] = enums::HEAD_OFFICE;
        }
        return $this->checkReturn(["data" => $res]);
    }

    public function getXzNums(array $userInfo): array
    {
        $data = [
            'xz_reply_num'  => 0,
            // 回复数 提交人的小红点
            'xz_submit_num' => 0,
            // 工单提交数据
            "is_xz"         => 0
            // 是否展示提交工单的入口
        ];
        // 提交人的 已经回复数据
        $replyData = (new AdministrationOrderRepository())->getInfoByParams([
            'status'           => enums::TICKET_STATUS_REPLY,
            'created_staff_id' => $userInfo['id'],
        ], ['count(*) AS nums']);
        if (empty($replyData)) {
            return $data;
        }
        $data['xz_reply_num'] = (int)$replyData[0]['nums'];

        // hr 待回复数
        if (true === $this->isOperationsSupportSpecialistJob($userInfo)) {
            $data['is_xz'] = 1;
            $submitData    = (new AdministrationOrderRepository())->getInfoByParams(['status' => enums::TICKET_STATUS_WAIT_REPLY],
                ['count(*) AS nums']);
            if (empty($submitData)) {
                return $data;
            }
            $data['xz_submit_num'] = (int)$submitData[0]['nums'];
        }
        return $data;
    }


    // *********************************************************

    /**
     * 判断当前用户是否属于 Flash Fulfillment部门或者子部门
     * @param $userInfo
     * @param $setVal
     * @return bool
     */
    public function isFlashFulfillmentDepartment($userInfo, &$setVal): bool
    {
        // 读取配置信息
        $auditPowerObj = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => enums::XZ_TICKET_AUDIT_POWER],
            'columns'    => ['set_val'],
        ]);

        // 数据为空的处理
        if (is_bool($auditPowerObj) || false === $auditPowerObj) {
            return false;
        }
        $auditPower = $auditPowerObj->toArray();
        $setVal     = json_decode($auditPower['set_val'], true);
        if (empty($setVal['department_id'])) {
            return false;
        }

        // 根据第一级部门id 获取部门链
        $headerDepartmentId = explode(',', $setVal['department_id']);
        $departmentList     = (new DepartmentRepository())->getDepartmentByIds($headerDepartmentId);

        if (empty($departmentList)) {
            return false;
        }
        $allSubDepartmentIds = [];
        foreach ($departmentList as $key => $val) {
            // 根据部门链获取子部门id
            $subDepartmentList = (new DepartmentRepository())->getDeptByLink($val['ancestry_v3']);
            if (empty($subDepartmentList)) {
                continue;
            }
            $subDepartmentIds    = array_column($subDepartmentList, 'id');
            $allSubDepartmentIds = array_merge($allSubDepartmentIds, $subDepartmentIds);
            unset($subDepartmentList, $subDepartmentIds);
        }
        // 当前部门和子部门合并
        $allDepartmentIds = array_merge($headerDepartmentId, $allSubDepartmentIds);
        // 当前登入人部门id 如果属于当前部门或者子部门 返回true
        if (in_array($userInfo['department_id'], $allDepartmentIds)) {
            return true;
        }
        return false;
    }


    /**
     * 判断当前登入用户是否属于 Operations Support Specialist 职位
     * @param $userInfo
     * @return bool
     */
    public function isOperationsSupportSpecialistJob($userInfo, $setVal = []): bool
    {
        if (empty($setVal)) {
            // 判断登入用户的职位是否属于 Operations Support Specialist 之前必须 要属于Flash Fulfillment 子公司
            $bool = $this->isFlashFulfillmentDepartment($userInfo, $setVal);
            if (false === $bool) {
                return false;
            }
        }

        // 如果即 没有职位 也没有配置 员工号 则返回false
        if (empty($setVal['job_id']) && empty($setVal['staff_info_id'])) {
            return false;
        }

        // 如果存在 job_title 且当前登入人的职位也匹配则返回true
        if (!empty($setVal['job_id'])) {
            $jobIds = explode(',', $setVal['job_id']);
            // 如果属于 Operations Support Specialist 则返回true
            if (in_array($userInfo['job_title'], $jobIds)) {
                return true;
            }
        }

        // 如果存在员工id 需要匹配员工id
        if (!empty($setVal['staff_info_id'])) {
            $staffInfoIds = explode(',', $setVal['staff_info_id']);
            // 如果属于 Operations Support Specialist 则返回true
            if (in_array($userInfo['staff_id'], $staffInfoIds)) {
                return true;
            }
        }

        return false;
    }

    // *********************************************************

    /**
     * 获取 待回复、已回复、已关闭工单列表
     * @param $paramIn
     * @param $userInfo
     * @return array
     */
    public function list($paramIn, $userInfo): array
    {
        if (empty($paramIn['page_num'])) {
            $paramIn['page_num'] = 1;
        }

        if (empty($paramIn['page_size'])) {
            $paramIn['page_size'] = 20;
        }

        // [1]翻页处理
        $pageNum  = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);
        $start    = ($pageNum - 1) * $pageSize;

        // [2]组织查询参数
        $params = [
            'status' => (int)$paramIn['status']    // 处理状态：1待回复，2已回复，3已关闭'
        ];

        // 问题类型
        if (isset($paramIn['question_type_id']) && !empty($paramIn['question_type_id'])) {
            $params['question_type_id'] = (int)$paramIn['question_type_id'];
        }

        // from=1 表示请求来自提交入口 需要拼接提交人的员工号
        if (1 == $paramIn['from']) {
            $params['created_staff_id'] = (int)$userInfo['id'];
        }

        $colum  = [
            "id",               // 主键id
            "order_code",       // 行政工单号码
            "question_type_id", // 问题类型
            "store_id",         // 问题所在网点
            "line_id",          // line_id
            "mobile",           // mobile
            "info",             // 问题详情
            "pics",             // 图片
            "updated_at",       // 更新时间
            "created_at",       // 提交时间
            "status",           // 状态
            "created_staff_id", // 提交人信息
            "created_staff_name", // 提交姓名
        ];
        $order  = ['field' => 'created_at', 'sort' => 'DESC'];
        $result = (new AdministrationOrderRepository())->list($params, $colum, $order, $pageSize, $start);

        // 返回数据
        $data = [
            "dataList"   => [],
            "pagination" => [
                "pagination" => $pageNum,
                "pageSize"   => $pageSize,
                "count"      => 0,
            ],
        ];

        if (empty($result['list']) || !is_array($result)) {
            return $data;
        }

        $storeIds = array_unique(array_column($result['list'], 'store_id'));
        // 去掉总部
        if ($index = array_search('-1', $storeIds)) {
            unset($storeIds[$index]);
        }

        // [4]获取网点
        $storeList = [];
        $storeRet  = SysStoreModel::find([
            'conditions' => "id IN ({store_ids:array})",
            'bind'       => [
                'store_ids' => array_values($storeIds),
            ],
            "columns"    => 'id,name',
        ])->toArray();
        if (!empty($storeRet)) {
            $storeList = array_column($storeRet, null, 'id');
        }

        // [5]获取问题类型
        $questionTypeList = [];
        $questionTypeIds  = array_values(array_unique(array_column($result['list'], 'question_type_id')));
        $params           = [
            "id" => $questionTypeIds,
        ];
        $questionTypeRet  = (new AdministrationQuestionTypeRepository())->getQuestionTypeByParams($params,
            ["id", "t_key"]);
        if (!empty($questionTypeRet)) {
            $questionTypeList = array_column($questionTypeRet, null, 'id');
        }

        // [6]数据组装处理
        foreach ($result['list'] as $key => &$val) {
            $val['store_name'] = "";
            if ('-1' == $val['store_id']) {
                $val['store_name'] = enums::HEAD_OFFICE;
            }

            if (!empty($storeList[$val['store_id']])) {
                $val['store_name'] = $storeList[$val['store_id']]['name'];
            }
            $val['pics']               = json_decode($val['pics'], true);
            $val['question_type_name'] = $this->getTranslation()->_($questionTypeList[$val['question_type_id']]['t_key']);
        }
        $data['dataList']                 = $result['list'];
        $data['pagination']['pagination'] = $pageNum;
        $data['pagination']['pageSize']   = $pageSize;
        $data['pagination']['count']      = $result['total'];
        return $data;
    }

    /**
     * 关闭工单
     * @param array $paramIn
     * @param array $userInfo
     * @return array
     */
    public function closeOrder(array $paramIn, array $userInfo): array
    {
        // [1] 根据传入的id 验证是否合法
        $queryParams = [
            'id'               => (int)$paramIn['id'],
            'created_staff_id' => (int)$userInfo['id'],
        ];
        $orderRet    = (new AdministrationOrderRepository())->getInfoByParams($queryParams, ['id', 'status']);
        if (empty($orderRet)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }

        // 如果状态已经是关闭 则直接返回
        if ($orderRet[0]['status'] == enums::TICKET_STATUS_CLOSED) {
            return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
        }

        //泰国时间
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $createTime = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
        $updateData = [
            'close_reason'   => trim($paramIn['close_reason']),
            'close_time'     => $createTime,
            'status'         => enums::TICKET_STATUS_CLOSED, // 已关闭
            'close_staff_id' => $userInfo['id'],             // 关闭人id
            'updated_at'     => $createTime,                 // 数据更新时间
        ];
        $whereData  = [
            'conditions' => 'id = ?',
            'bind'       => [(int)$paramIn['id']],
        ];
        $updateRet  = (new AdministrationOrderRepository())->update($updateData, $whereData);
        if (false === $updateRet) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "server error"]);
        }
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }

    /**
     * 获取工单详情
     * @param array $paramIn
     * @param array $userInfo
     * @param array $loginUserIdentity
     * @return array
     */
    public function orderDetail(array $paramIn, array $userInfo, array $loginUserIdentity): array
    {
        // [1] 根据传入的id 验证是否合法
        $queryParams = [
            'id' => (int)$paramIn['id'],
        ];

        // 如果是hr职位不需要传递created_staff_id
        if (false === $loginUserIdentity['isHrJob']) {
            $queryParams['created_staff_id'] = (int)$userInfo['id'];
        }

        $colum    = [
            'id',
            'order_code',
            'question_type_id',
            'store_id',
            'line_id',
            'mobile',
            'info',
            'created_at',
            'updated_at',
            'status',
            'pics',
        ];
        $orderRet = (new AdministrationOrderRepository())->getInfoByParams($queryParams, $colum);
        if (empty($orderRet)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }
        $orderInfo = $orderRet[0];

        // [2] 不是总部时 查询问题网点
        if ('-1' != $orderInfo['store_id']) {
            $storeRet = SysStoreModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $orderInfo['store_id'],
                ],
                "columns"    => 'id,name',
            ]);
            if (!is_object($storeRet) || empty($storeRet->toArray())) {
                return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "store query error"]);
            }
            $storeInfo               = $storeRet->toArray();
            $orderInfo['store_name'] = $storeInfo['name'];
        } else {
            $orderInfo['store_name'] = enums::HEAD_OFFICE;
        }

        // [3] 查询问题类型
        $params          = [
            "id" => $orderInfo['question_type_id'],
        ];
        $questionTypeRet = (new AdministrationQuestionTypeRepository())->getQuestionTypeByParams($params,
            ["id", "t_key"]);
        if (empty($questionTypeRet)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "question type query error"]);
        }
        $questionTypeInfo                = $questionTypeRet[0];
        $orderInfo['question_type_name'] = $this->getTranslation()->_($questionTypeInfo['t_key']);
        $orderInfo['pics']               = !empty($orderInfo['pics']) ? json_decode($orderInfo['pics'], true) : "";
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success", 'data' => $orderInfo]);
    }

    /**
     * 验证pic 数组的格式 必须是 pic_nam  pic_path
     * @param array $pics
     * @return array|bool
     */
    public function checkPics(array $pics)
    {
        if (empty($pics)) {
            return $pics;
        }
        foreach ($pics as $key => $val) {
            if (count($val) != 2) {
                return false;
            }
            if (!isset($val['pic_name']) || !isset($val['pic_path'])) {
                return false;
            }
        }
        return true;
    }

}