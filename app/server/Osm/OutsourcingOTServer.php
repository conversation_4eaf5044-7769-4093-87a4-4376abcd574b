<?php

namespace FlashExpress\bi\App\Server\Osm;


use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeDetailModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeDetailRepository;
use FlashExpress\bi\App\Repository\HubOutsourcingOvertimeRepository;
use FlashExpress\bi\App\Repository\OsOrderRepository;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;

class OutsourcingOTServer extends BaseServer
{
    public function __construct($lang = '', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
        $this->lang     = $lang;
    }
    /**
     * 生成查询条件
     * @param object $builder
     * @param array $params
     * @return object
     */
    public function getBuilderWhere(object $builder, array $params): object
    {
        // osm状态
        if (isset($params['osm_state']) && !empty($params['osm_state'])) {
            $builder->andWhere('o.osm_state = :osm_state:', ['osm_state' => (int)$params['osm_state']]);
        }

        // 外协公司id
        if (isset($params['company_id']) && !empty($params['company_id'])) {
            $builder->andWhere('d.company_id = :company_id:',
                ['company_id' => (int)$params['company_id']]);
        }

        // 加班日期
        if (isset($params['ot_date']) && !empty($params['ot_date'])) {
            $builder->andWhere('o.ot_date = :ot_date:', ['ot_date' => $params['ot_date']]);
        }

        if (isset($params['start_time']) && !empty($params['start_time']) && isset($params['end_time']) && !empty($params['end_time'])) {
            // 1、db中的开始时间落在了传入时间区间的中间
            // 2、db中的结束时间落在了传入时间区间的中间
            // 3、传入的时间区间吧db的开始和结束时间包裹起来了
            // 4、db的时间区间把传入的时间区间包裹起来了
            if (isset($params['source']) && $params['source'] == 'order') {
                $builder->andWhere('(o.start_time >= :start_time: AND o.start_time < :end_time:)
            OR (o.end_time > :start_time: AND o.end_time <= :end_time:)
            OR (o.start_time >= :start_time: AND o.end_time <= :end_time:) 
            OR (o.start_time <= :start_time: AND o.end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            } else {
                $builder->andWhere('(o.start_time >= :start_time: AND o.start_time <= :end_time:)
            OR (o.end_time >= :start_time: AND o.end_time <= :end_time:)
            OR (o.start_time >= :start_time: AND o.end_time <= :end_time:) 
            OR (o.start_time <= :start_time: AND o.end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            }
        }

        // 审批状态
        if (isset($params['apply_state']) && is_array($params['apply_state']) && !empty($params['apply_state'])) {
            $builder->andWhere('o.apply_state IN ({apply_state:array})', ['apply_state' => $params['apply_state']]);
        }

        return $builder;
    }

    /**
     * sql 查询
     * @param array $params
     * @param array $columns
     * @param bool $isCount
     * @return array
     */
    public function getOutsourcingOTQuery(array $params, array $columns = [], bool $isCount = false): array
    {
        if (empty($params)) {
            return [];
        }
        $str_column = empty($columns) ? '*' : implode(',', $columns);
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns($str_column);
        $builder->from(['o' => HubOutsourcingOvertimeModel::class]);
        $builder->leftjoin(HubOutsourcingOvertimeDetailModel::class, "d.hub_outsourcing_overtime_id=o.id", "d");
        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns($str_column);
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        if (empty($params['is_all'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->groupby('o.id');
        $builder->orderBy('o.id DESC');
        return $builder->getQuery()->execute()->toArray();
    }
    /**
     * 获取外协员工加班审批单
     * @param array $params
     * @param array $user_info
     * @return void
     */
    public function getOutsourcingOTList(array $params, array $user_info)
    {
        $params['page']       = empty($params['page']) ? 1 : $params['page'];
        $params['page_size']  = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size']  = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['company_id'] = $user_info['id'];

        $result = [
            'total'     => 0,
            'cur_page'  => (int)$params['page'],
            'page_size' => (int)$params['page_size'],
            'list'      => [],
        ];

        // 查询外协公司id是否合法
        $outsourcing_company_model = new OutsourcingCompanyModel();
        $outsourcing_company       = $outsourcing_company_model->getOneById($params['company_id']);
        if (empty($outsourcing_company) || $outsourcing_company['deleted'] != 0) {
            $this->getDI()->get('logger')->write_log('outsoucing-company-login-no-exists'.json_encode(['company_id' => $params['company_id']]),
                'info');
            throw new ValidationException($this->getTranslation()->_('outsourcing_company_no_exists'),
                ErrCode::VALIDATE_ERROR);
        }

        $count_columns = ['COUNT(DISTINCT o.id) AS count'];
        $result_count  = $this->getOutsourcingOTQuery($params, $count_columns, true);
        if ($result_count['count'] == 0) {
            return $result;
        }

        $count_list     = [
            'o.id',
            'o.store_id',
            'o.ot_date',
            'o.start_time',
            'o.end_time',
            'o.duration',
            'o.demand_num',
            'o.osm_state',
            'o.img',
        ];
        $result_list    = $this->getOutsourcingOTQuery($params, $count_list);
        $store_ids      = array_values(array_unique(array_column($result_list, 'store_id')));
        $sys_store_list = [];
        if (!empty($store_ids)) {
            $sys_store_arr  = SysStoreModel::find([
                'conditions' => 'id in ({ids:array}) ',
                'bind'       => ['ids' => $store_ids],
                'columns'    => "id,name",
            ])->toArray();
            $sys_store_list = array_column($sys_store_arr, null, 'id');
        }
        foreach ($result_list as $key => &$val) {
            $val['id']             = (int)$val['id'];
            $val['demand_num']     = (int)$val['demand_num'];
            $val['osm_state']      = (int)$val['osm_state'];
            $val['ot_title']       = isset($sys_store_list[$val['store_id']]) ? $sys_store_list[$val['store_id']]['name'] : "";
            $b_time                = date("H:i", strtotime($val['start_time']));
            $e_time                = date("H:i", strtotime($val['end_time']));
            $val['ot_time']        = $b_time.'-'.$e_time;
            $val['osm_state_text'] = $this->getTranslation()->_(HubOutsourcingOvertimeModel::$osm_state_text[$val['osm_state']]);
            $val['img']            = json_decode($val['img']);
            unset($val['start_time'], $val['end_time']);
        }
        $result['total'] = (int)$result_count['count'];
        $result['list']  = $result_list;
        return $result;
    }


    /**
     * osm获取可配置的外协员工名单
     * @param array $params
     * @param array $userInfo
     * @return array
     * @throws ValidationException
     */
    public function getOrderStaffList(array $params, array $userInfo): array
    {
        $params['page']      = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];
        $params['id']        = $this->processingDefault($params, 'id', 2);

        // 根据传入的id验证数据是否存在
        $os_ot_info = (new HubOutsourcingOvertimeRepository($this->timezone))->getOutsourcingOTById($params['id']);
        if (empty($os_ot_info)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $os_ot_detail_rep_obj                       = new HubOutsourcingOvertimeDetailRepository($this->timezone);
        $query_where['hub_outsourcing_overtime_id'] = $os_ot_info['id'];
        $count_columns                              = ['COUNT(*) AS count'];
        // 根据传入的订单号查询出当前工单已经配置过的人员
        $os_ot_detail_count = $os_ot_detail_rep_obj->getOutsourcingOTDetailQuery($query_where,
            $count_columns, true, true);

        //当前订单 已配置的人数
        $current_os_ot_detail_count = !empty($os_ot_detail_count) ? intval($os_ot_detail_count['count']) : 0;
        //当前订单 已配置的工号
        $current_os_ot_staff_ids = [];
        if (!empty($current_os_ot_detail_count)) {
            $query_where['is_all']     = 1;
            $columns                   = ['staff_id'];
            $current_os_ot_detail_list = $os_ot_detail_rep_obj->getOutsourcingOTDetailQuery($query_where,
                $columns);
            $current_os_ot_staff_ids   = !empty($current_os_ot_detail_list) ? array_column($current_os_ot_detail_list,
                'staff_id') : [];
        }

        // 1、获取外协员工加班时间与当前选择的加班时间有交集的员工
        $os_ot_where              = [
            'company_id' => $os_ot_info['outsourcing_company_id'],
            'start_time' => $os_ot_info['start_time'],
            'end_time'   => $os_ot_info['end_time'],
        ];
        $use_staff_info_ids = $this->getOutsourcingOTIntersection($os_ot_where);

        // 2、获取外协员工工单与当前选择加班时间有交接的员工
        $outsourcing_order_params = [
            'out_company_id'  => $os_ot_info['outsourcing_company_id'],
            'employment_date' => $os_ot_info['ot_date'],
            'start_time'      => $os_ot_info['start_time'],
            'end_time'        => $os_ot_info['end_time'],
        ];
        $order_use_staff_info_ids = (new OutsourcingOrderServer($this->lang))->getUsingStaffInfoV2($outsourcing_order_params);
        // 3、两种有交集的员工做合集并去重
        $use_staff_info_ids = array_values(array_unique(array_merge($use_staff_info_ids, $order_use_staff_info_ids)));

        //可选员工数量
        $osOrderRepository            = new OsOrderRepository($this->timezone);
        $params['store_id']           = $os_ot_info['store_id'];
        $params['company_id']         = $os_ot_info['outsourcing_company_id'];
        $params['use_staff_info_ids'] = $use_staff_info_ids;
        $count                        = $osOrderRepository->getOptionalStaffQuery($params, true);

        $total = !empty($count) ? intval($count['count']) : 0;
        if (empty($total)) {
            return ['total' => 0, 'list' => []];
        }
        //可选员工列表
        $list = $osOrderRepository->getOptionalStaffQuery($params);

        return [
            'total'                => $total,
            'cur_page'             => (int)$params['page'],
            'page_size'            => (int)$params['page_size'],
            'list'                 => $list,
            'selected_staff_count' => $current_os_ot_detail_count,
            'selected_staff_ids'   => $current_os_ot_staff_ids,
        ];
    }

    /**
     * 查询出与当前外协加班订单时间有交集的外协员工号
     * @param array $query_params
     * @param array $outsourcing_order_params
     * @return array
     */
    public function getOutsourcingOTIntersection(array $query_params): array
    {
        // 1、查询出符合时间范围的申请单
        $columns                = [
            'o.id',
            'o.serial_no',
            'o.start_time',
            'o.end_time',
        ];
        $query_params['is_all'] = 1;
        $query_params['apply_state'] = [1,2];   // 待审批、已同意
        $os_ot_list             = $this->getOutsourcingOTQuery($query_params, $columns);
        if (empty($os_ot_list)) {
            return $os_ot_list;
        }
        $os_ot_ids = array_values(array_unique(array_column($os_ot_list, 'id')));

        // 根据申请单查询出具体的id号
        $query_params['hub_outsourcing_overtime_id'] = $os_ot_ids;
        $os_ot_detail           = (new HubOutsourcingOvertimeDetailRepository($this->timezone))->getOutsourcingOTDetailQuery($query_params,
            ['staff_id']);
        $os_ot_detail_staff_ids = array_column($os_ot_detail, 'staff_id');
        return array_values(array_unique($os_ot_detail_staff_ids));
    }

    /**
     * 外协订单调用，查询时间有冲突的外协加班员工
     * @param array $orderInfo
     * @return array
     */
    public function getOutsourcingOTIntersectionV2(array $orderInfo): array
    {
        $os_ot_ids = [];
        if (empty($orderInfo)) {
            return $os_ot_ids;
        }

        $shiftList      = (new OutsourcingOrderServer($this->lang))->getShiftInfo([$orderInfo['shift_id']]);
        $shiftInfo      = !empty($shiftList[$orderInfo['shift_id']]) ? $shiftList[$orderInfo['shift_id']] : [];
        if (empty($shiftInfo)) {
            return  $os_ot_ids;
        }

        $employmentDate = $orderInfo['employment_date'];
        $orderStart     = $employmentDate.' '.$shiftInfo['start'].':00' ?? '';
        $orderEnd       = $employmentDate.' '.$shiftInfo['end'].':00' ?? '';

        if ($orderStart > $orderEnd) {
            $orderEnd = date('Y-m-d H:i', strtotime("+1 day", strtotime($orderEnd)));
        }
        $os_ot_where = [
            'company_id' => $orderInfo['out_company_id'],
            'start_time' => $orderStart,
            'end_time'   => $orderEnd,
            'source'     => 'order'
        ];
        return $this->getOutsourcingOTIntersection($os_ot_where);
    }

    /**
     * 获取已经配置的数据
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getAlreadyOrderStaffList(array $params)
    {
        $params['id']   = $this->processingDefault($params, 'id', 2);
        $data           = [
            'total'     => 0,
            'list'      => [],
        ];
        // 根据传入的id验证数据是否存在
        $os_ot_info = (new HubOutsourcingOvertimeRepository($this->timezone))->getOutsourcingOTById($params['id']);
        if (empty($os_ot_info)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $params['hub_outsourcing_overtime_id'] = $os_ot_info['id'];
        $os_ot_detail_count                    = (new HubOutsourcingOvertimeDetailRepository($this->timezone))->getOutsourcingOTDetailQuery($params,
            ['COUNT(*) AS count'], true);

        if (0 == $os_ot_detail_count['count']) {
            return $data;
        }
        $data['total'] = (int)$os_ot_detail_count['count'];

        // 获取数据
        $params['is_all'] = 1;  // 获取所有数据，不要分页
        $os_ot_detail_list = (new HubOutsourcingOvertimeDetailRepository($this->timezone))->getOutsourcingOTDetailQuery($params,
            ['staff_id']);
        $staff_ids         = array_column($os_ot_detail_list, 'staff_id');
        if (empty($staff_ids)) {
            return $data;
        }
        $ot_staff_list = HrStaffInfoServer::getUserInfoByStaffInfoIds($staff_ids, 'staff_info_id,name');
        $data['list']  = $ot_staff_list;

        return $data;
    }

}