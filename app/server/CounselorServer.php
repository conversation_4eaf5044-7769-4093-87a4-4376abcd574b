<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\HrInstructorRewardModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;

class CounselorServer extends BaseServer
{
    //一线职位配置
    protected $frontLineJobTitles    = [];
    protected $frontLineJobNonTitles = [];
    protected $branchWorkStaffIds    = [];
    protected $seniorWorkStaffIds    = [];
    protected $hireDays = 60;
    protected $hireInsDays = 30;

    public function __construct($lang = 'zh-CN', DiInterface $di = null)
    {
        parent::__construct($lang, $di);
    }

    /**
     * 获取辅导员数据包
     * @param $param
     * @return array
     */
    public function getList($param)
    {
        //获取网点辅导员数据
        $staffIds = $this->getStaffCounselorList($param);

        //获取网点主管辅导员数据
        if (empty($staffIds)) {
            $staffIds = $this->getSupervisorCounselorList($param);
        }

        //获取大区片区数据
        if (empty($staffIds)) {
            $staffIds = $this->getMangerCounselorList($param);
        }

        //格式化数据
        return $this->formatData($staffIds,$param);
    }

    /**
     * 获取大区片区数据
     */
    public function getMangerCounselorList($param)
    {
        $this->wLog('getCounselorList-Manger',$param);

        //获取
        $staffIds = $this->getStaffIds([
            'sys_store_id' => $param['sys_store_id'],
            'job_title'    => $this->seniorWorkStaffIds,
            'hire_type'    => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
            ]
        ]);

        $regionPieceManager = (new HrOrganizationDepartmentRelationStoreRepository($this->timeZone))->getOrganizationRegionPieceManagerId($param['sys_store_id']);

        $this->getDI()->get('logger')->write_log([
            'getOrganizationRegionPieceManagerId' => $regionPieceManager,
            'sys_store_id'                        => $param['sys_store_id'],
        ], 'info');


        if (!empty($regionPieceManager['region_manager_id'])) {
            $staffIds[] = $regionPieceManager['region_manager_id'];
        }

        if (!empty($regionPieceManager['piece_manager_id'])) {
            $staffIds[] = $regionPieceManager['piece_manager_id'];
        }

        if ($staffIds) {
            //筛选
            $staffIds = $this->getStaffIds([
                'staff_ids'    => $staffIds,
                'hire_type'    => [
                    HrStaffInfoModel::HIRE_TYPE_1,
                    HrStaffInfoModel::HIRE_TYPE_2,
                ]
            ]);
        }

        return $staffIds;
    }

    /**
     * 获取网点主管相关辅导员
     * @param $param
     * @return mixed
     */
    public function getSupervisorCounselorList($param)
    {
        $this->wLog('getCounselorList-Supervisor',$param);

        return $this->getStaffIds([
            'sys_store_id' => $param['sys_store_id'],
            'job_title'    => $this->branchWorkStaffIds,
            'hire_type'    => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
            ],
        ]);
    }

    /**
     * 获取辅导员数据
     * @param $param
     * @return mixed
     */
    public function getStaffIds($param)
    {
        $builder = $this->modelsManager->createBuilder();

        $builder->from(HrStaffInfoModel::class);

        if (!empty($param['sys_store_id'])) {
            $builder->andWhere('sys_store_id = :sys_store_id:', ['sys_store_id' => $param['sys_store_id']]);
        }

        if (!empty($param['staff_ids'])) {
            $builder->andWhere('staff_info_id IN ({staff_ids:array})', ['staff_ids' => $param['staff_ids']]);
        }

        if (!empty($param['job_title'])) {
            $builder->andWhere('job_title IN ({job_title:array})', ['job_title' => $param['job_title']]);
        }

        if (!empty($param['hire_date'])) {
            $builder->andWhere('hire_date <= :hire_date:', ['hire_date' => $param['hire_date']]);
        }

        if (!empty($param['hire_type'])) {
            $builder->andWhere('hire_type IN ({hire_type:array})', ['hire_type' => $param['hire_type']]);
        }

        $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $builder->andWhere('state = :state:', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        $builder->andWhere('wait_leave_state = :wait_leave_state:',
            ['wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO]);

        $data =  $builder->getQuery()->execute()->toArray();

        return $data ? array_column($data,'staff_info_id') :[];
    }

    /**
     * 获取网点员工数据
     * @param $params
     * @return array
     */
    public function getStaffCounselorList($param)
    {
        $this->wLog('getCounselorList-store',$param);
        //职位数据
        $sysStoreId = $param['sys_store_id'] ?? '';
        $jobTitles  = $this->getSearchJobTitles($param['job_title'] ?? 0);
        $hireDate   = date('Y-m-d', strtotime("-{$this->hireDays} days"));

        if (empty($jobTitles) || !$sysStoreId) {
            return [];
        }

        //获取符合条件的ids
        $staffIds = $this->getStaffIds([
            'sys_store_id' => $sysStoreId,
            'job_title'    => $jobTitles,
            'hire_date'    => $hireDate,
            'hire_type'    => [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
            ]
        ]);

        if (empty($staffIds)) {
            return [];
        }

        //去除辅导员超出限制的员工
        $staffIds = $this->deleteCounselorNumStaffIds($staffIds,$sysStoreId);

        return array_values($staffIds);
    }


    /**
     * 排查次数次数大于配置次数的员工
     * @param $staffIds
     * @param string $sysStoreId
     * @return array
     */
    public function deleteCounselorNumStaffIds($staffIds, $sysStoreId = '')
    {
        if (empty($staffIds)) {
            return [];
        }

        //获取员工辅导人数统计
        $maxNumber = (new SettingEnvServer)->getSetVal('max_number_of_staffs_mentored');

        //未设置 直接全部返回
        if ($maxNumber === '') {
            return $staffIds;
        }

        $maxNumber = intval($maxNumber);

        $counselorHireDate = date('Y-m-d', strtotime("-{$this->hireInsDays} days"));

        $counselorNums = $this->getStaffCounselorNums([
            'hire_date'    => $counselorHireDate,
            'sys_store_id' => $sysStoreId,
            'staffIds'     => $staffIds,
        ]);

        foreach ($staffIds as $k => $v) {
            if (isset($counselorNums[$v]) && ($counselorNums[$v] >= $maxNumber)) {
                unset($staffIds[$k]);
            }
        }

        return array_values($staffIds);
    }

    /**
     * 获取员工辅导员数量
     * @param array $staffIds
     * @return array
     */
    public function getStaffCounselorNums($param = [])
    {
        $staffIds = $param['staffIds'] ?? [];

        if (empty($staffIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('instructor_id,COUNT(1) as num');
        $builder->from(HrStaffInfoModel::class);
        $builder->andWhere('instructor_id IN ({instructor_id:array})', ['instructor_id' => $staffIds]);

        if (!empty($param['hire_date'])) {
            $builder->andWhere('hire_date > :hire_date:', ['hire_date' => $param['hire_date']]);
        }

        if (!empty($param['sys_store_id'])) {
            $builder->andWhere('sys_store_id = :sys_store_id:', ['sys_store_id' => $param['sys_store_id']]);
        }

        $builder->andWhere('state != :state:', ['state' => HrStaffInfoModel::STATE_2]);

        $builder->groupby('instructor_id');

        $staffInfoArr = $builder->getQuery()->execute()->toArray();

        return $staffInfoArr ? array_column($staffInfoArr, 'num', 'instructor_id') : [];
    }

    /**
     * 格式化返回数据
     */
    public function formatData($staffIds = [], $param = [])
    {
        if (empty($staffIds)) {
            return [];
        }

        //获取奖励次数基本信息
        $data = $this->getRewardNum($staffIds);

        //获取真实的辅导员工
        if (empty($data)) {
            return [];
        }

        $counselorHireDate = date('Y-m-d', strtotime("-{$this->hireInsDays} days"));

        $staffNumList = $this->getStaffCounselorNums([
            'hire_date'    => $counselorHireDate,
            'sys_store_id' => $param['sys_store_id'],
            'staffIds'     => $staffIds,
        ]);

        $jobIds = array_values(array_unique(array_filter(array_column($data, 'job_title'))));

        $jobTitleList = [];
        if ($jobIds) {
            $jobTitleList = (new HrJobTitleModel())->getListByIds($jobIds);
            $jobTitleList = array_column($jobTitleList,'job_name','id');
        }

        $returnData = [];
        foreach ($data as $v) {
            $info = [
                'counselor_staff_id'   => $v['staff_info_id'],
                'counselor_staff_name' => $v['name'] ?? '',
                'counselor_value'      => '（' . $v['staff_info_id'] . '）' . $v['name'],
                'counselor_job_name'   => $jobTitleList[$v['job_title']] ?? '',
                'counselor_hire_days'  => floor((time() - strtotime($v['hire_date'])) / 86400),
                'counselor_reward_num' => (int) $v['num'],
                'counselor_staff_num'  => (int) ($staffNumList[$v['staff_info_id']] ?? 0)
            ];

            $returnData[] = $info;
        }

        return $returnData;
    }

    /**
     * 获取员工奖励次数
     * @param array $staffIds
     * @return array
     */
    public function getRewardNum($staffIds = [])
    {
        if (empty($staffIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff.staff_info_id,staff.name,staff.job_title,staff.hire_type,COUNT(reward.instructor_id) as num,staff.hire_date as hire_date');
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrInstructorRewardModel::class, 'staff.staff_info_id = reward.instructor_id AND reward.is_deleted=0', 'reward');
        $builder->andWhere('staff.staff_info_id IN ({staff_info_id:array})', ['staff_info_id' => $staffIds]);
        $builder->groupby('staff.staff_info_id');
        $builder->orderby('num DESC,staff.hire_date ASC,staff.staff_info_id ASC');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取辅导员数据
     * @param $param
     * @return mixed
     */
    public function getStaffList($param)
    {
        $builder = $this->modelsManager->createBuilder();

        $builder->from(HrStaffInfoModel::class);

        if (!empty($param['sys_store_id'])) {
            $builder->andWhere('sys_store_id = :sys_store_id:', ['sys_store_id' => $param['sys_store_id']]);
        }

        if (!empty($param['job_title'])) {
            $builder->andWhere('job_title IN ({job_title:array})', ['job_title' => $param['job_title']]);
        }

        if (!empty($param['hire_type'])) {
            $builder->andWhere('hire_type IN ({hire_type:array})', ['hire_type' => $param['hire_type']]);
        }

        $builder->columns('staff_info_id,name,job_title,hire_type,hire_date');
        $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $builder->andWhere('state = :state:', ['state' => HrStaffInfoModel::STATE_1]);
        $builder->andWhere('formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_1]);
        $builder->andWhere('wait_leave_state = :wait_leave_state:', ['wait_leave_state' => HrStaffInfoModel::WAITING_LEAVE_NO]);

        return  $builder->getQuery()->execute()->toArray();
    }
}

