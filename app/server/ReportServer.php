<?php
/**
 * Created by <PERSON>p<PERSON><PERSON><PERSON>.
 * User: zyp
 * Date: 2020-01-07
 * Time: 14:21
 */


namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\ReportAbsenteeismLogModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditDescModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\SysStoreGoodsModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ReportRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Traits\FindApprovalNodeTrait;

class ReportServer extends AuditBaseServer
{
    use  FindApprovalNodeTrait;
    const PUNISH_CATEGORY_CANCEL_TASK    = 58;//虚假取消揽件任务
    const PUNISH_CATEGORY_MARK_REJECTION = 60;//虚假标记拒收
    const PUNISH_CATEGORY_PROBLEM        = 1;//虚假问题件/留仓件
    const PUNISH_CATEGORY_COMPLAINT      = 21;//客户投诉

    public static $punish_category = [
        self::PUNISH_CATEGORY_CANCEL_TASK,
        self::PUNISH_CATEGORY_MARK_REJECTION,
        self::PUNISH_CATEGORY_PROBLEM,
        self::PUNISH_CATEGORY_COMPLAINT,
    ];

    const PUNISH_LIMIT_NUM = 10;

    protected     $Cost;
    protected     $AuditListR;
    protected     $AuditList;
    protected     $auditlist;
    public        $timezone;
    public static $paramsIn = [];

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone   = $timezone;
        $this->report     = new ReportRepository($lang, $timezone);
        $this->public     = new PublicRepository();
        $this->auditlist  = new AuditListServer($lang, $timezone);
        $this->auditLog   = new StaffAuditToolLog();
        $this->ov         = new OvertimeRepository($timezone);
        $this->wf         = new WorkflowServer($this->lang, $this->timezone);
        $this->AuditListR = new AuditlistRepository($this->lang, $timezone);
    }

    protected function isHasPositions($staffId, $positions)
    {
        $isHas = HrStaffInfoPositionModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
            'bind'       => [
                'staff_id'  => $staffId,
                'positions' => $positions,
            ],
        ]);

        if ($isHas) {
            return true;
        }

        return false;
    }

    /**
     *
     * 举报下拉
     * 后续优化该方法
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function dictReportS($userinfo, $paramIn)
    {
        $loginStaffId       = $userinfo['staff_id'];
        $isSpecialPositions = false;
        $isDept             = false;
        $staffId            = $paramIn['staff_id'];
        $is_hire_type_un_paid = false;
        if ($staffId) {
            $auditServer        = new AuditServer($this->lang, $this->timezone);
            $isSpecialPositions = $auditServer->isQAQC($loginStaffId);
            if (isCountry('Ph') && $this->isHasPositions($loginStaffId,
                    [enums::HUB_QAQC_ROLES_ID, enums::NETWORK_QC_ROLES_ID, enums::QAQC_ROLES_ID])) {
                $isSpecialPositions = true;
            }
            $reportUser = $this->getReportUserInfo($staffId);
            if ($reportUser) {
                $node_department_id = $reportUser->node_department_id;
                if (empty($node_department_id)) {
                    $node_department_id = $reportUser->sys_department_id;
                }

                //适配my，之前代码写死ID，现在从setting_env获取配置
                $set_val        = (new SettingEnvServer())->getSetVal('qaqc_report_department_flow');
                $department_ids = json_decode($set_val, true);

                // 举报对象：Network Operations 部门的网点员工
                if (isCountry('MY')){
                    if (!empty($reportUser->hire_type) && in_array($reportUser->hire_type,HrStaffInfoModel::$agentTypeTogether)){
                        // 个人代理
                        $is_hire_type_un_paid = true;
                    }
                    $ids1 = array_merge(
                        $this->getDeptIdsByTopDeptId($department_ids['network_management']),
                        $this->getDeptIdsByTopDeptId($department_ids['transportation'])
                    );
                }else{
                    $ids1 = $this->getDeptIdsByTopDeptId($department_ids['network_operations']);//$ids1 = $this->getDeptIdsByTopDeptId(32);
                }
                //Hub management
                $ids2 = $this->getDeptIdsByTopDeptId($department_ids['hub_management']);
                //Shop Management
                $ids3 = $this->getDeptIdsByTopDeptId($department_ids['shop_management']);
                //netwok bulky
                //获取 env 配置
                $envModel       = new SettingEnvServer();
                $networkBulkyId = $envModel->getSetVal('dept_network_bulky_id');
                $ids4           = [];
                if ($networkBulkyId) {
                    $ids4 = $this->getDeptIdsByTopDeptId($networkBulkyId);
                }
                $ids5 = [];
                if (isCountry('TH')) {
                    $ids5 = $this->getDeptIdsByTopDeptId($envModel->getSetVal('dept_network_area_id'));
                    $ids5 = array_merge($ids5,
                        $this->getDeptIdsByTopDeptId($envModel->getSetVal('dept_network_bulky_area_id')));
                    $ids5 = array_merge($ids5,
                        $this->getDeptIdsByTopDeptId($envModel->getSetVal('dept_flash_freight_hub_dep_id')));
                    $ids5 = array_merge($ids5,
                        $this->getDeptIdsByTopDeptId($envModel->getSetVal('dept_city_distribution_center_project_id')));
                    $ids5 = array_merge($ids5, $this->getDeptIdsByTopDeptId($envModel->getSetVal('dept_pickup_dc_id')));
                }

                $idsArr = array_merge($ids1, $ids2, $ids3, $ids4, $ids5);
                $isDept = ($reportUser->sys_store_id != -1 && in_array($node_department_id, $idsArr));
            }
        }
        $resData = $this->report->dictReportR($isSpecialPositions, $isDept, $is_hire_type_un_paid);
        return $resData;
    }

    /**
     * 获取部门及其子部门
     * 举报对象：Network Operations、Hub management、Shop Management部门ids
     * @param string $topDeptId
     * @return array|string[]
     */
    public function getDeptIdsByTopDeptId($topDeptId = ''): array
    {
        if (empty($topDeptId)){
            return [];
        }
        $ids  = [$topDeptId];
        $dept = SysDepartmentModel::findFirst([
            'conditions' => ' id = :toId: ',
            'bind'       => [
                'toId' => $topDeptId,
            ],
        ]);
        if (isset($dept->id) && $dept->id) {
            $departmentChain = $dept->ancestry_v3;
            $data            = SysDepartmentModel::find([
                'conditions' => 'ancestry_v3 like :chain: or id = :id:',
                'bind'       => [
                    'chain' => "{$departmentChain}/%",
                    'id'    => $dept->id,
                ],
                'columns'    => 'id',
            ])->toArray();
            if ($data) {
                $ids = array_merge($ids, array_column($data, 'id'));
            }
        }
        return $ids;
    }


    /**
     * 获取用户信息
     * @param $params
     * @return array|void
     */
    public function getStaffInfo($params)
    {
        $resData = $this->report->getStaffInfoFormal($params['staff_id']);
        if (!$resData) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1001')));
        }

        $isPermission = $this->validateReportPermission($params['user_info']['staff_id'], $resData);

        if(!$isPermission) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('report_no_permission')));
        }
        if(isCountry(['TH','PH']) && in_array($resData['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('report_hire_type_err')));
        }
        $retData = [
            'data' => $resData,
        ];
        return $retData;
    }

    /**
     * 检验 是否有权限 举报
     * @param $userId
     * @param $staffInfo
     * @return bool
     */
    public function validateReportPermission($userId, $staffInfo)
    {
        //根据角色判断是否可以发送举报
        if((new AuditServer($this->lang, $this->timezone))->validateReportPermissionByRoles($userId)) {
            return true;
        }

        //查询当前登录人，负责的所有组织
        $departmentManager = SysDepartmentModel::find([
            'conditions' => "manager_id = :staff_id: and deleted = :deleted:",
            'bind' => [
                'staff_id'  => $userId,
                'deleted'   => enums::IS_DELETED_NO,
            ],
            'columns'    => 'id',
        ])->toArray();
        $managerAllIds = [];
        //获取负责所有组织及子集
        if(!empty($departmentManager)) {
            $departmentIds = array_column($departmentManager, 'id');
            foreach ($departmentIds as $oneId) {
                $managerIds = $this->getDeptIdsByTopDeptId($oneId);
                $managerAllIds = array_merge($managerAllIds, $managerIds);
            }
            $managerAllIds = array_values(array_unique($managerAllIds));
        }
        if(in_array($staffInfo['node_department_id'], $managerAllIds)) {
            return true;
        }

        //获取登录人 负责组织及子集下关联的 网点id
        $relateStores = [];
        if($managerAllIds) {
            $relateStores = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getDepartmentStoreRelationInfo($managerAllIds);
        }
        //获取登录人 负责 的大区片区网点 to 网点
        $managerStore = (new SysStoreServer($this->lang, $this->timezone))->getStoreByManager($userId);

        $managerStore = array_values(array_unique(array_merge($managerStore, $relateStores)));

        if(in_array($staffInfo['sys_store_id'], $managerStore)) {
            return true;
        }

        //找下级+ 下下级
        $subordinateStaffIds = [];
        $staffSubordinate = $this->getSubordinateInfo([$userId]);
        if($staffSubordinate) {
            $staffSubordinateIds = array_column($staffSubordinate, 'staff_info_id');

            $staffSubordinateSecond = $this->getSubordinateInfo($staffSubordinateIds);
            $staffSubordinateSecondIds = array_column($staffSubordinateSecond, 'staff_info_id');

            $subordinateStaffIds = array_merge($staffSubordinateIds, $staffSubordinateSecondIds);
        }

        if(in_array($staffInfo['staff_id'], $subordinateStaffIds)) {
            return true;
        }

        return false;
    }

    /**
     * 找下级
     * @param $staffIds
     * @return mixed
     */
    public function getSubordinateInfo($staffIds)
    {
        return HrStaffItemsModel::find([
            'conditions' => "item = :item: and value in ({value:array})",
            'bind' => [
                'item' => 'MANGER',
                'value' => $staffIds,
            ],
        ])->toArray();
    }

    public function validateReportReason($paramIn)
    {
        return true;

    }


    /**
     * 创建
     * @Access  public
     * @param array $paramIn
     * @param $userinfo
     * @return array
     */
    public function addReportS($paramIn = [], $userinfo)
    {

        $server = Tools::reBuildCountryInstance($this,[$this->lang,$this->timezone]);
        $server->validateReportReason($paramIn);

        $staffRepository = new StaffRepository($this->lang);
        $submitterInfo = $staffRepository->getStaffInfoOne($userinfo['id']);
        $reportInfo = $staffRepository->getStaffInfoOne($paramIn['report_id']);
        if (empty($reportInfo)) {
            throw new ValidationException($this->getTranslation()->_('wrong_staff_id'));
        }

        self::$paramsIn             = $paramIn;
        $returnData['data']         = [];
        $reportData['report_type']  = isset($paramIn['report_type']) ? $paramIn['report_type'] : 1;
        $reportData['reason']       = isset($paramIn['reason']) ? $paramIn['reason'] : 1;
        $reportData['remark']       = isset($paramIn['remark']) ? $paramIn['remark'] : '';
        $reportData['report_id']    = isset($paramIn['report_id']) ? $paramIn['report_id'] : '';
        $reportData['status']       = 1;//状态
        $reportData['submitter_id'] = isset($userinfo['id']) ? $userinfo['id'] : 0;//申请人
        $reportData['event_date']   = isset($paramIn['event_date']) ? $paramIn['event_date'] : '';//申请时间
        $serialNo                   = $this->getRandomId();
        $reportData['serial_no']    = !empty($serialNo) ? 'RE'.$serialNo : '';
        $imageData['image_path']    = isset($paramIn['image_path']) ? $paramIn['image_path'] : [];
        $reportData['created_at']   = gmdate("Y-m-d H:i:s", time());

        $reportData['staff_department_id'] = $reportInfo['node_department_id'];
        $reportData['staff_job_title']     = $reportInfo['job_title'];
        $reportData['staff_store_id']      = $reportInfo['sys_store_id'];

        $reportData['submitter_department_id']  = $submitterInfo['node_department_id'];
        $reportData['submitter_job_title']      = $submitterInfo['job_title'];
        $reportData['waybill_num']              = !empty($paramIn['waybill_num']) ? $paramIn['waybill_num'] : '';//处罚自动举报，处罚单号

        $reportUser = $this->getReportUserInfo($reportData['report_id']);
        $userDeptId = $reportUser->node_department_id;
        if (empty($userDeptId)) {
            $userDeptId = $reportUser->sys_department_id;
        }
        $db                    = WorkflowModel::beginTransaction($this);
        $reportData['wf_role'] = 'report_workflow_new';
        //插入数据
        $reportData['id']     = $this->report->addReportR($reportData, $imageData);
        self::$paramsIn['id'] = $reportData['id'];
        if (empty($reportData['id'])) {
            return $this->checkReturn(-3, 'please try again');
        }

        try {

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id'       => $reportUser->sys_store_id,                                 //被举报人网点
                'department_id'      => $userDeptId,                                               //被举报人的部门
                'staff_info_id'      => $reportData['report_id'],                                  //被举报人
            ];

            if (!((new ApprovalServer($this->lang, $this->timezone))->create($reportData['id'],
                    enums::$audit_type['RE'],
                    $userinfo['staff_id'],
                    null,
                    $extend
                ))) {
                throw new \Exception($this->getTranslation()->_('contract_create_workflow_error'));
            }
        } catch (\Exception $exception) {
            $db->rollBack();
            return $this->checkReturn(-3, $exception->getMessage());
        }
        $db->commit();
        $returnData['data'] = $reportData;
        $returnData['msg']  = $this->getTranslation()->_('5001');
        return $this->checkReturn($returnData);
    }

    public function getReportUserInfo($staffId = '')
    {
        $userDept = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);
        return $userDept;
    }

    /**
     * 修改记录状态记录日志
     * @Access  public
     * @param $paramIn
     * @param $userinfo
     * @return array|bool
     * @throws ValidationException
     */
    public function updateReportStatus($paramIn, $userinfo)
    {
        //获取记录信息
        self::$paramsIn     = $paramIn;
        $result             = $this->report->getReportR($paramIn);
        $paramIn['wf_role'] = $result['wf_role'];
        $reportStaffInfo    = $this->report->getStaffInfo($result['report_id']);
        $summary            = [
            [
                'key'   => 'report_break_rule_staff',
                'value' => $reportStaffInfo['name']."({$result['report_id']})",
            ],
            ['key' => 'report_reason', 'value' => $result['reason']],
        ];
        //记录不能为空

        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2201'));
        }
        //当前状态如果已经审批，申请人不可撤销    '2206' => '当前申请不能撤销',
        if ($result['status'] != 1 && $paramIn['status'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('2206'));
        }

        if ($paramIn['status'] == 2) {
            (new ApprovalServer($this->lang, $this->timezone))->approval(
                $result['id'],
                enums::$audit_type['RE'],
                $userinfo['id']
            );
        } else {
            if ($paramIn['status'] == 3) {
                (new ApprovalServer($this->lang, $this->timezone))->reject(
                    $result['id'],
                    enums::$audit_type['RE'],
                    $paramIn['reject_reason'],
                    $userinfo['id']
                );
            } else {
                if ($paramIn['status'] == 4) {
                    (new ApprovalServer($this->lang, $this->timezone))->cancel(
                        $result['id'],
                        enums::$audit_type['RE'],
                        $paramIn['reject_reason'],
                        $userinfo['id']
                    );
                } else {
                    return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
                }
            }
        }

        return true;
    }

    /**
     * 我的审批 点击 同意按钮 验证
     *
     * 1、qaqc审批：如果是则看是否是 一级审批人，如果是一级审批人则 事情描述、图片为必填项
     * 2、如果事情描述或 图片数量填写：事情描述 1000字符内，图片数量10张以内
     */
    public function validateReportSubmit($report_audit_id, $content_desc, $img_list, $submiterId)
    {
        $reportObj      = ReportAuditModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $report_audit_id],
        ]);
        $workflowParams = $this->getWorkflowParams($report_audit_id, null);
        //        if(empty($reportObj)){
        //            return $this->jsonReturn($this->checkReturn(-3, 'id error'));
        //        }
        // 判断是否是 qaqc 举报审批流
        if (isset($workflowParams['k1']) && $workflowParams['k1'] && in_array($workflowParams['k2'],
                ['report_qaqc_network', 'report_qaqc_hub', 'report_qaqc_shop'])) {
            $request = AuditApplyModel::findFirst(
                [
                    'conditions' => "biz_value = :value: and biz_type = :type:",
                    'bind'       => [
                        'type'  => enums::$audit_type['RE'],
                        'value' => $report_audit_id,
                    ],
                ]
            );
            if (empty($request)) {
                $logObj = StaffAuditApprovalModel::findFirst([
                    'conditions' => 'audit_id = :audit_id: and status = 7 and type = 17',
                    'bind'       => [
                        'audit_id' => $report_audit_id,
                    ],
                    'order'      => 'id desc',
                ]);
                if (empty($logObj)) {
                    $this->jsonReturn($this->checkReturn(-3, 'audit_id error'));
                }

                //一级审批节点，事情描述补充 必填
                if ($logObj->level == 1 && empty($content_desc)) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_fill_explanation')));
                }

                //一级审批节点，图片补充必填
                if ($logObj->level == 1 && empty($img_list)) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_insert_picture')));
                }
            } else {
                $streams = (new WorkflowServer($this->lang, $this->timezone))->getAuditLogs($request, $report_audit_id);

                $isLeave1 = false;
                if (is_array($streams) && count($streams) == 2 && in_array($submiterId,
                        explode(',', $streams[1]['staff_id']))) {
                    $isLeave1 = true;
                }

                //一级审批节点，事情描述补充 必填
                if ($isLeave1 && empty($content_desc)) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_fill_explanation')));
                }

                //一级审批节点，图片补充必填
                if ($isLeave1 && empty($img_list)) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('please_insert_picture')));
                }
            }
        }

        //如果填写事情描述，则验证长度是否超过1000长度；
        if ($content_desc && (!is_string($content_desc) || mb_strlen($content_desc) > 1000)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('5110')));
        }
        if ($img_list && (!is_array($img_list) || count($img_list) > 10)) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('upload_no_more_than_10_pictures')));
        }
    }


    /**
     * 举报申请 审批 - 同意- 申请描述、图片 信息的录入
     * @param $paramIn
     * @param $userinfo
     * @return mixed
     */
    public function insertReportAuditDesc($paramIn, $userinfo)
    {
        try {
            $insert_data = [
                'report_audit_id' => $paramIn['audit_id'],
                'staff_id'        => $userinfo['id'],
                'staff_name'      => $userinfo['name'],
                'content_desc'    => addslashes(trim($paramIn['content_desc'])),
                'img_list'        => implode(',', $paramIn['img_list']),
            ];
            $result      = $this->getDI()->get('db')->insertAsDict(
                'report_audit_desc', $insert_data
            );
            return $result;
        } catch (\Exception $e) {
            $this->wLog('写入失败:'.$e->getMessage().$e->getTraceAsString(), $insert_data, 'insertReportAuditDesc',
                'error');
            return false;
        }
    }

    /**
     * 举报申请 拓展表（获取审批人事情描述、图片 等信息）
     *
     * @param $report_audit_id 举报申请ID
     */
    public function getReportAuditDescById($report_audit_id)
    {
        try {
            //举报申请 拓展表（获取审批人事情描述、图片 等信息）
            $obj = ReportAuditDescModel::find([
                'conditions' => "report_audit_id = :report_audit_id:",
                'bind'       => [
                    'report_audit_id' => $report_audit_id,
                ],
            ]);
            if (empty($obj)) {
                return [];
            }
            $data_list = $obj->toArray();
            //获取工号和昵称映射关系数据
            $staff_id_arr       = array_column($data_list, 'staff_id');
            $staff_nickname_map = [];
            if ($staff_id_arr) {
                $staff_nickname_map = $this->getStaffNickNameByIds($staff_id_arr);
            }

            $ret_data = [];
            foreach ($data_list as $item) {
                $suffix   = '('.$item['staff_name'];
                $suffix   .= $staff_nickname_map[$item['staff_id']] ? '-'.$staff_nickname_map[$item['staff_id']] : '';
                $suffix   .= "-{$item['staff_id']})";
                $img_list = $item['img_list'] ? explode(',', $item['img_list']) : [];
                if ($item['content_desc']) {
                    $ret_data[] = [
                        'type'  => 'content_desc',
                        'key'   => $this->getTranslation()->_('content_desc_extend').$suffix,
                        'value' => $item['content_desc'],
                    ];
                }
                if ($img_list) {
                    $ret_data[] = [
                        'type'  => 'picture',
                        'key'   => $this->getTranslation()->_('img_extend').$suffix,
                        'value' => $img_list,
                    ];
                }
            }

            return $ret_data;
        } catch (\Exception $e) {
            $this->wLog('获取举报申请补充信息异常', $report_audit_id, 'getReportAuditDescById', 'error');
            return [];
        }
    }

    /**
     * 获取工号和昵称对应关系数组
     *
     * @param $staff_id_arr 工号ID数组
     * @return array
     */
    public function getStaffNickNameByIds($staff_id_arr)
    {
        //追加昵称字段
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staffs:array})',
            'bind'       => ['staffs' => $staff_id_arr],
            'columns'    => ['staff_info_id', 'nick_name'],
        ])->toArray();

        $staff_nickname_map = array_column($staffArr, 'nick_name', 'staff_info_id');
        return $staff_nickname_map;
    }

    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $postData['id']          = $auditId;
        $result                  = $this->report->getReportR($postData);
        $report_staff_info       = $this->report->getStaffInfo($result['report_id']);
        $imgs                    = $this->report->getAttachment($postData['id'], 'REPORT_AUDIT_IMG');
        $imgArr = [];
        foreach ($imgs as $v) {
            //convertImgUrl
            $imgArr[] = env('img_prefix') . $v['object_key'];
        }
        $staff_info = (new StaffServer())->get_staff($result['submitter_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
            if(!empty($result['submitter_department_id'])) {
                $staff_info['depart_name'] = (new DepartmentRepository($this->lang))->getDepartmentNameById($result['submitter_department_id']);
            }

            if(!empty($result['submitter_job_title'])) {
                $jobTitle = JobTitleRepository::getJobTitleInfo($result['submitter_job_title']);
                $staff_info['job_name'] = !empty($jobTitle) ? $jobTitle->job_name : '';
            }

        } else {
            if ($result['submitter_id']) {
                $staff_info = [
                    'name'        => '',
                    'id'          => 10000,
                    'depart_name' => 'system',
                ];
            }
        }

        if(!empty($result['staff_department_id'])) {
            $report_staff_info['depart_name'] = (new DepartmentRepository($this->lang))->getDepartmentNameById($result['staff_department_id']);
        }

        if(!empty($result['staff_job_title'])) {
            $jobTitle = JobTitleRepository::getJobTitleInfo($result['staff_job_title']);
            $report_staff_info['job_name'] = !empty($jobTitle) ? $jobTitle->job_name : $report_staff_info['job_name'];
        }

        if(!empty($result['staff_store_id'])) {
            $storeName = SysStoreRepository::getStoreName($result['staff_store_id']);
            $report_staff_info['store_name'] = !empty($storeName) ? $storeName : $report_staff_info['store_name'];
        }

        $reportServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $showDataDetail = $reportServer->getReportShowDetail($result,$staff_info,$imgArr,$report_staff_info);

        $fixInfo = $this->getFixInfo($result);
        $showDataDetail = array_merge($showDataDetail, $fixInfo);

        $detail_extend_data           = $this->getReportAuditDescById($auditId);
        $returnData['data']['detail'] = array_merge($showDataDetail, $detail_extend_data);

        //已经驳回，需要显示驳回原因
        if ($result['status'] == enums::APPROVAL_STATUS_REJECTED) {
            $returnData['data']['detail'][] = [
                'type'  => 'reject_reason',
                'key'   => $this->getTranslation()->_('reject_reason'),
                'value' => $result['reject_reason'] ?? '',
            ];
        }

        $data        = [
            'title'       => $this->AuditListR->getAudityType(AuditListEnums::APPROVAL_TYPE_REPORT),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => (string)AuditListEnums::APPROVAL_TYPE_REPORT,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['status'],
            'status_text' => $this->AuditListR->getAuditState($result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
            'reason'   => $result['reason'] ?? '',
        ];

        $is_required = false;
        //目前仅TH 有 举报原因为：违反公司的命令/通知/规则/纪律/规定 的自动举报
        if($result['status'] == enums::$audit_status['panding'] && $result['submitter_id'] == enums::SYSTEM_STAFF_ID && $result['reason'] == ReportRepository::REPORT_TYPE_VIOLATE_REGULATIONS) {
            //查找第一个审批节点，必填
            $findParams['auditId'] = $result['id'];
            $findParams['auditType'] = (string)AuditListEnums::APPROVAL_TYPE_REPORT;
            $findParams['staff_id'] = $user;
            $is_required = $this->findFirstApprovalNode($findParams);
        }

        $returnData['data']['head']                 = $data;
        $returnData['data']['extend']               = $result['extend'] ?? []; //
        $returnData['data']['is_qaqc_level1_audit'] = $this->isQaqcLevel1Audit($auditId, $user); //
        $returnData['data']['is_required'] = $is_required; //是否必填

        return $returnData;
    }

    protected function getReportShowDetail($result, $staff_info, $imgArr, $report_staff_info)
    {
        $t = $this->getTranslation();
        return [
            [
                'type'  => 'apply_parson',
                'key'   => $t->_('apply_parson'),
                'value' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['id'] ?? ''),
            ],
            [
                'type'  => 'apply_department',
                'key'   => $t->_('apply_department'),
                'value' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            ],
            [
                'type'  => 'report_id',
                'key'   => $t->_('report_id'),
                'value' => $report_staff_info['staff_id'] ?? '',
            ],
            [
                'type'  => 'report_name',
                'key'   => $t->_('report_name'),
                'value' => $report_staff_info['name'] ?? '',
            ],
            [
                'type'  => 'report_job_name',
                'key'   => $t->_('report_job_name'),
                'value' => $report_staff_info['job_name'] ?? '',
            ],
            [
                'type'  => 'report_store_name',
                'key'   => $t->_('report_store_name'),
                'value' => $report_staff_info['store_name'] ?? '',
            ],
            [
                'type'  => 'report_hire_type_text',
                'key'   => $t->_('hire_type'),
                'value' => $report_staff_info['hire_type_text'] ?? '',
            ],
            [
                'type'  => 'report_type',
                'key'   => $t->_('report_type'),
                'value' => $t->_('type-' . $result['report_type']),
            ],
            [
                'type'  => 'report_reason',
                'key'   => $t->_('report_reason'),
                'value' => $t->_('t_warning_' . $result['reason']),
            ],
            [
                'type'  => 'event_date',
                'key'   => $t->_('event_date'),
                'value' => $result['event_date'] ?? '',
            ],
            [
                'type'  => 'report_remark',
                'key'   => $t->_('report_remark'),
                'value' => $result['remark'] ?? '',
            ],
            [   'type'  => 'picture',
                'key'   => $t->_('picture'),
                'value' => $imgArr ?? [],
            ],
        ];
    }


    /**
     * 获取举报详情页 提交 按钮的 展示情况
     * 为了做其他国家的差异化 而 写的 function
     * @param $comeFrom
     * @param $auditId
     * @param $user
     * @param $result
     * @param $infos
     * @param $option
     * @param $state
     * @return array
     */
    public function getOption($comeFrom, $auditId, $user, $result, $infos, $option, $state)
    {
        return [$option, $state];
    }

    /**
     *
     * @param $status
     * @return int|mixed
     */
    public function transferAuditStatus($status)
    {
        return $this->auditlist->transferAuditStatus($status);
    }

    public function isQaqcLevel1Audit($auditId, $user)
    {
        $reportObj = ReportAuditModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => intval($auditId)],
        ]);
        if (empty($reportObj)) {
            $this->getDI()->get('logger')->write_log("isQaqcLevel1Audit has exception:，audit_id:".$auditId, 'info');
            return -1;
        }

        $workflowParams = $this->getWorkflowParams($auditId, $user);

        //与张帆对过，这里兼容处理
        if (isset($workflowParams['k1']) && $workflowParams['k1'] == 1 && in_array($workflowParams['k2'],
                ['report_qaqc_network', 'report_qaqc_hub', 'report_qaqc_shop'])) {
            $request = AuditApplyModel::findFirst(
                [
                    'conditions' => "biz_value = :value: and biz_type = :type:",
                    'bind'       => [
                        'type'  => enums::$audit_type['RE'],
                        'value' => $auditId,
                    ],
                ]
            );
            $streams = (new WorkflowServer($this->lang, $this->timezone))->getAuditLogs($request, $auditId);

            if (is_array($streams) && count($streams) == 2 && in_array($user, explode(',', $streams[1]['staff_id']))) {
                return 1;
            }
        }
        return 2;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        $reportAudit = ReportAuditModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $auditId],
        ])->toArray();

        $reportStaffInfo = $this->report->getStaffInfo($reportAudit['report_id']);

        $managePiece = $manageRegion = '';
        if ($reportStaffInfo['manage_region']) {
            $manageRegion = SysManageRegionModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => [
                    'id' => $reportStaffInfo['manage_region'],
                ],
            ]);
            $manageRegion = $manageRegion ? $manageRegion->toArray()['name'] : '';
        }

        if ($reportStaffInfo['manage_piece']) {
            $managePiece = SysManagePieceModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $reportStaffInfo['manage_piece']],
            ]);
            $managePiece = $managePiece ? $managePiece->toArray()['name'] : '';
        }


        return [
            [
                'key'   => 'report_break_rule_staff',
                'value' => $reportStaffInfo['name']."({$reportAudit['report_id']})",
            ],
            [
                'key'   => 'hr_probation_field_sys_store_name', // 所属网点
                'value' => $reportStaffInfo['store_name'] ?? '',
            ],
            [
                'key'   => 'region_piece_name', //  大区片区
                'value' => $manageRegion.' '.$managePiece,
            ],
            [
                'key'   => 'report_reason',
                'value' => $reportAudit['reason'],
            ],
        ];
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批

            $reportAudit = ReportAuditModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => [
                    'id' => $auditId,
                ],
            ]);
            if ($reportAudit) {
                $reportAudit->status         = $state;
                $reportAudit->final_approver = $extend['staff_id'];
                if ($state == 3 || $state == 4) {
                    $reportAudit->reject_reason = isset(self::$paramsIn['reject_reason']) && self::$paramsIn['reject_reason'] ? self::$paramsIn['reject_reason'] : '';
                }
                $reportAudit->final_approval_time = gmdate('Y-m-d H:i:s');
            }

            $reportAudit->save();
        }

        return true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $reportAudit = ReportAuditModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $auditId],
        ]);
        if (empty($reportAudit)) {
            $this->logger->write_log("invalid data! report id :" . $auditId);
            return [];
        }
        $reportAudit = $reportAudit->toArray();

        //获取员工信息
        $StaffRepository = new StaffRepository($this->lang);
        $columns    = 'hsi.staff_info_id, group_concat(hsip.position_category) as position_category,hsi.job_title_grade_v2,
            hsi.node_department_id,hsi.sys_store_id,hsi.job_title';
        $reportUser = $StaffRepository->getStaffInfoByStaffId($reportAudit['report_id'], $columns);

        //获取被举报人网点类型
        $reportStoreInfo = HrStaffInfoServer::getStaffStoreBySysStoreId($reportUser['sys_store_id'], 'category');

        //是否为部门负责人
        $isReportedOrgManager = $StaffRepository->checkSpecStaffOrgManagerByStaffId($reportUser['staff_info_id']);

        return [
            'request_department_id'          => $reportUser['node_department_id'],
            //表单提交的部门  被举报人的部门
            'report_reason_from_submit'      => $reportAudit['reason'],
            //表单提交的举报原因
            'report_type_from_submit'        => $reportAudit['report_type'],
            //表单提交的举报类型
            'sys_store_category'             => !empty($reportStoreInfo) ? $reportStoreInfo->category : 0,
            //被举报人网点类型
            'job_title_id'                   => $reportUser['job_title'],
            //被举报人职位
            'sys_store_id'                   => $reportUser['sys_store_id'],
            //被举报人所属网点
            'job_title_grade_v2_from_submit' => $reportUser['job_title_grade_v2'],
            'position_category_from_submit'  => !empty($reportUser['position_category']) ? explode(',',
                $reportUser['position_category']) : [],
            'is_org_manager_from_submit'     => $isReportedOrgManager,
            'is_auto_report'                 => $user == enums::SYSTEM_STAFF_ID,
        ];
    }

    /**
     * 校验已经固化的审批流是否有效
     * 如果饭
     * @param $flowId
     * @param $auditId
     * @return bool
     * @throws ValidationException
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function checkPersistWorkflow($flowId, $auditId): bool
    {
        $workflowServer = new WorkflowServer($this->lang, $this->timezone);

        $flowNodes   = $workflowServer->getFlowNodes($flowId);
        $currentNode = $workflowServer->getStartNode($flowId); // 开始节点

        do {
            $currentNode = $workflowServer->findNextNode($flowId, $currentNode->id,
                $this->getWorkflowParams(self::$paramsIn['id'], null));
            $currentNode = $workflowServer->pickupNode($flowNodes, $currentNode);
            $approvals   = $workflowServer->getNodeStaffIds($currentNode);
            if ($currentNode->type == enums::NODE_APPROVER && empty($approvals)) { // 审批节点 并且审批人为空

                $this->getDI()->get('logger')->write_log('checkPersistWorkflow currentNode :'.json_encode($currentNode->toArray(),
                        JSON_UNESCAPED_UNICODE), 'notice');
                throw new ValidationException($this->getTranslation()->_('wk_flow_error'));
            }
        } while ($currentNode->type != enums::NODE_FINAL);

        return true;
    }

    /**
     * 处罚运单号
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function waybillNumber($params)
    {
        $reportInfo = ReportRepository::getAuditOne(['id' => $params['report_id']]);
        if(empty($reportInfo)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }

        $retData['data'] = !empty($reportInfo['waybill_num']) ? explode(',', $reportInfo['waybill_num']) : [];
        return $retData;
    }

    /**
     * 处罚举报
     * @param $today
     * @return bool
     * @throws \Exception
     */
    public function punish_report($today)
    {
        /**
         * 1. 时间范围：
         *    1. 当月9号到次月6号，每天0:00统计8天前到月初1号之间累计处罚记录
         *    2. 次月7号，0:00统计7天前到月初即上月整月累计处罚记录
         */
        $resultDate = $this->calculateDateRange($today);
        if (empty($resultDate['start_date']) || empty($resultDate['end_date'])) {
            return false;
        }

        $dateList                  = DateHelper::DateRange(strtotime($resultDate['start_date']),
            strtotime($resultDate['end_date']));
        $params['punish_category'] = implode(',', self::$punish_category);
        $params['store_category']  = implode(',', [
            SysStoreServer::CATEGORY_SP,
            SysStoreServer::CATEGORY_DC,
            SysStoreServer::CATEGORY_BDC,
            SysStoreServer::CATEGORY_PDC,
        ]);
        $params['staff_state']     = implode(',', [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION]);
        $params['formal']          = implode(',', [HrStaffInfoModel::FORMAL_1]);
        $params['hire_type']       = implode(',', [
            HrStaffInfoModel::HIRE_TYPE_1,
            HrStaffInfoModel::HIRE_TYPE_2,
            HrStaffInfoModel::HIRE_TYPE_3,
            HrStaffInfoModel::HIRE_TYPE_4,
        ]);
        $params['is_sub_staff']    = HrStaffInfoModel::IS_SUB_STAFF_0;
        $params['is_money']        = true;//true 金额不是0，false 是0

        //按天去查 处罚记录
        $staffPunishList = [];
        foreach ($dateList as $oneDate) {
            $params['abnormal_time'] = $oneDate;//日期
            $ret                     = new ApiClient('ard_api', '', 'abnormal.getStaffPunishStatisticsByDay',
                $this->lang);
            $ret->setParams($params);
            $res = $ret->execute();
            $this->getDI()->get('logger')->write_log("punish_report 参数:" . json_encode($params) . ";结果:" . json_encode($res),
                'info');
            if (!isset($res['result'])) {
                $this->getDI()->get('logger')->write_log('punish_report 处罚举报，getStaffPunishStatisticsByDay 获取数据异常，请重新执行任务:report_absenteeism punish_report【1】',
                    'error');
                return false;
            }
            if ($res['result']['code'] != 1) {
                $this->getDI()->get('logger')->write_log('punish_report 处罚举报，getStaffPunishStatisticsByDay 获取数据异常，请重新执行任务:report_absenteeism punish_report 【2】',
                    'error');
                return false;
            }

            if (empty($res['result']['data'])) {
                continue;
            }

            $data = $res['result']['data'];

            foreach ($data as $oneData) {
                if (!isset($staffPunishList[$oneData['staff_info_id']])) {
                    $staffPunishList[$oneData['staff_info_id']]['total_num']  = $oneData['total_num'];
                    $staffPunishList[$oneData['staff_info_id']]['event_date'] = $oneData['abnormal_time'];
                } else {
                    $staffPunishList[$oneData['staff_info_id']]['total_num']  = $staffPunishList[$oneData['staff_info_id']]['total_num'] + $oneData['total_num'];
                    $staffPunishList[$oneData['staff_info_id']]['event_date'] = $staffPunishList[$oneData['staff_info_id']]['event_date'] . ',' . $oneData['abnormal_time'];
                }
            }
        }

        $whereAb['report_month'] = date('Y-m', strtotime($resultDate['start_date']));
        $whereAb['type']         = ReportAbsenteeismLogModel::TYPE_PUNISH;
        $whereAb['state']        = ReportAbsenteeismLogModel::REPORT_STATE_SUCCESS;
        $reportLog               = ReportRepository::getAbsenteeismInfo($whereAb);
        $reportStaffIds          = !empty($reportLog) ? array_column($reportLog, 'id', 'staff_info_id') : [];

        unset($params['abnormal_time']);
        $params['start_data'] = $resultDate['start_date'];
        $params['end_data']   = $resultDate['end_date'];

        $reportData = [];
        foreach ($staffPunishList as $staffId => $oneStaff) {
            if ($oneStaff['total_num'] < self::PUNISH_LIMIT_NUM) {
                continue;
            }

            if (isset($reportStaffIds[$staffId])) {
                echo "工号:" . $staffId . '已举报' . PHP_EOL;
                continue;
            }

            $params['staff_info_id'] = $staffId;
            $ret                     = new ApiClient('ard_api', '', 'abnormal.getStaffPunishMergeColumn', $this->lang);
            $ret->setParams($params);
            $res = $ret->execute();
            $this->getDI()->get('logger')->write_log("punish_report 参数:" . json_encode($params) . ";结果:" . json_encode($res),
                'info');
            if (!isset($res['result'])) {
                $this->getDI()->get('logger')->write_log('punish_report 处罚举报，getStaffPunishMergeColumn 获取数据异常，请重新执行任务:report_absenteeism punish_report【1】',
                    'error');
                return false;
            }
            if ($res['result']['code'] != 1) {
                $this->getDI()->get('logger')->write_log('punish_report 处罚举报，getStaffPunishMergeColumn 获取数据异常，请重新执行任务:report_absenteeism punish_report【2】',
                    'error');
                return false;
            }
            if (empty($res['result']['data'])) {
                $this->getDI()->get('logger')->write_log([
                    'punish_report-getStaffPunishMergeColumn-data-empty' => [
                        'params' => $params,
                        'res'    => $res,
                    ],
                ],
                    'error');
                return false;
            }
            $waybill_num = array_values(array_filter($res['result']['data']));
            if (empty($waybill_num)) {
                $this->getDI()->get('logger')->write_log([
                    'punish_report-getStaffPunishMergeColumn-waybill_num' => [
                        'params' => $params,
                        'res'    => $res,
                    ],
                ],
                    'error');
                return false;
            }

            $eventInfo['staff_info_id'] = $staffId;
            $eventInfo['event_date']    = $oneStaff['event_date'];
            $eventInfo['waybill_num']   = implode(',', $waybill_num);
            $reportData[]               = $eventInfo;//需要举报的员工信息
        }

        foreach ($reportData as $oneData) {
            $this->sendReport($oneData, $whereAb['report_month']);
        }

        return true;
    }

    /**
     * 发送举报
     * @param $eventInfo
     * @param $report_month
     */
    public function sendReport($eventInfo, $report_month)
    {
        //考勤日未举报过，操作：进行举报
        //调用举报方法，进行举报
        $report_data   = $this->createReportDataInfo($eventInfo);
        $report_result = $this->addReport($report_data);

        //写入report_absenteeism_log表
        $eventInfo['num'] = 1;//1次 按天的维度，默认给1
        $eventInfo['type'] = ReportAbsenteeismLogModel::TYPE_PUNISH;//1次 按天的维度，默认给1
        unset($eventInfo['event_date']);//需要，单个日期，所以删掉了。
        $report_log       = $this->createReportLogData($eventInfo, $report_month, $report_result);
        $this->addReportAbsenteeismLog($report_log);

        //输出结果
        $this->outputMsg($report_result, $eventInfo);
    }

    /**
     * 输出执行结果
     * @param $report_result
     * @param $staff_info
     */
    public function outputMsg($report_result, $staff_info){
        $logger = $this->getDI()->get('logger');
        if($report_result['code']==1){
            $logger->write_log('[punish_reportAction]员工id举报成功 staff_info_id:' . $staff_info['staff_info_id'], 'info');
            echo 'staff_info_id='.$staff_info['staff_info_id'].' report success' . PHP_EOL;
        }else{
            $logger->write_log('[punish_reportAction]员工id举报失败 staff_info_id:' . $staff_info['staff_info_id'], 'error');
            echo 'staff_info_id='.$staff_info['staff_info_id'].' report fail' . PHP_EOL;
        }
    }

    /**
     * 写入举报旷工日志表
     * @param $report_log
     * @return bool
     */
    public function addReportAbsenteeismLog($report_log){
        $report_log_result = false;
        try{
            $report_log_result = $this->getDI()->get('db')->insertAsDict('report_absenteeism_log', $report_log);
            if(!$report_log_result){
                $msg = "automatic report insert fail:".var_export($report_log,true).PHP_EOL;
                $this->getDI()->get("logger")->write_log($msg, 'info');
            }
        }catch(\Exception $e){
            $msg = "automatic report insert fail:".var_export($report_log,true).PHP_EOL;
            $this->getDI()->get("logger")->write_log($msg, 'info');
        }
        return $report_log_result;
    }

    /**
     * 调用ReportServer::addReportS方法，写入举报表数据
     * @param $report_data
     * @return array
     */
    public function addReport($report_data){
        try{
            $report_result = $this->addReportS($report_data['paramIn'], $report_data['userinfo']);
        }catch (\Exception $e){
            $report_result = [
                'code' => -1,
                'msg' => '请求异常',
                'data' => $e->getMessage()
            ];
        }
        return $report_result;
    }

    /**
     * 生成举报旷工日志数据
     * @param $staff_info
     * @param $report_month
     * @param $report_result
     * @return array
     */
    function createReportLogData($staff_info, $report_month, $report_result){
        $report_state_detail = '';
        if($report_result['code']==1){
            $report_state_detail = '';
        }elseif($report_result['code']==0){
            $report_state_detail = json_encode($report_result['data']);
        }elseif($report_result['code']==-1){
            $report_state_detail = $report_result['data'];
        }

        $report_log = [
            'staff_info_id' => $staff_info['staff_info_id'],
            'report_month' => $report_month,
            'absenteeism_num' => $staff_info['num'],
            'report_state' => $report_result['code'],
            'report_state_detail' => $report_state_detail,
            'attendance_date' => $staff_info['event_date'] ?? NULL,
            'type' => $staff_info['type'] ?? 0,
        ];
        return $report_log;
    }


    /**
     * 生成举报数据
     * @param $staff_info
     * @return mixed
     */
    function createReportDataInfo($staff_info)
    {
        $t               = $this->getTranslation('th');
        $return['paramIn']  = [
            'report_type' => ReportAuditModel::REPORT_TYPE_WARNING_LETTER,
            'reason'      => ReportRepository::REPORT_TYPE_VIOLATE_REGULATIONS,
            'remark'      => $t->_('violate_regulations_describe'),
            'report_id'   => $staff_info['staff_info_id'],
            'event_date'  => $staff_info['event_date'],
            'waybill_num' => $staff_info['waybill_num'],
        ];
        $return['userinfo'] = [
            'id'                => 10000,
            'staff_id'          => 10000,
            'organization_type' => 2,
        ];
        return $return;
    }


    /**
     * 获取统计处罚的开始结束日期。
     * @param $currentDate
     * @return array
     * @throws \Exception
     */
    public function calculateDateRange($currentDate) {
        $current = new DateTime($currentDate);
        $currentDay = (int)$current->format('d');
        $lastMonth = clone $current;
        $lastMonth->modify('last month');

        $result = [
            'start_date' => '',
            'end_date' => ''
        ];
        if ($currentDay >= 1 && $currentDay <= 6) {
            $eightDaysAgo = clone $current;
            $eightDaysAgo->modify('-8 days');
            $startDay = 1;
            $startMonth = (int)$eightDaysAgo->format('m');
            $startYear = (int)$eightDaysAgo->format('Y');
            $endDay = (int)$eightDaysAgo->format('d');
        } elseif ($currentDay >= 9) {
            $eightDaysAgo = clone $current;
            $eightDaysAgo->modify('-8 days');
            $startDay = 1;
            $startMonth = (int)$eightDaysAgo->format('m');
            $startYear = (int)$eightDaysAgo->format('Y');
            $endDay = (int)$eightDaysAgo->format('d');
        } elseif ($currentDay == 7) {
            $startDay = 1;
            $startMonth = (int)$lastMonth->format('m');
            $startYear = (int)$lastMonth->format('Y');
            $endDay = (int)$lastMonth->format('t');
        } else {//8号，不执行任务
            return $result;
        }

        $startDate = sprintf('%04d-%02d-%02d', $startYear, $startMonth, $startDay);
        $endDate = sprintf('%04d-%02d-%02d', $startYear, $startMonth, $endDay);
        $result = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];

        return $result;
    }
    //处理状态
    public function getWarningStatus($status, $warning_status)
    {
        return $warning_status;
    }

    public function getFixInfo($result)
    {
        $reportServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $status       = $reportServer->getWarningStatus($result['status'], $result['warning_status']);
        $t            = $this->getTranslation();
        $data[]       = [
            'type'  => 'fix_status',
            'key'   => $t->_('fix_status'),
            'value' => $t->_(ReportAuditModel::$reportStatus[$status]),
        ];
        if (!in_array($status,
            [ReportAuditModel::REPORT_WARNING_STATUS_UN_PROCESS, ReportAuditModel::REPORT_WARNING_STATUS_NO_PROCESS])) {
            $value = '';
            $log   = ReportRepository::getAuditLogOne(['report_id' => $result['id']]);

            if ($result['warning_status'] == ReportAuditModel::REPORT_WARNING_STATUS_WARNING && $log['warning_status'] == ReportAuditModel::REPORT_WARNING_STATUS_WARNING) {
                if (!empty($log['warning_id'])) {
                    $warningInfo = MessageWarningModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $log['warning_id']],
                    ]);
                    $value       = !empty($warningInfo) ? $warningInfo->warning_no : '';
                }
            }

            if ($result['warning_status'] == ReportAuditModel::REPORT_WARNING_STATUS_UN_WARNING) {
                $value = $log['remark'];
            }

            $data[] = [
                'type'  => 'handle_remark',
                'key'   => $t->_('handle_remark'),
                'value' => $value,
            ];
        }

        return $data;
    }
}
