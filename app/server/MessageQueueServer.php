<?php

namespace FlashExpress\bi\App\Server;

require_once(BASE_PATH . '/app/library/mns_sdk/mns-autoloader.php');
use AliyunMNS\Client;
use AliyunMNS\Exception\MnsException;
use AliyunMNS\Requests\PublishMessageRequest;
use AliyunMNS\Requests\SendMessageRequest;
use Exception;


class MessageQueueServer extends BaseServer
{
    // mns 配置项的redis key
    const MNS_SETTING_REDIS_KEY = 'by_mns_setting_redis_key';
    const MNS_QUEUE_NAME_PREFIX = 'by_mns_queue_name';
    const MNS_TOPIC_NAME_PREFIX = 'by_mns_topic_name';

    private $accessId;
    private $accessKey;
    private $endPoint;

    private $singleClient; // 单利

    private $queue_name_key;

    protected $logger;

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->logger = $this->getDI()->get('logger');


        $this->genMnsSetting();
    }

    /**
     * 获取队列配置
     *
     */
    protected function genMnsSetting()
    {
        try {
            // 1. 取redis
            $redis = $this->getDI()->get('redisLib');
            $cache_key = md5(self::MNS_SETTING_REDIS_KEY.'01');
            $cache_setting = $redis->get($cache_key);
            $setting_data = !empty($cache_setting) ? json_decode($cache_setting, true) : [];

            // 2. 取mysql
            if (empty($setting_data)) {
                $setting_env = new SettingEnvServer();
                $setting_data['mns_mnsAccessId'] = $setting_env->getSetVal('mns_mnsAccessId');
                $setting_data['mns_mnsAccessKey'] = $setting_env->getSetVal('mns_mnsAccessKey');
                $setting_data['mns_mnsEndPoint'] = $setting_env->getSetVal('mns_mnsEndPoint');

                // 写redis
                $redis->set($cache_key, json_encode($setting_data), 10*60);
            }

            $this->accessId =  $setting_data['mns_mnsAccessId'];
            $this->accessKey =  $setting_data['mns_mnsAccessKey'];
            $this->endPoint =  $setting_data['mns_mnsEndPoint'];
        } catch (Exception $e) {
            $this->logger->write_log('backyard mns exception ' . $e->getMessage(), 'error');
        }
    }

    /**
     * mq: 向指定队列 - 写入消息数据
     *
     * @param string $queue_name 队列名称
     * @param array $data 消息数据结构  示例： ['msg_id'=>'160263912139585346468210']
     *
     * @return mixed
     */
    public function sendToMNS(string $queue_name, array $data,$delaySeconds = null)
    {
        try {
            if(empty($queue_name) || empty($data)){
                return false;
            }

            $params = [];
            $params['locale'] = 'en';
            $params['data'] = json_encode($data);

            $this->logger->write_log('backyard mns start: queue_name: '.$queue_name.', params: ' . json_encode($params), 'info');

            // 1. client
            $client = $this->getClient();
            $queue = $client->getQueueRef($queue_name, false);

            // 2. send message
            $messageBody = json_encode($params);
            $messageBody = base64_encode($messageBody);

            $request = new SendMessageRequest($messageBody,$delaySeconds);
            $res = $queue->sendMessage($request);
            $message_id = $res->getMessageId();

            $this->logger->write_log('backyard mns end: queue_name: '.$queue_name.',  getMessageId ' . $message_id, 'info');

            return $message_id;
        } catch (MnsException $e) {
            $this->logger->write_log('backyard mns exception: queue_name: '.$queue_name . $e->getMessage(), 'error');

            return false;
        }
    }

    public function getClient()
    {
        if (!$this->singleClient) {
            $this->singleClient = new Client($this->endPoint, $this->accessId, $this->accessKey);
        }

        return $this->singleClient;
    }

    public function reGetClient()
    {
        $this->singleClient = null;
        return $this->getClient();
    }

    public function sendToTopic($topicKey, array $datas)
    {
        $debug = debug_backtrace(0, 2);
        try {
            $topicName = $this->getTopicName($topicKey);
            $this->getClient();

            $topic = $this->singleClient->getTopicRef($topicName);
            $messageBody = base64_encode(json_encode($datas, JSON_UNESCAPED_UNICODE));
            $request = new PublishMessageRequest($messageBody);

            $this->getDI()->get("logger")->write_log("send_to_topic_msg func:" . $debug[1]['class'] . "::" . $debug[1]['function'] . " topic " . $topicKey . " data " . json_encode($datas, JSON_UNESCAPED_UNICODE), "info");
            $response = $topic->publishMessage($request);
            if ($response->isSucceed()) {
                return true;
            } else {
                $this->getDI()->get("logger")->write_log("send_to_topic_msg func:" . $debug[1]['class'] . "::" . $debug[1]['function'] . " topic " . $topicKey . " data " . json_encode($datas, JSON_UNESCAPED_UNICODE), "error");
            }
        } catch (MnsException $e) {
            $this->getDI()->get("logger")->write_log("send_to_topic_error: msg"
                . $e->getMessage()
                . " topicKey " . $topicKey
                . " data " . json_encode($datas, JSON_UNESCAPED_UNICODE)
                . " file " . $e->getFile()
                . " code " . $e->getCode()
                . " line "
                . $e->getLine() , "error");
        }
        return false;
    }

    /**
     *
     * 获取topic名称
     *
     * @param string $topicNameKey
     * @param string $topicName
     */
    public function getTopicName(string $topicNameKey)
    {
        return $this->getKeyName(self::MNS_TOPIC_NAME_PREFIX, $topicNameKey);
    }

    /**
     * 获取队列名称
     *
     * @param string $queue_name_key
     *
     * @return string $queue_name
     */
    public function getQueueName(string $queue_name_key)
    {
        return $this->getKeyName(self::MNS_QUEUE_NAME_PREFIX, $queue_name_key);
    }

    private function getKeyName($prefix, $key)
    {
        $keyName = '';
        try {
            // 1. 取redis
            $redis = $this->getDI()->get('redisLib');
            $cache_key = md5($prefix.$key.'01');
            $keyName = $redis->get($cache_key);

            // 2. 取mysql
            if (empty($keyName)) {
                $setting_env = new SettingEnvServer();
                $keyName = $setting_env->getSetVal($key);

                // 写redis
                $res = $redis->set($cache_key, $keyName, 7*86400);
            }

        } catch (Exception $e) {
            $this->logger->write_log('backyard mns exception ' . $e->getMessage(), 'error');
        }
        return $keyName;

    }
}
