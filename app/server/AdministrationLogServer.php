<?php

/**
 * 行政工单日志业务类
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Repository\AdministrationLogRepository;
use FlashExpress\bi\App\Repository\AdministrationOrderRepository;
use Phalcon\Db;

class AdministrationLogServer extends BaseServer
{

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    /**
     * 获取沟通记录
     * @param array $param
     * @param array $userInfo
     * @param array $loginUserIdentity
     * @return array
     */
    public function getLogList(array $param, array $userInfo, array $loginUserIdentity): array
    {
        // [1] 验证传入的id 是否 合法
        $res = (new AdministrationOrderRepository())->getInfoByParams(['id' => (int)$param['id']], ['id']);
        if (empty($res)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }

        // [2] 组织查询参数进行数据查询
        $pageNum = 1;
        if (!empty($param['page_num'])) {
            $pageNum = intval($param['page_num']);
        }

        $pageSize = 10;
        if (!empty($param['page_size'])) {
            $pageSize = intval($param['page_size']);
        }
        $start = ($pageNum - 1) * $pageSize;

        $params = [
            "is_deleted" => AdministrationLogRepository::NOT_DELETED,
            "order_id"   => (int)$param['id'],
        ];
        $colum  = [
            "id",                 // 主键id
            "order_id",           // 工单id
            "created_staff_id",   // 创建人工号
            "created_staff_name", // 创建人姓名
            "created_type",       // 1提交，2回复
            "mark",               // 消息内容
            "pics",               // 图片
            "created_at",         // 创建时间
        ];
        $order  = [
            'field' => 'id',
            "sort"  => "ASC",
        ];
        $result = (new AdministrationLogRepository())->list($params, $colum, $order, $pageSize, $start);

        // 返回数据格式
        $data = [
            "dataList"   => [],
            "pagination" => [
                "pagination" => $pageNum,
                "pageSize"   => $pageSize,
                "count"      => 0,
            ],
        ];

        // 为空时 则 直接返回
        if (empty($result['list'])) {
            return $data;
        }

        // [3]组装返回格式
        foreach ($result['list'] as $key => &$val) {
            $val['pics']     = json_decode($val['pics'], true);
            $val['is_right'] = 0;       // 消息左侧展示
            if ($userInfo['id'] == $val['created_staff_id']) {
                $val['is_right'] = 1;   // 消息右侧展示
            }
        }

        $data['dataList']                 = $result['list'];
        $data['pagination']['pagination'] = $pageNum;
        $data['pagination']['pageSize']   = $pageSize;
        $data['pagination']['count']      = $result['total'];
        return $data;
    }

    /**
     * 消息回复
     * @param array $paramIn
     * @param array $userInfo
     * @param array $loginUserIdentity
     * @return array
     */
    public function reply(array $paramIn, array $userInfo, array $loginUserIdentity): array
    {
        // [1] 查询工单是否存在
        $res = (new AdministrationOrderRepository())->getInfoByParams(
            ['id' => (int)$paramIn['id']],
            [
                'id',
                'status',
                'created_staff_id',
                'first_deal_staff_id',
                'first_deal_staff_name',
                'first_deal_staff_time',
            ]
        );
        if (empty($res)) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }

        // 提交员工
        if ($loginUserIdentity['isHrJob'] === false && $userInfo['id'] != $res[0]['created_staff_id']) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }

        // 如果是关闭则不允许回复
        if ($res[0]['status'] == enums::TICKET_STATUS_CLOSED) {
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "id param error"]);
        }

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $curTime  = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
        $this->getDI()->get('db')->begin();
        $data["order_id"]           = (int)$paramIn["id"];                                   // 工单id
        $data['created_staff_id']   = $userInfo['id'];                                       // 消息回复人id
        $data['created_staff_name'] = $userInfo['name'];                                     // 消息回复人姓名
        $data['mark']               = trim($paramIn['mark']);                                // 消息内容
        $data['pics']               = "";
        if (!empty($paramIn['pics'])) {
            $data['pics'] = json_encode($paramIn['pics'], JSON_UNESCAPED_UNICODE); // 图片
        }
        $data['created_at']   = $curTime;                                                    // 时间
        $data['created_type'] = AdministrationLogRepository::CREATED_TYPE_SUBMIT;            // 员工提交
        // from 1 表示提交 2 表示回复
        if ($paramIn['from'] == 2 && $loginUserIdentity['isHrJob'] === true) {
            $data['created_type'] = AdministrationLogRepository::CREATED_TYPE_REPLY; // HR回复
        }

        // [2] 记录回复日志
        $lastInsertId = (new AdministrationLogRepository())->add($data);
        if (!$lastInsertId) {
            // 插入失败时 回滚
            $this->getDI()->get('db')->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "insert log error"]);
        }

        // [3] 更新 行政工单表的状态、时间 和首次回复人等。
        $updateData["updated_at"] = $curTime;                                                // 更新时间
        $updateData["status"]     = enums::TICKET_STATUS_WAIT_REPLY;                         // 默认是待回复
        if ($paramIn['from'] == 2 && $loginUserIdentity['isHrJob'] === true) {
            $updateData["status"] = enums::TICKET_STATUS_REPLY;     // 已经回复
            if (0 === (int)$res[0]['first_deal_staff_id'] && empty($res[0]['first_deal_staff_id'])) {
                // 如果是 hr登入 需要维护 首次回复人的信息和时间
                $updateData['first_deal_staff_id']   = (int)$userInfo['id'];
                $updateData['first_deal_staff_name'] = trim($userInfo['name']);
                $updateData['first_deal_staff_time'] = $curTime;
            }
            // 最后回复人信息
            $updateData['last_deal_staff_id']   = (int)$userInfo['id'];
            $updateData['last_deal_staff_name'] = trim($userInfo['name']);
            $updateData['last_deal_staff_time'] = $curTime;
        }
        $whereData                          = [
            'conditions' => 'id = ?',
            'bind'       => [(int)$paramIn['id']],
        ];
        $bool                               = (new AdministrationOrderRepository())->update($updateData, $whereData);
        if (!$bool) {
            // 更新失败 回滚
            $this->getDI()->get('db')->rollback();
            return $this->checkReturn(["code" => ErrCode::FAIL, "msg" => "update order error"]);
        }
        $this->getDI()->get('db')->commit();
        return $this->checkReturn(["code" => ErrCode::SUCCESS, "msg" => "success"]);
    }
}