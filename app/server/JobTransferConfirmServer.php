<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\SysListRepository;

class JobTransferConfirmServer extends BaseServer
{
    public static $validateDetail = [
        'id' => 'Required|Int',
    ];

    public static $validateFace = [
        'face_image_url' => 'Required|StrLenGe:1',
    ];

    public static $validateConfirm = [
        'id'    => 'Required|Int',
        'state' => 'Required|Int',
    ];

    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang, $timezone);
    }

    /**
     * @description 获取确认列表
     * @param array $paramIn
     * @return array
     */
    public function getConfirmList(array $paramIn = []): array
    {
        $staffInfoId = $this->processingDefault($paramIn, 'staff_info_id');
        $pageSize    = $this->processingDefault($paramIn, 'page_size');
        $pageNum     = $this->processingDefault($paramIn, 'page_num');

        //获取员工信息
        $staffInfo   = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        $staffInfoId = $this->getPendingConfirmStaffInfoId($staffInfo);

        $columns = [
            'id',
            'current_department_id',
            'current_store_id',
            'current_position_id',
            'after_date',
            'confirm_state',
        ];
        $builder  = $this->getJobTransferBuilder(['staff_info_id' => $staffInfoId]);
        $itemList = $builder->columns($columns)
            ->limit($pageSize, ($pageNum - 1) * $pageSize)
            ->orderBy('id desc')
            ->getQuery()->execute()->toArray();

        //查询职位名称 ids
        $sysObj         = new SysListRepository();
        $positionIds    = array_values(array_unique(array_column($itemList, 'current_position_id')));
        $positionData   = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData   = array_column($positionData, 'name', 'id');

        //查询网点名称 ids
        $storeIds  = array_values(array_unique(array_column($itemList, 'current_store_id')));
        $storeData = $sysObj->getStoreList(['ids' => $storeIds]);
        $storeData = array_column($storeData, 'name', 'id');

        //查询部门名称 ids
        $departmentIds  = array_values(array_unique(array_column($itemList, 'current_department_id')));
        $departmentData = $sysObj->getDepartmentList(['ids' => $departmentIds]);
        $departmentData = array_column($departmentData, 'name', 'id');

        //获取确认状态
        $confirmState = JobTransferConfirmEnums::getCodeTxtMap();

        foreach ($itemList as &$item) {
            $item['current_department_label'] = $departmentData[$item['current_department_id']] ?? '';
            $item['current_store_label']      = $storeData[$item['current_store_id']] ?? '';
            $item['current_position_label']   = $positionData[$item['current_position_id']] ?? '';
            $item['confirm_state_label']      = $this->getTranslation()->_($confirmState[$item['confirm_state']]);
        }

        $totalCount        = $builder->columns('count(1) as cou')->getQuery()->getSingleResult();
        $totalCount        = !empty($totalCount) ? $totalCount->cou: 0;
        $result['list']    = $itemList;
        $result['is_over'] = $pageNum * (($pageNum - 1) * $pageSize) >= $totalCount ? 1 : 0;

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * @description 获取转岗builder
     * @param $params
     * @return mixed
     */
    public function getJobTransferBuilder($params)
    {
        $staffInfoId = $params['staff_info_id'];

        //一线/非一下一阶段审批通过OR特殊转岗审批通过
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->where('staff_id = :staff_info_id:', ['staff_info_id' => $staffInfoId]);
        $builder->inWhere('confirm_state', JobTransferConfirmEnums::getAllValidConfirmState());
        $builder->andWhere('approval_state_stage_one = :approval_state_one: and type in({normal_type:array}) or approval_state = :special_approval_state: and type = :special_type:',
            [
                'approval_state_one'     => enums::APPROVAL_STATUS_APPROVAL,
                'normal_type'            => [
                    JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE,
                    JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE,
                ],
                'special_approval_state' => enums::APPROVAL_STATUS_APPROVAL,
                'special_type'           => JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL,
            ]);
        return $builder;
    }

    /**
     * @description 获取转岗详情
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getConfirmDetail($params)
    {
        $id          = $params['id'];
        $staffInfoId = $params['staff_info_id'];

        //获取转岗人信息
        $staffService = new StaffServer();
        $staffInfo = $staffService->getStaffInfoSpecColumns($staffInfoId,
            "h.staff_info_id, 
                h.name as staff_name, 
                sd.name as sys_department_name, 
                date_format(h.hire_date,'%Y-%m-%d') hire_date");
        //获取转岗信息
        $jobTransferRepo = new JobtransferRepository($this->timeZone);
        $detail = $jobTransferRepo->getJobTransferDetailById($id);

        //查询职位名称 ids
        $sysObj         = new SysListRepository();
        $positionIds    = [$detail['current_position_id'], $detail['after_position_id']];
        $positionData   = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData   = array_column($positionData, 'name', 'id');

        //查询网点名称 ids
        $storeIds  = [$detail['current_store_id'], $detail['after_store_id']];
        $storeData = $sysObj->getStoreList(['ids' => $storeIds]);
        $storeData = array_column($storeData, 'name', 'id');

        //查询部门名称 ids
        $departmentIds  = [$detail['current_department_id'], $detail['after_department_id']];
        $departmentData = $sysObj->getDepartmentList(['ids' => $departmentIds]);
        $departmentData = array_column($departmentData, 'name', 'id');

        //获取确认状态
        $confirmState = JobTransferConfirmEnums::getCodeTxtMap();

        //获取页眉页脚
        $queryParams = [
            'staff_info_id' => $staffInfoId,
            'department_id' => $detail['after_department_id'],
        ];
        $templateCnf = (new PdfHelperServer())->getHeaderAndFooter($queryParams);

        //获取转岗前后工资
        $salaryDetail = $this->getTransferSalaryById(['job_transfer_id' => $id]);
        if (isCountry()) {

            if (in_array($detail['after_hire_type'], HrStaffInfoModel::$agentTypeTogether)) { //个人代理无薪资
                $base_salary_before = $base_salary_after = null;
            } else {
                $beforeUnit         = $detail['current_hire_type'] != HrStaffInfoModel::HIRE_TYPE_3 ? $this->getTranslation()->_('transfer_unit_month') : $this->getTranslation()->_('transfer_unit_daily');
                $afterUnit          = $detail['after_hire_type'] != HrStaffInfoModel::HIRE_TYPE_3 ? $this->getTranslation()->_('transfer_unit_month') : $this->getTranslation()->_('transfer_unit_daily');
                $base_salary_before = isset($salaryDetail['base_salary_before']) ? $salaryDetail['base_salary_before'] . ' ' . $beforeUnit : null;
                $base_salary_after  = isset($salaryDetail['base_salary_after']) ? $salaryDetail['base_salary_after'] . ' ' . $afterUnit : null;
            }
        } else {
            $base_salary_before = $salaryDetail['base_salary_before'] ?? null;
            $base_salary_after  = $salaryDetail['base_salary_after'] ?? null;
        }
        $result = [
            'staff_info_id'                => $staffInfoId,
            'staff_name'                   => $staffInfo['staff_name'],
            'belong_store_name'            => $storeData[$detail['current_store_id']] ?? '',
            'first_level_department_name'  => $staffInfo['sys_department_name'],
            'before_job_title_name'        => $positionData[$detail['current_position_id']] ?? '',
            'before_store_name'            => $storeData[$detail['current_store_id']] ?? '',
            'before_department_name'       => $departmentData[$detail['current_department_id']] ?? '',
            'before_job_title_grade'       => sprintf('F%d', $detail['current_job_title_grade']),
            'before_base_salary'           => $base_salary_before,
            'before_working_day_rest_type' => $this->getTranslation()->_('working_day_rest_type_' . $detail['before_working_day_rest_type']) ?? '',
            'before_manager_id'            => $detail['current_manager_id'],
            'after_job_title_name'         => $positionData[$detail['after_position_id']] ?? '',
            'after_store_name'             => $storeData[$detail['after_store_id']] ?? '',
            'after_department_name'        => $departmentData[$detail['after_department_id']] ?? '',
            'after_job_title_grade'        => sprintf('F%d', $detail['after_job_title_grade']),
            'after_base_salary'            => $base_salary_after,
            'after_working_day_rest_type'  => $this->getTranslation()->_('working_day_rest_type_' . $detail['after_working_day_rest_type']) ?? '',
            'after_manager_id'             => $detail['after_manager_id'],
            'after_date'                   => $detail['after_date'],
            'before_hire_type'             => $detail['current_hire_type'],
            'after_hire_type'              => $detail['after_hire_type'],
            'hire_date'                    => $staffInfo['hire_date'],
            'confirm_state'                => $detail['confirm_state'],
            'confirm_state_label'          => $this->getTranslation()->_($confirmState[$detail['confirm_state']]),
            'type'                         => $detail['type'],
            'state'                        => $detail['state'],
        ];
        $result = array_merge($result, $templateCnf);

        return $this->checkReturn(['data' => $result]);
    }

    /**
     * @description 获取转岗基础薪资
     * @param array $params
     */
    public function getTransferSalaryById($params = [])
    {
        $ac = new ApiClient('hcm_rpc', '', 'get_job_transfer_base_salary_after');
        $ac->setParams($params);
        $ac_result = $ac->execute();

        if (!isset($ac_result['result']['code']) || $ac_result['result']['code'] != ErrCode::SUCCESS) {
            $this->logger->write_log(sprintf('get getTransferSalary err, params: %s', json_encode($params)));
        }
        if (!isset($ac_result['result']['data']) || empty($ac_result['result']['data'])) {
            return [];
        }
        return $ac_result['result']['data'] ?? [];
    }

    /**
     * @description 人脸认证
     * @param array $params
     * @param array $userinfo
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @throws \Exception
     */
    public function authentication(array $params = [], array $userinfo = []): array
    {
        $staffInfoId  = $this->processingDefault($userinfo, 'staff_id');
        $faceImageUrl = $this->processingDefault($params, 'face_image_url');

        //AI人脸校验开关
        $settingEnv   = (new SettingEnvServer())->getSetVal('is_open_auth');
        if (RUNTIME == 'dev' && isset($settingEnv) && $settingEnv == CommonEnums::SWITCH_CLOSED) {
            return $this->checkReturn([]);
        }

        //图片质量检测
        $settingModel = new BySettingRepository($this->lang);
        $attServer    = new AttendanceServer($this->lang, $this->timeZone);

        //图片质量
        $attServer->analyzeFace($faceImageUrl, $staffInfoId);

        //完善图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $faceImageUrl = $attServer->format_oss([
            'bucket' => $this->config->application->oss_bucket,
            'path'   => $faceImageUrl,
        ], $flag ?? 0);

        //活体检测
        //人脸比对之前需要做活体检测
        //如果失败，则不继续人脸对比
        $liveCheckParam = "url={$faceImageUrl}";
        $faceCompareInfo = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_CHECK)
            ->sendRequestWithHttpCode($liveCheckParam, $staffInfoId);

        $faceCompareScore = $faceCompareInfo['response']['result']['score'] ?? 0;

        //人脸比对基础分数
        $aliveBaseScore = $settingModel->get_setting('alive_score');
        $this->logger->write_log(sprintf('[job transfer confirm authentication] live check staff id: {%s}, score: {%s}, params in %s',
            $staffInfoId, $faceCompareScore, $faceImageUrl), 'info');

        if ($faceCompareScore >= 0 && $aliveBaseScore > $faceCompareScore) { //非活体
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('job_transfer.err_msg.12'),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        }

        //获取底片
        $photographicPlate = $attServer->get_face_img($staffInfoId, $flag ?? 0);
        if (empty($photographicPlate)) { //不存在底片
            $staffInfo = (new StaffServer())->getStaffById($staffInfoId);
            //如当前账号是子账号
            if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
                $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staffInfoId");
                if (!empty($supportInfo)) {
                    $staffInfoId = $supportInfo['staff_info_id'];
                    $photographicPlate = $attServer->get_face_img($staffInfoId, $flag ?? 0);
                }
            }
            if (empty($photographicPlate)) {
                http_response_code(422);
                throw new BusinessException($this->getTranslation()->_('job_transfer.err_msg.12'),ErrCode::AI_IMAGE_VERIFY_ERROR);
            }
        }

        //人脸比对
        $old_url = urlencode(str_replace("\\/", '/', $photographicPlate));
        $new_url = urlencode(str_replace("\\/", '/', $faceImageUrl));
        $param   = "urlA={$old_url}&urlB={$new_url}";
        $identifyResult = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_COMPARE_FACE)
            ->sendRequestWithHttpCode($param, $staffInfoId);

        if ($identifyResult['http_code'] != 200 || !$identifyResult['response']['result']) {//不匹配

            $this->logger->write_log(sprintf('staff id %s compare face not consistent; face image url : %s,Photographic Plate url: %s',
                $staffInfoId, $faceImageUrl, $photographicPlate), 'info');

            //比对不匹配只记录，不报错
            http_response_code(422);
            throw new BusinessException($this->getTranslation()->_('job_transfer.err_msg.12'),ErrCode::AI_IMAGE_VERIFY_ERROR);
        } else {

            $this->logger->write_log(sprintf('staff id %s compare face consistent!!!; face image url : %s,Photographic Plate url: %s',
                $staffInfoId, $faceImageUrl, $photographicPlate), 'info');
        }

        return $this->checkReturn([]);
    }

    /**
     * @description 转岗确认
     * @param $params
     * @return array
     * @throws ValidationException
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function doConfirm($params)
    {
        $id          = $this->processingDefault($params, 'id');
        $state       = $this->processingDefault($params, 'state');
        $staffInfoId = $this->processingDefault($params, 'staff_info_id');
        $signUrl     = $this->processingDefault($params, 'sign_url');

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $jobTransferInfo = JobTransferModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $id
                ],
                'for_update' => true
            ]);
            if (empty($jobTransferInfo)) {
                throw new ValidationException('data not exist');
            }

            //只有待确认状态才可以确认
            if ($jobTransferInfo->confirm_state != JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                throw new ValidationException('please try again');
            }
            $jobTransferInfo->confirm_state = $state;
            $jobTransferInfo->sign_url      = $signUrl;
            $jobTransferInfo->confirm_date  = date('Y-m-d');

            if ($jobTransferInfo->type == JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL) {
                $jobTransferInfo->save();
            } else {
                //转岗拒绝
                //审批状态变更为已驳回
                //转岗状态变更为未转岗
                $requestObj = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $id);
                if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT) {
                    $jobTransferInfo->approval_state = enums::APPROVAL_STATUS_REJECTED;
                    $jobTransferInfo->state = JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED;

                    //确认拒绝
                    $requestObj->setState(Enums::APPROVAL_STATUS_REJECTED);
                    $requestObj->setFinalApprovalTime(date('Y-m-d H:i:s'));
                    $requestObj->setFinalApprover($staffInfoId);
                    $requestObj->save();

                    //返还HC
                    if (!empty($jobTransferInfo->hc_id)) {
                        (new HcServer($this->lang, $this->timeZone))->remittanceHc($jobTransferInfo->hc_id);
                    }
                }
                $jobTransferInfo->save();

                //更新日志
                //Notice: 一阶段审批完成插入一个待确认的log，在员工确认同意/拒绝后，要同步更改这条日志的状态
                $confirmState = $state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS ? enums::WF_ACTION_CONFIRM_PASS: enums::WF_ACTION_CONFIRM_REJECT;
                $workflowService = new WorkflowServer($this->lang, $this->timeZone);
                $workflowService->updateAuditLog($requestObj, $staffInfoId, $confirmState, null);

                //创建二阶段审批流
                if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
                    //获取下一个阶段的审批流ID
                    $workflowInfo = WorkflowModel::findFirst(JobTransferEnums::TRANSFER_OUT_WORKFLOW_ID);

                    //创建二阶段审批
                    $extend['from_submit'] = [
                        'department_id' => [$jobTransferInfo->current_department_id, $jobTransferInfo->after_department_id],
                        'sys_store_id'  => [$jobTransferInfo->current_store_id, $jobTransferInfo->after_store_id],
                    ];
                    $creatParams = [
                        'audit_type'   => AuditListEnums::APPROVAL_TYPE_JT,
                        'submitter_id' => $jobTransferInfo->submitter_id,
                        'workflow_id'   => $workflowInfo->next_stage_flow_id,
                        'audit_id'     => $jobTransferInfo->id,
                        'extend'       => $extend,
                    ];
                    (new ApprovalServer($this->lang, $this->timeZone))->createMultiStage($creatParams);

                    $this->logger->write_log(sprintf('[doConfirm] create workflow ,audit id is %d, params is %s', $id, json_encode($creatParams)), 'info');
                } else {
                    $this->sendRejectNotice($jobTransferInfo);
                }
            }

            $db->commit();
        } catch (ValidationException $ve) {
            if ($db->isUnderTransaction()) {
                $db->rollback();
            }
            $this->logger->write_log('doConfirm err' . $ve->getMessage() . $ve->getTraceAsString(), 'info');
        }

        return $this->checkReturn(['data' => true]);
    }

    /**
     * 获取待确认工号
     * 如工号是子账号，获取主账号
     *
     * @param $staff_info
     * @return mixed
     */
    public function getPendingConfirmStaffInfoId($staff_info)
    {
        $staffInfoId = $staff_info['staff_info_id'];
        if ($staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            //获取主账号信息
            $supportInfo = HrStaffApplySupportStoreModel::findFirst('sub_staff_info_id = ' . $staff_info['staff_info_id']);
            if (!empty($supportInfo)) {
                $staffInfoId = $supportInfo->staff_info_id;
            }
        }
        return $staffInfoId;
    }

    /**
     * @description 获取转岗确认信息
     * @param $staff_info
     * @return array
     */
    public function getConfirmInfo($staff_info): array
    {
        if (empty($staff_info)) {
            return [
                'is_show' => 0,
                'num'     => 0,
            ];
        }
        //如果登录账号是子账号
        $staffInfoId = $this->getPendingConfirmStaffInfoId($staff_info);

        //是否显示入口
        $showInfo = JobTransferModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and confirm_state > 0',
            'bind' => [
                'staff_id' => $staffInfoId
            ],
        ]);
        //红点数量
        //指定工号 & 待确认 & （待转岗 | 转岗成功 & 特殊批量转岗）
        $transferInfo = JobTransferModel::count([
            'conditions' => 'staff_id = :staff_id: and confirm_state = :confirm_state: and (state = 1 and type in(1,2) or state = 3 and type = 3)',
            'bind'       => [
                'staff_id'      => $staffInfoId,
                'confirm_state' => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
            ],
        ]);
        return [
            'is_show' => !empty($showInfo) ? 1 : 0,
            'num'     => $transferInfo,
        ];
    }

    /**
     * @description 获取待确认转岗信息
     * @param $staff_info_id
     * @return mixed
     */
    public function getPendingConfirmInfo($staff_info_id)
    {
        $checkStaffIds = [$staff_info_id];
        $staffInfo     = (new StaffServer())->getStaffById($staff_info_id);

        //如当前账号是子账号
        if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            //获取主账号信息
            $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");
            if (!empty($supportInfo)) {
                $checkStaffIds[] = $supportInfo['staff_info_id'];
            }
        }
        return JobTransferModel::findFirst([
            'conditions' => 'staff_id in({staff_id:array}) and confirm_state = :confirm_state:',
            'bind'       => [
                'staff_id'      => $checkStaffIds,
                'confirm_state' => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
            ],
        ]);
    }

    /**
     * @description 确认拒绝提醒
     *  消息标题：工号（姓名）拒绝转岗提醒
     *  消息内容：工号（姓名），所属部门，职位，所属网点，拒绝了期望转岗日期为YYYY-MM-DD的转岗申请，请尽快协调沟通。
     *
     * @param $jobTransferInfo
     */
    public function sendRejectNotice($jobTransferInfo)
    {
        if (empty($jobTransferInfo)) {
            return;
        }
        //申请人
        $noticeStaffList[] = $jobTransferInfo->submitter_id;

        //转出HrBP
        if (!in_array($jobTransferInfo->after_hire_type, HrStaffInfoModel::$agentTypeTogether)) {
            $hrBP = (new WorkflowServer($this->lang, $this->timeZone))->findHRBP($jobTransferInfo->current_department_id,
                ["store_id" => $jobTransferInfo->current_store_id]);
            $this->logger->write_log("转岗前hrBP:" . json_encode($hrBP), "info");
            if ($hrBP) {
                $noticeStaffList = array_merge($noticeStaffList, explode(',', $hrBP));
            }
        }

        //消息发送黑名单
        $server = $this->class_factory("JobTransferV2Server", $this->lang, $this->timeZone);
        $blackList = $server->getBlackList();

        //排掉黑名单里的HrBp
        $noticeStaffList = array_values(array_diff($noticeStaffList, $blackList));
        if (empty($noticeStaffList)) {
            return;
        }
        $staffInfo = $server->getJobTransferInfo(['id' => $jobTransferInfo->id]);

        //获取消息体内容并发送
        if (in_array($jobTransferInfo->after_hire_type, HrStaffInfoModel::$agentTypeTogether)) {
            $jumpUrl = (new SettingEnvServer())->getSetVal('agent_job_transfer_activate_url');

            $messageContent = $this->getTranslation()->_('job_transfer.agent_reject_notice_content', [
                'staff_id'                => $staffInfo['staff_id'],
                'staff_name'              => $staffInfo['staff_name'],
                'current_department_name' => $staffInfo['current_department_name'],
                'current_position_name'   => $staffInfo['current_position_name'],
                'current_store_name'      => $staffInfo['current_store_name'],
                'after_date'              => $staffInfo['after_date'],
                'jump_url'                => urlencode($jumpUrl),
            ]);

        } else {
            $messageContent = $this->getTranslation()->_('job_transfer.reject_notice_content', [
                'staff_id'                => $staffInfo['staff_id'],
                'staff_name'              => $staffInfo['staff_name'],
                'current_department_name' => $staffInfo['current_department_name'],
                'current_position_name'   => $staffInfo['current_position_name'],
                'current_store_name'      => $staffInfo['current_store_name'],
                'after_date'              => $staffInfo['after_date'],
            ]);
        }
        $html                        = PushServer::getInstance($this->lang, $this->timeZone)->formatPushMsgContent($messageContent);
        $id                          = time() . $jobTransferInfo->submitter_id . rand(1000000, 9999999);
        $param['staff_users']        = PushServer::getInstance($this->lang, $this->timeZone)->formatPushMsgStaff($noticeStaffList);
        $param['message_title']      = $this->getTranslation()->_('job_transfer.reject_notice_title',
            ['staff_info' => sprintf("%d (%s)", $staffInfo['staff_id'], $staffInfo['staff_name'])]);
        $param['message_content']    = $html;
        $param['id']                 = $id;
        $param['category']           = -1;
        $this->logger->write_log('sendRejectNotice:' . json_encode($param), 'info');

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        $this->logger->write_log('sendRejectNotice-result:' . json_encode($res), 'info');
    }

    /**
     * 薪资单位
     * @param $unit
     * @return string
     */
    protected function formatSalaryUnit($unit): string
    {
        $unitText = '';
        switch ($unit) {
            case 1: //每日薪资
                $unitText = 'per working day';
                break;
            case 2: //每月薪资
                $unitText = 'per month';
                break;
            default:
                break;
        }
        return $unitText;
    }

    /**
     * 获取主账号
     * @param $staff_info_id
     * @return int
     */
    public function changeStaffIdToMaster($staff_info_id): int
    {
        $staffInfo         = (new StaffServer())->getStaffById($staff_info_id);
        $masterStaffInfoId = 0;
        //如当前账号是子账号
        if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");
            if (!empty($supportInfo)) {
                $masterStaffInfoId = $supportInfo['staff_info_id'];
            }
        }
        return !empty($masterStaffInfoId) ? $masterStaffInfoId: $staff_info_id;
    }

    /**
     * 是否进入二阶段
     * 转岗分为如下阶段
     * 一阶段审批 ==> 转岗确认阶段 ==> 二阶段审批
     *
     * @param $request
     * @return bool
     */
    public function isSecondStageAudit($request): bool
    {
        $workflow = WorkflowModel::getWorkflowInfo($request->getWorkflowId());
        if ($workflow->next_stage_flow_id != WorkflowModel::WORKFLOW_NEXT_STAGE_FLOW_ID) { //转岗确认阶段
            return false;
        }

        // 二阶段审批
        return true;
    }
}