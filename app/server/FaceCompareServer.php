<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AiStateEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class FaceCompareServer extends BaseServer
{

    public function __construct($lang = '', $timezone = '+08:00')
    {
        parent::__construct($lang, $timezone);
    }

    protected $is_check_image_quality = true;
    protected $is_check_live          = true;


    protected $validate_face_with_mask = true;

    protected $alive_base_score = 0.05;

    //病假材料
    const BIZ_TYPE_SICK_LEAVE = 1;
    //修改手机号码
    const BIZ_TYPE_EDIT_PERSON_MOBILE = 2;
    //修改个人邮箱
    const BIZ_TYPE_EDIT_PERSON_EMAIL = 3;
    //修改银行卡信息
    const BIZ_TYPE_EDIT_BANK_INFO = 4;

    const BIZ_TYPE_TICKET_MAP = [
        self::BIZ_TYPE_SICK_LEAVE         => 'SL-%s-',
        self::BIZ_TYPE_EDIT_PERSON_MOBILE => 'PM-%s-',
        self::BIZ_TYPE_EDIT_PERSON_EMAIL  => 'PE-%s-',
        self::BIZ_TYPE_EDIT_BANK_INFO     => 'BI-%s-',
    ];


    public function setCheckImageQuality($is_check_image_quality)
    {
        $this->is_check_image_quality = $is_check_image_quality;
    }


    public function setCheckLive($is_check_live)
    {
        $this->is_check_live = $is_check_live;
    }


    /**
     * 图片质量
     * @param $staffId
     * @param $compare_img
     * @return array|mixed
     * @throws ValidationException
     */
    public function imageQuality($staffId, $compare_img)
    {
        $params = "url={$compare_img}&face_attributes=quality,mask&max_face_num=2";
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_FACE_QUALITY)->send($params, $staffId);

        //无返回结果 || 返回参数有问题
        if (empty($result) || !isset($result['result']['face_list'][0]['code']) || isset($result['result']['error'])) {
            $this->logger->write_log("quality check error:" . json_encode($result), "info");
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('please_retry'), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }

        $detectCode = $result['result']['face_list'][0]['code'];

        if ($result['result']['face_num'] != 1) {
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('err_msg_multiple_faces'),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        //高质量图片定义：
        //同时满足face_num=1和code=0才算高质量
        if ($detectCode != AiStateEnums::ANALYZE_FACE_HIGH_QUALITY) {
            switch ($detectCode) {
                case AiStateEnums::ANALYZE_FACE_WITH_MASK:
                    $message = "err_msg_face_with_mask";
                    break;
                case AiStateEnums::ANALYZE_FACE_BRIGHTNESS_NOT_ACCEPT:
                    $message = "err_msg_brightness_not_accept";
                    break;
                case AiStateEnums::ANALYZE_FACE_LOW_QUALITY:
                case AiStateEnums::ANALYZE_FACE_SHARPNESS_NOT_ACCEPT:
                    $message = "err_msg_image_low_quality";
                    break;
                case AiStateEnums::ANALYZE_FACE_INCOMPLETE:
                    $message = "err_msg_face_incomplete";
                    break;
                default:
                    $message = "4008";
                    break;
            }

            if ($detectCode == AiStateEnums::ANALYZE_FACE_WITH_MASK && $this->validate_face_with_mask) {
                http_response_code(422);
                throw new ValidationException($this->getTranslation()->_($message), ErrCode::AI_IMAGE_VERIFY_ERROR);
            }
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_($message), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }

        return $result;
    }


    /**
     * 活体验证
     * @param $staff_id
     * @param $compare_url
     * @return true
     * @throws ValidationException
     */
    public function checkLive($staff_id, $compare_url): bool
    {
        //如果失败，则不继续人脸对比
        $liveCheckParam  = "url={$compare_url}";
        $faceCompareInfo = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_CHECK)->sendRequestWithHttpCode($liveCheckParam,
            $staff_id);

        $faceCompareScore = $faceCompareInfo['response']['result']['score'] ?? 0;

        if ($faceCompareScore <= $this->alive_base_score) { //非活体
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('check_live_error_msg'),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        return true;
    }


    /**
     * @param $staff_id
     * @param $source_image
     * @param $compare_image
     * @return true
     * @throws BusinessException
     * @throws ValidationException
     */
    public function fire($staff_id, $source_image, $compare_image): bool
    {
        $this->logger->write_log([
            'staff_id'      => $staff_id,
            'source_image'  => $source_image,
            'compare_image' => $compare_image,
        ], 'info');

        if (!empty($this->is_check_image_quality)) {
            //图片质量验证分析
            $this->imageQuality($staff_id, $compare_image);
        }

        if (!empty($this->is_check_live)) {
            //活体分析
            $this->checkLive($staff_id, $compare_image);
        }

        //人脸比对
        $this->compareFace($staff_id, $source_image, $compare_image);

        return true;
    }


    /**
     * 人脸比对
     * @param $staff_id
     * @param $compare_image
     * @param $source_img
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function compareFace($staff_id, $source_img, $compare_image): bool
    {
        //人脸比对
        $old_url        = urlencode(str_replace("\\/", '/', $source_img));
        $new_url        = urlencode(str_replace("\\/", '/', $compare_image));
        $param          = "urlA={$old_url}&urlB={$new_url}";
        $identifyResult = AiServer::getInstance()->setConfig(enums::IDENTIFY_LIVE_COMPARE_FACE)
            ->sendRequestWithHttpCode($param, $staff_id);

        if ($identifyResult['http_code'] != 200) {//不匹配
            //比对不匹配只记录，不报错
            http_response_code(422);
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        if (!$identifyResult['response']['result']) {//不匹配
            //比对不匹配只记录，不报错
            http_response_code(422);
            throw new BusinessException($this->getVerifyFailMessage($staff_id),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        return true;
    }

    public function getVerifyFailMessage($staff_id)
    {
        $setting_model              = new BySettingRepository($this->lang);
        $facial_recognition_mailbox = $setting_model->get_settingFromCache('facial_recognition_mailbox');
        $staffInfo                  = (new StaffServer())->getStaffById($staff_id);
        if (!empty($staffInfo) && $staffInfo['formal'] == 0) {
            $notice = $this->getTranslation()->_('can_not_match_os');//外协
        } elseif ($facial_recognition_mailbox) {
            $notice = $this->getTranslation()->_('can_not_match_show_mailbox',
                ['mailbox' => $facial_recognition_mailbox]);
            $notice = str_replace('\\n', "\n", $notice);
        } else {
            $notice = $this->getTranslation()->_('can_not_match');
        }
        return $notice;
    }

    public function formatImgUrl($param)
    {
        $server = Tools::reBuildCountryInstance(new AttendanceServer($this->lang, $this->timeZone),
            [$this->lang, $this->timeZone]);
        return $server->faceFormatUrlFle($param['staff_info_id'], $this->request->get('fileName'));
    }

    /**
     * @param $biz_type
     * @param $staff_id
     * @return false|string
     * @throws BusinessException
     */
    protected function sourceUrlEmptyLogic($biz_type, $staff_id)
    {

        switch ($biz_type) {
            case self::BIZ_TYPE_SICK_LEAVE:
                return $this->getTicket($biz_type, $staff_id);
            case self::BIZ_TYPE_EDIT_PERSON_MOBILE:
            case self::BIZ_TYPE_EDIT_PERSON_EMAIL:
            case self::BIZ_TYPE_EDIT_BANK_INFO:
                //暂无法修改信息，请先完成一次上班打卡人脸认证
                throw new BusinessException($this->getTranslation()->_('update_staff_info_need_face_negatives'),ErrCode::AI_IMAGE_VERIFY_FACE_NEGATIVES_EMPTY);
            default:
                break;
        }
        return false;
    }


    /**
     * 构建人脸比对数据
     * @param $param
     * @return string[]
     * @throws BusinessException
     * @throws ValidationException
     */
    public function formatCompareData($param): array
    {
        $flag = false;//是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = true;
        }
        $staffId    = $param['staff_info_id'];
        $server     = new AttendanceServer($this->lang, $this->timeZone);
        $source_url = $server->get_face_img($staffId, $flag);
        if (empty($source_url)) {//如果没有底片 也放过
            if ($ticket = $this->sourceUrlEmptyLogic($param['biz_type'], $staffId)) {
                return ['ticket' => $ticket];
            }
        }
        //拼接图片 全路径
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $param['face_image'];
        $face_image       = $server->format_oss($format, $flag);
        $this->fire($staffId, $source_url, $face_image);
        $ticket =  $this->getTicket($param['biz_type'], $staffId);
        return ['ticket' => $ticket];
    }

    /**
     * 缓存凭证
     * @param $key
     * @param $staffId
     * @param int $expire
     * @return mixed
     */
    protected function setTicket($key, $staffId, int $expire = 300)
    {
        $cache = $this->getDI()->get('redisLib');
        return $cache->set($key, $staffId, $expire);
    }

    /**
     * 获取凭证
     * @param $bit_type
     * @param $staff_id
     * @return string
     * @throws BusinessException
     * @throws Exception
     */
    protected function getTicket($bit_type, $staff_id): string
    {
        switch ($bit_type) {
            case self::BIZ_TYPE_SICK_LEAVE:
                $expire = 300;
                break;
            default:
                $expire = 600;
                break;
        }
        if (empty(self::BIZ_TYPE_TICKET_MAP[$bit_type])) {
            throw new Exception('BIZ_TYPE_TICKET_MAP 未定义的 biz_type');
        }
        $ticket   = sprintf(self::BIZ_TYPE_TICKET_MAP[$bit_type], $staff_id) . uuid();
        if($this->setTicket($ticket,$staff_id, $expire)){
            return $ticket;
        }
        throw new BusinessException($this->getTranslation()->_('server_error'));
    }

    /**
     * 验证凭证
     * @param $ticket
     * @param $staff_id
     * @return bool
     * @throws ValidationException
     */
    public function checkTicket($ticket, $staff_id): bool
    {
        $cache = $this->getDI()->get('redisLib');
        $data  = $cache->get($ticket);
        $staffIds[] = $staff_id;
        $att_re = new AttendanceRepository($this->lang, $this->timeZone);
        $supportStaffInfo = $att_re->getSupportOsStaffInfo($staff_id);
        if ($supportStaffInfo) {
            $staffIds[] = $supportStaffInfo['sub_staff_info_id'];
        }
        if (empty($data) || !in_array($data,$staffIds)) {
            throw new ValidationException($this->getTranslation()->_('face_ticket_expired'));
        }
        return true;
    }



}