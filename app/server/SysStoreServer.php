<?php

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\StorePickupVirtualRelationModel;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;

class SysStoreServer extends BaseServer
{

    public const MANAGE_ORG_TYPE_DEPARTMENT = 1; //管辖部门
    public const MANAGE_ORG_TYPE_REGION = 2;     //管辖大区
    public const MANAGE_ORG_TYPE_PIECE = 3;      //管辖片区
    public const MANAGE_ORG_TYPE_STORE = 4;      //管辖网点

    const CATEGORY_DC = 2;
    const CATEGORY_SP = 1;
    const CATEGORY_BRANCH = 3;
    const CATEGORY_SHOP_PO = 4;
    const CATEGORY_SHOP_PD = 5;
    const CATEGORY_FH = 6;
    const CATEGORY_SHOP_U = 7;
    const CATEGORY_HUB = 8;
    const CATEGORY_OS = 9;
    const CATEGORY_BDC = 10;
    const CATEGORY_B_HUB = 12;
    const CATEGORY_CDC = 13;
    const CATEGORY_PDC = 14;


    /**
     * 黑名单网点
     * @var array
     */
    public $store_blacklist = [];


    /**
     * 设置fleet黑名单网点
     * @return array
     * @throws \Exception
     */
    public function makeFleetStoreBlacklist()
    {
        //is_fleet_limit 为空 则 不走黑名单限制
        $is_fleet_limit = (new SettingEnvServer())->getSetVal('is_fleet_limit');
        if(empty($is_fleet_limit)) {
            return [];
        }

        $url = $this->config->api->java_http_url . "/svc/fleet/audit/tandem/query/config_store";
        $res = $this->httpPost($url, [],null,10,false);
        if ($res['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log("fleet query store black list result " . json_encode($res,JSON_UNESCAPED_UNICODE));
            throw new BusinessException($this->getTranslation()->_('server_error'));
        }
        if(empty($res['data'])){
            return [];
        }
        $this->store_blacklist = $res['data'];
        return $res['data'];
    }

    /**
     * 获取网点管理人员
     * @return array
     */
    public function getSysStoreList(): array
    {
        $sql        = "
            -- 网点管理人员
            SELECT
                id as store_id,
                manager_id,
                manager_name,
                manager_phone
            FROM
                sys_store;
        ";
        $info_data  = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $store_list = [];
        foreach ($info_data as $key => $value) {
            $store_list[$value['store_id']] = $value;
        }
        return $store_list;
    }

    /**
     * 校验用户是否是网点经理
     * @param $manager_id
     * @return mixed
     */
    public function checkOutletManager($manager_id){
        $sql        = "
            -- 网点列表
            SELECT
                id
            FROM
                sys_store
            WHERE
                manager_id = '{$manager_id}';";
        $storeArr = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($storeArr, 'id');
    }

    /**
     * 获取网点列表
     * @param int $type
     * @param int $isAddHead  1-追加head office 2-不追加head office
     * @return array
     */
    public function list($paramIn = []): array
    {
        //[1]参数定义
        $type       = $this->processingDefault($paramIn, 'type', 2,0);
        $isAddHeader= $this->processingDefault($paramIn, 'isAddHead', 2,1);

        $returnData = [];
        $condition = 'state = :state:';
        $bind['state'] = 1;

        if(!empty($this->store_blacklist) && is_array($this->store_blacklist)){
            $condition .= ' and  id not in ({store_ids:array})';
            $bind['store_ids'] = array_values($this->store_blacklist);
        }


        $info_data = SysStoreModel::find([
            'conditions' => $condition,
            'columns'    => 'id as store_id,name,short_name,manager_id',
            'bind'       => $bind,
        ])->toArray();

        if ($isAddHeader == 1) {
            //追加Head Office
            array_unshift($info_data, ["store_id"=> '-1', "name"=> enums::HEAD_OFFICE]);
        }

        $storeDataArr = [];
        if ($type == 1){
            foreach ($info_data as $k => $v){
                $storeDataArr[$v['store_id']] = $v['name'];
            }
        } else {
            $storeDataArr = $info_data;
        }

        $returnData['data']['dataList'] = $storeDataArr;
        return $returnData;
    }

    /**
     * 获取网点列表
     * @param int $type
     * @param int $isAddHead  1-追加head office 2-不追加head office
     * @return array
     */
    public function listV2($paramIn = []): array
    {
        //[1]参数定义
        $queryName   = $this->processingDefault($paramIn, 'store_name', 1, '');
        $type        = $this->processingDefault($paramIn, 'type', 2, 0);
        $isAddHeader = $this->processingDefault($paramIn, 'isAddHead', 2, 1);

        $returnData    = [];
        $condition     = 'state = :state:';
        $bind['state'] = 1; //网点有效

        if(!empty($this->store_blacklist) && is_array($this->store_blacklist)){
            $condition .= ' and  id not in ({store_ids:array})';
            $bind['store_ids'] = array_values($this->store_blacklist);
        }

        if (!empty($queryName)) {
            $condition .= ' and name like :store_name:';
            $bind['store_name'] = '%' . $queryName . '%';
        }

        $info_data = SysStoreModel::find([
            'conditions' => $condition,
            'columns'    => 'id as store_id,name,short_name,manager_id,use_state,category',
            'bind'       => $bind,
            'limit'      => 20,
        ])->toArray();

        foreach ($info_data as & $store) {
            $store = (new SysStoreServer())->formatStoreUseState($store);
        }

        if ($isAddHeader == 1) {
            //追加Head Office
            array_unshift($info_data, ['store_id' => '-1', 'name' => enums::HEAD_OFFICE]);
        }

        $storeDataArr = [];
        if ($type == enums::RESPONSE_TYPE_KEY_VALUE) {
            foreach ($info_data as $v) {
                $storeDataArr[$v['store_id']] = $v['name'];
            }
        } else {
            $storeDataArr = $info_data;
        }

        $returnData['data']['dataList'] = $storeDataArr;
        return $returnData;
    }

    /**
     * 获取 虚拟网点，真实网点
     * @param $paramIn
     * @return array
     */
    public function storeListByType($paramIn)
    {
        $type = $this->processingDefault($paramIn, 'type', 2, 0);
        $name = $this->processingDefault($paramIn, 'name', 1, 0);

        $returnData['data']['dataList'] = [];
        if (!in_array($type, [SysStoreModel::STORE_VIRTUAL, SysStoreModel::STORE_REALITY])) {
            return $returnData;
        }
        if (empty($name)) {
            return $returnData;
        }

        $storeIdsArray = $this->getVirtualStoreInfo();

        $bind = ['state' => SysStoreModel::STATE_1,  'name' => '%'.$name.'%' ,'ids'=>$storeIdsArray];
        $conditions = 'id not in ({ids:array})';
        if ($type == SysStoreModel::STORE_VIRTUAL) {
            $conditions = 'id in ({ids:array})';
        }

        if(!empty($this->store_blacklist) && is_array($this->store_blacklist)){
            $conditions .= ' and  id not in ({store_ids:array})';
            $bind['store_ids'] = array_values($this->store_blacklist);
        }

        $storeList = SysStoreModel::find([
            'conditions' => 'state = :state: and ' . $conditions . ' and name like :name:',
            'bind'       => $bind,
            'columns'    => 'id, name, short_name,use_state,category',
            'limit'      => 30,
        ])->toArray();

        //格式化use_state
        foreach ($storeList as & $store) {
            $store = (new SysStoreServer())->formatStoreUseState($store);
        }

        $returnData['data']['dataList'] = $storeList;

        return $returnData;
    }

    /**
     * 获取flash express自建、有效的网点
     */
    public function getSelfBuildStore()
    {
        $sql        = "
            -- 网点列表
            SELECT
                ss.id as store_id,
                ss.name,
                ss.category
            FROM sys_store ss
            WHERE
                ss.`category` != 6 and ss.`state` = 1;";
        $info_data  = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($info_data) {
            foreach ($info_data as $k => $info) {
                $managers = (new \FlashExpress\bi\App\Server\StaffServer())->getStoreManager($info['store_id']);
                if ($managers) {
                    $info_data[$k]['manager_id'] = $managers;
                } else {
                    $info_data[$k]['manager_id'] = null;
                }
            }
        }
        return $info_data;
    }

    /**
     * 获取网点名字
     * @param $stores
     */
    public function getStoreName($stores)
    {
        if (empty($stores)) {
            return [];
        }

        foreach ($stores as $v) {
            $condition[] = "'{$v}'";
        }

        $query_sql = "
            --
            select 
                id as store_id
                ,name
            from 
                sys_store
            where id in (" . implode(',', $condition) . ")";
        $info_data  = $this->getDI()->get('db_rby')->query($query_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return array_column($info_data, 'name', 'store_id');
    }

    /**
     * 获取网点所在大区
     * @param string $store
     * @return mixed
     */
    public function getStoreRegion($store)
    {
        if (empty($store)) {
            return '';
        }

        $query_sql = "
            --
            select 
                sp.geography_code as region_id
                ,sp.sorting_no as region_name
            from sys_store ss 
                left join sys_province sp on ss.province_code = sp.code
            where
                ss.id = :store_id";
        $params['store_id'] = $store;
        $info_data  = $this->getDI()->get('db_rby')->query($query_sql, $params)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取网点所在的大区和片区
     * @param string $store
     */
    public function getStoreRegionPiece($store)
    {
        if (empty($store)) {
            return [];
        }

        $query_sql = "
            --
            select 
                manage_region
                ,manage_piece
                ,name
            from 
                sys_store
            where
                id = '{$store}'";
        $info_data = $this->getDI()->get('db_rby')->query($query_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取网点所在大区的名字
     * @param $store
     * @return string
     */
    public function getStoreRegionName($store)
    {
        if (empty($store)) {
            return '';
        }

        $query_sql = "
            --
            select 
                name as manage_region
            from 
                sys_manage_region
            where
                id = '{$store}'";
        $info_data  = $this->getDI()->get('db_rby')->query($query_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取网点所在片区的名字
     * @param $store
     * @return string
     */
    public function getStorePieceName($store)
    {
        if (empty($store)) {
            return '';
        }

        $query_sql = "
            --
            select 
                name as manage_piece
            from 
                sys_manage_piece
            where
                id = '{$store}'";
        $info_data  = $this->getDI()->get('db_rby')->query($query_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * @description 获取指定网点所在大区、片区的信息
     * @param $store_id
     * @return array
     */
    public function getStorePieceAndRegionInfo($store_id): array
    {
        if ($store_id == enums::HEAD_OFFICE_ID) {
            return [];
        }

        $repository = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        $storeInfo  = $repository->getOrganizationRegionPiece($store_id);

        if (!empty($storeInfo->piece_id)) {
            $pieceInfo = SysManagePieceModel::findFirst($storeInfo->piece_id);
        }

        if (!empty($storeInfo->region_id)) {
            $regionInfo = SysManageRegionModel::findFirst($storeInfo->region_id);
        }

        return [
            'piece_id'    => $storeInfo->piece_id ?? 0,
            'piece_name'  => $pieceInfo->name ?? '',
            'region_id'   => $storeInfo->region_id ?? 0,
            'region_name' => $regionInfo->name ?? '',
        ];
    }

    //获取所有hub网点 id
    public function getHubStore($str_where){
        $sql = "select id from sys_store where category in ({$str_where})";
        $data = $this->getDI()->get('db_rby')->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        return  array_column($data,'id');
    }

    /**
     * 模糊匹配获取网点id和名字
     * @param $store_name
     * @param $notType
     * @param $in_type
     * @return array
     */
    public function searchStore($store_name ,$notType = 0, $in_type = 0)
    {
        $addsql = empty($store_name) ?  " " : " and  `name` LIKE '%".$store_name."%'";
        $addsql .= $notType ?  " and category != $notType  " : " ";

        if(!empty($in_type))
            $addsql .= " and category = {$in_type} ";

        $sql = " select * from   sys_store  where  1=1 {$addsql} ";
        $info_data  = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info_data??[];
    }

    /**
     * 用网点id獲取網點信息
     * @param $storeId
     * @return array
     */
    public function getStoreByid($storeId)
    {
        if (empty($storeId)) {
            return [];
        }
        $sql = "select * from   sys_store  where  id  = '".$storeId."'";
        $info_data  = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data??[];
    }

    public function getStoreInfoByid($storeId)
    {
        if (empty($storeId)) {
            return [];
        }
        $rows = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $storeId],
        ]);
        return !empty($rows) ? $rows->toArray() : [];
    }

    /**
     * 根据片区描述名称 获取 网点id
     * @param $piece
     */
    public function get_store_by_area($piece){
        $where = '';
        if(!empty($piece) && is_array($piece)){
            // where ( name like xx or name like aa)
            foreach ($piece as $area){
                $where .= "or `name` like '{$area}%' ";
            }
            $where = ltrim($where,'or');
        }
        $sql = " select id from sys_store ss
                where ss.`manage_piece`  in 
                (select id from `sys_manage_piece` where ({$where})) 
                and ss.`category` in (1,10) ";

        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

    }

    /**
     *
     * 批量获取多个 网点信息
     * @param $store_ids
     */
    public function get_batch_store($store_ids){
        if(empty($store_ids))
            return [];
        $str = "'" . implode("','" ,$store_ids) . "'";
        $sql = " select id,category,lat,lng from sys_store where id in ({$str}) ";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    public function getStoreManagerByStoreId($store_id) {

        $manager_id = '';
        $manager_name = '';
        $manager_mobile = '';
        $store = SysStoreModel::findFirst([
            'conditions' => 'id = :storeId:',
            'bind'       => ['storeId' => $store_id],
        ]);
        $store = $store ? $store->toArray() : [];
        if(empty($store)) {
            return [
                'manager_id' => $manager_id,
                'manager_name' => $manager_name,
                'manager_mobile' => $manager_mobile,
            ];
        }
        //网点负责人
        if(!empty($store['manager_id'])) {
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $store['manager_id']],
            ]);
            $staff_info = $staff_info ? $staff_info->toArray() : [];
            if(!empty($staff_info)) {
                $manager_mobile = empty($staff_info['mobile_company']) ? $staff_info['mobile'] : $staff_info['mobile_company'];
                $manager_name = $staff_info['name'];
                $manager_id = $staff_info['staff_info_id'];
            }
        }

        //片区负责人
        if(empty($manager_mobile) && !empty($store['manage_piece'])) {
            $piece_info = SysManagePieceModel::findFirst([
                'conditions' => 'id = :piece_id:',
                'bind'       => ['piece_id' => $store['manage_piece']],
            ]);
            $piece_info = $piece_info ? $piece_info->toArray() : [];
            if(!empty($piece_info) && !empty($piece_info['manager_id'])) {
                $staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'       => ['staff_info_id' => $piece_info['manager_id']],
                ]);
                $staff_info = $staff_info ? $staff_info->toArray() : [];
                if(!empty($staff_info)) {
                    $manager_mobile = empty($staff_info['mobile_company']) ? $staff_info['mobile'] : $staff_info['mobile_company'];
                    $manager_name = $staff_info['name'];
                    $manager_id = $staff_info['staff_info_id'];
                }
            }
        }

        //大区负责人
        if(empty($manager_mobile) && !empty($store['manage_region'])) {
            $region_info = SysManageRegionModel::findFirst([
                'conditions' => 'id = :region_id:',
                'bind'       => ['region_id' => $store['manage_region']],
            ]);
            $region_info = $region_info ? $region_info->toArray() : [];
            if(!empty($region_info) && !empty($region_info['manager_id'])) {
                $staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'       => ['staff_info_id' => $region_info['manager_id']],
                ]);
                $staff_info = $staff_info ? $staff_info->toArray() : [];
                if(!empty($staff_info)) {
                    $manager_mobile = empty($staff_info['mobile_company']) ? $staff_info['mobile'] : $staff_info['mobile_company'];
                    $manager_name = $staff_info['name'];
                    $manager_id = $staff_info['staff_info_id'];
                }
            }
        }

        return [
            'manager_id' => $manager_id,
            'manager_name' => $manager_name,
            'manager_mobile' => $manager_mobile,
        ];

    }

    /**
     * 大区负责人 或者片区负责人 获取大区//片区下的网点
     * @param $staff_info_id
     * @return array
     */
    public function getStoreByManager($staff_info_id)
    {
        $allManagerStores = [];
        //查询出本人所管辖的大区
        $region_list =  SysManageRegionModel::find([
            'manager_id=:manager_id: and deleted=0 ',
            'bind' => ['manager_id' => $staff_info_id],
            'columns' => ['id'],
        ])->toArray();
        $mange_region_ids     = array_values(array_filter(array_unique(array_column($region_list, 'id'))));  //管辖大区

        //查询出本人所管辖的片区
        $piece_list =  SysManagePieceModel::find([
            'manager_id=:manager_id: and deleted=0 ',
            'bind' => ['manager_id' => $staff_info_id],
            'columns' => ['id'],
        ])->toArray();

        $mange_piece_ids      = array_values(array_filter(array_unique(array_column($piece_list, 'id'))));   //管辖片区
        $this->getDI()->get("logger")->write_log("获取oa权限结构查询出大区和片区 getStoreByManager 大区.".json_encode($mange_region_ids)." 片区" .json_encode($mange_piece_ids)." 用户为:".$staff_info_id,"info");
        //查询出管辖的网点
        $stores_ids = SysStoreModel::find([
            'manager_id=:manager_id: and state = :state:',
            'bind' => ['manager_id' => $staff_info_id, 'state' => SysStoreModel::STATE_1],
            'columns' => ['id'],
        ])->toArray();
        $stores_ids      = array_values(array_filter(array_unique(array_column($stores_ids, 'id'))));   //管辖片区
        if(!empty($stores_ids)){
            $allManagerStores = array_merge($allManagerStores, $stores_ids);  //管辖网点
            $this->getDI()->get("logger")->write_log("获取oa权限结构查询管辖网点 getStoreByManager .".json_encode($stores_ids)." 用户为:".$staff_info_id,"info");
        }

        //管辖片区
        if (!empty($mange_piece_ids)) {

            //todo 获取片区下所有网点
            $store_ids              = $this->getStoreListByPieceNew($mange_piece_ids);
            $allManagerStores = array_merge($allManagerStores, $store_ids);
        }

        //管辖大区
        if (!empty($mange_region_ids)) {
            //todo 获取大区下所有网点
            $store_ids              = $this->getStoreListByRegionNew($mange_region_ids);
            $allManagerStores = array_merge($allManagerStores, $store_ids);
        }

        $this->getDI()->get("logger")->write_log("获取oa权限结构查询出大区和片区的网点 getStoreByManager .".json_encode($allManagerStores)." 用户为:".$staff_info_id,"info");

        return $allManagerStores;
    }
    /**
     * 获取片区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByPieceNew(array $manage_piece){
        if(empty($manage_piece) ) return [];
        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_piece in ({manage_piece_ids:array})",
            'bind'       => [
                'manage_piece_ids' => $manage_piece,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids,'id');
    }

    /**
     * 获取指定大区下所有网点
     * @param array $manage_region
     * @return array
     */
    public function getStoreListByRegionNew(array $manage_region){
        if(empty($manage_region) ) return [];

        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_region in ({manage_region_ids:array})",
            'bind'       => [
                'manage_region_ids' => $manage_region,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids,'id');
    }




   /**
   * 根据名称查询网点数据
   * @Date: 6/28/22 9:02 PM
   * @author: peak pan
   * @return:
   **/
    public function searchStoreList(array $store_where){

        $info_data = SysStoreModel::find([
            'conditions' =>'state = 1 and name like :name:',
            'bind'       => ['name' => '%'.$store_where['name'].'%'],
            'columns'    => 'id,name',
            'limit'     =>20,
        ])->toArray();

        if ($store_where['isAddHeader'] == 1) {
            //追加Head Office
            array_unshift($info_data, ["id"=> '-1', "name"=> enums::HEAD_OFFICE]);
        }
        return $info_data;

    }

    /**
     * 根据 category 获取网点数据
     * @param int $category
     * @param int $isAddHeader
     * @return mixed
     */
    public function getStoreListByCategory(int $category, array $column = [], int $isAddHeader = 1)
    {
        if (0 >= $category) {
            return [];
        }

        $field = "*";
        if (!empty($column)) {
            $field = implode(',', $column);
        }

        $sql              = "
            -- 网点列表
            SELECT
                {$field}
            FROM
                sys_store
            WHERE
                state = 1 AND category = :category;";
        $bind['category'] = $category;
        $storeData        = $this->getDI()->get('db')->query($sql, $bind)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($isAddHeader == 1) {
            //追加Head Office
            array_unshift($storeData, ["store_id" => '-1', "name" => enums::HEAD_OFFICE]);
        }
        return $storeData;
    }

    /**
     * 获取虚拟网点ID
     * @return array
     */
    public function getVirtualStoreInfo()
    {

        $virtualRelationInfo = StorePickupVirtualRelationModel::find([
            'columns'    => 'DISTINCT(pickup_virtual_store_id) as pickup_virtual_store_id',
            'conditions' => 'deleted =  0',
        ])->toArray();

        if (!empty($virtualRelationInfo)) {
            return array_column($virtualRelationInfo, 'pickup_virtual_store_id');
        }
        return [];
    }

    public function queryList($paramIn = []): array
    {
        //[1]参数定义
        $name        = $this->processingDefault($paramIn, 'name');
        $type        = $this->processingDefault($paramIn, 'type', 2, 0);
        $isAddHeader = $this->processingDefault($paramIn, 'isAddHead', 2, 1);

        $returnData    = [];
        $condition     = 'state = :state:';
        $bind['state'] = 1;
        //模糊查询网点
        if (!empty($name)) {
            $condition    .= ' and name like :name: ';
            $bind['name'] = '%' . $name . '%';
        }
        $info_data = SysStoreModel::find([
            'conditions' => $condition,
            'columns'    => 'id as store_id,name,short_name,manager_id',
            'bind'       => $bind,
            'limit'      => 30,
        ])->toArray();

        if ($isAddHeader == 1) {
            //追加Head Office
            array_unshift($info_data, ["store_id" => '-1', "name" => enums::HEAD_OFFICE]);
        }

        $storeDataArr = [];
        if ($type == 1) {
            foreach ($info_data as $k => $v) {
                $storeDataArr[$v['store_id']] = $v['name'];
            }
        } else {
            $storeDataArr = $info_data;
        }

        $returnData['data']['dataList'] = $storeDataArr;
        return $returnData;
    }

    /**
     * @param $storeId
     * @return array
     */
    public function getRegionAndPieceName($storeId)
    {
        $storeRegionPieceId = $this->getStoreRegionPiece($storeId);
        $storeRegionPiece['store_name'] = $storeRegionPieceId['name'];
        if (empty($storeRegionPieceId['manage_region'])) {
            $storeRegionPiece['manage_region'] = '';
        } else {
            $storeRegionName = $this->getStoreRegionName($storeRegionPieceId['manage_region']);
            $storeRegionPiece['manage_region'] = $storeRegionName['manage_region'] ?? '';
        }
        if (empty($storeRegionPieceId['manage_piece'])) {
            $storeRegionPiece['manage_piece'] = '';
        } else {
            $storePieceName = $this->getStorePieceName($storeRegionPieceId['manage_piece']);
            $storeRegionPiece['manage_piece'] = $storePieceName['manage_piece'] ?? '';
        }
        return $storeRegionPiece;
    }

    /**
     * 根据网点ID获取真实网点ID
     * @param $store_id
     * @return mixed
     */
    public function getRealStoreById($store_id)
    {
        if (empty($store_id)) {
            return $store_id;
        }
        $storeInfo = StorePickupVirtualRelationModel::findFirst([
            'conditions' => 'pickup_virtual_store_id =  :pickup_virtual_store_id: and deleted = 0 ',
            'bind'       => ['pickup_virtual_store_id' => $store_id],
            'columns'    => 'store_id',
        ]);

        return !empty($storeInfo) ? $storeInfo->store_id : $store_id;
    }


    /**
     * 计算两个网点经纬度距离 返回单位米
     * @param $store_id_a
     * @param $store_id_b
     * @return int|null
     */
    public function calculateDistanceStore($store_id_a,$store_id_b)
    {
        $apart = null;
        if (empty($store_id_a) || empty($store_id_b)){
            return $apart;
        }
        $store_data = SysStoreModel::find([
            'conditions' => "id in ({storeIdsArr:array})",
            'bind' => [
                'storeIdsArr' => [$store_id_a,$store_id_b],
            ],
            'columns'    => 'id,lat,lng',
        ])->toArray();
        $store_data = array_column($store_data,null,'id');
        if (empty($store_data[$store_id_a]['lat']) || empty($store_data[$store_id_a]['lng']) || empty($store_data[$store_id_b]['lat']) || empty($store_data[$store_id_b]['lng'])){
            return $apart;
        }else{
            $apart = calculateDistance($store_data[$store_id_a]['lat'],$store_data[$store_id_a]['lng'],$store_data[$store_id_b]['lat'],$store_data[$store_id_b]['lng']);
        }
        return $apart;
    }


    protected $all_store_cache_columns = ['id', 'name', 'short_name', 'category'];
    protected $all_store_cache_prefix  = 'all_store_';//前缀

    protected function allStoreCacheKey(): string
    {
        return $this->all_store_cache_prefix . implode('_', $this->all_store_cache_columns);
    }

    public function getAllStore()
    {
        $redis = $this->getDI()->get('redisLib');
        $store = $redis->get($this->allStoreCacheKey());
        if (empty($store)) {
            return $this->setAllStoreToCache();
        }
        return json_decode($store, true);
    }

    public function setAllStoreToCache(): array
    {
        $query       = SysStoreModel::query()->columns($this->all_store_cache_columns);
        $collections = $query->execute();
        $store       = array_column($collections->toArray(), null, 'id');
        $redis       = $this->getDI()->get('redisLib');
        $redis->set($this->allStoreCacheKey(), json_encode($store, JSON_UNESCAPED_UNICODE), 86400);
        return $store;
    }

    /**
     * @description 根据部门ID，查在组织架构中负责的网点
     * @param $department_id
     * @return array
     */
    public function getManageStoresByDepartmentId($department_id)
    {
        //获取指定部门
        //$department_service = new SysDepartmentServer();
        //$department_ids     = $department_service->getChildrenListByDepartmentIdV2($department_id, true);
        $department_ids[]   = $department_id;

        //获取管辖的网点
        $allManageStores = $this->getStoreByRelationInfo($department_ids, self::MANAGE_ORG_TYPE_DEPARTMENT);
        return array_values(array_unique($allManageStores));
    }

    /**
     * 查询部门关联的网点
     * @param $items
     * @param $type
     * @return array
     */
    protected function getStoreByRelationInfo($items, $type): array
    {
        if (empty($items)) {
            return [];
        }

        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'items_ids'      => $items,
        ];
        $conditions = 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and ';

        switch ($type) {
            case self::MANAGE_ORG_TYPE_DEPARTMENT:
                $conditions .= 'department_id in ({items_ids:array})';
                break;
            case self::MANAGE_ORG_TYPE_REGION:
                $conditions .= 'region_id in ({items_ids:array})';
                break;
            case self::MANAGE_ORG_TYPE_PIECE:
                $conditions .= 'piece_id in ({items_ids:array})';
                break;
            default:
                $conditions .= 'store_id in ({items_ids:array})';
                break;
        }

        $storeRelate = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        return !empty($storeRelate) ? array_column($storeRelate, 'store_id') : [];
    }

    /**
     * @description 搜索 - 营业中的网点（注意目前只有th转岗在用这个逻辑）
     * @param $storeName
     * @return array
     */
    public function searchStoreListByName($storeName): array
    {
        if (isCountry('TH')){
            return SysStoreModel::find([
                'conditions' => 'use_state = :use_state: and name like :store_name:',
                'bind'       => [
                    'store_name' => $storeName.'%',
                    'use_state' => SysStoreModel::USE_STATE_YES,
                ],
                'columns'    => 'id,name',
            ])->toArray();
        }else{
            return SysStoreModel::find([
                'conditions' => 'name like :store_name:',
                'bind'       => [
                    'store_name' => $storeName.'%',
                ],
                'columns'    => 'id,name',
            ])->toArray();
        }
    }


    /**
     * 批量获取 网点名称 大区名称 片区名称
     * @param $storeIds
     * @return array
     */
    public function batchStorePieceRegion($storeIds){
        if (empty($storeIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.id store_id,s.name store_name,s.lat,s.lng,r.id region_id, r.name region_name,p.id piece_id, p.name piece_name');
        $builder->from(['s' => SysStoreModel::class]);
        $builder->andWhere('s.id in ({store_ids:array})', ['store_ids' => $storeIds]);
        $builder->leftJoin(SysManageRegionModel::class,'s.manage_region = r.id','r');
        $builder->leftJoin(SysManagePieceModel::class,'s.manage_piece = p.id','p');
        $data = $builder->getQuery()->execute()->toArray();

        if(!empty($data)){
            $data = array_column($data, null, 'store_id');
        }

        return $data;
    }

    /**
     * 格式化 use_state  目前 SHOP FH类型的网点没有营业状态，默认为营业
     * 其他需要也用到use_state要与相关产品确认一下是否是这样的逻辑
     * TH&MY：SHOP(pickup-only);SHOP(pickup-only);USHOP
     * PH：SHOP(pickup-only);SHOP(pickup-only);USHOP;FH
     * @param $data
     * @return mixed
     */
    public function formatStoreUseState($data)
    {
        if (!empty($data['category'])) {
            $notUseStateCategoryList = [];

            if (isCountry(['TH','MY'])) {
                $notUseStateCategoryList = [
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_pickup_delivery'],
                    enums::$stores_category['shop_ushop'],
                ];
            } elseif (isCountry('PH')) {
                $notUseStateCategoryList = [
                    enums::$stores_category['fh'],
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_pickup_delivery'],
                    enums::$stores_category['shop_ushop'],
                ];
            } elseif (isCountry('LA')) {
                $notUseStateCategoryList = [
                    enums::$stores_category['fh'],
                ];
            }

            if (in_array($data['category'], $notUseStateCategoryList)) {
                $data['use_state'] = (string)SysStoreModel::USE_STATE_YES;
            }
        }

        return $data;
    }
}
