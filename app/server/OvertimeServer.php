<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\StaffConsentAgreementLogModel;


class OvertimeServer extends AuditBaseServer
{
    protected $re;
    public $timezone;
    public $auditlist;
    public $overtime;
    public $staff;
    public $userInfo;
    public $audit;
    public $public;
    public $language;
    public $other;

    public $reference;//审批流相关参考数据
    public $detailData;//详情页展示相关数据
    public $staffInfo;//hrs 员工信息
    public $transferInfo;//固化信息表
    public $param;//入参
    public $shiftInfo;//班次信息
    public $is_anticipate = HrOvertimeModel::UN_ANTICIPATE;//是否是 预申请 0 和 1
    public $overType;//超时加班类型
    public $normalType;//非超时加班类型

    public $is_check_attendance = true;//是否验证考勤
    //审批时候 审批人是否不填写驳回原因 系统自动补填原因 1 是 2 否
    public $isAutoRejectReason = false;
    //审批同意 需要填写审批原因
    public $isNeedApprovalReason = false;

    public $user;//审批流用的user

    //分类 1补卡 2请假 3申请LH费 加班类型 5=工作日加班，6=节假日加班，7=晚班 8-节假日正常上班
    //加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班'
    protected $type_format = array(
        1 => 5,
        2 => 6,
        3 => 7,
        4 => 8,
    );

    //超时加班 只能是 上班之后 申请晚上下班之后的 对应时长 需要动态获取
    public $overOt = [
        HrOvertimeModel::OVERTIME_1 => [],
        HrOvertimeModel::OVERTIME_2 => [],
    ];

    //全天加班 上班时间开始就可以申请的 节假日 或者 休息日加班 对应时长 是固定的
    public $holidayOt = [
        HrOvertimeModel::OVERTIME_4 => [],
        HrOvertimeModel::OVERTIME_5 => [],
        HrOvertimeModel::OVERTIME_6 => [],
    ];

    public function __construct($lang = 'zh-CN', $timezone,$userInfo = [])
    {
        parent::__construct($lang);
        $this->overtime     = new OvertimeRepository($timezone);
        $this->staff        = new StaffRepository();
        $this->audit        = new AuditRepository();
        $this->public       = new PublicRepository();
        $this->userInfo     = $userInfo;
        $this->language     = $lang;
        $this->timezone     = $timezone;
        $this->auditlist    = new AuditlistRepository($this->lang, $this->timezone);
        $this->other        = new OtherRepository($this->lang, $this->timezone);

    }

    private static $single = null;
    public static function getInstance($lang, $timezone)
    {
        if (!self::$single) {
            self::$single = new self($lang, $timezone);
        }

        return self::$single;
    }

    public function get_type_format(){
        return $this->type_format;
    }

    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $reason        = $this->processingDefault($paramIn, 'reason');
        $duration      = floatval($paramIn['duration']);
        $date          = $this->processingDefault($paramIn, 'date_at');
        $reason        = addcslashes(stripslashes($reason),"'");

        if(empty($date) || empty($start_time) || empty($type)){
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        //拼接endtime
        $startTime  = strtotime($start_time);
        $start_time = date('Y-m-d H:i:s',$startTime);
        $endTime    = empty($end_time) ? 0 : strtotime($end_time);
        if(!empty($endTime)){
            $end_time   = date('Y-m-d H:i:s',$endTime);
        }else{
            $endTime    = $startTime + floatval($duration) * 3600;
            $end_time   = date('Y-m-d H:i:s',$endTime);
        }

        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId, [$date]);
        $shift_info = $shift_info[$date] ?? [];
        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        if(empty($paramIn['is_bi'])){
            $ot_server->checkOTTimePeriod($type, $shift_info, $date);
        }

        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $shift_info;
        $check_data = $ot_server->checkOvertime($paramIn);

        if($check_data['code'] != 1)
            return $check_data;

        //获取参考数据
        [$references, $extend] = $ot_server->getReference($staffId, $type, $date);

        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = '';//bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state = 1;
        if(!empty($paramIn['is_bi'])){
            $higher = $paramIn['operator'];
            $state = 2;
        }
        $serialNo = $this->getID();
        $insertParam = [
            'staff_id'   => $staffId,
            'type'       => $type,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'reason' => $reason,
            'reject_reason' => '',
            'state'     => $state,
            'duration'     => $check_data['data']['duration'],
            'higher_staff_id' => $higher,
            'is_anticipate' =>$check_data['data']['is_anticipate'],
            'date_at' => $date,
            'references' => $references,
            'serial_no' => (!empty($serialNo) ?'OT'.$serialNo : NULL),
            'wf_role'   => 'ot_new'
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        $overtimeId = $this->overtime->addOvertime($insertParam);

        if ($overtimeId && empty($paramIn['is_bi'])) { //非bi途径
            try {

                //创建
                $server = new ApprovalServer($this->lang, $this->timezone);
                $requestId = $server->create($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId,null,$extend);
                if (!$requestId) {
                    throw new \Exception('创建审批流失败');
                }
                $db->commit();
            } catch (\Exception $e){
                $db->rollback();
                $this->wLog('pushError',$e->getMessage(), 'overtime');
                return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
            }
        } else if (!empty($paramIn['is_bi'])) { //bi添加加班
            $db->commit();
            return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
        } else {
            $db->rollback();
            return $this->checkReturn(-3,$this->getTranslation()->_('4101'));
        }
        return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
    }

    /**
     * 获取依赖数据 默认方法 没用
     * @param $staffId
     * @param $type
     * @param $date
     */
    public function getReference($staffId, $type, $date)
    {
        return [0, []];
    }


    /**
     * 同上 没用 但是得有个默认
     * @return array
     */
    public function checkOvertime($paramIn){
        return $this->checkReturn(array('data' => []));
    }

    /**
     * 编辑加班  bi工具
     * @param $paramIn
     */
    public function edit_overtime($paramIn){
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        //工作日校验 加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $date          = $this->processingDefault($paramIn, 'date_at');
        $duration      = floatval($paramIn['duration']);
        $overtime_id = intval($paramIn['overtime_id']);

        if(empty($date) || empty($start_time) || empty($type) || empty($overtime_id)){
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        $startTime = strtotime($start_time);
        $endTime = $startTime + floatval($duration) * 3600;
        $end_time = date('Y-m-d H:i:s',$endTime);
        $paramIn['is_edit'] = 1;
        $staff_re = new StaffRepository($this->lang);
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId, [$date]);
        $shift_info = $shift_info[$date] ?? [];
        $paramIn['shift_info'] = $shift_info;

        //参数塞进去
        $this->param = $paramIn;
        $this->staffInfo = $staff_re->getStaffPosition($staffId);
        if (empty($this->staffInfo)) {
            throw new ValidationException('can not find staff');
        }
        //验证 类型和时长有没有权限
        $this->checkTypeDuration();

        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);

        $res = $ot_server->checkOvertime($paramIn);

        if($res['code'] != 1){
            return $res;
        }

        $model = new OvertimeRepository($this->timezone);
        $info = $model->getInfoById($overtime_id);
        if($info['state'] != 2 && empty($paramIn['due_to_holiday'])){//非审核通过的 记录 不允许 修改 没走完正常审核逻辑
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_bi_notice'));
        }

        $update_data = [
            'type'       => $type,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'state'     => isset($paramIn['due_to_holiday']) ? $info['state'] :  enums::APPROVAL_STATUS_APPROVAL,//bi 工具 直接审核通过 因holiday变更的除外
            'duration'     => $res['data']['duration'],
            'higher_staff_id' => $paramIn['operator'],
            'is_anticipate' =>$res['data']['is_anticipate'],
            'date_at' => $date,
        ];

        $model = new OvertimeRepository($this->timezone);
        $flag = $model->updateInfoByTable('hr_overtime','overtime_id',$overtime_id,$update_data);
        if($flag){
            if ($type != $info['type']) {
                (new AuditApplyRepository)->editOtTypeOfSummary($overtime_id,$staffId,$type);
            }
            return $this->checkReturn([]);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }

    }

    /**
     * 修改状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws \Exception
     */
    public function updateOvertimeV3($paramIn = [])
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $overtimeId    = $this->processingDefault($paramIn, 'audit_id',2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');
        $state         = $this->processingDefault($paramIn, 'status',2);
        $server = new ApprovalServer($this->lang, $this->timezone);

        //获取审批详情
        $overtimeList = $this->overtime->infoOvertime(['overtime_id'=>$overtimeId]);
        if (empty($overtimeList)){
            return $this->checkReturn(-3,$this->getTranslation()->_('4102'));
        }
        //oa 审批处理中 不能操作
        if($overtimeList['in_approval'] == HrOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])){
            return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
        }

        //校验当前审批人审批状态
        if ($state == enums::$audit_status['approved']) {
            //同意
            $reason = null;
            if(!empty($reject_reason) && $reject_reason != '1')//前端默认写死了个1  因为后端 需要必填1个字符
                $reason = filter_param($reject_reason);
                $flag = $server->approval($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId,$reason);
        } else {
            //驳回
            $flag = $server->reject($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data'=>['audit_id'=>$overtimeId]]);
    }



    /**
     * 获取加班类型
     * @Access  public
     * @Param   request $paramIn
     * @Param   request $userinfo
     * @Return  jsonData
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        $data                           = $this->getAllOtType();
        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    //所有加班类型展示 这里面不做权限判断
    public function getAllOtType($locale = ''){
        $new_duration = array(
            array('time_hour' => 4,'time_text' => $this->getTranslation($locale)->_('ot_4_new_text')),
            array('time_hour' => 8,'time_text' => $this->getTranslation($locale)->_('ot_8_new_text')),
        );
        $new_customer = array(
            array('time_hour' => 2,'time_text' => '2h'),
            array('time_hour' => 3,'time_text' => '3h'),
            array('time_hour' => 4,'time_text' => '4h'),
            array('time_hour' => 5,'time_text' => '5h'),
            array('time_hour' => 6,'time_text' => '6h'),
            array('time_hour' => 7,'time_text' => '7h'),
            array('time_hour' => 8,'time_text' => '8h'),
        );
        return  [
            [
                'code' => '1',
                'msg'  => $this->getTranslation($locale)->_('5107'),
                'sub_msg' => $this->getTranslation($locale)->_('1.5_times_salary'),
                'duration' => $new_customer
            ],
            [
                'code' => '2',
                'msg'  => $this->getTranslation($locale)->_('5108'),
                'sub_msg' => $this->getTranslation($locale)->_('3_times_salary'),
                'duration' => $new_customer
            ],
            [
                'code' => '4',
                'msg'  => $this->getTranslation($locale)->_('5111'),
                'sub_msg' => $this->getTranslation($locale)->_('1_times_salary'),
                'duration' => $new_duration
            ]
        ];

    }

    /**
     * 获取加班类型，用于hcm-api系统展示
     */
    public function getOvertimeTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }
        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $staff_re = new StaffRepository($this->lang);
        if(empty($this->staffInfo)){
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        $data = $ot_server->getAllOtType();
        $data = $this->getTypeDuration($data);

        return $data;
    }

    /**
     * OT添加验证
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkOtDataPosition($paramIn = [])
    {
        //[1]校验添加权限
        $positionData   = UC('otPosition')['otData'];
        //[2]获取当前员工职位
        $staffId   = $this->processingDefault($paramIn, 'staff_id', 2);
        $staffData = $this->staff->getStaffPosition($staffId);
        if(!empty($positionData) && !empty($staffData))
        {
            $jobTitleId      = $this->processingDefault($staffData, 'job_title', 2);
            $positionIdArr   = array_keys($positionData);
            if(in_array($jobTitleId, $positionIdArr))
            {
                return true;
            }
        }
        return true;
    }


    /**
     * 撤销申请
     * @param array $paramIn
     * @return array
     */
    public function cancelV3($paramIn = [])
    {
        $staff_id    = intval($paramIn['staff_id']);
        $overtime_id = intval($paramIn['audit_id']);
        $cancel_reason = $paramIn['cancel_reason'] ?? '';
        try{
            //获取该条记录
            $data = $this->overtime->getInfoById($overtime_id);

            //oa 审批处理中 不能操作
            if($data['in_approval'] == HrOvertimeModel::IN_APPROVAL){
                return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
            }

            //获取员工名称
            $public_model = new PublicRepository();
            $staff_name = $public_model->getStaffName($staff_id);
            if(empty($data))
                return $this->checkReturn(-3,$this->getTranslation()->_('2201'));

            //新增逻辑 每个月20号之后，不可以撤销所选日期为本月15号以及之前的申请数据。
            //15 20号时间戳
            $tmp_20 = strtotime(date('Y-m-20'));
            $tmp_15 = strtotime(date('Y-m-16'));
            $current_tmp = time();

            if($current_tmp >= $tmp_20){//已经过了20号
                //获取所选日期
                $apply_date = $data['date_at'];
                $apply_tmp = strtotime($apply_date);
                if(empty($apply_date)){
                    $apply_date = substr($data['start_time'],0,10);
                    $apply_tmp = strtotime($data['start_time']);
                }

                if(empty($apply_date))
                    return $this->checkReturn(-3,$this->getTranslation()->_('2206'));

                if($apply_tmp < $tmp_15){//不可以撤销所选日期为本月15号以及之前的申请数据
                    return $this->checkReturn(-3,$this->getTranslation()->_('2207'));
                }
            }

            //[2]验证数据
            //验证审批状态
            //已撤销的不能再撤销
            if (in_array($data['state'], [enums::APPROVAL_STATUS_CANCEL])) {
                throw new ValidationException($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
            }


            //获取审批流
            //撤销
            $server = new ApprovalServer($this->lang, $this->timezone);
            $otServer = Tools::reBuildCountryInstance($this,[$this->lang,$this->timezone]);
            $auditType = $otServer->getAuditType($data);


            if ($data['staff_id'] == $staff_id) {
                $server->cancel($overtime_id, $auditType, $cancel_reason,$staff_id);
            } else {

                $server->approvalCancel($overtime_id, $auditType, '',$staff_id);
            }

            return $this->checkReturn(1);
        }catch (\Exception $e){
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    public function getAuditType($info){
        return AuditListEnums::APPROVAL_TYPE_OVERTIME;
    }


    /**
     * Audit Log表状态转换
     */
    public function getAuditLogTransfer($type)
    {
        $this->type_format[$type];
    }


    /**
     * bi工具 页面 列表页用
     * @param $param
     */
    public function overtime_list($param)
    {
        $staff_id = intval($param['staff_id']);
        $date = $param['date_at'];
        try{
            $ot_model = new OvertimeRepository($this->timezone);
            $list = $ot_model->getOtByDate($staff_id,$date);
            if(!empty($list)){
                foreach($list as $k => $li){
                    if($li['state'] != 2)
                        unset($list[$k]);
                }
                $list = array_values($list);
            }
            return $this->checkReturn(['data'=>$list]);
        }catch (\Exception $e){
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 获取详情 只有泰国用
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id'=>$auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if(empty($staff_info['data']))
            throw new ValidationException('wrong hrs staff info');

        $staff_info = $staff_info['data'];


        //[2]组织详情数据
        $t = $this->getTranslation();
        $overtimeType = $this->getDetailOtType();
//        $overtimeSubtype = $this->getOvertimeSubType();

        $duration_count = round((strtotime($result['end_time']) - strtotime($result['start_time'])) / 3600,1);

        $detailLists = [
            'apply_parson'       => sprintf('%s ( %s )', $staff_info['name'] , $staff_info['id']),
            'apply_department'   => sprintf('%s - %s', $staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'OT_date'       => $result['date_at'],
            'OT_type'       => ($overtimeType[$result['type']] ?? ''),
            'start_time'    => $result['start_time'],
            'end_time' => $result['end_time'],
            'duration'      => $duration_count,
            'OT_reason'     => $result['reason'],
            'ot_detail_6' => $staff_info['store_name'],
        ];


        $references = json_decode($result['references'], true) ?? '';
	    $envModel = new SettingEnvServer();

        //根据配置项类型 展示结束时间 是否加1小时 只有泰国和菲律宾 公用 其他国家都有
        $ot_9_hour_type = $envModel->getSetVal('ot_9_hour');
        if(!empty($ot_9_hour_type)){
            $ot_9_types = explode(',',$ot_9_hour_type);
            if(in_array($result['type'],$ot_9_types) && $result['duration'] == 8)
                $detailLists['end_time'] = date('Y-m-d H:i:s', strtotime($result['end_time'] . ' + 1 hours'));
        }

        //对应国家 配置了 env  才会显示
        $networkBulkyId = $envModel->getSetVal('dept_network_bulky_id');
	    $networkManagementId = $envModel->getSetVal('dept_network_management_id');
	    $sys_department_ids[] = !empty($networkManagementId) ? $networkManagementId : 4;// 原来默认是 4
	    if(!empty($networkBulkyId)){
		    $sys_department_ids[] = $networkBulkyId;
	    }

	    //这里是 network management 部门 和 network bulky 的  DC Officer 职位
        $limitJobTitle = array(enums::$job_title['dc_officer'],enums::$job_title['assistant_branch_supervisor']);
        if (in_array($references['sys_department_id'],$sys_department_ids) && in_array($references['job_title'],$limitJobTitle)) {
            //获取定制 详情页字段
            $ext_param['networkManagementId'] = $networkManagementId;
            $ext_server = new OvertimeExtendServer($this->lang,$this->timezone);
            $other_detail_data = $ext_server->ot_dc_detail($staff_info,$result,$ext_param);
            $detailLists = array_merge($detailLists,$other_detail_data);

        }

        if (isset($references['store_category']) && in_array($references['store_category'], explode(',', HrOvertimeModel::$job_apply['shop_category']))
            && in_array($references['job_title'], [enums::$job_title['shop_officer'], enums::$job_title['shop_cashier']])
        ) {
            $detailLists = array_merge($detailLists, [
                'ot_detail_11' => $references['region_name'] ?? '', //员工所在大区名称
//                'ot_detail_6' => $references['store_name'] ?? '', //员工所在网点名称
                'attendance_num' => $references['attendance_num'] ?? '', //员工申请OT日所在网点出勤人数
                'parcel_num' => $references['parcel_num'] ?? '', //员工申请OT日所在网点总揽派件量
                'ot_detail_10' => ($references['all_effective_num'] ?? 0) . ' ' .  $t->_('ot_detail_4'),//上周SHOP相关网点平均工作效率
                'ot_detail_2' => ($references['store_effective_num'] ?? 0) . ' ' .  $t->_('ot_detail_4'),//上周特定网点平均工作效率
            ]);

            if (date("Y-m-d", strtotime($result['start_time'])) > date('Y-m-d', strtotime($result['created_at']))) {
                unset($detailLists['attendance_num']);
                unset($detailLists['parcel_num']);
            }

            if ($result['type'] == 1) { //1。5倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_3' => ($references['duration'] ?? 0) . "h"
                ]);
            }
            if ($result['type'] == 2) { //3倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_9' => ($references['duration'] ?? 0) . "h"
                ]);
            }
            if ($result['type'] == 4) { //1倍工资 才显示
                $detailLists = array_merge($detailLists, [
                    'ot_detail_8' => ($references['duration'] ?? 0) . "h"
                ]);
            }
        }

        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'       => $this->auditlist->getAudityType(enums::$audit_type['OT']),
            'id'          => $result['overtime_id'],
            'staff_id'    => $result['staff_id'],
            'type'        => enums::$audit_type['OT'],
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $this->auditlist->getAuditStatus('10' . $result['state']),
            'notice'      => $result['notice'] ?? '',
            'serial_no'   => $result['serial_no'] ?? '',
            'is_alert' => $references['is_alert'] ?? 0,//针对 dc 休息日申请ot 要弹审批通过的原因 如果是 ph 或者爆仓 可以不填写
        ];


        $returnData['data']['head']   = $data;
        return $returnData;
    }

    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true, true, true, true, true, false);
    }
    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        //获取加班详情
        $info = $this->overtime->infoOvertime(['overtime_id'=>$auditId]);
        if (empty($info)) {
            return '';
        }

        //新版 duration 废弃表里字段
        $new_duration = round((strtotime($info['end_time']) - strtotime($info['start_time'])) / 3600,2);

        $param = [
            [
                'key'   => "OT_date",
                'value' => $info['date_at']
            ],
            [
                'key'   => "OT_type",
                'value' => $info['type']
            ],
            [
                'key'   => "duration",
                'value' => $new_duration . 'h'
            ]
            ,[
                'key'   => "created_at",
                'value' => $info['real_create']
            ]
        ];
        return $param ?? "";
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $detail = HrOvertimeModel::findFirst($auditId);
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']]
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $data['approver_id']   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $data['approver_name'] = isset($staff) && $staff ? $staff['name'] : '';
                $data['reject_reason'] = isset($extend['remark']) ? $extend['remark'] : '';
            }
            $data['state'] = $state;
            $data['in_approval'] = HrOvertimeModel::NOT_IN_APPROVAL;
            $this->getDI()->get('db')->updateAsDict(
                'hr_overtime',
                $data,
                'overtime_id = '.$auditId
            );

            $this->sendMessage($detail->toArray(),$state);
        }
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        //OT Type
        $detail     = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        $references = json_decode($detail['references'], true);

        $data = [
            'category'           => $references['store_category'] ?? 0,
            'department_id'      => $references['sys_department_id'] ?? 0,
            'node_department_id' => isset($references['node_department_id']) ? $references['node_department_id'] : 0,
            'k'                  => $references['all_effective_num'] ?? 0,
            'k1'                 => $references['store_effective_num'] ?? 0,
            'duration'           => $references['duration'] ?? 0,
            'type'               => $detail['type'],
            'job_title'          => $references['job_title'] ?? 0,
            'organization_type'  => $references['organization_type'] ?? 0,
            'company_id'         => $references['company_id'] ?? 0,
            'ot_date'            => 'date_at',//表单填写的加班日期
            'staff_info_id'      => $detail['staff_id'],//申请人 工号要获取对应的ph
            'dc_in_all_hours'    => $references['dc_in_all_hours'] ?? 0,//当前网点 dc 已经申请的小数 一周内已经申请的对应ot类型的时长
            'dc_should_hours'    => $references['dc_should_hours'] ?? 0, //当前网点 申请时候 应有的小时预算
            'ph_num'             => $references['ph_num'] ?? 0, //申请当月 有多少个 ph
            'pay_flag'           => $references['pay_flag'] ?? 0, //子公司 专用字段
            'money_flag'         => $references['money_flag'] ?? 0, //子公司 专用字段
            'is_bulky'           => $references['is_bulky'] ?? 0, //泰国 bulky 调整新增 审批流用
            'is_pcc'             => $references['is_pcc'] ?? 0, //泰国 pcc 调整新增 审批流用

        ];
        return $data;
    }

    /**
     * 获取加班类型
     */
    //上面方法改了名 不然和 module 里面的 方面名冲突
    public function getDetailOtType()
    {

        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $data = $ot_server->getApprovalOtType();
        return array_column($data, 'msg', 'code');
    }


    /**
     * 获取加班类型
     */
    public function getOvertimeSubType()
    {
        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        $data = $ot_server->getAllOtType();
        return array_column($data, 'sub_msg', 'code');
    }


    //申请加班操作 验证 有没有类型和时长的权限
    public function checkTypeDuration()
    {
        $checkParam['staff_id'] = $this->staffInfo['staff_info_id'];
        $typeList               = $this->getTypeOvertime($checkParam);

        if ($typeList['code'] != 1 || empty($typeList['data']['dataList'])) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }
        //整理数据
        $typeList = array_column($typeList['data']['dataList'], null, 'code');

        //能申请的 类型
        $typeArr = array_keys($typeList);
        if(!in_array($this->param['type'], $typeArr)){
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }

        //能申请的时长
        $durationArr = array_column($typeList[$this->param['type']]['duration'], 'time_hour');
        $this->logger->write_log("checkTypeDuration {$this->staffInfo['id']} ".json_encode($typeArr).json_encode($durationArr), 'info');
        if (!in_array($this->param['duration'], $durationArr)) {
            throw new ValidationException($this->getTranslation()->_('ot_duration_permission'));
        }

        return true;
    }

    /**
     * OT提醒
     * @return int
     */
    public function isReadRule(): int
    {
        $uid                           = $this->userInfo['id'];
        $StaffConsentAgreementLogModel = StaffConsentAgreementLogModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND type = :type: ',
            'bind'       => [
                'staff_info_id' => $uid,
                'type'          => StaffConsentAgreementLogModel::TYPE_OVERTIME_READ_RULE,
            ],
        ]);

        if ($StaffConsentAgreementLogModel) {
            return 1;
        }
        $staffItemModel                = new StaffConsentAgreementLogModel();
        $staffItemModel->staff_info_id = $uid;
        $staffItemModel->type          = StaffConsentAgreementLogModel::TYPE_OVERTIME_READ_RULE;
        $staffItemModel->content       = 1;
        $staffItemModel->save();
        return 0;
    }
    //获取各个国家的 ot 规则弹窗
    public function getOtRuleText($param){
        $staffRe = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($param['user_info']['id']);
        if(empty($staffInfo)){
            return '';
        }
        $countryServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        return $countryServer->ruleConfirm($staffInfo);
    }
    //目前只 越南 其他国家 按原来走
    public function ruleConfirm($staffInfo){
        return '';
    }


    //节假日加班 8小时 需要+1小时的type
    public function getHourOverType()
    {
        $holidayKeys    = empty($this->holidayOt) ? [] : array_keys($this->holidayOt);
        //有的国家走配置
        $envModel       = new SettingEnvServer();
        $ot_9_hour_type = $envModel->getSetVal('ot_9_hour');
        $typeArr        = empty($ot_9_hour_type) ? $holidayKeys : explode(',', $ot_9_hour_type);
        return $typeArr;
    }

    //审批超时 拒绝 发消息
    public function sendMessage($detail, $state, $isUnpaid = false)
    {
        if (!in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_TIMEOUT])) {
            return true;
        }
        $title = $content = $suffix = '';
        //个人代理翻译后缀
        if($isUnpaid){
            $suffix = '_unpaid';
        }
        if ($state == enums::APPROVAL_STATUS_REJECTED) {
            $title   = 'ot_reject_title' . $suffix;
            $content = 'ot_reject_content' . $suffix;
        }
        if ($state == enums::APPROVAL_STATUS_TIMEOUT) {
            $title   = 'ot_timeout_title' . $suffix;
            $content = 'ot_timeout_content' . $suffix;
        }

        $locale       = getCountryDefaultLang();
        $hc_re        = new HcRepository($this->timeZone);
        $staffAccount = $hc_re->getStaffAcceptLang([$detail['staff_id']]);
        foreach ($staffAccount as $account) {
            $locale = strtolower(substr($account['accept_language'], 0, 2)) ?: $locale;
        }
        //整理变量
        $server            = Tools::reBuildCountryInstance($this, [$locale, $this->timezone]);
        $typeList          = $server->getAllOtType($locale);
        $typeList          = array_column($typeList, 'msg', 'code');
        $bind['type_text'] = $typeList[$detail['type']];
        //开始结束时间 注意一小时
        $bind['ot_start'] = date('Y-m-d H:i', strtotime($detail['start_time']));
        $bind['ot_end']   = date('Y-m-d H:i', strtotime($detail['end_time']));
        $holidayType      = $server->holidayOt;
        if (!isCountry('MY') && in_array($detail['type'], array_keys($holidayType)) && $detail['duration'] == 8) {
            $bind['ot_end'] = date('Y-m-d H:i', strtotime($detail['end_time']) + 3600);
        }

        $title   = $this->getTranslation($locale)->_($title);
        $content = $this->getTranslation($locale)->_($content, $bind);

        if (RUNTIME == 'dev') {//测试用 看push 发的对不对
            $title = $detail['staff_id'].$title;
        }
        $send_message = [
            'staff_users'        => [$detail['staff_id']],
            'message_title'      => $title,
            'message_content'    => "<p style='font-size: 32px;'>".$content."</p>",
            'staff_info_ids_str' => $detail['staff_id'],
            'category'           => -1,
            'push_state'         => 1,
            'id'                 => time().$detail['staff_id'].rand(1000000, 9999999),
        ];
        $hcm_rpc      = new ApiClient('hcm_rpc', '', 'add_kit_message',$locale);
        $hcm_rpc->setParams($send_message);
        $res = $hcm_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'overtime sendMessage',
                'message'  => '消息发送失败',
                'params'   => $send_message,
                'result'   => $res,
            ]);
            return true;
        }

        //发push
        $scheme     = "flashbackyard://fe/page?path=message&messageid=".$send_message['id'];
        PushServer::getInstance($locale, $this->timeZone)->sendPush($detail['staff_id'], $title, $content, $scheme, 'backyard');
        PushServer::getInstance($locale, $this->timeZone)->sendPush($detail['staff_id'], $title, $content, $scheme, 'kit');
    }

    //整理根据配置返回的 类型数组 对应的加班类型
    public function formatPermissionOvertimeType($dataList, $typeList){
        if(empty($typeList)){
            return [];
        }
        if(!is_array($typeList)){
            return [];
        }
        $data   = array_column($dataList, null, 'code');
        $return = [];
        foreach ($typeList as $type) {
            $return[] = $data[$type];
        }
        return $return;
    }

    //获取 加班类型 对应的 小时枚举 每个人不一样 走规则配置
    public function getTypeDuration($data){
        $position = HrStaffInfoPositionModel::find("staff_info_id = {$this->staffInfo['staff_info_id']}")->toArray();
        $position = empty($position) ? [] : array_column($position, 'position_category');
        $params = [
            'w_f_condition_staff_id'           => $this->staffInfo['staff_info_id'],
            'w_f_condition_store_id'           => $this->staffInfo['sys_store_id'],
            'w_f_condition_job_title'          => $this->staffInfo['job_title'],
            'w_f_condition_sex'                => $this->staffInfo['sex'],
            'w_f_condition_state'              => $this->staffInfo['state'],
            'w_f_condition_job_title_grade_v2' => $this->staffInfo['job_title_grade_v2'],
            'w_f_condition_node_department_id' => $this->staffInfo['node_department_id'],
            'w_f_condition_hire_type'          => $this->staffInfo['hire_type'],
            'w_f_condition_category'           => $this->staffInfo['category'],
            'w_f_condition_probation'          => $this->staffInfo['status'] == HrProbationModel::STATUS_FORMAL ? 1 : 2,
            'w_f_condition_position_category'  => $position,
            'w_f_condition_nationality'        => $this->staffInfo['nationality'],
        ];

        // 配置 获取加班类型
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OT_apply_rules')
            ->loadParameters($params)
            ->getConfig();

        $setting = $res['response_data'] ?? [];
        $data = $this->formatPermissionOvertimeType($data, $setting);

        //2-8小时 产品说 1-8小时
        $i = 1;
        $limitDuration = [];
        while ($i <= 8){
            $limitDuration[] = $i;
            $i += 0.5;
        }

        foreach ($data as &$da) {
            $da['duration'] = [];
            $params['type'] = $da['code'];
            $res = ConditionsRulesServer::getInstance()
                ->setRuleKey('OT_Hours')
                ->loadParameters($params)
                ->getConfig();

            $durations = $res['response_data'] ?? '';

            if(empty($durations)){
                continue;
            }

            //整理时长
            $durations = str_replace('，', ',', $durations);
            $durations = explode(',', trim($durations));
            sort($durations);

            foreach ($durations as $d){
                if(!in_array($d, $limitDuration)){
                    continue;
                }
                $row['time_hour'] = (float)$d;
                $row['time_text'] = "{$d}h";
                $da['duration'][] = $row;
            }
        }
        return $data;
    }

    //oa 系统 获取 加班列表页 三个月的数据
    public function rpcOtList($param)
    {
        $return = ['count' => 0, 'list' => []];
        if (empty($param['operate_id'])) {
            return self::checkReturn(['data' => $return]);
        }
        $addHour = $this->config->application->add_hour;

        //[2]获取列表数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['a' => AuditApplyModel::class]);
        $builder->join(HrStaffInfoModel::class, 'a.submitter_id = s.staff_info_id', 's');
        $builder->join(AuditApprovalModel::class, 'a.flow_id = p.flow_id and a.biz_type = 4', 'p');
        $builder->join(HrOvertimeModel::class, 'o.overtime_id = a.biz_value', 'o');
        if (!empty($param['piece_id']) || !empty($param['region_id'])) {
            $builder->leftJoin(SysStoreModel::class, 's.sys_store_id = store.id', 'store');
        }

        $builder = $this->rpcBuildCondition($builder, $param);
        //分页 总数
        $totalCount      = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        $return['count'] = (int)$totalCount->total;
        if (empty($return['count'])) {
            return self::checkReturn(['data' => $return]);
        }
        $builder->columns("o.overtime_id,s.staff_info_id,s.name,a.final_approver,s.node_department_id,s.sys_department_id,
        s.sys_store_id as store_id,o.created_at,o.date_at,o.type,o.duration,a.updated_at,p.state approval_state,o.state,o.in_approval,o.references");

        $page   = $param['pageNum'] ?? 1;
        $size   = $param['pageSize'] ?? 20;
        $offset = $size * ($page - 1);
        $builder->limit($size, $offset);
        //排序
        $builder->orderBy("a.created_at desc");
        $data = $builder->getQuery()->execute()->toArray();

        //网点名称信息 store_name
        $storeIds    = array_column($data, 'store_id');
        $storeIds    = array_diff($storeIds, [null]);
        $storeIds    = array_values(array_unique($storeIds));
        $storeServer = new SysStoreServer($this->lang, $this->timezone);
        $storeInfo   = $storeServer->batchStorePieceRegion($storeIds);

        $dept_ids = array_column($data, 'node_department_id');
        if (!empty($dept_ids)) {
            //部门名称 department_name
            $departmentData = SysDepartmentModel::find([
                'columns'    => 'id, name',
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $dept_ids],
            ])->toArray();
        }

        $departmentData = empty($departmentData) ? [] : array_column($departmentData, 'name', 'id');


        $otServer    = new OvertimeServer($this->lang, $this->timezone);
        $otServer    = Tools::reBuildCountryInstance($otServer, [$this->lang, $this->timezone]);
        $allOtType   = $otServer->getAllOtType($this->lang);
        $allOtType   = array_column($allOtType, 'msg', 'code');
        $auditListRe = new AuditlistRepository($this->lang, $this->timezone);
        $envModel    = new SettingEnvServer();
        $nw_id       = $envModel->getSetVal('dept_network_management_id');

        foreach ($data as &$da) {
            $storeId               = $da['store_id'];
            $da['store_name']      = $storeInfo[$storeId]['store_name'] ?? '';
            $da['department_name'] = $departmentData[$da['node_department_id']] ?? '';
            $da['region_name']     = $storeInfo[$storeId]['region_name'] ?? '';
            $da['piece_name']      = $storeInfo[$storeId]['piece_name'] ?? '';
            $da['created_at']  = date('Y-m-d H:i:s', strtotime($da['created_at']) + ($addHour * 3600));//申请时间
            $da['updated_at']  = date('Y-m-d H:i:s', strtotime($da['updated_at']) + ($addHour * 3600));//审批时间
            $da['type_text']   = $allOtType[$da['type']] ?? '';//ot类型
            $da['duration']    = $da['duration'].'h';//时长
            $da['state_text']  = $auditListRe->getAuditState($da['state']);//审批状态
            if($da['in_approval'] == HrOvertimeModel::IN_APPROVAL){
                $da['state_text'] .= '(' . $this->getTranslation()->_('in_approval') . ')';
            }

            //如果 审核通过 并且当前登陆人是最后审批人 可以撤销
            $da['is_cancel_button'] = false;
            if($da['state'] == enums::$audit_status['approved'] && $da['final_approver'] == $param['operate_id']){
                $da['is_cancel_button'] = true;
            }
            //所在网点当天人效
            if (isCountry('PH')) {
                if ($nw_id == $da['sys_department_id']) {
                    //$otServer->getNwEffectDetail($da, $da['staff_info_id']);
                    $da['is_approval_reason'] = $otServer->getIsNeedApprovalReason();
                } else {
                    $da['is_approval_reason'] = 0;
                }
            }
        }

        $return['list'] = $data;
        return self::checkReturn(['data' => $return]);
    }

    //构造条件
    public function rpcBuildCondition($builder, $param)
    {
        $addHour = $this->config->application->add_hour;
        $builder->andWhere('p.approval_id = :approval_id:', ['approval_id' => $param['operate_id']]);//审批人
        //当前登陆人的待审批列表
        if(!empty($param['tab_type']) && $param['tab_type'] == 1){
            $builder->andWhere('p.state = 1');
        }else{
            //已处理tab页 不展示 当前登陆人待审批数据
            $builder->andWhere('p.state != 1');
            if(!empty($param['state'])){
                $builder->inWhere('o.state', $param['state']);//审批状态
            }
        }

        //申请人工号
        if (!empty($param['staff_info_id'])) {//submitter_id
            $builder->andWhere('a.submitter_id = :submitter_id:', ['submitter_id' => $param['staff_info_id']]);//申请人
        }

        if (!empty($param['apply_time_start'])) {
            //转零时区
            $start = date('Y-m-d H:i:s', strtotime("{$param['apply_time_start']} -{$addHour} hour"));
            $end   = date('Y-m-d H:i:s', strtotime("{$param['apply_time_end']} -{$addHour} hour +1 day") -1 );
            $builder->betweenWhere('o.created_at', $start, $end);
        }
        //ot 日期
        if (!empty($param['ot_date_start'])) {
            $builder->betweenWhere('o.date_at', $param['ot_date_start'], $param['ot_date_end']);
        }

        if (!empty($param['department_id'])) {
            //是否包含子部门
            if(!empty($param['is_sub_department'])){
                $ids = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($param['department_id']);
            }
            if(empty($ids)){
                $ids[] = $param['department_id'];
            }
            $builder->inWhere('s.node_department_id',$ids);//部门
        }

        if (!empty($param['store_id'])) {
            $builder->inWhere('s.sys_store_id', $param['store_id']);//网点id
        }

        if (!empty($param['piece_id'])) {
            $builder->inWhere('store.manage_piece', $param['piece_id']);//片区id
        }
        if (!empty($param['region_id'])) {
            $builder->andWhere('store.manage_region = :region_id:', ['region_id' => $param['region_id']]);//大区id
        }

        return $builder;
    }

    /**
     * 验证 支援 班次开始时间 和加班开始时间是不是一致 只针对 休息日1。3倍
     * 1. OT开始时间 不等于 支援班次开始时间
    1. 不允许提交，提示语：因当天需要支援，OT开始时间需从支援班次开始时间HH:MM开始申请
    2. 包括BY提交申请OT和HCM后台提交增加OT
     * @param $param
     * @param $limitTypes
     * @return bool
     */
    public function checkOtSupport($param, $limitTypes){
        if(empty($limitTypes) || !in_array($param['type'], $limitTypes)){
            return true;
        }
        //支援信息 1. 审批状态：待审批、已同意
        //2. 支援状态：待生效、已生效、已失效
        $supportInfo = HrStaffApplySupportStoreModel::findFirst([
            'columns' => 'staff_info_id,sub_staff_info_id,shift_start,shift_end',
            'conditions' => 'staff_info_id = :staff_id: and employment_begin_date <= :date_at: and employment_end_date >= :date_at: 
            and status in (1,2) and support_status in (1,2,3)',
            'bind' => ['staff_id' => $param['staff_id'], 'date_at' => $param['date_at']]
        ]);
        if(empty($supportInfo)){
            return true;
        }

        $supportShift = $param['date_at'] . ' ' . $supportInfo->shift_start;
        if(strtotime($param['start_time']) != strtotime($supportShift)){
            //因当天需要支援，OT开始时间需从支援班次开始时间HH:MM开始申请
            throw new ValidationException($this->getTranslation()->_('ot_support_start_notice',['shift_start' => $supportInfo->shift_start]));
        }

        return true;
    }


}
