<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/29/23
 * Time: 8:32 PM
 */

namespace FlashExpress\bi\App\Server;

use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use FlashExpress\bi\App\Enums\ActivityEnums;
use FlashExpress\bi\App\Models\backyard\ActivityRecordModel;

class AnniversaryServer extends ActivityServer
{
    protected $activityCode = ActivityRecordModel::CODE_ANNIVERSARY;

    public function initData($param)
    {
        parent::initData($param);
        //真实日期
        $realDate = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
        $year     = date('Y');
        if (!empty($realDate)) {
            //当年日期
            $this->activityDate = $year.date('-m-d', strtotime($realDate));
        }
    }

    public function getImgParam($staff)
    {
        $locale    = $staff['locale'];
        $file_path = APP_PATH."/views/activity/anniversary.ftl";
        //获取对应周年
        $year_num              = $this->getCycle($staff['activity_date'], $this->activityCode);
        $t_param['name']       = $staff['name'];
        $t_param['year_order'] = ActivityEnums::$enOrders[$year_num];
        //模板里面的参数
        $p['file_name']      = $this->activityCode;
        $tpl_url             = "anniversary_tpl_url_{$year_num}";
        $p['data']['bg_img'] = $this->setExpire(600)->getBgImgFromCache($tpl_url);
        //翻译文案变量
        $tKey = 'anniversary_content_'.intval($year_num);

        //html 样式要变
        $p['data']['class_1'] = 'p1';
        $p['data']['class_2'] = 'p2';
        $p['data']['class_3'] = 'p3';
        $p['data']['class_4'] = 'text_2';
        if($year_num >= 6){//6周年以后的样式换另外一个 字太长了
            $p['data']['class_1'] = 'years-p1';
            $p['data']['class_2'] = 'years-p2';
            $p['data']['class_3'] = 'years-p3';
            $p['data']['class_4'] = 'text_1';
        }

        //第一行翻译
        $p['data']['anniversary_head'] = $this->getTranslation($locale)->_('anniversary_head', ['name' => $staff['name']]);
        //第二行
        $p['data']['anniversary_content'] = $this->getTranslation($locale)->_($tKey);
        //第三行
        $orderKey                      = ActivityEnums::$enOrders[$year_num];
        $p['data']['anniversary_foot'] = $this->getTranslation($locale)->_('anniversary_foot', ['year_order' => $this->getTranslation($locale)->_($orderKey)]);

        //新增 二维码图片
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('Recruit_Anniversary')
            ->setParameters(['staff_info_id' => $staff['staff_info_id']])
            ->getConfig();
        $qrCodeUrl = $res['response_data'] ?? '';
        $qrCode = new QrCode($qrCodeUrl);
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $p['data']['qr_code'] = $qrCode->writeDataUri();
        $p['data']['activity_click_here'] = $this->getTranslation($locale)->_('activity_click_here');

        //语言环境code 对应 class
        $p['data']['country_code'] = substr(strtolower($locale),0,2);

        //图片尺寸
        $p['viewport']['width']  = ActivityEnums::IMG_WIDTH;
        $p['viewport']['height'] = ActivityEnums::IMG_HEIGHT;
        //获取 html 模板 oss
        if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
            $p['tpl_url'] = $this->getTplPath($file_path);
        } else {
            $p['tpl_url'] = $this->getTplPathFromCache($file_path);
        }
        return $p;
    }

}

