<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class InventoryMessage extends BaseMessageServer
{
    /**
     * @description 生成url，用于嵌入iframe
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s/#/%s?inventory_check_id=%s&msg_category=%d", env('sign_url'), $this->getNormalMessageUrl(),
            $this->getMessageResponseData()->getContent(),
            $this->getMessageResponseData()->getCategory()
        );
    }

    /**
     * @description 是否设置消息读取状态为已读
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        //未读
        return $this->getMessageResponseData()->getReadState() == MessageCourierModel::READ_STATE_UNREAD;
    }

    /**
     * @description 是否需要更改置顶状态为不置顶，，可继承自定义逻辑
     * @return bool
     */
    protected function isNeedChangeTopState(): bool
    {
        return true;
    }
}