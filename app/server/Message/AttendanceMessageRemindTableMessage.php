<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;


/**
 * 考勤消息提醒表格版本消息
 */
class AttendanceMessageRemindTableMessage extends BaseMessageServer
{
    public function getMessageDataContent($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('mc.id as msg_id,mc.title,m.message content');
        $builder->from(['mc' => MessageCourierModel::class]);
        $builder->innerJoin(MessageContentModel::class, 'mc.message_content_id = m.id', 'm');
        $builder->andWhere("mc.id = :message_id: and staff_info_id=:staff_info_id:",
            ['message_id' => $params['msg_id'], 'staff_info_id' => $params['staff_info_id']]);
        $builder->andWhere("mc.is_del = 0");
        $messageInfo            = $builder->getQuery()->getSingleResult()->toArray();
        $messageInfo['content'] = (array)json_decode($messageInfo['content'], true);
        return $messageInfo;
    }
}