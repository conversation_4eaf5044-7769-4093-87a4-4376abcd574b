<?php

namespace FlashExpress\bi\App\Server\Message;

use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Server\BaseServer;

abstract class BaseMessageServer extends BaseServer
{
    const SUBMIT_STATE_MUST_SUBMIT = 1; //表单必须提交
    //消息模版
    protected $message_template = "<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><div style='position:fixed;left:0;top:0;width:100%%;height:100%%'><iframe src='%s' width='100%%' height='100%%' frameborder='0'></iframe></div>";
    protected $message_data; //用于保存 MessageDataServer对象

    /**
     * @description 初始化: 加载消息请求数据、加载消息返回结构数据，加载消息子类自定义配置
     * @param MessageDataServer $message_data
     * @return $this
     */
    public function init(MessageDataServer $message_data): BaseMessageServer
    {
        $this->setMessageData($message_data);
        $this->loadIndividualConfig();
        return $this;
    }

    /**
     * @description 各个子类自定义加载项,可加载自定义数据等
     * @return void
     */
    protected function loadIndividualConfig()
    {
    }

    /**
     * @description 返回消息详情，返回MessageResponse对象的数组结构
     * @return array
     */
    public function getMessageDetail(): array
    {
        //自定定义逻辑
        $this->beforeExecute();

        $messageResponse = $this->getNormalMessage();

        //如果想用 MessageResponse::getContent() 请 替换为 MessageResponse::getContentId()
        //原因 $this->getNormalMessage() 这里  MessageResponse::setContent() 重新赋值了。

        //调整页眉、页脚内容
        if ($this->isChangeTopContent()) {
            $messageResponse->setTopText($this->changeTopContent());
        }
        if ($this->isChangeFootContent()) {
            $messageResponse->setFootText($this->changeFootContent());
        }

        //设置必填
        if ($this->isSetFormStateToMustSubmit()) {
            $messageResponse->setMustSubmit(self::SUBMIT_STATE_MUST_SUBMIT);
        }

        //自定定义逻辑
        $this->afterExecute();

        return $this->getMessageResponseData()->toArray();
    }

    /**
     * @description 更改页眉内容，可自定义返回内容
     * @return string
     */
    protected function changeTopContent(): string
    {
        return $this->getMessageResponseData()->getTopText();
    }

    /**
     * @description 更改页脚内容，可自定义返回内容
     * @return string
     */
    protected function changeFootContent(): string
    {
        return $this->getMessageResponseData()->getFootText();
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    protected function beforeExecute()
    {
    }

    /**
     * @description 实现该方法，可自定义逻辑
     * @return void
     */
    protected function afterExecute()
    {
        //设置已读
        if ($this->isSetReadStateToHasRead()) {
            $this->setMessageReadStatus($this->isNeedChangeTopState());
            //$this->getMessageResponseData()->setReadState(MessageCourierModel::READ_STATE_HAS_READ);
        }
    }

    /**
     * @description 是否需要更改置顶状态为不置顶，，可继承自定义逻辑
     * true=需要更新置顶状态，false=不更新置顶状态
     *
     * @return bool
     */
    protected function isNeedChangeTopState(): bool
    {
        return false;
    }

    /**
     * @description 是否设置消息读取状态为已读，如果设置会改变消息的已读状态
     * true=需要更新已读状态，false=不更新消息已读状态
     *
     * @return bool
     */
    protected function isSetReadStateToHasRead(): bool
    {
        return false;
    }

    /**
     * @description 是否设置消息提交状态为必须提交
     * true=需要更新必须提交状态，false=不更新消息必须提交状态
     *
     * @return bool
     */
    protected function isSetFormStateToMustSubmit(): bool
    {
        return false;
    }

    /**
     * @description 是否更改页脚
     * true=需要更新页脚，false=不更新消息页脚
     *
     * @return bool
     */
    protected function isChangeFootContent(): bool
    {
        return false;
    }

    /**
     * @description 是否更改页眉
     * true=需要更新页眉，false=不更新消息页眉
     *
     * @return bool
     */
    protected function isChangeTopContent(): bool
    {
        return false;
    }


    protected function isNeedSetDetailUrl(): bool
    {
        return true;
    }

    /**
     * @description 获取普通消息
     * @return MessageResponse
     *
     */
    protected function getNormalMessage(): MessageResponse
    {
        $messageDetailUrl = $this->generateMessageUrl();
        if (empty($messageDetailUrl)) {
            return $this->getMessageResponseData();
        }
        $responseObj = $this->getMessageResponseData();
        if ($this->isNeedSetDetailUrl()) {
            $responseObj->setMsgDetailUrl($messageDetailUrl);
        }
        return $responseObj->setContent($this->generateMessageContent($messageDetailUrl));
    }

    /**
     * @description 设置已读、置顶状态
     * @param bool $is_update_top_state
     */
    protected function setMessageReadStatus(bool $is_update_top_state = true)
    {
        $model = MessageCourierModel::findFirst([
            "conditions" => "id = :msg_id:",
            "bind"       => [
                "msg_id" => $this->getMessageResponseData()->getMsgId(),
            ],
        ]);
        if (empty($model)) {
            $this->logger->write_log(sprintf("empty data, msg_id : %s,please check data!",
                $this->getMessageResponseData()->getMsgId()));
            return;
        }
        $model->read_state = MessageCourierModel::READ_STATE_HAS_READ;
        if ($is_update_top_state) {
            $model->top_state = MessageCourierModel::TOP_STATE_UNSET_TOP;
        }
        $model->updated_at = gmdate("Y-m-d H:i:s");
        $model->save();
    }

    /**
     * @description 获取前端url地址
     * @return string
     */
    protected function getNormalMessageUrl(): string
    {
        switch ($this->getMessageResponseData()->getCategory()) {
            case MessageEnums::MESSAGE_CATEGORY_ORDINARY:
                $message = "public-msg";
                break;
            case MessageEnums::MESSAGE_CATEGORY_QUESTIONNAIRE:
                $message = "Questionnaire";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CORRECTION_LETTER:
                $message = "behaviorRectification";
                break;
            case MessageEnums::MESSAGE_CATEGORY_ANSWER_QUESTION:
                $message = "messageAnswer";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CONTRACT:
                $message = "electronic-contract-sign";
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRAINING:
                $message = "trainInfo";
                break;
            case MessageEnums::MESSAGE_CATEGORY_COMPLETE_RESUME:
                $message = "myResume";
                break;
            case MessageEnums::MESSAGE_CATEGORY_INTERVIEWS:
                $message = "interviewNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION:
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_MY:
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_MY_V2:
                $message = "evaluationNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_INVENTORY_CHECK:
            case MessageEnums::MESSAGE_CATEGORY_INVENTORY_CHECK_REMIND:
                $message = "inventory";
                break;
            case MessageEnums::MESSAGE_CATEGORY_GET_CONFIRM:
                $message = "classesNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_EVALUATION_SIGN_UP:
                $message = "evaluateNewsInfo";
                break;
            case MessageEnums::MESSAGE_CATEGORY_TP3:
            case MessageEnums::MESSAGE_CATEGORY_TP3_REFILL:
                $message = "TP3News";
                break;
            case MessageEnums::MESSAGE_CATEGORY_SALARY_APPROVAL:
            case MessageEnums::MESSAGE_CATEGORY_OFFER_SIGN_APPROVAL:
                $message = "salary-offer-approve";
                break;
            case MessageEnums::MESSAGE_CATEGORY_COURIER_COMMITMENT_LETTER:
                $message = "loapoa-news";
                break;
            case MessageEnums::MESSAGE_CATEGORY_RESUME_RECOMMEND:
            case MessageEnums::MESSAGE_CATEGORY_RESUME_RECOMMEND_CANCELED:
                $message = "resume-filter";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CERTIFICATE_UPLOAD_REMINDER:
                $message = "IdCardNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_FEEDER:
                $message = "feeder";
                break;
            case MessageEnums::MESSAGE_CATEGORY_PLAN_STOCK_CHECK:
                $message = "stockPackageMsg";
                break;
            case MessageEnums::MESSAGE_CATEGORY_ARCHIVED:
                $message = "downloadContract";
                break;
            case MessageEnums::MESSAGE_CATEGORY_73:
                $message = "apologize";
                break;
            case MessageEnums::MESSAGE_CATEGORY_72:
                $message = "taxFillingMsg";
                break;
            case MessageEnums::MESSAGE_CATEGORY_74:
                $message = "pdf-220719";
                break;
            case MessageEnums::MESSAGE_CATEGORY_75:
                $message = "ot-tips-news";
                break;
            case MessageEnums::MESSAGE_CATEGORY_FLEET_SCHEDULE_NOTICE:
                $message = "jbcSchedulingNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_FINE:
            case MessageEnums::MESSAGE_CATEGORY_CODE_INTERIOR_ORDER:
            case MessageEnums::MESSAGE_CATEGORY_CODE_SALARY_TAX:
            case MessageEnums::MESSAGE_CATEGORY_LEAVE_ASSET:
            case MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY:
            case MessageEnums::MESSAGE_CATEGORY_CODE_FASTING:
            case MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN:
            case MessageEnums::MESSAGE_CATEGORY_CODE_SICK_CERTIFICATE:
                $message = $this->getMessageResponseData()->getContent();
                break;
            case MessageEnums::MESSAGE_CATEGORY_BANK_CARD_REFUSE:
                $message = "bankNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_BANK_CARD_REFUSE_PH:
                $message = "bankupbankNews";
                break;
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_AUTO_RECEPTION:
                $message = "asset-management-news";
                break;
            case MessageEnums::MESSAGE_CATEGORY_INSURANCE_BENEFICIARY:
                $message = "insurance-beneficiary-message";
                break;
            case MessageEnums::MESSAGE_CATEGORY_KPI_STAFF_CONFIRM:
                $message = "KPINews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_BANK_ACCOUNT:
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FILL_BANK_CARD_ANNEX'):
                $message = "bankTipNews";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_EPF_NO_NOTICE:
                $message = "epfNoNotice";
                break;
            case MessageEnums::MESSAGE_CATEGORY_MATERIAL_RECEIVING_REMINDER:
                $message = "consumables";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_EA_FORM_NOTICE:
                $message = "ea-form-message";
                break;
            case MessageEnums::MESSAGE_CATEGORY_RENEW_CONTRACT_PROPOSE:
                $message ='renewal-proposal-news';
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_INQUIRING_ABOUT_TASKS:
                $message = "inquire-tasks";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_INVOICE:
                $message = "invoice-message";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_PAYMENT_VOUCHER:
                $message = "payment-voucher-message";
                break;
            case MessageEnums::MESSAGE_CATEGORY_CODE_TAX_WITHHOLDING_VOUCHER:
                $message = "tax-withholding-voucher-message";
                break;
            case MessageEnums::MESSAGE_MY_PAYSLIP:
                $message = "payslip-message";
                break;
            case MessageEnums::MESSAGE_CATEGORY_AUTO_TRANSFER_TO_PERSONAL_AGENT:
                $message = 'asset-proxy-message';
                break;
            case MessageEnums::MESSAGE_CATEGORY_TRANSFER_NOTICE:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER:
            case MessageEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_PENDING_RECEIPT_REMINDER:
                $message = 'asset-ledger-message';
                break;
            case MessageEnums::MESSAGE_CATEGORY_ASSET_RETURN_REJECT:
            case MessageEnums::MESSAGE_CATEGORY_ASSET_RETURN_FINISHED:
                $message = 'asset-return-message';
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_CODE_RESIDENCE_BOOKLET'):
                $message = "residenceBooklet";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY')://合同审核业务
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY_V2')://合同审核业务
                $message = "person-contract-expires-renewal-msg";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_APPLY_V3')://合同审核业务V3
                $message = "personal-renewal-reminder-message";
                break;
            //合同信函
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_NOTICE'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_BUSINESS_CONTRACT_LETTER_REJECT'):
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_AGENT_CONTRACT_ATTACHMENT'):
                $message = "ic-contract-expries-attachments-msg";
                break;
            case EnumSingleton::getInstance()->getEnums('RECEIVED_NOT_PLACED_WAREHOUSE')://已揽收未入仓
                $message = "received-not-placed-warehouse";
                break;
            case MessageEnums::MESSAGE_CATEGORY_ATTENDANCE_MESSAGE_REMIND_TABLE:
                $message = "attendance-remind";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_VAN_CONTAINER_REJECT')://车厢类型变更申请 驳回消息
                $message = "car-information-reject-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_VAN_CONTAINER')://转岗发送车厢信息
                $message = "car-information-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_PASS')://耗材申请-审批通过
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_WMS_APPLY_AUDIT_REJECT')://耗材申请-审批驳回
                $message = 'wms-approval-message';
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_PASS')://资产申请-审批通过
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_ASSET_APPLY_AUDIT_REJECT')://资产申请-审批驳回
                $message = 'asset-approval-message';
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SX_CARD')://TH神仙卡限制
                $message = "sx-sim-check";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET'): //试用期目标
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET_BP'): //试用期目标-BP
                $message = "regular-assess-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PAYROLL_FILE_2316')://2316文件，签字消息
                $message = "payroll-2316-file-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_SEND_PDF'):
                $content = $this->getMessageResponseData()->getContent();
                $message = "conversion-evaluation" . "?{$content}";//pdf 地址 pdf_url=xxxxx
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FACE_BLACKLIST')://人脸黑名单
                $message = "facial-blacklist-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_IC_SETTLEMENT_MSG')://PH个人代理结算
                $message = "cost-detail";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COURIER_VEHICLE_INSPECTION')://快递员车辆稽查消息
                $message = "vehicle-inspection-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_STAGE_DONE')://试用期转正评估每阶段完成签字消息
                $message = "regular-assess-result-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_NEW_GOODS_NOTICE')://商城上新通知
                $message = "goods-message";
                break;
            case EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_HUB_FULL_ATTENDANCE')://hub全勤奖
                $message = "hub-full-attendance-message";
                break;
            default:
                $message = "";
        }

        return $message;
    }

    /**
     * @description 生成url，用于嵌入iframe 【!!! 如果返回空，则返回content显示内容为数据库中message_content表中message字段内容】
     * @return string
     */
    protected function generateMessageUrl(): string
    {
        return sprintf("%s/#/%s?msg_id=%s", env('sign_url'), $this->getNormalMessageUrl(),
            $this->getMessageResponseData()->getMsgId());
    }

    /**
     * @description 生成普通类型消息，消息内容iframe嵌入url
     * @param $message_url
     * @return string
     */
    protected function generateMessageContent($message_url): string
    {
        return sprintf($this->message_template, $message_url);
    }

    /**
     * @description 获取MessageResponse对象
     * @return MessageResponse
     */
    public function getMessageResponseData(): MessageResponse
    {
        return $this->getMessageData()->getMessageResponse();
    }

    /**
     * @description 获取MessageRequest对象
     * @return mixed
     */
    public function getMessageRequestData(): MessageRequest
    {
        return $this->getMessageData()->getMessageRequest();
    }

    /**
     * @description 获取MessageDataServer对象
     * @return mixed
     */
    public function getMessageData(): MessageDataServer
    {
        return $this->message_data;
    }

    /**
     * @description 保存MessageDataServer对象
     * @param mixed $message_data
     */
    public function setMessageData($message_data): void
    {
        $this->message_data = $message_data;
    }

    protected function delayMsgIsMustSubmit(): bool
    {
        $messageData      = $this->getMessageResponseData();
        $redisObj         = $this->getDI()->get('redisLib');
        $delayMsgCacheKey = sprintf(RedisEnums::DELAY_MESSAGE, $messageData->getStaffInfoId());
        $cacheDelayData   = $redisObj->get($delayMsgCacheKey);

        $check = true;
        if (!empty($cacheDelayData) && in_array($messageData->getMsgId(), json_decode($cacheDelayData, true))) {
            $check = false;
        }
        return $check;
    }
}