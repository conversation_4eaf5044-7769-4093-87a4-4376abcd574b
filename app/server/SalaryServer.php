<?php

namespace FlashExpress\bi\App\Server;


use app\enums\StaffAuditTypeEnums;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveAllowanceModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\OtherRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\StaffAuditToolLog;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Repository\WmsRepository;
use Phalcon\Db;
use Phalcon\Db\Column;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;

class SalaryServer extends AuditBaseServer
{
    public      $timezone;

    public  static   $params = [];
    /**
     * 薪资单位
     */
    const SALARY_UNIT = [
        'THB' => 1,
        'PHP' => 2,
        'VND' => 3,
        'MYR' => 4,
        'LAK' => 5,
        'IN' => 6,
        'SGD' => 7,
    ];


    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->lang = $lang;
        $this->timezone = $timezone;
        parent::__construct($lang, null);
        $this->wf = new WorkflowServer($this->lang, $this->timezone);
        $this->other = new OtherRepository($timezone, $lang);
        $this->pub = new PublicRepository($timezone);
        $this->wms = new WmsRepository($timezone);
        $this->auditlist = new AuditlistRepository($lang, $timezone);
        $this->auditToolLog = new StaffAuditToolLog();
    }

    /**
     * 面试待发offer
     */
    const STATE_INTERVIEW_WAIT_SEND_OFFER = 20;
    const STATE_INTERVIEW_SEND_OFFER = 25;
    const STATE_INTERVIEW_REVOKED_OFFER = 30;
    const STATE_INTERVIEW_CANCEL_OFFER = 31;
    const STATE_INTERVIEW_ENTRY = 40;

    // 薪资查看权限工号
    const SALARY_VIEW_PERMISSION = [21358, 33287, 31856, 17758, 28228, 56780, 23357, 21899, 30555, 24715];

    /**
     * 最大50000泰铢
     */
    const MAX_BASICSALARY = 50000;

    const STATUS_NO = 0; //  不需要审批
    const STATUS_PANDING = 1; // 待审批
    const STATUS_APPROVAED = 2; // 已同意
    const STATUS_DISMISSSED = 3; // 已驳回
    const STATUS_REVOKED = 4; // 已撤销

    const STATUS_APPROVAED_20 = 20; // 已同意
    const STATUS_APPROVAED_21 = 21; // 已同意 已离职自动通过
    const STATUS_APPROVAED_22 = 22; // 已同意 已停职自动通过
    const STATUS_APPROVAED_23 = 23; // 已同意 已停职自动通过


	const STATE_1 = 1;//  1 通过
	const STATE_2 = 2; // 通过进入下一轮
	const STATE_3 = 3; // 不通过
	const STATE_0 = 0; // 终试通过

    const STATUS_DEL = 9; // 删除


    const STATUS_PANDING_APPROVAL = 7; // 待审批


    /**
     * workflow role
     */
    const WORKFLOW_ROLE_CEO = 'salary_approve_ceo';
    const WORKFLOW_ROLE = 'salary_approve';


    /**
     * 审批
     *
     * @param $userinfo
     * @param $id
     * @param $status
     * @param $remark
     * @return array
     *
     */
    public function updateApprove($userinfo, $id, $status, $remark)
    {
        $userinfo['staff_id'] = $userinfo['id'];
        $info = $this->getSalaryInfo($id);
        $db = $this->getDI()->get("db");

        self::$params = [
            'id' => $id,
            'userinfo' => $userinfo,
            'status' => $status,
            'revoke_remark' => $remark
        ];

        if (strtolower(env('country_code', 'Th')) == 'th') { // 泰国
            $auditApply = AuditApplyModel::findFirst([
                'conditions' => ' biz_value = :biz_value: and biz_type = :biz_type: ',
                'bind' => ['biz_value' => $id, 'biz_type' => enums::$audit_type['SA']]
            ]);

            if ($auditApply) {
                try {
                    $db->begin();
                    if ($status == enums::APPROVAL_STATUS_APPROVAL) {
                        (new ApprovalServer($this->lang, $this->timezone))->approval(
                            $id,
                            enums::$audit_type['SA'],
                            $userinfo['id']
                        );
                    } else if ($status == enums::APPROVAL_STATUS_REJECTED) {
                        (new ApprovalServer($this->lang, $this->timezone))->reject(
                            $id,
                            enums::$audit_type['SA'],
                            $remark,
                            $userinfo['id']
                        );

                    } else if ($status == enums::APPROVAL_STATUS_CANCEL) {
                        (new ApprovalServer($this->lang, $this->timezone))->cancel(
                            $id,
                            enums::$audit_type['SA'],
                            $remark,
                            $userinfo['id']
                        );
                    } else {
                        throw new \Exception('no exist status ' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                    }

                    $db->commit();
                    return $this->checkReturn(1);
                } catch (\Exception $e) {
                    $db->rollback();
                    $this->getDI()->get("logger")->write_log("salary_approval_error: 事务未提交"
                        . json_encode($userinfo, JSON_UNESCAPED_UNICODE)
                        . " id: " . $id
                        . " message: " . $e->getMessage()
                        . " line: " . $e->getLine()
                        . " file: " . $e->getFile());
                    return $this->checkReturn(["code" => -1, "msg" =>  $this->getTranslation()->_("4008")]);
                }
            }else{
                $model = SalaryApproveModel::findFirst($id);
                if($model){
                    $model->status = enums::APPROVAL_STATUS_CANCEL;
                    $model->save();
                }
            }

            return $this->checkReturn(1);
        } else {
             // 新审批流
            try {
                $db->begin();
                if ($status == enums::APPROVAL_STATUS_APPROVAL) {
                    (new ApprovalServer($this->lang, $this->timezone))->approval(
                        $id,
                        enums::$audit_type['SA'],
                        $userinfo['id']
                    );
                } else if ($status == enums::APPROVAL_STATUS_REJECTED) {
                    (new ApprovalServer($this->lang, $this->timezone))->reject(
                        $id,
                        enums::$audit_type['SA'],
                        $remark,
                        $userinfo['id']
                    );

                } else if ($status == enums::APPROVAL_STATUS_CANCEL) {
                    (new ApprovalServer($this->lang, $this->timezone))->cancel(
                        $id,
                        enums::$audit_type['SA'],
                        $remark,
                        $userinfo['id']
                    );
                } else {
                    throw new \Exception('no exist status ' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                }

                $db->commit();
                return $this->checkReturn(1);
            } catch (\Exception $e) {
                $db->rollback();
                $this->getDI()->get("logger")->write_log("salary_approval_error: 事务未提交"
                    . json_encode($userinfo, JSON_UNESCAPED_UNICODE)
                    . " id: " . $id
                    . " message: " . $e->getMessage()
                    . " line: " . $e->getLine()
                    . " file: " . $e->getFile());
                return $this->checkReturn(["code" => -1, "msg" =>  $this->getTranslation()->_("4008")]);
            }
        }

    }

    private function approvalStatusMaps($status)
    {
        $maps = [
            self::STATUS_PANDING => enums::$audit_list_status['panding'],
            self::STATUS_APPROVAED => enums::$audit_list_status['approved_approval'],
            self::STATUS_REVOKED => enums::$audit_list_status['revoked'],
            self::STATUS_DISMISSSED => enums::$audit_list_status['dismissed'],
        ];

        return $maps[$status];
    }

    /**
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $basicSalary
     * @param $trialSalary
     * @param $renting
     * @param $inSalaryRange
     * @param $positionAllowance
     * @param $deminimis_benefits
     * @param $other_non_taxable_allowance
     * @param $xinghuoAllowance
     * @return array
     *
     */
    public function isCanApproval_v2(
        $resumeId,
        $jobId,
        $userinfo,
        $basicSalary,
        $trialSalary,
        $renting,
        $inSalaryRange,
        $positionAllowance,
        $deminimis_benefits = 0,
        $other_non_taxable_allowance = 0,
        $xinghuoAllowance,
        $currency = SalaryApproveModel::CURRENCY_THB,
        $subsidy_type = SalaryApproveModel::SUBSIDY_TYPE_NOT_SELECT
    )
    {

        $this->getDI()->get('logger')->write_log("addApprove isCanapproval" . json_encode(func_get_args()), 'info');
        $data = $this->getLastApprove($resumeId);
        if ($data && in_array($data['status'], [self::STATUS_NO, self::STATUS_APPROVAED])) {
            return $this->checkReturn(1);
        }

        if (!$basicSalary) {

            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('basic_salary_is_null')
            ]);
        }


        if (!$trialSalary) {
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('salary_is_null')
            ]);
        }

        $data = $this->isCanAddApprove($resumeId); // 是否是待发offer状态
        if (!$data) {
            // 简历状态不正确
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('please try again')
            ]);
        }

//        if (
//            ($trialSalary + $renting) >= 50000 && strtolower(env('country_code', 'Th')) == 'ph'
//            ||
//            ($trialSalary + $renting) >= 9750000 && strtolower(env('country_code', 'Th')) == 'la'
//            ||
//            ($trialSalary + $renting) >= 3800 && strtolower(env('country_code', 'Th')) == 'my'
//        ) {
//            // 需要进行薪资审批
//            return $this->checkReturn(-3, $this->getTranslation()->_("please_submit_approval"));
//        } else {
//            if (in_array($jobId, $this->allowJobIds())){
//                // 指定职位 不需要进行薪资审批
//                return $this->checkReturn(1);
//            } else {
//
//                return $this->checkReturn(-3, $this->getTranslation()->_("please_submit_approval"));
//            }
//        }

        if (
        (isCountry(['Th', 'Id'])) && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary + $positionAllowance + $xinghuoAllowance , $currency, $data)
            ||
            isCountry('Ph') && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary + $deminimis_benefits + $other_non_taxable_allowance + $xinghuoAllowance)
            ||
            isCountry('Vn') && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary + $xinghuoAllowance)
            ||
            isCountry('My') && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary + $xinghuoAllowance)
            ||
            isCountry('LA') && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary + $xinghuoAllowance)
            ||
            !isCountry() && !isCountry('Ph') && !isCountry('LA') && !isCountry('VN') && !isCountry('MY') && !isCountry('Id') && $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $inSalaryRange, $renting + $trialSalary)
        ) {
            return $this->checkReturn([
                "code" => -3,
                "msg" => $this->getTranslation()->_('please_submit_approval')
            ]);

        }

        return $this->checkReturn(1);
    }

    /**
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $money
     * @param $trialSalary
     * @return array
     *
     */
    public function isCanApproval_v4($resumeId, $jobId, $userinfo, $money, $trialSalary)
    {
        $this->getDI()->get('logger')->write_log("addApprove isCanApproval_v4" . json_encode(func_get_args()), 'info');
        $data = $this->getLastApprove($resumeId);
        if ($data && in_array($data['status'], [self::STATUS_NO, self::STATUS_APPROVAED])) {
            return $this->checkReturn(1);
        }
        if (!$money) {
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('basic_salary_is_null')
            ]);
        }

        if (!$trialSalary) {
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('salary_is_null')
            ]);
        }
        $data = $this->isCanAddApprove($resumeId); // 是否是待发offer状态
        if (!$data) {
            // 简历状态不正确
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('please try again')
            ]);
        }

        if ($data['nationality'] == 2) {
            $isCan = $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], 0, $trialSalary['money']);
        } else {
            $isCan = $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], 0, $trialSalary['money'] + $trialSalary['house_rental'] + $trialSalary['xinghuo_allowance'] + $trialSalary['language_allowance']);
        }

        if ($isCan) {
            return $this->checkReturn([
                "code" => -3,
                "msg" => $this->getTranslation()->_('please_submit_approval')
            ]);
        }

        return $this->checkReturn(1);
    }


    public function isCanApproval_v3($resumeId, $jobId, $userinfo, $basicSalary, $trialSalary, $renting)
    {
        $this->getDI()->get('logger')->write_log("addApprove isCanapproval" . json_encode(func_get_args()), 'info');
        $data = $this->getLastApprove($resumeId);
        if ($data && in_array($data['status'], [self::STATUS_NO, self::STATUS_APPROVAED])) {
            return $this->checkReturn(1);
        }
        if (!$basicSalary || !$trialSalary) {
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('salary_is_null')
            ]);
        }

        $data = $this->isCanAddApprove($resumeId); // 是否是待发offer状态
        if (!$data) {
            // 简历状态不正确
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('please try again')
            ]);
        }

        if (in_array($jobId, $this->firstJobIds()) && $trialSalary < 12900000) {
            // 不需要进行薪资审批

            return $this->checkReturn(1);
        }

        return $this->checkReturn(-3, $this->getTranslation()->_("please_submit_approval"));

    }

    /**
     * 发送offer判断是否需要薪资审批
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $basicSalary
     * @param $trialSalary
     * @param $renting
     */
    public function isCanApproval($resumeId, $jobId, $userinfo, $basicSalary, $trialSalary, $renting)
    {
        $this->getDI()->get('logger')->write_log("addApprove isCanapproval" . json_encode(func_get_args()), 'info');
        $data = $this->getLastApprove($resumeId);
        if ($data && in_array($data['status'], [self::STATUS_NO, self::STATUS_APPROVAED])) {
            return $this->checkReturn(1);
        }
        if (!$basicSalary || !$trialSalary) {
            if ($data) {
                if ($data['basic_salary'] && $data['trial_salary']) {
                    // 发送了薪资审批且薪资都有值
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => $this->getTranslation()->_('salary_is_null')
                    ]);
                }
            } else {
                return $this->checkReturn([
                    "code" => -1,
                    "msg" => $this->getTranslation()->_('salary_is_null')
                ]);
            }
        }
        $data = $this->isCanAddApprove($resumeId);
        $departmentId = $data['hc_department_id'];
        if ($trialSalary + $renting >= self::MAX_BASICSALARY) {
            // 审批流 1
            $workflowRole = self::WORKFLOW_ROLE_CEO;
        } else {
            //
            // 是否是部门且他的一级部门 和 指定职位
            //
            if (
                ($jobId == enums::$job_title['branch_supervisor'] && ($departmentId == enums::$department['Network Management'] || enums::$department['Network Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['assistant_branch_supervisor'] && ($departmentId == enums::$department['Network Management'] ||  enums::$department['Network Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['van_courier'] && ($departmentId == enums::$department['Network Management'] || enums::$department['Network Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['bike_courier'] && ($departmentId == enums::$department['Network Management'] ||  enums::$department['Network Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['dc_officer'] && ($departmentId == enums::$department['Network Management'] ||  enums::$department['Network Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['hub_staff'] && ($departmentId == enums::$department['Hub Management'] || enums::$department['Hub Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['shop_supervisor'] && ($departmentId == enums::$department['Shop Management'] || enums::$department['Shop Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['shop_officer'] && ($departmentId == enums::$department['Shop Management'] || enums::$department['Shop Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['shop_cashier'] && ($departmentId == enums::$department['Shop Management'] ||  enums::$department['Shop Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['shop_bike'] && ($departmentId == enums::$department['Shop Management'] || enums::$department['Shop Management'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['warehouse_staff'] && ($departmentId == enums::$department['Warehouse Operations'] || enums::$department['Warehouse Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['store_officer'] && (in_array($departmentId, [
                            enums::$department['Shop Management'],
                            enums::$department['University Project']
                        ]) || in_array($this->getFirstDepartmentId($departmentId), [
                            enums::$department['Shop Management'],
                            enums::$department['University Project'],
                        ])))
                || ($jobId == enums::$job_title['boat_courier'] && (in_array($departmentId, [
                            enums::$department['Network Management'],
                            enums::$department['Network Planning'],
                            enums::$department['Network Training'],
                            enums::$department['Network Operations'],
                            enums::$department['Network Support'],
                            enums::$department['Network Bulky'],
                        ]) || in_array($this->getFirstDepartmentId($departmentId), [
                            enums::$department['Network Management'],
                            enums::$department['Network Planning'],
                            enums::$department['Network Training'],
                            enums::$department['Network Operations'],
                            enums::$department['Network Support'],
                            enums::$department['Network Bulky'],
                        ])))
                || ($jobId == enums::$job_title['fleet_driver'] && (in_array($departmentId, [
                            enums::$department['Transportation'],
                            enums::$department['fleet_management'],
                        ]) || in_array($this->getFirstDepartmentId($departmentId), [
                            enums::$department['Transportation'],
                            enums::$department['fleet_management'],
                        ])))
                || ($jobId == enums::$job_title['freight_hub_staff'] && ($departmentId == enums::$department['flash_freight_hub'] || enums::$department['flash_freight_hub'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['freight_hub_staff'] && ($departmentId == enums::$department['freight_hub_north_area'] || enums::$department['freight_hub_north_area'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['onsite_staff'] && ($departmentId == enums::$department['onsite_managemnet'] || enums::$department['onsite_managemnet'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['branch_supervisor'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['assistant_branch_supervisor'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['van_courier'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['bike_courier'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['dc_officer'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
                || ($jobId == enums::$job_title['boat_courier'] && ($departmentId == enums::$department['Network Bulky Operations'] || enums::$department['Network Bulky Operations'] == $this->getFirstDepartmentId($departmentId)))
            ) {
                // 不需要审批
//                $db->insertAsDict("salary_approve", [
//                    "resume_id" => $resumeId,
//                    "serial_no" => "SA" . $this->getID(),
//                    "submitter_id" => $userinfo['id'],
//                    "job_id" => $jobId,
//                    "workflow_role" => '',
//                    "basic_salary" => $basicSalary,
//                    "trial_salary" => $trialSalary,
//                    "status" => self::STATUS_NO, // 不需要审批
//                ]);
                return $this->checkReturn(1);
            } else {
                // 审批流 2
                $workflowRole = self::WORKFLOW_ROLE;
            }
        }

        if (isset($workflowRole)) {
            return $this->checkReturn(-3, $this->getTranslation()->_("please_submit_approval"));
        }
    }

    public function allowJobIds()
    {
        return [
            enums::$job_title['dc_supervisor'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['assistant_branch_supervisor'],
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['dc_officer'],
            enums::$job_title['hub_operator'],
            enums::$job_title['hub_staff'],
            enums::$job_title['shop_supervisor'],
            enums::$job_title['shop_officer'],
            enums::$job_title['shop_bike'],
            enums::$job_title['shop_cashier'],
            enums::$job_title['warehouse_staff'],
            enums::$job_title['freight_hub_staff'],
            enums::$job_title['fleet_driver'],
            enums::$job_title['store_officer'],
            enums::$job_title['boat_courier'],
            enums::$job_title['onsite_officer'],
        ];
    }


    /**
     *
     * @param $resumeId
     * @return array
     *
     */
    public function salaryItems($resumeId)
    {
        $lastResume = $this->getLastApprove($resumeId);
        $data = $this->isCanAddApprove($resumeId);
        $approveAllowance = SalaryApproveAllowanceModel::findFirst([
            'conditions' => ' approve_id = :approve_id: ',
            'bind' => ['approve_id' => $lastResume['id']]
        ]);
        $approveAllowance = $approveAllowance ? $approveAllowance->toArray() : [];

        $money = [
            'money' =>(int) $lastResume['money'] * 100,
            'basic_salary' => (int) $lastResume['basic_salary'] * 100,
            'welfare_allowance' => (int)$lastResume['welfare_allowance'] * 100,
            'variable_allowance' => (int)$lastResume['variable_allowance'] * 100,
            'food_allowance' => (int)$lastResume['food_allowance'] * 100,
            'attendance_allowance' => (int)$lastResume['attendance_allowance'] * 100,
            'transport_allowance' => (int)$lastResume['transport_allowance'] * 100,
            'house_rental' => (int)$lastResume['renting'] * 100,
            'xinghuo_allowance' =>(int) $lastResume['xinghuo_allowance'] * 100,
            'language_allowance' => (int)$lastResume['language_allowance'] * 100,
            'is_computer_allowance' => intval($lastResume['is_computer_allowance'] ?? 0),
            'is_camera_allowance' => intval($lastResume['is_camera_allowance'] ?? 0),
        ];
        $trail_salary = [
            'money' => (int) (isset($approveAllowance['money']) &&  $approveAllowance['money'] ? $approveAllowance['money'] * 100 : 0),
            'basic_salary' => (int) (isset($approveAllowance['basic_salary']) && $approveAllowance['basic_salary'] ? $approveAllowance['basic_salary'] * 100 : 0),
            'welfare_allowance' => (int) (isset($approveAllowance['welfare_allowance']) && $approveAllowance['welfare_allowance'] ? $approveAllowance['welfare_allowance'] * 100 : 0),
            'variable_allowance' => (int) (isset($approveAllowance['variable_allowance']) && $approveAllowance['variable_allowance'] ? $approveAllowance['variable_allowance'] * 100 : 0),
            'food_allowance' =>(int) (isset($approveAllowance['food_allowance']) && $approveAllowance['food_allowance'] ? $approveAllowance['food_allowance'] * 100 : 0),
            'attendance_allowance' => (int) (isset($approveAllowance['attendance_allowance']) && $approveAllowance['attendance_allowance'] ? $approveAllowance['attendance_allowance'] * 100 : 0),
            'transport_allowance' => (int) (isset($approveAllowance['transport_allowance']) && $approveAllowance['transport_allowance'] ? $approveAllowance['transport_allowance'] * 100 : 0),
            'house_rental' => (int) (isset($approveAllowance['house_rental']) && $approveAllowance['house_rental'] ? $approveAllowance['house_rental'] * 100 : 0),
            'xinghuo_allowance' => (int) (isset($approveAllowance['xinghuo_allowance']) && $approveAllowance['xinghuo_allowance'] ? $approveAllowance['xinghuo_allowance'] * 100 : 0),
            'language_allowance' =>(int) (isset($approveAllowance['language_allowance']) && $approveAllowance['language_allowance'] ? $approveAllowance['language_allowance'] * 100 : 0),
            'is_computer_allowance' => intval($approveAllowance['is_computer_allowance'] ?? 0),
            'is_camera_allowance' => intval($approveAllowance['is_camera_allowance'] ?? 0),

        ];
        if ($data['nationality'] == 2) {
            unset($money['welfare_allowance']);
            unset($money['xinghuo_allowance']);
            unset($money['language_allowance']);
            unset($trail_salary['welfare_allowance']);
            unset($trail_salary['xinghuo_allowance']);
            unset($trail_salary['language_allowance']);
        }

        return $this->checkReturn(['data' => [
            'nationality' => (int) $data['nationality'],
            'money' => $money,
            'trial_salary' => $trail_salary,
        ]]);
    }

    /**
     *
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $workDays
     * @param $money
     * @param $trialSalary
     * @return array|void
     *
     */
    public function addApprove_v4($resumeId, $jobId, $userinfo, $job_title_grade, $current_salary, $in_salary_range, $work_days,$company, $money, $trialSalary)
    {
        $this->getDI()->get('logger')->write_log("addApprove params" . json_encode(func_get_args()), 'info');

        $db = WorkflowModel::beginTransaction($this);
        try {

            if ($data = $this->isCanAddApprove($resumeId)) {
                $lastResume = $this->getLastApprove($resumeId);
                if ($lastResume && in_array($lastResume['status'], [self::STATUS_PANDING, self::STATUS_APPROVAED])) {
                    // 正在审批 或 一审批通过
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => "该offer薪资正在审批中或已审批通过"
                    ]);
                }

                $money['basic_salary'] = $money['basic_salary'] ? ($money['basic_salary'] / 100) :0;
                $money['house_rental'] = $money['house_rental'] ? ($money['house_rental'] / 100) :0;
                $money['xinghuo_allowance'] = $money['xinghuo_allowance'] ? ($money['xinghuo_allowance'] / 100) :0;
                $money['money'] = $money['money'] ? ($money['money'] / 100) :0;
                $money['welfare_allowance'] = $money['welfare_allowance'] ? ($money['welfare_allowance'] / 100) :0;
                $money['food_allowance'] = $money['food_allowance'] ? ($money['food_allowance'] / 100) :0;
                $money['variable_allowance'] = $money['variable_allowance'] ? ($money['variable_allowance'] / 100) :0;
                $money['attendance_allowance'] = $money['attendance_allowance'] ? ($money['attendance_allowance'] / 100) :0;
                $money['transport_allowance'] = $money['transport_allowance'] ? ($money['transport_allowance'] / 100) :0;
                $money['language_allowance'] = $money['language_allowance'] ? ($money['language_allowance'] / 100) :0;
                $money['is_computer_allowance'] = $money['is_computer_allowance'] ? $money['is_computer_allowance'] :0;
                $money['is_camera_allowance'] = $money['is_camera_allowance'] ? $money['is_camera_allowance'] :0;

                $trialSalary['basic_salary'] = $trialSalary['basic_salary'] ? ($trialSalary['basic_salary'] / 100) :0;
                $trialSalary['house_rental'] = $trialSalary['house_rental'] ? ($trialSalary['house_rental'] / 100) :0;
                $trialSalary['xinghuo_allowance'] = $trialSalary['xinghuo_allowance'] ? ($trialSalary['xinghuo_allowance'] / 100) :0;
                $trialSalary['money'] = $trialSalary['money'] ? ($trialSalary['money'] / 100) :0;
                $trialSalary['welfare_allowance'] = $trialSalary['welfare_allowance'] ? ($trialSalary['welfare_allowance'] / 100) :0;
                $trialSalary['food_allowance'] = $trialSalary['food_allowance'] ? ($trialSalary['food_allowance'] / 100) :0;
                $trialSalary['variable_allowance'] = $trialSalary['variable_allowance'] ? ($trialSalary['variable_allowance'] / 100) :0;
                $trialSalary['attendance_allowance'] = $trialSalary['attendance_allowance'] ? ($trialSalary['attendance_allowance'] / 100) :0;
                $trialSalary['transport_allowance'] = $trialSalary['transport_allowance'] ? ($trialSalary['transport_allowance'] / 100) :0;
                $trialSalary['language_allowance'] = $trialSalary['language_allowance'] ? ($trialSalary['language_allowance'] / 100) :0;
                $trialSalary['is_computer_allowance'] = $trialSalary['is_computer_allowance'] ? $trialSalary['is_computer_allowance'] :0;
                $trialSalary['is_camera_allowance'] = $trialSalary['is_camera_allowance'] ? $trialSalary['is_camera_allowance'] :0;

                if ($data['nationality'] == 2) {
                    $isCan = $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], 0, $trialSalary['money']);
                } else {
                    $isCan = $this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], 0, $trialSalary['money'] + $trialSalary['house_rental'] + $trialSalary['xinghuo_allowance'] + $trialSalary['language_allowance']);
                }
                if (!$isCan) {
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => $this->getTranslation()->_('no_need_salary_approval')
                    ]);
                }

                $salaryApproveModel = new SalaryApproveModel();
                $salaryApproveModel->resume_id = $resumeId;
                $salaryApproveModel->serial_no = 'SA' . $this->getID();
                $salaryApproveModel->submitter_id = $userinfo['id'];
                $salaryApproveModel->job_id = $jobId;
                $salaryApproveModel->basic_salary = $money['basic_salary'] ?? 0;
                $salaryApproveModel->trial_salary = $trialSalary['money'] ?? 0;
                $salaryApproveModel->renting = $money['house_rental'] ?? 0;
                $salaryApproveModel->status = enums::APPROVAL_STATUS_PENDING;
                $salaryApproveModel->job_title_grade = $job_title_grade;
                $salaryApproveModel->current_salary = $current_salary ? $current_salary / 100 : 0;
                $salaryApproveModel->is_express_line = $this->isExpressLine($data['hc_department_id']) ? 1 : 2;
                $salaryApproveModel->in_salary_range = $this->isExpressLine($data['hc_department_id']) ? $in_salary_range : 1 ;
                $salaryApproveModel->work_days = $work_days;
                $salaryApproveModel->xinghuo_allowance = $money['xinghuo_allowance'] ?? 0;
                $salaryApproveModel->money = $money['money'] ?? 0;
                $salaryApproveModel->welfare_allowance = $money['welfare_allowance'] ?? 0;
                $salaryApproveModel->food_allowance = $money['food_allowance'] ?? 0;
                $salaryApproveModel->variable_allowance = $money['variable_allowance'] ?? 0;
                $salaryApproveModel->attendance_allowance = $money['attendance_allowance'] ?? 0;
                $salaryApproveModel->transport_allowance = $money['transport_allowance'] ?? 0;
                $salaryApproveModel->language_allowance = $money['language_allowance'] ?? 0;
                $salaryApproveModel->is_computer_allowance = $money['is_computer_allowance'] ?? 0;
                $salaryApproveModel->is_camera_allowance = $money['is_camera_allowance'] ?? 0;
                $salaryApproveModel->company = $company ?? 0;
                $salaryApproveModel->create();
                $approveId = $salaryApproveModel->id;

                $salaryApproveAllowanceModel = new SalaryApproveAllowanceModel();
                $salaryApproveAllowanceModel->approve_id = $approveId;
                $salaryApproveAllowanceModel->money = $trialSalary['money'] ?? 0;
                $salaryApproveAllowanceModel->basic_salary = $trialSalary['basic_salary'] ?? 0;
                $salaryApproveAllowanceModel->welfare_allowance = $trialSalary['welfare_allowance'] ?? 0;
                $salaryApproveAllowanceModel->variable_allowance = $trialSalary['variable_allowance'] ?? 0;
                $salaryApproveAllowanceModel->food_allowance = $trialSalary['food_allowance'] ?? 0;
                $salaryApproveAllowanceModel->attendance_allowance = $trialSalary['attendance_allowance'] ?? 0;
                $salaryApproveAllowanceModel->transport_allowance = $trialSalary['transport_allowance'] ?? 0;
                $salaryApproveAllowanceModel->house_rental = $trialSalary['house_rental'] ?? 0;
                $salaryApproveAllowanceModel->xinghuo_allowance = $trialSalary['xinghuo_allowance'] ?? 0;
                $salaryApproveAllowanceModel->language_allowance = $trialSalary['language_allowance'] ?? 0;
                $salaryApproveAllowanceModel->is_computer_allowance = $trialSalary['is_computer_allowance'] ?? 0;
                $salaryApproveAllowanceModel->is_camera_allowance = $trialSalary['is_camera_allowance'] ?? 0;
                $salaryApproveAllowanceModel->save();

                //对接可视化 部门id用新的变量
                $department_id = $this->getFirstDepartmentId($data['hc_department_id']);
                $extend = [
                    'department_id' => $department_id,
                    'from_submit'   => ['department_id' => $department_id],
                ];
                if (!(new ApprovalServer($this->lang, $this->timezone))->create($approveId,
                        enums::$audit_type['SA'],
                        $userinfo['id']
                        , null, $extend)) {
                    throw new \Exception('create workflow failing');
                }
                $db->commit();
                return $this->checkReturn(['data' => $approveId]);
            } else {
                return $this->checkReturn([
                    "code" => -1,
                    "msg" => $this->getTranslation()->_('不是待审批offer')
                ]);
            }
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('salary_approve_data_error =>' . json_encode([
                    'Err_Msg' => $e->getMessage(),
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Code' => $e->getCode(),
                ], JSON_UNESCAPED_UNICODE), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101')) ;
        }
    }

    /**
     * 添加
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $basicSalary
     * @param $trialSalary
     * @param $renting
     * @param $job_title_grade  职级
     * @param $current_salary   当前薪资
     * @param $in_salary_range 是否在薪资范围内
     * @param $work_days 工作天数
     * @param $positionAllowance
     * @param $deminimis_benefits
     * @param $other_non_taxable_allowance
     * @param $xinghuoAllowance
     * @param $company
     *
     */
    public function addApprove_v2($params = []) {
        $resumeId                    = $params["resume_id"] ?? 0;
        $jobId                       = $params["job_id"] ?? 0;
        $userinfo                    = $params["userinfo"] ?? [];
        $basicSalary                 = intval($params["money"] ?? 0);
        $trialSalary                 = intval($params["trial_salary"] ?? 0);
        $renting                     = intval($params["renting"] ?? 0);
        $job_title_grade             = intval($params["job_title_grade"] ?? 0);
        $current_salary              = intval($params["current_salary"] ?? 0);
        $in_salary_range             = intval($params["in_salary_range"] ?? 1);
        $work_days                   = intval($params["work_days"] ?? 0);
        $positionAllowance           = intval($params["position_allowance"] ?? 0);
        $company                     = $params["company"] ?? '';
        $deminimis_benefits          = intval($params["deminimis_benefits"] ?? 0);
        $other_non_taxable_allowance = intval($params["other_non_taxable_allowance"] ?? 0);
        $xinghuoAllowance            = intval($params["xinghuo_allowance"] ?? 0);
        // 实习期工资，金额/Day
        $internship_salary            = intval($params["internship_salary"] ?? 0);
        $currency                     = $params["currency"] ?? SalaryApproveModel::CURRENCY_THB;
        $subsidyType                  = $params["subsidy_type"] ?? SalaryApproveModel::SUBSIDY_TYPE_NOT_SELECT;
        $is_computer_allowance        = intval($params["is_computer_allowance"] ?? 0);
        $is_camera_allowance          = intval($params["is_camera_allowance"] ?? 0);
        $language_ability             = intval($params['language_ability']??0);//语言能力  泰国在用
        $exp                          = intval($params['exp'] ?? 0);

        $this->getDI()->get('logger')->write_log("addApprove params" . json_encode(func_get_args()), 'info');

        $db = WorkflowModel::beginTransaction($this);
        try {
            if ($data = $this->isCanAddApprove($resumeId)) {
                $lastResume = $this->getLastApprove($resumeId);
                if ($lastResume && in_array($lastResume['status'], [self::STATUS_PANDING, self::STATUS_APPROVAED]) ) {
                    // 正在审批 或 一审批通过
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => "该offer薪资正在审批中或已审批通过"
                    ]);
                }
                if (
                (isCountry() || isCountry('Id')) && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary + $positionAllowance + $xinghuoAllowance , $currency, $data)
                    ||
                    isCountry('Ph') && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary + $deminimis_benefits + $other_non_taxable_allowance + $xinghuoAllowance)
                    ||
                    isCountry('Vn') && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary + $xinghuoAllowance)
                    ||
                    isCountry('My') && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary + $xinghuoAllowance)
                    ||
                    isCountry('LA') && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary + $xinghuoAllowance)
                    ||
                    !isCountry() && !isCountry('Ph') && !isCountry('La') && !isCountry('Vn') && !isCountry('My') && !isCountry('Id') && !$this->isCanSubmitSalaryApprove($data['hc_department_id'], $data['job_title'], $in_salary_range, $renting + $trialSalary)
                ) {
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => $this->getTranslation()->_('no_need_salary_approval')
                    ]);
                }
                // 添加审批
                $salaryApproveModel = new SalaryApproveModel();
                $salaryApproveModel->resume_id = $resumeId;
                $salaryApproveModel->serial_no = 'SA' . $this->getID();
                $salaryApproveModel->submitter_id = $userinfo['id'];
                $salaryApproveModel->job_id = $jobId;
                $salaryApproveModel->workflow_role = 'salary_approve_new';
                $salaryApproveModel->basic_salary = $basicSalary;
                $salaryApproveModel->trial_salary = $trialSalary;
                $salaryApproveModel->renting = $renting;
                $salaryApproveModel->status = enums::APPROVAL_STATUS_PENDING;
	            $salaryApproveModel->job_title_grade = $job_title_grade;
	            $salaryApproveModel->current_salary = $current_salary;
                $salaryApproveModel->is_express_line = $this->isExpressLine($data['hc_department_id']) ? 1 : 2;
                $salaryApproveModel->in_salary_range = $this->isExpressLine($data['hc_department_id']) ? $in_salary_range : 1 ;
                $salaryApproveModel->position_allowance = $positionAllowance;
                $salaryApproveModel->deminimis_benefits = $deminimis_benefits;
                $salaryApproveModel->other_non_taxable_allowance = $other_non_taxable_allowance;
                $salaryApproveModel->xinghuo_allowance = $xinghuoAllowance;
                $salaryApproveModel->currency = $currency;
                $salaryApproveModel->subsidy_type = $subsidyType;
                $salaryApproveModel->is_computer_allowance = $is_computer_allowance;
                $salaryApproveModel->is_camera_allowance = $is_camera_allowance;
                $salaryApproveModel->language_ability = $language_ability;
                $salaryApproveModel->exp = $exp;

                $is_front_line_job = (new OfferSignApproveServer($this->lang,$this->timezone))->isFrontLineJob($jobId);

                if (
                    isCountry('Th') && $is_front_line_job == 0
                    ||
                    isCountry('Ph') && $this->isFirstLineJob($data['hc_department_id'], $jobId) == false
                    ||
                    isCountry('My') && $this->isFirstLineJob($data['hc_department_id'], $jobId) == false
                    ||
                    isCountry('Id') && $this->isFirstLineJob($data['hc_department_id'], $jobId) == false
                    ||
                    isCountry('Vn') && $this->isFirstLineJob($data['hc_department_id'], $jobId) == false
                    ||
                    isCountry('La') && $this->isFirstLineJob($data['hc_department_id'], $jobId) == false
                ) {
                    $salaryApproveModel->work_days = $work_days;
                }

                //是否是特殊简历打标签也展示
                if ($this->isHeadOfferRecruit($data)) {
                    $salaryApproveModel->work_days = $work_days;
                }

                if (isCountry('Vn') || isCountry('My') || isCountry('Id')) {

                    $salaryApproveModel->company = $company;
                }
                $salaryApproveModel->create();

                $approveId = $salaryApproveModel->id;

                //对接可视化部门 id 用新的变量
                $department_id = $this->getFirstDepartmentId($data['hc_department_id']);
                $extend = [
                    'department_id' => $department_id,
                    'from_submit'   => ['department_id' => $department_id],
                ];
                if (!(new ApprovalServer($this->lang, $this->timezone))->create($approveId,
                        enums::$audit_type['SA'],
                        $userinfo['id']
                        , null, $extend)) {
                    throw new \Exception('create workflow failing');
                }
                $db->commit();
                return $this->checkReturn(['data' => $approveId]);
            } else {
                return $this->checkReturn([
                    "code" => -1,
                    "msg" => $this->getTranslation()->_('不是待审批offer')
                ]);
            }
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get('logger')->write_log('salary_approve_data_error =>' . json_encode([
                    'Err_Msg' => $e->getMessage(),
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Code' => $e->getCode(),
                ], JSON_UNESCAPED_UNICODE), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101')) ;
        }

    }

    /**
     * 是否需要薪资审批
     *
     * @param $departmentId 部门ID
     * @param $jobId 职位ID
     * @param $inSalaryRange 是否在薪资范围内
     * @param $salaryUnit 薪资单位
     * @param $salary 薪资
     *
     */
    public function isCanSubmitSalaryApprove($departmentId, $jobId, $inSalaryRange, $salary, $currency = SalaryApproveModel::CURRENCY_THB, $resume_info = [])
    {
        $defaultThSalary = 30000;

        if (strtolower(env('country_code', 'Th')) == 'th' && $currency == SalaryApproveModel::CURRENCY_SGD) {
            $defaultThSalary =  1200;
        }

        if ($this->isFirstLineJob($departmentId, $jobId)) {

            //增加是否为总部招聘判定 START
            $jobOfficeIds  = (new SettingEnvServer())->setExpire(120)->getSetValToArray('jointly_recruited_ids');

            // 一线职位
            if (
                strtolower(env('country_code', 'Th')) == 'th' && $salary < $defaultThSalary && (
                    isset($resume_info['is_head_office_recruit']) && ($resume_info['is_head_office_recruit'] == HrResumeModel::IS_HEAD_OFFICE_NO) ||
                    !empty($jobId) && !in_array($jobId, $jobOfficeIds)
                )
                || strtolower(env('country_code', 'Th')) == 'ph' && $salary < 45000
                || strtolower(env('country_code', 'Th')) == 'my' && $salary < 3750
                || strtolower(env('country_code', 'Th')) == 'vn' && $salary < 20477816
                || strtolower(env('country_code', 'Th')) == 'id' && $salary < 13453518
                || strtolower(env('country_code', 'Th')) == 'la' && $salary < 8640000
            ) {
                return 0;
            }
        }

        return 1;
    }

    /**
     * 检查简历是否为打标签的简历
     * @param $info
     * @return bool
     * 公共方法
     */
    public function isHeadOfferRecruit($info): bool
    {
        $jobTitleId = $info['job_title'];
        $is_head_office_recruit = $info['is_head_office_recruit'] ?? HrResumeModel::IS_HEAD_OFFICE_NO;

        $jobOfficeIds  = (new SettingEnvServer())->setExpire(120)->getSetValToArrayFromCache('jointly_recruited_ids');

        if (
            isCountry() &&
            ($is_head_office_recruit == HrResumeModel::IS_HEAD_OFFICE_YES) &&
            in_array($jobTitleId, $jobOfficeIds)
        ) {
            return true;
        }

        return false;
    }

    /**
     * 是否是快递业务线
     *
     * @param $departmentId
     * @return bool
     *
     */
    public function isExpressLine($departmentId)
    {
        $departmentIds = (new SettingEnvServer())->getSetVal('express_line_departments');
        $departmentIds = explode(',', $departmentIds);

        if ($departmentIds) {
            $sysDepartments = SysDepartmentModel::find([
                'columns' => 'id, ancestry_v3',
                'conditions' => ' id in ({ids:array}) ',
                'bind' => ['ids' => $departmentIds]
            ])->toArray();
            $ancestries = array_column($sysDepartments, 'ancestry_v3');

            foreach ($ancestries as $ancestry) {

                if (SysDepartmentModel::findFirst([
                    'conditions' => ' id = :id: and ancestry_v3 like :ancestry_v3: ',
                    'bind' => [
                        'id' => $departmentId,
                        'ancestry_v3' => $ancestry . '%'
                    ]])) {

                    return true;
                }
            }
        }

        return false;
    }
    /**
     * 是否是一线员工职位
     * 紧根据配置的职位来判断是否一线
     * offer模块使用此逻辑判断是否一线
     * @param $job_id
     */
    public static $front_line_employee_jobids=[];

    public function isFrontLineJob($job_id){
        if(empty(self::$front_line_employee_jobids)){
            $front_line_jobids = (new SettingEnvServer())->getSetVal('front_line_employee_jobids');
            self::$front_line_employee_jobids = explode(',',$front_line_jobids);
        }
        if(self::$front_line_employee_jobids && in_array($job_id,self::$front_line_employee_jobids)){
            return 1;
        }

        return 0;
    }

    /**
     * 是否是一线职位
     * @param $departmentId
     * @param $JobId
     * @return bool
     *
     */
    public function isFirstLineJob($departmentId, $JobId)
    {
        $jobs = (new SettingEnvServer())->setExpire(120)->getSetValFromCache('first_line_jobs');
        $items = explode(',', $jobs);
        foreach ($items as $item) {
            $i = explode("|", $item);
            if (isset($i[0]) && isset($i[1])) {
                if ($i[0] == $departmentId && $i[1] == $JobId) {
                    return true;
                }
            }

        }

        return false;
    }

    /**
     * 印尼 添加
     *
     * @param $resumeId
     * @param $jobId
     * @param $userinfo
     * @param $basicSalary
     * @param $trialSalary
     * @param $renting
     * @param $job_title_grade  职级
     * @param $current_salary   当前薪资
     *
     */
    public function addApprove_v3($resumeId, $jobId, $userinfo, $basicSalary, $trialSalary, $renting,$job_title_grade=0,$current_salary=0)
    {
        $this->getDI()->get('logger')->write_log("addApprove params" . json_encode(func_get_args()), 'info');

        if (!$trialSalary || !$basicSalary) {
            return $this->checkReturn([
                "code" => -1,
                "msg" => $this->getTranslation()->_('salary_is_null')
            ]);
        }

        $db = WorkflowModel::beginTransaction($this);
        try {
            if ($data = $this->isCanAddApprove($resumeId)) {
                $lastResume = $this->getLastApprove($resumeId);
                if ($lastResume && in_array($lastResume['status'], [self::STATUS_PANDING, self::STATUS_APPROVAED])) {
                    // 正在审批 或 一审批通过
                    return $this->checkReturn([
                        "code" => -1,
                        "msg" => "该offer薪资正在审批中或已审批通过"
                    ]);
                }
            }

            // 是一线职位 是薪资小于等于12900000的薪资审批，不需要审批
            if (in_array($jobId, $this->firstJobIds()) && $trialSalary < 12900000) {
                // 不需要审批
                $db->insertAsDict("salary_approve", [
                    "resume_id" => $resumeId,
                    "serial_no" => "SA" . $this->getID(),
                    "submitter_id" => $userinfo['id'],
                    "job_id" => $jobId,
                    "workflow_role" => '',
                    "basic_salary" => $basicSalary,
                    "trial_salary" => $trialSalary,
                    "renting" => $renting,
                    "status" => self::STATUS_NO, // 不需要审批
                ]);

                return $this->checkReturn([
                    "code" => -1,
                    "msg" => $this->getTranslation()->_('no_need_salary_approval')
                ]);

            }
            // 添加审批
            $salaryApproveModel = new SalaryApproveModel();
            $salaryApproveModel->resume_id = $resumeId;
            $salaryApproveModel->serial_no = 'SA' . $this->getID();
            $salaryApproveModel->submitter_id = $userinfo['id'];
            $salaryApproveModel->job_id = $jobId;
            $salaryApproveModel->workflow_role = 'salary_approve_new';
            $salaryApproveModel->basic_salary = $basicSalary;
            $salaryApproveModel->trial_salary = $trialSalary;
            $salaryApproveModel->renting = $renting;
            $salaryApproveModel->status = enums::APPROVAL_STATUS_PENDING;
            $salaryApproveModel->job_title_grade = $job_title_grade;
            $salaryApproveModel->current_salary = $current_salary;
            $salaryApproveModel->create();

            $approveId = $salaryApproveModel->id;

            //对接可视化 部门 id 用新的变量
            $department_id = $this->getFirstDepartmentId($data['hc_department_id']);
            $extend = [
                'department_id' => $department_id,
                'from_submit'   => ['department_id' => $department_id],
            ];
            if (!(new ApprovalServer($this->lang, $this->timezone))->create($approveId,
                    enums::$audit_type['SA'],
                    $userinfo['id']
                    , null, $extend)) {
                throw new \Exception('create workflow failing');
            }

            $db->commit();
            return $this->checkReturn(['data' => $approveId]);
        } catch (\Exception $e) {

            $db->rollBack();
            $this->getDI()->get('logger')->write_log('salary_approve_data_error =>' . json_encode([
                    'Err_Msg' => $e->getMessage(),
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Code' => $e->getCode(),
                ], JSON_UNESCAPED_UNICODE), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4101')) ;
        }
    }


    /**
     * 印尼 一线职位
     *
     * @return array
     */
    public function firstJobIds()
    {

        return [
            enums::$job_title['bike_courier'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['dc_officer'],
            enums::$job_title['shop_cashier'],
            enums::$job_title['shop_officer'],
            enums::$job_title['shop_supervisor'],
            enums::$job_title['store_officer'],
            enums::$job_title['van_courier'],
            enums::$job_title['shop_bike'],
            enums::$job_title['hub_staff'],
            enums::$job_title['warehouse_staff'],
            enums::$job_title['assistant_branch_supervisor'],
            enums::$job_title['boat_courier'],
            enums::$job_title['onsite_staff'],
            enums::$job_title['fleet_driver'],
            enums::$job_title['freight_hub_staff']
        ];
    }



    /**
     * 获取申请详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {

       $dateDetail = $this->approvalDetail([
            'sa_id' => $auditId,
            'userinfo' => ['id' => $user, 'staff_id' => $user]
        ], $comeFrom);
       unset($dateDetail['data']['stream']);
       return $dateDetail;
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {

        //'summary' => json_encode([["key" => "report_job_name", "value" => $jobName], ["key" => "basic_salary", "value" => $basicSalary . " THB"]], JSON_UNESCAPED_UNICODE),

        $salaryApprove = SalaryApproveModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $auditId]
        ]);
        if ($salaryApprove) {
            $salaryApprove = $salaryApprove->toArray();
            $data = $this->isCanAddApprove($salaryApprove['resume_id'], []);
            $jobs = $this->wms->getJobByids([$data['hc_job_id'], $salaryApprove['job_id']]);
            $jobName = '';
            if ($jobs) {
                $jobs = array_column($jobs,null, 'id');
                $jobName = $jobs[$salaryApprove['job_id']]['name'];
            }
            if (isCountry('id')) {

                return [
                    ['key' => 'report_job_name', 'value' => $jobName],
                    ['key' => 'salary_package', 'value' => number_format($salaryApprove['trial_salary'])],
                ];
            } else if (isCountry('th') && $salaryApprove['currency'] == SalaryApproveModel::CURRENCY_SGD) {
                return [
                    ['key' => 'report_job_name', 'value' => $jobName],
                    ['key' => 'basic_salary_sgd', 'value' => $salaryApprove['basic_salary'].' SGD'],
                ];
            } else {

                return [
                    ['key' => 'report_job_name', 'value' => $jobName],
                    ['key' => 'basic_salary', 'value' => $salaryApprove['basic_salary']],
                ];

            }
        }

        return [];
    }

    /**
     * 审批结束回调函数,设置审批状态等
     * @param int $auditId 审批ID
     * @param int $state 审批状态
     * @param null $extend 扩展字段
     * @param bool $isFinal 是否为最终审批 true-是 false-否
     * @return mixed
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $auditApply = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind' => [
                    'type'  => enums::$audit_type['SA'],
                    'value' => $auditId,
                ]
            ]);
            $salaryApproveModel = SalaryApproveModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind' => [
                    'id' => $auditId
                ]
            ]);
            $salaryApproveModel->status = $state;
            if ($auditApply && in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_CANCEL])) {
                $salaryApproveModel->cancel_reason = self::$params['revoke_remark'];
            }

            $salaryApproveModel->save();

            if ($state == enums::APPROVAL_STATUS_REJECTED) {

                $this->insertLog($salaryApproveModel->resume_id, $extend['staff_id'], 19);
            } elseif ($state == enums::APPROVAL_STATUS_CANCEL) {

                $this->insertLog($salaryApproveModel->resume_id, $extend['staff_id'], 18);
            } else if ($state == enums::APPROVAL_STATUS_APPROVAL) {
	            //回传当前薪资
	            try {
		            if (property_exists($salaryApproveModel, 'resume_id') && property_exists($salaryApproveModel, 'current_salary')) {
			            $resumeData = ['current_salary' => $salaryApproveModel->current_salary];
			            $success    = $this->getDI()->get('db')->updateAsDict(
				            'hr_resume',
				            $resumeData,
				            'id = ' . $salaryApproveModel->resume_id
			            );
			            $this->getDI()->get("logger")->write_log("SalaryServer setProperty 最终审批 同步当前薪资到hr_resume id=>" . $salaryApproveModel->resume_id . " 修改内容 ==>" . json_encode($resumeData, JSON_UNESCAPED_UNICODE) . " 结果:" . json_encode($success, JSON_UNESCAPED_UNICODE), "info");
			            if (!$success){
				            $this->getDI()->get("logger")->write_log("SalaryServer setProperty 最终审批 同步当前薪资到hr_resume id=>" . $salaryApproveModel->resume_id . " 修改内容 ==>" . json_encode($resumeData, JSON_UNESCAPED_UNICODE) . " 结果:" . json_encode($success, JSON_UNESCAPED_UNICODE), "error");
			            }
                        //todo 给薪资审批发起人发送审批通过的消息
                        $this->sendSalaryApprovePassedMsg($salaryApproveModel->resume_id,$salaryApproveModel->submitter_id);
		            }

	            } catch (\Exception $e) {
		            $this->getDI()->get("logger")->write_log("SalaryServer setProperty  最终审批 同步当前薪资到hr_resume error:" . $e->getMessage(), "error");
	            }

            }
        }
        //以前没有返回值 现在返回一个
        return  true;
    }

    /**
     * 样例
     * from_node_id | to_node_id | valuate_formula | valuate_code
     * -------------+------------+-----------------+-------------
     *      4       |     5      |    $p1 == 4     | getSubmitterDepartment
     *
     * 表示当提交人的部门为4时，审批节点4的下一个节点是5
     * 需要在 getWorkflowParams 中返回申请人所在的部门字段
     *
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $salaryApprove = SalaryApproveModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $auditId]
        ]);

        $data = $this->isCanAddApprove($salaryApprove->resume_id);

        $params = [
            'country_code' => env('country_code', 'Ph'), // 国家
            'k1' => $salaryApprove->trial_salary + $salaryApprove->renting,
            'k' => $salaryApprove->trial_salary + $salaryApprove->renting, // 试用期工资
            'k2' => $this->isFirstLineJob($data['hc_department_id'], $salaryApprove->job_id) ? 1 : 2, // 是否是一线职位
            'request_department_id' => $data['hc_department_id'], // 部门 id
            'is_express_line' => $salaryApprove->is_express_line, // 是否快递业务线 1 是 2 否
            'is_salary_range' => $salaryApprove->in_salary_range, // 是否在薪资范围内 1 是 2 否
            'salary_unit' => SalaryServer::SALARY_UNIT['THB']
        ];

        if (isCountry('Th') || isCountry('Id')) {
            $params['k'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->position_allowance + $salaryApprove->xinghuo_allowance ;
            $params['k1'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->position_allowance + $salaryApprove->xinghuo_allowance ;
        }

        if (isCountry('Id')) {

            $approveAllowance = SalaryApproveAllowanceModel::findFirst([
                'conditions' => ' approve_id = :approve_id: ',
                'bind' => ['approve_id' => $auditId]
            ]);
            $approveAllowance = $approveAllowance ? $approveAllowance->toArray() : [];
            if ($approveAllowance) {
                // 印尼 新数据
                $params['k'] = $salaryApprove->money;
                $params['k1'] = $salaryApprove->money;
                if ($data['nationality'] != 2) {
                    // 非中国籍
                    $params['k'] = $params['k'] + $salaryApprove->renting + $salaryApprove->xinghuo_allowance + $salaryApprove->language_allowance;
                    $params['k1'] = $params['k1'] + $salaryApprove->renting + $salaryApprove->xinghuo_allowance + $salaryApprove->language_allowance;
                }

            }
        }

        if (isCountry('Ph')) {
            $params['k'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->deminimis_benefits + $salaryApprove->other_non_taxable_allowance + $salaryApprove->xinghuo_allowance;
            $params['k1'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->deminimis_benefits + $salaryApprove->other_non_taxable_allowance + $salaryApprove->xinghuo_allowance;
        }
        if (isCountry('Vn') || isCountry('La') || isCountry('My')) {
            $params['k'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->xinghuo_allowance;
            $params['k1'] = $salaryApprove->trial_salary + $salaryApprove->renting + $salaryApprove->xinghuo_allowance;
        }

        if (isCountry('Ph')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['PHP'];
        } else if (isCountry('Vn')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['VND'];
        } else if (isCountry('My')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['MYR'];
        } else if (isCountry('La')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['LAK'];
        } else if (isCountry('Id')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['IN'];
        } else if (isCountry('SGD')) {
            $params['salary_unit'] = SalaryServer::SALARY_UNIT['SGD'];
        }

        //泰国新需求，添加SGD币种

        if (isCountry('Th')) {
            if ($salaryApprove->currency == SalaryApproveModel::CURRENCY_SGD) {
                $params['salary_unit'] = SalaryServer::SALARY_UNIT['SGD'];
            }
        }

        return $params;
    }

    /**
     * 获取待发offer 面试ID
     *
     * @param $resumeId
     * @param $operatorId
     * @param int $action 17 申请  18 撤销 19 驳回 关联win——hr 项目
     */
    public function insertLog($resumeId, $operatorId, $action = 17)
    {
        $sql = "-- 获取待发offer interview_id
                select
                        hr_interview.interview_id
                from
                    hr_resume
                left join hr_interview on hr_resume.id = hr_interview.resume_id and hr_resume.hc_id = hr_interview.hc_id 
                where hr_interview.state = :interview_state and hr_resume.id = :resume_id ";
        $info = $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC, [
            "interview_state" => 20,
            "resume_id" => $resumeId,
        ], [
            "interview_state" => Column::BIND_PARAM_INT,
            "resume_id" => Column::BIND_PARAM_INT,
        ]);
        $interViewId = $info ?  $info['interview_id'] : 0;
        $moduleType = 13; // 关联win-hr 项目的enums下的 module type
        $this->getDI()->get("db")->insertAsDict("hr_log", [
            "staff_info_id" => $operatorId,
            "module_id" => $interViewId,
            "module_type" => $moduleType,
            "action" => $action,
        ]);

    }

    /**
     * 审批流 获取当前节点 如果离职获取下一节点
     *
     * @param $workflow
     * @param int $node
     * @param  $userinfo
     * @param  $approveId
     * @param bool $isRecord
     */
    public function getStaffId($workflow, $userinfo = [], $approveId = 0, $node = 1, $isRecord = false)
    {
        if (isset($workflow[$node])) {
            $staffs = $workflow[$node]['staff_id'];
            // 只有一个人审批
            $staffId = array_keys($staffs)[0];
            $staff = $this->getDI()->get("db_rby")->fetchOne("--
                select * from hr_staff_info where staff_info_id = :staff_id", Db::FETCH_ASSOC, [
                "staff_id" => $staffId
            ], [
                "staff_id" => Column::BIND_PARAM_INT]);
            if ($staff && $staff['state'] == 1) {
                return $staffId;
            } else {
                if ($isRecord) {
                    // 需要记录 自动审批通过
                    $log['staff_id'] = $userinfo['id'];
                    $log['type'] = enums::$audit_type['SA'];
                    $log['original_type'] = 1;
                    $log['original_id'] = $approveId;
                    $log['operator'] = $staff && isset($staff['staff_info_id']) ? $staff['staff_info_id'] : $staffId;
                    $log['operator_name'] = $staff && isset($staff['name']) ? $staff['name'] : '';
                    $toStatusType = self::STATUS_APPROVAED_20;
                    if ($staff && $staff['state'] == 2) {
                        $toStatusType = self::STATUS_APPROVAED_21;
                    } else if ($staff && $staff['state'] == 3) {
                        $toStatusType = self::STATUS_APPROVAED_22;
                    }
                    $log['to_status_type'] = $toStatusType;
                    $log['created_at'] = gmdate("Y-m-d H:i:s", strtotime("+ 5 seconds"));
                    $result = $this->other->insertLog($log);
                }
                if (isset($workflow[$node+1]) && isset($workflow[$node+1]['staff_id'])) {
                    return $this->getStaffId($workflow, $userinfo, $approveId, $node + 1, $isRecord);
                }
            }
        }

        return null;
    }

    /**
     * win hr 获取审批详情
     *
     * @param $userinfo
     * @param $resumeId
     *
     */
    public function winHrApprovalDetail($userinfo, $resumeId)
    {
        $info = $this->getLastApprove($resumeId);
        $returnData = (new ApprovalServer($this->lang, $this->timezone))->getAuditDetail($info['id'], AuditListEnums::APPROVAL_TYPE_SALARY, $userinfo['id'], 1,null,'',1,'WinHR');

        //提交人头像
        $returnData['data']['head']['avator'] = (new AuditlistRepository($this->lang, $this->timezone))->getPersonAvator($info['staff_id_union']);
        return $this->checkReturn($returnData);
    }

    /**
     *
     * 获取审批详情
     * 数据结构
     *
     * @param $postData ["sa_id" => 5, "url" => "", "userinfo" => ""]
     * @param $comFrom
     *
     */
    public function approvalDetail($postData, $comFrom)
    {
        $salaryInfo = $this->getSalaryInfo($postData['sa_id']);
        if (!$salaryInfo) {
            return [];
        }
        $jobs = $this->wms->getJobByids([$salaryInfo['job_id']]);
        if ($jobs) {
            $jobs = array_column($jobs, null, 'id');
        }

	    //获取简历附件  1 是半身照
	    $hr_annex =  $this->getHrAnnex(1,$salaryInfo['resume_id']);
        $resume = (new ResumeRepository($this->timezone))->getResumeInfoById($salaryInfo['resume_id']);

	    $data = [
            "title"                  => $this->auditlist->getAudityType(enums::$audit_type['SA']),
            "id"                     => $postData['sa_id'],
            "staff_id"               => $salaryInfo['submitter_id'],
            "job_id"                 => $salaryInfo['job_id'],
            "job_name"               => isset($jobs[$salaryInfo['job_id']]) ? $jobs[$salaryInfo['job_id']]['name'] : "",
            "type"                   => enums::$audit_type['SA'],
            "created_at"             => show_time_zone($salaryInfo['created_at']),
            "updated_at"             => show_time_zone($salaryInfo['updated_at']),
            "status_text"            => $this->auditlist->getAuditStatus('10' . $salaryInfo['status']),
            "status"                 => $this->transferStatus($salaryInfo['status']),
            "serial_no"              => $salaryInfo['serial_no'],
            'salary_view_permission' => $postData['userinfo']['id'] == $salaryInfo['submitter_id'] || in_array($postData['userinfo']['id'],
                self::SALARY_VIEW_PERMISSION) ? 1 : 0,
            //候选人照片  半身照
            'candidate_avator'       => $hr_annex['url'] ?? 'https://' . env('hris_profile_uri') . env('default_avator'),
            'currency'               => $salaryInfo['currency'],
            'subsidy_type'           => $salaryInfo['subsidy_type'],
        ];
        if (isCountry('TH')) {
            $data['language_ability'] = $salaryInfo['language_ability'];
        }
        if ($salaryInfo['is_express_line'] == 1) {

            $data['in_salary_range'] = $salaryInfo['in_salary_range'];
            $data['in_salary_range_text'] = $salaryInfo['in_salary_range'] ? $this->getTranslation()->_('accident_report_whether_' . $salaryInfo['in_salary_range']) : '';
        } else {
            $data['in_salary_range'] = '';
            $data['in_salary_range_text'] = '';
        }


        $detail = $this->getDetail_1($salaryInfo);

        $streams = $this->getStreams($postData, $comFrom);

        $salaryItems = [];
        if (isCountry('Id')) {
            $salaryItems = $this->salaryApproveItems($salaryInfo);
        }

        return [
            "data" => [
                "head"         => $data,
                "detail"       => $detail,
                "salary_items" => $salaryItems,
                "stream"       => $streams,
            ],
        ];
    }

    public function salaryApproveItems($lastResume)
    {
        $approveAllowance = SalaryApproveAllowanceModel::findFirst([
            'conditions' => ' approve_id = :approve_id: ',
            'bind' => ['approve_id' => $lastResume['id']]
        ]);
        $approveAllowance = $approveAllowance ? $approveAllowance->toArray() : [];

        $items = [];
        if ($approveAllowance) { // 新版本薪资项目

            // 通过试用期薪酬包
            // is_computer_allowance 0=未选择，1=有，2=没有
            if ($approveAllowance['is_computer_allowance'] == 1){
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_yes");
            }elseif ($approveAllowance['is_computer_allowance'] == 2){
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_no");
            }else{
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_not_selected");
            }
            // is_camera_allowance 0=未选择，1=有，2=没有
            if ($approveAllowance['is_camera_allowance'] == 1){
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_yes");
            }elseif ($approveAllowance['is_camera_allowance'] == 2){
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_no");
            }else{
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_not_selected");
            }

            $items[0]['money'] = [
                'k' => 'money',
                'key' => $this->getTranslation()->_('salary_package'),
                'value' => number_format($approveAllowance['money'] ?? 0) . ' IDR',
                'simple_val' => (int) ($approveAllowance['money'] ?? 0) ,
                'items' => [
                    'basic_salary' => [
                        'k' => 'basic_salary',
                        'key' => $this->getTranslation()->_('basic_salary'),
                        'value' =>  number_format($approveAllowance['basic_salary']) . ' IDR/Month',
                        'simple_val' => $approveAllowance['basic_salary'],
                    ],
                    'welfare_allowance' => [
                        'k' => 'welfare_allowance',
                        'key' => $this->getTranslation()->_('welfare_allowance'),
                        'value' => number_format($approveAllowance['welfare_allowance']) . ' IDR/Month',
                        'simple_val' => $approveAllowance['welfare_allowance'],
                    ],
                    'variable_allowance' => [
                        'k' => 'variable_allowance',
                        'key' => $this->getTranslation()->_('variable_allowance'),
                        'value' => number_format($approveAllowance['variable_allowance']) . ' IDR/Day',
                        'simple_val' => $approveAllowance['variable_allowance'],
                    ]
                ]
            ];
            $items[0]['house_rental'] = [
                'k' => 'house_rental',
                'key' => $this->getTranslation()->_('renting'),
                'value' => number_format($approveAllowance['house_rental']) . ' IDR/Month',
                'simple_val' => $approveAllowance['house_rental'],
            ];
            $items[0]['is_computer_allowance'] = [
                'k' => 'is_computer_allowance',
                'key' => $this->getTranslation()->_('is_computer_allowance'),
                'value' => $is_computer_allowance_val,
                'simple_val' => $approveAllowance['is_computer_allowance'],
            ];
            $items[0]['is_camera_allowance'] = [
                'k' => 'is_camera_allowance',
                'key' => $this->getTranslation()->_('is_camera_allowance'),
                'value' => $is_camera_allowance_val,
                'simple_val' => $approveAllowance['is_camera_allowance'],
            ];
            $items[0]['xinghuo_allowance'] = [
                'k' => 'xinghuo_allowance',
                'key' => $this->getTranslation()->_('xinghuo_allowance'),
                'value' => number_format($approveAllowance['xinghuo_allowance']) . ' IDR/Month',
                'simple_val' => $approveAllowance['xinghuo_allowance'],
            ];
            $items[0]['language_allowance'] = [
                'k' => 'language_allowance',
                'key' => $this->getTranslation()->_('language_allowance'),
                'value' => number_format($approveAllowance['language_allowance']) . ' IDR/Month',
                'simple_val' => $approveAllowance['language_allowance'],
            ];

            // 试用期薪酬包
            // is_computer_allowance 0=未选择，1=有，2=没有
            if ($lastResume['is_computer_allowance'] == 1){
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_yes");
            }elseif ($lastResume['is_computer_allowance'] == 2){
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_no");
            }else{
                $is_computer_allowance_val = $this->getTranslation()->_("allowance_not_selected");
            }
            // is_camera_allowance 0=未选择，1=有，2=没有
            if ($lastResume['is_camera_allowance'] == 1){
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_yes");
            }elseif ($lastResume['is_camera_allowance'] == 2){
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_no");
            }else{
                $is_camera_allowance_val = $this->getTranslation()->_("allowance_not_selected");
            }

            $items[1]['money'] = [
                'k' => 'money',
                'key' => $this->getTranslation()->_('salary_package'),
                'value' => number_format($lastResume['money']) . ' IDR',
                'simple_val' => $lastResume['money'],
                'items' => [
                    'basic_salary' => [
                        'k' => 'basic_salary',
                        'key' => $this->getTranslation()->_('basic_salary'),
                        'value' => number_format($lastResume['basic_salary']) . ' IDR/Month',
                        'simple_val' => $lastResume['basic_salary'],
                    ],
                    'welfare_allowance' => [
                        'k' => 'welfare_allowance',
                        'key' => $this->getTranslation()->_('welfare_allowance'),
                        'value' => number_format($lastResume['welfare_allowance']) . ' IDR/Month',
                        'simple_val' => $lastResume['welfare_allowance'],
                    ],
                    'variable_allowance' => [
                        'k' => 'variable_allowance',
                        'key' => $this->getTranslation()->_('variable_allowance'),
                        'value' => number_format($lastResume['variable_allowance']) . ' IDR/Day',
                        'simple_val' => $lastResume['variable_allowance'],
                    ]
                ]
            ];
            $items[1]['house_rental'] = [
                'k' => 'house_rental',
                'key' => $this->getTranslation()->_('renting'),
                'value' => number_format($lastResume['renting']) . ' IDR/Month',
                'simple_val' => $lastResume['renting'],
            ];
            $items[1]['is_computer_allowance'] = [
                'k' => 'is_computer_allowance',
                'key' => $this->getTranslation()->_('is_computer_allowance'),
                'value' => $is_computer_allowance_val,
                'simple_val' => $lastResume['is_computer_allowance'],
            ];
            $items[1]['is_camera_allowance'] = [
                'k' => 'is_camera_allowance',
                'key' => $this->getTranslation()->_('is_camera_allowance'),
                'value' => $is_camera_allowance_val,
                'simple_val' => $lastResume['is_camera_allowance'],
            ];
            $items[1]['xinghuo_allowance'] = [
                'k' => 'xinghuo_allowance',
                'key' => $this->getTranslation()->_('xinghuo_allowance'),
                'value' => number_format($lastResume['xinghuo_allowance']) . ' IDR/Month',
                'simple_val' => $lastResume['xinghuo_allowance'],
            ];
            $items[1]['language_allowance'] = [
                'k' => 'language_allowance',
                'key' => $this->getTranslation()->_('language_allowance'),
                'value' => number_format($lastResume['language_allowance']) . ' IDR/Month',
                'simple_val' => $lastResume['language_allowance'],
            ];
        }
        return $items;
    }

    /**
     *
     * @param $idUnion
     *
     */
    private function getApproveGrpByStatus($idUnion)
    {
        $sql = "--
        select
            *,
            group_concat(approval_id) as approval_ids
        from
            staff_audit_union
        where id_union = :id_union and type_union = :type_union group by status_union for update;
        ";
        $db = $this->getDI()->get('db');
        $list = $db->fetchAll($sql, Db::FETCH_ASSOC, [
            "id_union" => $idUnion,
            "type_union" => enums::$audit_type['SA'],
        ]);

        return array_column($list, null, 'status_union');
    }

    private function getAutoApprovedStatus($status)
    {
        $maps = [
            self::STATUS_APPROVAED_20 => $this->getTranslation()->_('auto_pandding_20'),
            self::STATUS_APPROVAED_21=> $this->getTranslation()->_('auto_pandding_21'),
            self::STATUS_APPROVAED_22=> $this->getTranslation()->_('auto_pandding_22'),
            self::STATUS_APPROVAED_23=> $this->getTranslation()->_('auto_pandding_22'),
        ];

        return isset($maps[$status]) ? $maps[$status] : '';
    }

    /**
     * 审批流程
     * @param $postData
     * @param $comFrom
     */
    private function getStreams($postData, $comFrom)
    {
        $auditLogs = $this->auditToolLog->getAuditRecords(['id' => $postData['sa_id'], 'type' => enums::$audit_type['SA']]);

        $list = $this->getApproveGrpByStatus("sa_" . $postData['sa_id']);
        $salaryApproval = $this->getSalaryInfo($postData['sa_id']);

        $streams = [];
        foreach ($auditLogs as $k => $auditLog) {
            $staff = (new StaffRepository()) -> getStaffpositionV2($auditLog['operator']);
            $staff_name = $staff['name'];
            $staff_id = $staff['id'];
            if(isCountry('ID')) {
                $staff_name = $staff['id'] == '56780' ? '' : $staff['name'];
                $staff_id = $staff['id'] == '56780' ? '' : $staff['id'];
            }
            if ($k == 0) {
                $streams[] = [
                    "staff_id" => $staff_id,
                    "name" => $staff_name ?? '',
                    "position" => $staff['job_name'] ?? '',
                    "department" => $staff['department_name'] ?? '',
                    "store_id" => $staff['organization_id'] ?? '',
//                    "status" => $this->getAutoApprovedStatus(self::STATUS_APPROVAED_21),
                    "status" => $this->getTranslation()->_('send_request'),
                    "status_code" => 8,
                    "wait_time" => "",
                    "time" => $auditLog['created_at'],
                    "is_OK" => 0
                ];
            } else {
                if (in_array($auditLog['to_status_type'], [self::STATUS_APPROVAED_20, self::STATUS_APPROVAED_21, self::STATUS_APPROVAED_22, self::STATUS_APPROVAED_23])) {
                    $streams[] = [
                        "staff_id" => $staff_id,
                        "name" => $staff_name ?? '',
                        "position" => $staff['job_name'] ?? '',
                        "department" => $staff['department_name'] ?? '',
                        "store_id" => $staff['organization_id'] ?? '',
                        "status" => $this->getAutoApprovedStatus($auditLog['to_status_type']),
                        "status_code" => (int)$auditLog['to_status_type'],
                        "wait_time" => "",
                        "remark" => '',
                        "time" => $auditLog['created_at'],
                        "is_OK" => 0
                    ];
                } else {
                    $streams[] = [
                        "staff_id" => $auditLog['operator'],
                        "name" => $staff['name'] ?? '',
                        "position" => $staff['job_name'] ?? '',
                        "department" => $staff['department_name'] ?? '',
                        "store_id" => $staff['organization_id'] ?? '',
//                        "status" => $this->getAutoApprovedStatus(self::STATUS_APPROVAED_21),
                        "status" => $this->auditlist->getAuditStatus((string)$this->approvalStatusMaps($auditLog['to_status_type'])),
                        "status_code" =>  (int)$auditLog['to_status_type'],
                        "wait_time" => "",
                        "remark" => in_array($auditLog['to_status_type'], [self::STATUS_REVOKED, self::STATUS_DISMISSSED]) ?  $salaryApproval['cancel_reason']: '',
                        "time" => $auditLog['created_at'],
                        "is_OK" => 0
                    ];
                }
            }
        }
        if (isset($list[enums::$audit_list_status['panding_approval']]['approval_ids'])) {
            $approvalIds = explode(",", $list[enums::$audit_list_status['panding_approval']]['approval_ids']);
            $staff = (new StaffRepository()) -> getStaffpositionV2($approvalIds[0]);
            $staff_name = $staff['name'];
            $staff_id = $staff['id'];
            if(isCountry('ID')) {
                $staff_name = $staff['id'] == '56780' ? '' : $staff['name'];
                $staff_id = $staff['id'] == '56780' ? '' : $staff['id'];
            }
            $time = strtotime(date("Y-m-d H:i:s")) - strtotime(show_time_zone($list[enums::$audit_list_status['panding_approval']]['created_at']));

            $streams[] = [
                "staff_id" => $staff_id,
                "name" => $staff_name ?? '',
                "position" => $staff['job_name'] ?? '',
                "department" => $staff['department_name'] ?? '',
                "store_id" => $staff['organization_id'] ?? '',
                "status" => $this->auditlist->getAuditStatus(107),
                "status_code" => 7,
                "wait_time" => sprintf($this->getTranslation()->_("wait_time"), gmdate("H", $time), gmdate("i", $time)),
                "time" => "",
                "is_OK" => 0
            ];
        }

        return $streams;

    }

    /**
     * 获取详情 detail
     * @param $salaryInfo
     */
    private function getDetail_1($salaryInfo)
    {

        $requestSrc = $this->getAuditDetailRequest()->getRequestSrc();

        $resumeId = $salaryInfo['resume_id'];
        $info = $this->isCanAddApprove($resumeId, []);
        $this->getDI()->get("logger")->write_log("salaryApproval" . json_encode($info, JSON_UNESCAPED_UNICODE), "info");
        $detail = [];
        $jobs = $this->wms->getJobByids([$salaryInfo['job_id'], $info['hc_job_id']]);
        $jobName = '';
        if ($jobs) {
            $jobs = array_column($jobs,null, 'id');
            $jobName = $jobs[$salaryInfo['job_id']]['name'];
        }
        //是否是一线职位
        $is_front_line_job = (new OfferSignApproveServer($this->lang,$this->timezone))->isFrontLineJob($salaryInfo['job_id']);

        $jd = $this->getDI()->get("db")->fetchOne("--
        select
            hr_jd.job_id ,
            hr_jd.job_name,
            hr_hc.province_code,
            hr_hc.job_title,
            hr_resume.address_id,
            hr_resume.credentials_num,
            hr_resume.recruit_type,
            hr_resume.is_head_office_recruit
            from 
                hr_resume
            left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id
            left join hr_hc on hr_interview.hc_id = hr_hc.hc_id
            left join hr_jd on hr_hc.job_id = hr_jd.job_id
            where hr_resume.id = " . $salaryInfo['resume_id'], Db::FETCH_ASSOC);

        $innerJobName = $province = '';
        if ($jd) {
            $innerJobName = $jd['job_name'];
            $province = (new StaffRepository())->getProvince($jd['address_id']);
        }

        $departMents = (new SysListRepository())->getDepartmentList(["ids" => $info['hc_department_id']]);
        $department = '';
        if ($departMents) {
            $departMents = array_column($departMents, null, 'id');
            $department = $departMents[$info['hc_department_id']]['name'];
        }

        $stores= (new SysListRepository())->getStoreList(["ids" =>getIdsStr([$info['worknode_id']])]);
        $store = '';
        if ($stores) {
            $stores = array_column($stores, null, 'id');
            if ($info['worknode_id'] && isset($stores[$info['worknode_id']])) {
                $store = $stores[$info['worknode_id']]['name'];
            }
        }
        //当前用户信息
        $staff_info = (new StaffServer())->get_staff($salaryInfo['submitter_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        //获取简历附件 16 是简历附件
        $hr_annex =  $this->getHrAnnex(16,$salaryInfo['resume_id']);
        $t = $this->getTranslation();
        if ($info) {
            $detail[] = ["k" => "apply_parson", "key" => $t->_("apply_parson"), "value" => sprintf('%s ( %s )', $staff_info['name'] ?? "", $salaryInfo['submitter_id']), 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "apply_department", "key" => $t->_("apply_department"), "value" => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''), 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "candidate", "key" => $t->_("candidate"), "value" => $info['name'], 'simple_val' => '', 'type' => 0];

            $detail[] = ["k" => "resume_attachment", "key" => $t->_("resume_attachment"), "value" => $hr_annex['original_name'] ?? '', 'simple_val' => $hr_annex["url"] ?? '', 'type' => 1];//简历附件  type 1是附件

            //泰国添加薪资附件
            if (isCountry('TH')) {
                $salary_annex = $this->getHrAnnex(HrAnnexModel::FILE_TYPE_SALARY_CERTIFIACTE, $salaryInfo['resume_id']);
                $detail[]     = [
                    "k"          => "salary_attachment",
                    "key"        => $t->_("salary_attachment"),
                    "value"      => $salary_annex['original_name'] ?? '',
                    'simple_val' => $salary_annex["url"] ?? '',
                    'type'       => HrAnnexModel::TYPE_RESUME_SALARY
                ];
            }

            $detail[] = ["k" => "cv_id", "key" => $t->_("cv_id"), "value" => $info['id'], 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "hc_id", "key" => $t->_("hc_id"), "value" => $info['hc_id'], 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "jd_name", "key" => $t->_("position_name"), "value" => $innerJobName, 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "job_title_grade", "key" => $t->_("hr_probation_field_grade"), "value" => !isset($salaryInfo['job_title_grade']) || is_null($salaryInfo['job_title_grade']) ? '' : "F".$salaryInfo['job_title_grade'], 'simple_val' => !isset($salaryInfo['job_title_grade']) || is_null($salaryInfo['job_title_grade']) ? '' : $salaryInfo['job_title_grade'], 'type' => 0]; // 职级
            if (isCountry('TH') && !empty($salaryInfo['language_ability'])) {
                $detail[] = ["k" => "language_ability", "key" => $t->_("language_ability"), "value" => $t->_("language_ability_".$salaryInfo['language_ability']), 'simple_val' => '', 'type' => 0];
            }
            $detail[] = ["k" => "department", "key" => $t->_("salary_department"), "value" => $department, 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "city", "key" => $t->_("salary_city"), "value" => $province ? $province['name'] : '', 'simple_val' => '', 'type' => 0];
            $detail[] = ["k" => "store", "key" => $t->_("salary_store"), "value" => $store, 'simple_val' => '', 'type' => 0];


            $detail[] = ["k" => "job", "key" => $t->_("salary_job"), "value" => $jobName, 'simple_val' => '', 'type' => 0];
            if (strtolower(env('country_code', 'Th')) == 'th') {
                $currency = SalaryApproveModel::CURRENCY_THB_COMPANY;
                if (isset($salaryInfo['currency']) && $salaryInfo['currency'] == SalaryApproveModel::CURRENCY_SGD) {
                    $currency = SalaryApproveModel::CURRENCY_SGD_COMPANY;
                }

                $lastBaseSalary = $this->getLastBaseSalary($jd['credentials_num']);
                $lastBaseSalary = $lastBaseSalary ? bcdiv($lastBaseSalary,100,0) : 0;
                if ($lastBaseSalary > 0) {
                    $detail[] = ["k"          => "last_base_salary",
                                 "key"        => $t->_("last_base_salary"),
                                 "value"      => $lastBaseSalary . " {$currency}",
                                 'simple_val' => $lastBaseSalary,
                                 'type'       => 0,
                    ];
                }

                if (isset($jd['recruit_type']) && $jd['recruit_type'] != 3) {
                    $detail[] = ["k"          => "current_salary",
                                 "key"        => $t->_("current_salary"),
                                 "value"      => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],
                                         100, 0) . " {$currency}"),
                                 'simple_val' => (!isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary']),
                                 'type'       => 0,
                    ];//当前薪资
                }

                $detail[] = ["k"          => "money",
                             "key"        => $t->_("salary_money"),
                             "value"      => $salaryInfo['basic_salary'] . " {$currency}",
                             'simple_val' => $salaryInfo['basic_salary'],
                             'type'       => 0,
                ];
                $detail[] = ["k"          => "trial_salary",
                             "key"        => $t->_("trial_salary"),
                             "value"      => $salaryInfo['trial_salary'] . " {$currency}",
                             'simple_val' => $salaryInfo['trial_salary'],
                             'type'       => 0,
                ];
                $detail[] = ["k"          => "renting",
                             "key"        => $t->_("renting"),
                             "value"      => $salaryInfo['renting'] . " {$currency}",
                             'simple_val' => $salaryInfo['renting'],
                             'type'       => 0,
                ];
                $detail[] = ["k"          => "exp",
                             "key"        => $t->_("job_transfer.exp_allowance"),
                             "value"      => $salaryInfo['exp'] . " {$currency}",
                             'simple_val' => $salaryInfo['exp'],
                             'type'       => 0,
                ];

                if($is_front_line_job == 0){ //非一线职位显示工作天数

                    // 非一线 泰国 添加 星火激励进贴 岗位进贴字段
                    $detail[] = ["k"          => "position_allowance",
                                 "key"        => $t->_("job_transfer.position_allowance"),
                                 "value"      => $salaryInfo['position_allowance'] . " {$currency}",
                                 'simple_val' => (int)$salaryInfo['position_allowance'],
                                 'type'       => 0,
                    ];
                    $detail[] = ["k"          => "xinghuo_allowance",
                                 "key"        => $t->_("xinghuo_allowance"),
                                 "value"      => $salaryInfo['xinghuo_allowance'] . " {$currency}",
                                 'simple_val' => (int)$salaryInfo['xinghuo_allowance'],
                                 'type'       => 0,
                    ];


                }
                if ($is_front_line_job == 0 || $this->isHeadOfferRecruit($jd)) {
                    $detail[] = [
                        "k"          => "work_days",
                        "key"        => $t->_("work_days"),
                        "value"      => $salaryInfo['work_days'],
                        'simple_val' => $salaryInfo['work_days'],
                        'type'       => 0,
                    ];
                }
            } else if (strtolower(env('country_code', 'Ph')) == 'ph') {
                $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],100,0) . " PHP"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'], 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $t->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " PHP", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $t->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " PHP", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $t->_("renting"), "value" => $salaryInfo['renting'] . " PHP", 'simple_val' => $salaryInfo['renting'], 'type' => 0];
                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $t->_("xinghuo_allowance"), "value" => $salaryInfo['xinghuo_allowance'] . " PHP", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }
                if (!empty($salaryInfo['deminimis_benefits'])) {

                    $detail[] = ["k" => "deminimis_benefits", "key" => $t->_("deminimis_benefits"), "value" => $salaryInfo['deminimis_benefits'] . " PHP", 'simple_val' => (int)$salaryInfo['deminimis_benefits'], 'type' => 0];
                }
                if (!empty($salaryInfo['other_non_taxable_allowance'])) {

                    $detail[] = ["k" => "other_non_taxable_allowance", "key" => $t->_("other_non_taxable_allowance"), "value" => $salaryInfo['other_non_taxable_allowance'] . " PHP", 'simple_val' => (int)$salaryInfo['other_non_taxable_allowance'], 'type' => 0];
                }

                if (($requestSrc == 'WinHR' || $salaryInfo['work_days'] != HrStaffInfoModel::WEEK_WORKING_DAY_FREE ) && ! $this->isFirstLineJob($info['hc_department_id'], $salaryInfo['job_id'])) {

                    $detail[] = ["k" => "work_days", "key" => $t->_("work_days"), "value" => $salaryInfo['work_days'], 'simple_val' => $salaryInfo['work_days'], 'type' => 0];
                }

            } else if (strtolower(env('country_code', 'My')) == 'my') {
                $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],100,0) . " MYR"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'], 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $t->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " MYR", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $t->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " MYR", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $t->_("renting"), "value" => $salaryInfo['renting'] . " MYR", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $t->_("xinghuo_allowance"), "value" => $salaryInfo['xinghuo_allowance'] . " MYR", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }

                if (!$this->isFirstLineJob($info['hc_department_id'], $salaryInfo['job_id'])) {
                    $detail[] = ["k" => "company", "key" => $t->_("company"), "value" => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'], 'simple_val' => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'] ];// 公司

                    $detail[] = ["k" => "work_days", "key" => $t->_("work_days"), "value" => $salaryInfo['work_days'], 'simple_val' => $salaryInfo['work_days'], 'type' => 0];
                }

            } else if (strtolower(env('country_code', 'La')) == 'la') {
                $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],100,0) . " LAK"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'], 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $t->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " LAK", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $t->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " LAK", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $t->_("renting"), "value" => $salaryInfo['renting'] . " LAK", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {
                    $detail[] = ["k" => "xinghuo_allowance", "key" => $t->_("xinghuo_allowance"), "value" => $salaryInfo['xinghuo_allowance'] . " LAK", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }

                if (
                    ! $this->isFirstLineJob($info['hc_department_id'], $salaryInfo['job_id'])
                ) {

                    $detail[] = ["k" => "work_days", "key" => $t->_("work_days"), "value" => $salaryInfo['work_days'], 'simple_val' => $salaryInfo['work_days'], 'type' => 0];
                }

            } else if (strtolower(env('country_code', 'Id')) == 'id') {
                $approveAllowance =
                SalaryApproveAllowanceModel::findFirst([
                    'conditions' => 'approve_id = :id:',
                    'bind' => ['id' => $salaryInfo['id']]
                ]);
                if ($approveAllowance) {
                    //改bug
                    $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : ((int)$salaryInfo['current_salary'] . " IDR"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'] * 100, 'type' => 0];//当前薪资
                    $detail[] = ["k" => "work_days", "key" => $t->_("work_days"), "value" => $salaryInfo['work_days'], 'simple_val' => $salaryInfo['work_days'], 'type' => 0];
                    $detail[] = ["k" => "company", "key" => $t->_("company"), "value" => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'], 'simple_val' => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'] ];// 公司
                } else {

                    // 兼容老数据
                    $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],100,0) . " IDR"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'], 'type' => 0];//当前薪资
                    $detail[] = ["k" => "money", "key" => $t->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " IDR", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                    $detail[] = ["k" => "trial_salary", "key" => $t->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " IDR", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                    $detail[] = ["k" => "renting", "key" => $t->_("renting"), "value" => $salaryInfo['renting'] . " IDR", 'simple_val' => $salaryInfo['renting'], 'type' => 0];
                    if($is_front_line_job == 0){ //非一线职位显示工作天数

                        // 非一线 印尼 添加 星火激励进贴 岗位进贴字段
                        $detail[] = ["k" => "position_allowance", "key" => $t->_("job_transfer.position_allowance"), "value" => $salaryInfo['position_allowance'] . " IDR", 'simple_val' => (int)$salaryInfo['position_allowance'], 'type' => 0];
                        $detail[] = ["k" => "xinghuo_allowance", "key" => $t->_("xinghuo_allowance"), "value" => $salaryInfo['xinghuo_allowance'] . " IDR", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                    }
                }

            } else if (strtolower(env('country_code', 'Id')) == 'vn') {
                if (
                    ! $this->isFirstLineJob($info['hc_department_id'], $salaryInfo['job_id'])
                ) {
                    $detail[] = ["k" => "company", "key" => $t->_("company"), "value" => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'], 'simple_val' => !isset($salaryInfo['company']) || is_null($salaryInfo['company']) ? '' : $salaryInfo['company'] ];// 公司
                }

                $detail[] = ["k" => "current_salary", "key" => $t->_("current_salary"), "value" => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : (bcdiv((int)$salaryInfo['current_salary'],100,0) . " VND"), 'simple_val' => !isset($salaryInfo['current_salary']) || is_null($salaryInfo['current_salary']) ? '' : $salaryInfo['current_salary'], 'type' => 0];//当前薪资

                $detail[] = ["k" => "money", "key" => $t->_("salary_money"), "value" => $salaryInfo['basic_salary'] . " VND", 'simple_val' => $salaryInfo['basic_salary'], 'type' => 0];
                $detail[] = ["k" => "trial_salary", "key" => $t->_("trial_salary"), "value" => $salaryInfo['trial_salary'] . " VND", 'simple_val' => $salaryInfo['trial_salary'], 'type' => 0];
                $detail[] = ["k" => "renting", "key" => $t->_("renting"), "value" => $salaryInfo['renting'] . " VND", 'simple_val' => $salaryInfo['renting'], 'type' => 0];

                if (!empty($salaryInfo['xinghuo_allowance'])) {

                    $detail[] = ["k" => "xinghuo_allowance", "key" => $t->_("xinghuo_allowance"), "value" => (int)$salaryInfo['xinghuo_allowance'] . " VND", 'simple_val' => (int)$salaryInfo['xinghuo_allowance'], 'type' => 0];
                }

                if (
                    ! $this->isFirstLineJob($info['hc_department_id'], $salaryInfo['job_id'])
                ) {

                    $detail[] = ["k" => "work_days", "key" => $t->_("work_days"), "value" => $salaryInfo['work_days'], 'simple_val' => $salaryInfo['work_days'], 'type' => 0];
                }

            }
        }


        return $detail;

    }

    /**
     * @description:  获取简历附件
     *
     * @param $file_type int  0=其他，1=本人半身照，2=身份证正面，3=身份证反面，4=户籍照第一页，5=户籍照应聘者本人信息页，6=兵役服役证明，7=成绩报告单，8=清白证明书，9=补充附件，10=驾驶证正面，11=驾驶证反面，12=车辆登记薄，13=车辆照片，14=车辆使用授权，15=签名，16=个人简历, 17=残疾证正面，18=残疾证反面, 19=最高学历证书
     * @param $oss_bucket_key int  key
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/9/26 16:09
     */
    public function getHrAnnex($file_type = 16,$oss_bucket_key = ''){
        try{
            $hr_annex = HrAnnexModel::findFirst(
                [
                    "conditions" => "file_type = :file_type: AND oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND type = 1 and deleted=0 ",
                    'bind' => [
                        'file_type' => $file_type, //16是附件简历
                        'oss_bucket_key' => $oss_bucket_key,
                        'oss_bucket_type'=>'HR_CV_ANNEX',
                    ],
                ]
            );
            if($hr_annex){
                $hr_annex = $hr_annex->toArray();
                $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
                $hr_annex["url"] = !empty($hr_annex['object_key']) ? $img_prefix . $hr_annex['object_key'] : '';
            }
            return $hr_annex;
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log("获取简历附件， getHrAnnex error:" . $e->getMessage(), "error");
            return[];
        }



    }

    /**
     *
     * 转 状态
     * @param $status
     */
    private function transferStatus($status)
    {
        $statusMaps = [
            1 => 7
        ];
        return isset($statusMaps[$status]) ? $statusMaps[$status] : $status;
    }

    /**
     * @param $id
     *
     */
    public function getSalaryInfo($id)
    {
        $sql = "
        --
        select * from salary_approve where id = :id for update ";
        $db = $this->getDI()->get("db");
        return $db->fetchOne($sql, Db::FETCH_ASSOC, ["id" => $id]);
    }

    /**
     * 简历最新审批
     *
     *
     * @param $resumeId
     * @return mixed
     *
     */
    public function getLastApprove($resumeId)
    {
        $sql = "--
            select 
                * 
            from salary_approve 
            where resume_id = :resume_id  order by id desc";

        return $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC, [
            'resume_id' => $resumeId
        ], [
            'resume_id' => Column::BIND_PARAM_INT
        ]);
    }


    /**
     *
     * 获取一级部门 如果自己是一级部门 返回自己ID
     *
     * @param $departmentId
     * @return int
     */
    private function getFirstDepartmentId($departmentId): int
    {
        //获取当前部门
        $department = FleSysDepartmentModel::findFirst([
            'conditions' => "id = :department_id:",
            'bind'       => [
                'department_id' => $departmentId
            ],
            'columns' => "id,level,ancestry_v3,type"
        ]);
        //非CEO Group部门还不存在部门链的
        if (isset($department->id) && $department->id != 999 && !(isset($department->ancestry_v3) && $department->ancestry_v3)) {
            return 0;
        }
        $deptArr = explode('/', $department->ancestry_v3);
        if (isset($department->type) && $department->type == 3) { //直属部门

            //获取部门链中CLevel以下部门
            //例如：999/333/146/147/148/149 ==> [999,222,333,444] ==> [146,147,148,149]
            //==> 146
            $depAncestryChain  = array_diff($deptArr, [999, 222, 333, 444]);
            $tmpDept = array_shift($depAncestryChain);

            //找出部门链中的一级部门
            $levelOneDepartment = FleSysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind'       => [
                    'department_id'   => $tmpDept,
                ],
                'columns' => "id"
            ]);
            $resultDepartmentId = isset($levelOneDepartment->id) && $levelOneDepartment->id ? $levelOneDepartment->id : 0;
        } else if (isset($department->type) && $department->type == 2) { //非直属部门

            //找出部门链中的一级部门
            $levelOneDepartment = FleSysDepartmentModel::findFirst([
                'conditions' => "id in ({departmentIds:array}) and level = 1",
                'bind'       => [
                    'departmentIds'   => $deptArr,
                ],
                'columns' => "id"
            ]);
            $resultDepartmentId = isset($levelOneDepartment->id) && $levelOneDepartment->id ? $levelOneDepartment->id : 0;
        } else {
            $resultDepartmentId = $department->id ?? 0;
        }

        return $resultDepartmentId;
    }

    /**
     * 是否可以添加 薪资审批
     * @param $resumeId
     * @param int[] $state
     *
     */
    private function isCanAddApprove($resumeId, $state = [self::STATE_INTERVIEW_WAIT_SEND_OFFER])
    {
        if ($state) {
            $state = implode(",", $state);
            $sql = "-- 待发offer
        select 
            hr_resume.id,
            hr_resume.name,
            hr_resume.phone,
            hr_resume.email,
            hr_resume.address_id,
            hr_resume.nationality,
            hr_hc.department_id as hc_department_id,
            hr_hc.job_id as hc_job_id,
            hr_hc.job_title,
            hr_hc.hc_id,
            hr_hc.worknode_id,
            hr_hc.city_code,
            hr_resume.is_head_office_recruit
        from 
            hr_resume 
        left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id
        left join hr_hc on hr_interview.hc_id = hr_hc.hc_id
        where 
            hr_resume.id = :resume_id and hr_interview.state in (" . $state . ") 
        ";
        } else {
            $sql = "-- 待发offer
        select 
            hr_resume.id,
            hr_resume.name,
            hr_resume.phone,
            hr_resume.email,
            hr_resume.address_id,
            hr_resume.nationality,
            hr_hc.department_id as hc_department_id,
            hr_hc.job_id as hc_job_id,
            hr_hc.job_title,
            hr_hc.hc_id,
            hr_hc.worknode_id,
            hr_hc.city_code,
            hr_resume.is_head_office_recruit
        from 
            hr_resume 
        left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id
        left join hr_hc on hr_interview.hc_id = hr_hc.hc_id
        where 
            hr_resume.id = :resume_id 
        ";
        }

        $db = $this->getDI()->get("db");
        return $db->fetchOne($sql, Db::FETCH_ASSOC, [
            "resume_id" => $resumeId,
        ]);
    }


    /**
     * push 消息给审批人
     */
    public function pushMsgs()
    {
        $sql = "--
                select
                        *
                from salary_approve
                where id in (
                    select max(id) from salary_approve group by resume_id
                ) and status = :status";
        $salaryApprovalList = $this->getDI()->get("db")->fetchAll($sql, Db::FETCH_ASSOC, [
            'status' => self::STATUS_PANDING
        ], [
            'status' => Column::BIND_PARAM_INT
        ]);
        $publicRepository = (new PublicRepository($this->lang, null));

        $pushedStaffs = [];
        if ($salaryApprovalList) foreach ($salaryApprovalList as $item) {
            $sql = "--
                    select 
                        * 
                    from staff_audit_union 
                    where id_union = :id_union and type_union = :type_union and status_union = :status_union ";
            $auditInfo = $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC, [
                "id_union" => "sa_" . $item['id'],
                "type_union" => enums::$audit_type['SA'],
                "status_union" => enums::$audit_list_status['panding_approval']
            ], [
                "id_union" => Column::BIND_PARAM_STR,
                "type_union" => Column::BIND_PARAM_INT,
                "status_union" => Column::BIND_PARAM_INT
            ]);
            if ($auditInfo) {
                $staffData = (new \FlashExpress\bi\App\Repository\StaffRepository())->checkoutStaff($auditInfo['approval_id']);
                $staffAccount = $this->getDI()->get("db_fle")->fetchOne("select * from staff_account where staff_info_id = " . $auditInfo['approval_id'], Db::FETCH_ASSOC);
                if ($staffAccount && isset($staffAccount['accept_language'])) {
                    $this->lang = $staffAccount['accept_language'];
                } else {
                    $this->lang = 'en';
                }
                if (!(isset($pushedStaffs) && in_array($auditInfo['approval_id'], $pushedStaffs))) {
                    $model = new StaffAccountModel();
                    $lang = $model->getAcceptLanguage($auditInfo['approval_id']);
                    //异步push消息=>审批人通知
                    $t              = $this->getTranslation($lang);
                    $params = [
                        'staff_info_id' => $auditInfo['approval_id'],    //接收push信息人id
                        'message_title' => $t->_("4015"),    //push标题
                        'message_content' => $t->_("approval_push_msg"),    //push标题
                        'userinfo' => $staffData,
                        'audit_type' => "",    //模块名称
                        'is_audit' => 1    //xx状态
                    ];
                    $publicRepository->pushMessage($params);
                    $pushedStaffs[] = $auditInfo['approval_id'];
                    echo "push_salary_msg . " . \GuzzleHttp\json_encode($params, JSON_UNESCAPED_UNICODE) . "\r\n";
                    $this->getDI()->get("logger")->write_log("push_salary_msg . " . \GuzzleHttp\json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
                }

            }
        }


    }

    /**
     * @description: 获取面试记录列表
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/9/26 17:27
     */
    public function getInterviewRecord($params=[])
    {
        $page                  = $params['page'];
        $limit                 = $params['limit'];

        $returnData = [
            'dataList'=>[],
            'current_page' => $page,
            'count'        => 0
        ];
        try {
            //offer签字 入口进来
            if (isset($params['type']) && $params['type'] == AuditListEnums::APPROVAL_TYPE_OFFER_SIGNATURE) {
                $segmentation = strpos($params['id'], '_');
                $arr = $segmentation ? explode('_', $params['id']) : '';
                if(is_array($arr)){
                    $params['id'] = $arr[1];
                }
                $offer_sign = HrInterviewOfferSignApproveModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => ['id' => $params['id']],
                    'columns' => 'resume_id'
                ]);
                if ($offer_sign) {
                    $result = $offer_sign->toArray();
                } else {
                    throw new \Exception("没有找到offer签字记录 ==> params : " . json_encode($params, JSON_UNESCAPED_UNICODE));
                }
            }else{
                //查询薪资审批表
                $salaryApprove = SalaryApproveModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => ['id' => $params['id']],
                    'columns' => 'resume_id'
                ]);
                if ($salaryApprove) {
                    $result = $salaryApprove->toArray();
                } else {
                    throw new \Exception("没有找到薪资审批 ==> params : " . json_encode($params, JSON_UNESCAPED_UNICODE));
                }
            }
            $hr_resume = HrResumeModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => ['id' => $result['resume_id']],
                'columns' => 'id,hc_id'
            ]);
            $interviewInfo = false;
            if($hr_resume){
                $resumeData = $hr_resume->toArray();
                //查询面试表
                $interviewInfo = HrInterviewModel::findFirst([
                    'conditions' => ' resume_id = :resume_id: and hc_id = :hc_id:',
                    'bind'       => ['resume_id' => $result['resume_id'],'hc_id' => $resumeData['hc_id']],
                    'columns'    => 'interview_id,hc_id',
                    'order'      => 'interview_id desc',
                ]);
            }
            if ($interviewInfo) {
                $interviewInfo = $interviewInfo->toArray();
                //查询面试记录
                $list = HrInterviewInfoModel::find([
                    'conditions' => "interview_id = :interview_id: and hc_id = :hc_id: and state != :state: ",
                    'bind'       => ['interview_id' => $interviewInfo['interview_id'],'hc_id'=>$interviewInfo['hc_id'],'state'=>self::STATE_0],
                    'columns'    => 'interview_info_id,level,evaluate,state,created_at,interviewer_id',
                    'limit'      => $limit,
                    'offset'     => ($page - 1) * $limit,
                    'order'      => 'created_at desc',
                ])->toArray();
                $couInfo = HrInterviewInfoModel::findFirst([
                    'conditions' => "interview_id = :interview_id: and hc_id = :hc_id: and state != :state: ",
                    'bind'       => ['interview_id' => $interviewInfo['interview_id'],'hc_id'=>$interviewInfo['hc_id'],'state'=>self::STATE_0],
                    'columns'    => ' count(1) AS cou ',
                ]);
                if ($couInfo) {
                    $couInfo             = $couInfo->toArray();
                    $returnData['count'] = $couInfo['cou'] ?? 0;
                }
                if (!empty($list)) {
                    //查询
                    $interviewerIds = array_column($list, 'interviewer_id');

                    //查询面试官
                    $staffList = (new StaffRepository($this->lang))->getStaffListDepartmentJob($interviewerIds);
                    //拼装数据
                    $staffList = array_column($staffList, null, 'staff_info_id');
                    $add_hour = $this->config->application->add_hour;
                    foreach ($list as $k => $v) {
                        $returnData['dataList'][] = [
//							'level'               => $v['level'],//面试轮数
                            'level'               => $returnData['count'] - (($page - 1) * $limit + $k),//面试轮数  面试轮数现在是不会重新更新的  需要重新计算
                            'evaluate_date'       => date('Y-m-d H:i:s', strtotime($v['created_at']) + $add_hour * 3600),
                            'interviewer_id_text' =>  isset($staffList[$v['interviewer_id']]) && !empty($v['interviewer_id']) ?
                                sprintf(" %s (%s) , %s , %s ",
                                    $staffList[$v['interviewer_id']]['staff_name'],
                                    $staffList[$v['interviewer_id']]['staff_info_id'],
                                    $staffList[$v['interviewer_id']]['name'],
                                    $staffList[$v['interviewer_id']]['job_name'] )
                                : '',//面试官 && 工号 && 部门 && 职位
                            'state_text'          => $v['state'] == self::STATE_3 ? $this->getTranslation()->_('interview_result_not_pass') : $this->getTranslation()->_('interview_result_pass'),//3 是不通过
                            'evaluate'            => is_null($v['evaluate']) ? '' : $v['evaluate'],//评价内容
                        ];
                    }
                }

            }
            return $returnData;

        } catch (\Exception $e) {

            $this->getDI()->get('logger')->write_log('getInterviewRecord =>' . json_encode([
                    'Err_Msg'  => $e->getMessage(),
                    'Err_File' => $e->getFile(),
                    'Err_Line' => $e->getLine(),
                    'Err_Code' => $e->getCode(),
                ], JSON_UNESCAPED_UNICODE), 'error');
            return $returnData;
        }

    }

    /**
     *
     * 给薪资审批发起人发送审批通过的消息
     *
     * @param $resume_id 简历ID
     * @param $submitter_staff_id 薪资审批发起人ID
     *
     * @return bool
     */
    public function sendSalaryApprovePassedMsg($resume_id,$submitter_staff_id){

        try{
            if(empty($submitter_staff_id)){
                $this->getDI()->get('logger')->write_log("salary_approve_msg发送消息失败:cvid-{$resume_id}的薪资审批申请的发起人ID为空",'info');
                return ;
            }
            //获取简历中应聘者姓名
            $resume_info = (new ResumeRepository($this->timezone))->getResumeInfoById($resume_id);
            if ($resume_info) {
                $resume_name = $resume_info->first_name .' '.$resume_info->last_name;
            }

            $current_lang = $this->lang;
            //获取薪资审批发起人by上设置的语言
            $lang = (new StaffServer($this->lang,$this->timezone))->getLanguage($submitter_staff_id);

            $msg_title = $this->getTranslation($lang)->_('salary_approve_passed_msg_title');
            $msg_content = $this->getTranslation($lang)->_('salary_approve_passed_msg_content',['resume_id'=>$resume_id,'resume_name'=>$resume_name ?? '']);


            //组装站内信消息参数
            $msg_data = [
                "staff_users"       => [['id'=>$submitter_staff_id]],  //审批人ID
                "message_title"     => $msg_title, //标题
                "message_content"   => $msg_content,//内容
                "category"          => MessageEnums::MESSAGE_CATEGORY_SALARY_APPROVAL,
            ];
            //调用消息接口
            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($msg_data);
            $res = $bi_rpc->execute();

            if ($res && $res['result']['code'] == 1) {
                $msg_id = $res['result']['data'][0];
                $this->getDI()->get('logger')->write_log("salary_approve_msg发送消息成功:msg_id:{$msg_id},cvid:{$resume_id},msg_data:" . json_encode($msg_data),'info');
            }else{
                $this->getDI()->get('logger')->write_log("salary_approve_msg发送消息失败:msg_data:" . json_encode($msg_data).',消息返回：'.json_encode($res),'info');
            }
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("salary_approve_msg发送消息异常:".$e->getMessage(),'error');
        }


        return true;
    }

    /**
     * @param $identity
     * @return false|mixed
     */
    public function getLastBaseSalary($identity)
    {
        $staffRepo = new StaffRepository();
        $staff = $staffRepo->getLastStaffInfoByIdentity($identity);
        if ($staff){
            $salary = $staffRepo->getStaffSalary($staff['staff_info_id']);
            if ($salary){
                return $salary['base_salary'];
            }
        }
        return false;
    }

}
