<?php
/**
 * 用户事务一致性消息发送
 * 非必须建议使用hcm-PRC 方式
 * 本方法只测试了发送普通消息其他消息使用请自行测试
 */

namespace FlashExpress\bi\App\Server;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class MessageCourierServer extends BaseServer
{
    public function add_kit_message($param): array
    {
        //必填项
        $message_title = $param['message_title'];
        $message_title = stripslashes($message_title);  //反转义title

        $message_content = $param['message_content'];
        $staff_users     = $param['staff_users'];//接收消息 员工 一个工号时候格式是 [12345], 多个工号格式是 array( 0 => array('id' => 12345))

        //问卷类型消息新增字段默认值设置
        $questionnaire_lib_id = $param['questionnaire_lib_id'] ?? 0;
        $feedback_setting     = $param['feedback_setting'] ?? 0;
        $end_time             = $param['end_time'] ?? null;
        $audit_status         = $param['audit_status'] ?? 0;

        //非必填
        $staff_info_ids_str = empty($param['staff_info_ids_str']) ? '' : $param['staff_info_ids_str'];//bi页面 指定员工发送
        $add_userid         = empty($param['add_userid']) ? 0 : $param['add_userid'];                 //操作人 id
        $top                = empty($param['top']) ? 3 : $param['top'];                               //置顶状态 bi库（1.永久置顶；2:按时间区间置顶；3:未置顶） coupon库 状态为 0和1
        //bi工具 发送消息接受人组别 根据页面 四个按钮对应  其他渠道分类 不用传
        $group      = empty($param['to_group']) ? '' : trim($param['to_group']);
        $send_type  = empty($param['send_type']) ? 0 : intval($param['send_type']);
        $related_id = $param['related_id'] ?? '';

        //判断 category 只有为0的 是页面录入 其他 没有分类 都写成-1 未知类型
        if (isset($param['category']) && $param['category'] === 0) {
            $category = 0;
        } else {
            $category = empty($param['category'])? -1: intval($param['category']);
        }
        $category_code = empty($param['category_code']) ? 0 : intval($param['category_code']);
        $by_show_type  = $param['by_show_type'] ?? 0;


        //新增bi页面 定时发送功能
        $set_time       = empty($param['set_time']) ? null : $param['set_time'];
        $publish_status = empty($set_time) ? 6 : 9;


        //下面都是我粘贴过来的 不是我拼的
        $by_db = $this->getDI()->get('db');
        // message_content & message_courier 表 迁移到 db_message
        $message_db = $this->getDI()->get('db_coupon');

        $pushNum    = count($staff_users);

        try {
            //开启事务
            $by_db->begin();
            $message_db->begin();
            //message_content 主键 和 message_courier message_content_id字段 也是 bi  message表 remote_message_id字段
            $remote_message_id = time().rand(100000000, 999999999);
            $id                = 0;

            // 如果是问卷消息，则message中的content和message_content 中的内容不同
            // message中的content为FBI后台录入的原生html，message_content为可跳转到问卷消息页的html
            // 其他类型的消息，该字段维持原逻辑
            if (!empty($questionnaire_lib_id)) {
                $remote_message_content = $this->make_qn_msg_remote_tpl($remote_message_id);
            } else {
                $remote_message_content = $message_content;
            }

            //如果是 定时发送 不插入coupon库
            if (empty($set_time)) {
                $model = new MessageContentModel();
                //消息内容表
                $sql_param                  = [
                    'id'         => $remote_message_id,
                    'message'    => $remote_message_content,
                    'related_id' => $related_id,
                ];

                $message_db_message_content = $model->insert_record($sql_param);

                if (!$message_db_message_content) {
                    $by_db->rollback();
                    $message_db->rollback();
                    return ['error,please,try again1', '-1', []];
                }

                //消息员工表
                if ($pushNum == 1 && !empty($param['id'])) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             //添加单条记录
                    $id                           = $insert['id'] = $insert['mns_message_id'] = $param['id'];                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             //不知道 mns_message_id 干什么用的
                    $insert['staff_info_id']      = $staff_users[0];
                    $insert['title']              = $message_title;
                    $insert['category']           = $category;
                    $insert['category_code']      = $category_code;
                    $insert['top_state']          = empty($param['top_state']) ? 0 : $param['top_state'];
                    $insert['read_state']         = empty($param['read_state']) ? 0 : $param['read_state'];
                    $insert['push_state']         = empty($param['push_state']) ? 0 : $param['push_state'];
                    $insert['push_time']          = empty($param['push_time']) ? null : $param['push_time'];
                    $insert['source_type']        = empty($param['source_type']) ? 0 : $param['source_type'];
                    $insert['message_content_id'] = $remote_message_id;
                    $insert['by_show_type']       = $by_show_type;
                    $insert['is_del']             = 0;
                    $insert['update_state']       = 0;

                    $model = new MessageCourierModel();

                    $flag  = $model->insert_record($insert);

                    if (!$flag) {
                        $by_db->rollback();
                        $message_db->rollback();
                        return ['error,please,try again202', '-1', []];
                    }

                } else {//批量多条 并且没有id 入参
                    // 如果是答题消息，且发送人数超过500，则临时设置执行时间

                    $messages_title       = addslashes($message_title);    //转义title
                    $message_courier_data = '';
                    $id                   = 0;
                    foreach ($staff_users as $staff_info) {
                        $id                   = time().$staff_info['id'].rand(1000000, 9999999);
                        $message_courier_data .= '(';
                        $message_courier_data .= $id;
                        $message_courier_data .= ','.$staff_info['id'];
                        $message_courier_data .= ','."'{$messages_title}'";
                        $message_courier_data .= ','.$category;
                        $message_courier_data .= ','.$category_code;
                        if ($top == 1) {
                            $message_courier_data .= ','. 1;
                        } else {
                            $message_courier_data .= ','. 0;
                        }
                        $message_courier_data .= ','. 1;

                        $message_courier_data .= ','. 1;
                        $message_courier_data .= ','.$id;
                        $message_courier_data .= ','.$remote_message_id;
                        $message_courier_data .= ','.$by_show_type;
                        $message_courier_data .= '),';
                    }
                    $message_courier_data = rtrim($message_courier_data, ',');

                    $message_db_message_courier_sql    = "INSERT INTO message_courier (id,staff_info_id,title,category,category_code,top_state,push_state,source_type,mns_message_id,message_content_id,by_show_type) VALUES {$message_courier_data}";
                    $message_db_message_courier_result = $message_db->execute($message_db_message_courier_sql);
                    if (!$message_db_message_courier_result) {
                        $by_db->rollback();
                        $message_db->rollback();
                        return ['error,please,try again2', '-1', []];
                    }
                }
            }


            //如果是定时发送消息 并且 没有指定工号发送  需要吧 要接受的员工工号 存入staff_info_ids 便于定时任务 发送消息
            if (!empty($set_time) && $send_type != 4) {
                // 针对外部调用，不同的数据结构兼容取值
                if (isset($staff_users[0]['id'])) {
                    $staff_info_ids_str = implode(',', array_column($staff_users, 'id'));
                } else {
                    $staff_info_ids_str = implode(',', $staff_users);
                }
            }

            //上面insert语句，替换参数绑定方式
            $message_insert_data = [
                'title'             => $message_title,
                'content'           => $message_content,
                'staff_info_ids'    => $staff_info_ids_str,
                'staff_num'         => $pushNum,
                'add_userid'        => $add_userid,
                'last_edit_userid'  => $add_userid,
                'publish_status'    => $publish_status,
                'top_status'        => $top,
                'send_type'         => $send_type,
                'to_group'          => $group,
                'remote_message_id' => $remote_message_id,
                'category'          => $category,
                'by_show_type'      => $by_show_type,
                'audit_status'      => $audit_status,
                'again_time'        => gmdate('Y-m-d H:i:s'),
                'real_send_time'    => gmdate('Y-m-d H:i:s'),
            ];
            if (!empty($set_time)) {
                $message_insert_data['set_time'] = $set_time;
                $message_insert_data['again_time']     = gmdate('Y-m-d H:i:s', strtotime($set_time));
                $message_insert_data['real_send_time'] = gmdate('Y-m-d H:i:s', strtotime($set_time));//发布时间

            } else {
                // 为空，则增加默认值，配合索引
                $message_insert_data['set_time'] = '1970-01-01 00:00:00';
            }
            //questionnaire_lib_id,feedback_setting,end_time
            if (!empty($questionnaire_lib_id)) {
                $message_insert_data['questionnaire_lib_id'] = $questionnaire_lib_id;
            }
            if (in_array($feedback_setting, [0, 1])) {
                $message_insert_data['feedback_setting'] = $feedback_setting;
            }
            if (!empty($end_time)) {
                $message_insert_data['end_time'] = $end_time;
            }
            if (!empty($category_code)) {
                $message_insert_data['category_code'] = $category_code;
            }

            $this->getDI()->get("logger")->write_log("add kit message message data is  ".json_encode($message_insert_data,
                    JSON_UNESCAPED_UNICODE), "info");


            if (in_array($category, [7, 33, 141])) {
                $message_insert_data['recipient_staff_info_id'] = intval(current($staff_users));
                $by_db_message                                  = $by_db->insertAsDict('message', $message_insert_data);

                if (!$by_db_message) {
                    $by_db->rollback();
                    $message_db->rollback();
                    return ['error,please,try again3', '-1', []];
                }
            }
            
            $by_db->commit();
            $message_db->commit();
            return ['ok', '1', [$id]];
        } catch (\Exception $e) {
            $by_db->rollback();
            $message_db->rollback();
            return [$e->getMessage(), '-1', []];
        }
    }

    /**
     * 生成问卷消息在backyard的详情模板
     * @param $remote_msg_id  int 远端消息ID
     * @return $tpl string 模板内容
     *
     */
    public function make_qn_msg_remote_tpl($remote_msg_id = 0){
        // 模板跳转内容, 改为 backyard端输出时, 自行处理 2020.01
        return '';

        $questionnaire_msg_url = "BACKYARD_FRONT_HOST?msg_id=$remote_msg_id";
        $tpl = <<<EOF
        <meta name=viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" /><div style="postion:fixed;left:0;top:0;width:100%;height:100%"><iframe src='{$questionnaire_msg_url}' width='100%' height='100%' scrolling='no' frameborder="0"></iframe></div>
EOF;

        return trim($tpl);
    }

    /**
     * 迁移hcm
     * @param $db_connection
     * @return bool
     */
    public function topMessage4($db_connection): bool
    {
        $sql = "SELECT id,remote_message_id,top_status FROM message WHERE top_status = 1 ORDER BY updated_at DESC LIMIT 100 OFFSET 4";
        $rdb = $this->getDI()->get('db_rby');
        while (($tops = $rdb->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC)) && !empty($tops)) {
            foreach ($tops as $msg) {
                if ($db_connection['db']->execute("UPDATE message SET top_status = 3  WHERE id = {$msg['id']}") === false) {
                    return false;
                }
                if ($db_connection['db_message']->execute("UPDATE message_courier SET top_state = 0  WHERE message_content_id = '{$msg['remote_message_id']}'") === false) {
                    return false;
                }
            }
        }
        return true;
    }
}