<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\PasswordHash;
class PayrollServer extends BaseServer{

    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    public function verify_fle($data)
    {
        $data['account'] = intval($data['account']);
        // 判断是否为空
        if( empty($data['account']) || empty($data['pwd']) ) return '';
        $hash = new PasswordHash(10, false);
        // 查询数据库
        $pusher_sql = 'select id,name,email,encrypted_password,organization_id,department_id as position_category from staff_info where id = ?';
        $dataFind = $this->getDI()->get('db_fle')->query($pusher_sql, [$data['account']])->fetch(\Phalcon\Db::FETCH_ASSOC);
        if( empty($dataFind) ) return '';

        $bool = $hash->checkPassword($data['pwd'],$dataFind['encrypted_password']);
        if (!$bool)return '';
        return $dataFind;
    }

    //泰国 马来 上月24 到 本月 23
    public function formatSalaryDate($month){
        $start = date('Y-m-24', strtotime("{$month} -1 month"));

        $end = date('Y-m-23', strtotime("{$month}"));

        return [$start,$end];
    }



}