<?php

namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingOvertimeApplyModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\StaffConsentAgreementLogModel;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;


class OsOvertimeServer extends AuditBaseServer
{

    public $staffInfo;//hrs 员工信息
    public $osStaffInfo;//被操作人 外协员工信息
    public $param;//入参
    public $shiftInfo;//班次信息
    public $userInfo;//登陆的 fle 的 用户信息

    public function __construct($lang = 'zh-CN', $timezone, $userInfo = [])
    {
        $this->userInfo = $userInfo;
        parent::__construct($lang, $timezone);
    }

    private static $single = null;

    public static function getInstance($lang, $timezone)
    {
        if (!self::$single) {
            self::$single = new self($lang, $timezone);
        }
        return self::$single;
    }


    //获取可视范围网点 管辖网点 和所属网点合集
    public function getViewStores($param)
    {
        //管辖网点和大区
        $re             = new StaffWorkAttendanceRepository($this->lang);
        $storeData      = $re->getManagerStore($param['staff_id']);
        $storeCondition = empty($storeData) ? [] : array_column($storeData, 'store_id');
        //查询条件
        $conditions    = ' state = :state: ';
        $bind['state'] = SysStoreModel::STATE_1;
        //所属网点
        $staffRe   = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($param['staff_id']);
        if ($staffInfo['sys_store_id'] != enums::HEAD_OFFICE_ID) {
            $storeCondition[] = $staffInfo['sys_store_id'];
        }
        //负责网点
        $manageStore = SysStoreModel::find([
            'columns'    => 'id, name',
            'conditions' => 'manager_id = :staff_id:',
            'bind'       => ['staff_id' => $param['staff_id']],
        ])->toArray();
        if (!empty($manageStore)) {
            $storeCondition = array_merge($storeCondition, array_column($manageStore, 'id'));
        }
        if (!empty($storeCondition)) {
            $storeCondition = array_values($storeCondition);
            $conditions     .= ' and id in ({ids:array}) ';
            $bind['ids']    = $storeCondition;
        }

        $page = $param['page'] ?? 1;
        $size = $param['size'] ?? 100;
        if (!empty($param['store_str'])) {
            $conditions     .= ' and (id like :id_str: or name like :id_str:) ';
            $str            = trim($param['store_str']);
            $bind['id_str'] = "%{$str}%";
        }

        $data = SysStoreModel::find([
            'columns'    => 'id as store_id, name as store_name',
            'conditions' => $conditions,
            'bind'       => $bind,
            'offset'     => ($page - 1) * $size,
            'limit'      => $size,
        ])->toArray();

        return $data;
    }

    //获取 当前网点 打卡的班次和数量
    public function getViewShifts($param)
    {
        if (empty($param['store_id'])) {
            return [];
        }
        $today     = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        //取配置信息
        $envModel = new SettingEnvServer();
        $setting  = $envModel->getSetVal('OS_OT_apply_staff');
        $whereString = '';
        if(!empty($setting)){
            $whereString = $this->formatSettingWhere($setting);
        }

        if(empty($setting) || empty($whereString)){
            return [];
        }

        $builder  = $this->modelsManager->createBuilder();
        $builder->from(['s' => StaffWorkAttendance::class]);
        $builder->columns("s.shift_id,h.type,h.start,h.[end],count(s.staff_info_id) as num");
        $builder->join(HrStaffInfoModel::class, 's.staff_info_id = staff.staff_info_id and staff.formal = 0', 'staff');
        $builder->join(HrShiftModel::class, 's.shift_id = h.id', 'h');
        $builder->andWhere('staff.sys_store_id = :store_id:', ['store_id' => $param['store_id']]);
        $builder->andWhere('s.started_at is not null');
        $builder->inWhere('s.attendance_date', [$today, $yesterday]);
        if (!empty($setting) && !empty($whereString)) {
            $builder->andWhere($whereString);
        }
        $builder->groupBy('s.shift_id');
        $data = $builder->getQuery()->execute()->toArray();
        if(empty($data)){
            return [];
        }
        foreach ($data as &$da){
            switch ($da['type']) {
                case 'EARLY':
                    $shift = $this->getTranslation()->_('shift_early');
                    break;
                case 'MIDDLE':
                    $shift = $this->getTranslation()->_('shift_middle');
                    break;
                case 'NIGHT':
                    $shift = $this->getTranslation()->_('shift_night');
                    break;
                default:
                    $shift = $this->getTranslation()->_('shift_early');
            }
            $da['shift_name'] = $shift;
        }

        return $data;
    }


    //获取对应网点 可申请工号列表
    public function getOsStaffs($param)
    {
        //网点和班次id 获取对应 当天和前一天 打卡的外协员工
        if (empty($param['store_id']) || empty($param['shift_id'])) {
            return [];
        }
        $today     = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        //取配置信息
        $envModel = new SettingEnvServer();
        $setting  = $envModel->getSetVal('OS_OT_apply_staff');
        $whereString = '';
        if(!empty($setting)){
            $whereString = $this->formatSettingWhere($setting);
        }
        if(empty($setting) || empty($whereString)){
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => StaffWorkAttendance::class]);
        $builder->columns("staff.staff_info_id,staff.name,staff.job_title,j.job_name");
        $builder->join(HrStaffInfoModel::class, 's.staff_info_id = staff.staff_info_id and staff.formal = 0', 'staff');
        $builder->join(HrJobTitleModel::class, 'staff.job_title = j.id', 'j');
        $builder->andWhere('staff.sys_store_id = :store_id:', ['store_id' => $param['store_id']]);
        $builder->andWhere('s.shift_id = :shift_id:', ['shift_id' => $param['shift_id']]);
        $builder->andWhere('s.started_at is not null');
        $builder->inWhere('s.attendance_date', [$today, $yesterday]);
        if (!empty($setting) && !empty($whereString)) {
            $builder->andWhere($whereString);
        }

        $data = $builder->getQuery()->execute()->toArray();
        $data = array_column($data,null,'staff_info_id');
        $data = array_values($data);
        return $data;
    }

    //又改成 带部门的配置了  部门｜职位,部门｜职位
    public function formatSettingWhere($setting){
        $setting = str_replace('，',',',$setting);
        $setting = str_replace('｜','|',$setting);
        $setting = explode(',', $setting);
        $depServer = new SysDepartmentServer($this->lang, $this->timeZone);

        $where = [];
        foreach ($setting as $item){
            $row = explode('|', $item);
            if(empty($row[0]) || empty($row[1])){
                continue;
            }
            $departmentId = intval($row[0]);
            $depIds = $depServer->getChildrenListByDepartmentIdV2($departmentId, true);
            $depIds[] = $departmentId;
            $depIds = implode(',', $depIds);
            $jobId = intval($row[1]);
            $where[] = " (staff.node_department_id in ({$depIds}) and staff.job_title = {$jobId}) ";
        }
        if(empty($where)){
            return '';
        }
        $str = '(' . implode('or', $where) . ')';
        return $str;
    }

    /**
     * 新建加班
     * @param array $param
     * @return array
     * @throws \Exception
     */
    public function addOsOt($param)
    {
        if (empty($param['date_at']) || empty($param['start_time']) || empty($param['type']) || empty($param['staff_info_ids'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        $duration = floatval($param['duration']);
        $end_time = date('Y-m-d H:i:s', strtotime($param['start_time']) + ($duration * 3600));

        //写数据表 等任务 异步生成审批流
        $insert['staff_info_id'] = $param['staff_id'];//操作申请人
        $insert['os_staff_ids']  = $param['staff_info_ids'];
        $insert['type']          = OutsourcingOvertimeModel::TYPE_ADD_WORK_TIME;
        $insert['start_time']    = $param['start_time'];
        $insert['end_time']      = $end_time;
        $insert['duration']      = $duration;
        $insert['reason']        = $param['reason'];
        $insert['date_at']       = $param['date_at'];
        $insert['lang']          = $this->lang;
        $model                   = new OutsourcingOvertimeApplyModel();
        $model->create($insert);
        return $this->checkReturn(['data' => []]);
    }

    //任务跑生成审批记录
    public function addByTask($param)
    {
        $this->param = $param;

        $this->initOsOt();
        /**
         * @see \FlashExpress\bi\App\Modules\Ph\Server\OsOvertimeServer::checkPeriod()
         * @see \FlashExpress\bi\App\Modules\Ph\Server\OsOvertimeServer::checkOsOt()
         */
        $this->checkPeriod();
        $this->checkOsOt();
        $this->checkTypeDuration();

        $this->saveOsOtApproval();
    }

    //初始化数据
    public function initOsOt()
    {
        $osId                    = $this->param['os_id'];
        $this->param['duration'] = floatval($this->param['duration']);
        $this->param['end_time'] = date('Y-m-d H:i:s',
            strtotime($this->param['start_time']) + ($this->param['duration'] * 3600));
        $shiftServer             = new HrShiftServer();
        //外协员工班次
        $shift_info      = $shiftServer->getShiftInfos($osId, [$this->param['date_at']]);
        $this->shiftInfo = $shift_info[$this->param['date_at']] ?? [];
        //操作人 信息 审批用
        $staff_re          = new StaffRepository($this->lang);
        $this->staffInfo   = $staff_re->getStaffPosition($this->param['staff_id']);
        $this->osStaffInfo = $staff_re->getStaffPosition($this->param['os_id']);
    }


    //申请加班操作 验证 有没有类型和时长的权限
    public function checkTypeDuration()
    {
        $checkParam['staff_id'] = $this->staffInfo['staff_info_id'];
        //重写方法
        $typeList = $this->getTypeOsOvertime($checkParam);

        if ($typeList['code'] != 1 || empty($typeList['data']['dataList'])) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }
        //整理数据
        $typeList = array_column($typeList['data']['dataList'], null, 'code');

        //能申请的 类型
        $typeArr = array_keys($typeList);
        if (!in_array($this->param['type'], $typeArr)) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }

        //能申请的时长
        $durationArr = array_column($typeList[$this->param['type']]['duration'], 'time_hour');
        $this->logger->write_log("checkTypeDuration {$this->staffInfo['id']} " . json_encode($typeArr) . json_encode($durationArr),
            'info');
        if (!in_array($this->param['duration'], $durationArr)) {
            throw new ValidationException($this->getTranslation()->_('os_ot_duration_permission'));
        }
        return true;
    }


    //保存审批流相关 任务调用
    public function saveOsOtApproval()
    {
        $serialNo    = $this->getID();
        $insertParam = [
            'serial_no'     => (!empty($serialNo) ? 'OSOT' . $serialNo : null),
            'oid'           => $this->param['id'],
            'staff_info_id' => $this->param['staff_id'],
            'os_staff_id'   => $this->param['os_id'],
            'type'          => $this->param['type'],
            'start_time'    => $this->param['start_time'],
            'end_time'      => $this->param['end_time'],
            'reason'        => $this->param['reason'],
            'reject_reason' => '',
            'state'         => enums::$audit_status['panding'],
            'duration'      => $this->param['duration'],
            'date_at'       => $this->param['date_at'],
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        $model = new OutsourcingOvertimeModel();

        try {
            $model->create($insertParam);
            //创建 审批数据
            $server    = new ApprovalServer($this->lang, $this->timeZone);

            //扩展
            $extend['from_submit'] = [
                'staff_info_id' => $this->param['os_id'],
            ];

            $requestId = $server->create($model->id,
                AuditListEnums::APPROVAL_TYPE_OVERTIME_OS,
                $this->param['staff_id'],
                null,
                $extend
            );
            if (!$requestId) {
                $db->rollback();
                $this->logger->write_log("saveOsOtApproval {$this->param['staff_id']} {$this->param['os_id']} failed");
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("saveOsOtApproval {$this->param['staff_id']} {$this->param['os_id']} failed " . $e->getMessage());
        }
        return true;
    }


    //批量添加 错误信息 给申请人发消息
    public function sendErrorMsg($info, $errorData)
    {
        //获取员工名称
        $staffIds  = array_column($errorData, 'staff_info_id');
        $staffName = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id, name',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        if (empty($staffName)) {
            $this->logger->write_log("sendErrorMsg 没有员工信息 " . json_encode($staffIds), 'info');
            return true;
        }
        $staffName = array_column($staffName, 'name', 'staff_info_id');
        $title     = $this->getTranslation()->_('os_ot_add_approval_title');
        $content   = $this->getTranslation()->_('os_ot_add_approval_content',
            ['date_at' => $info->date_at, 'start_time' => $info->start_time, 'duration' => $info->duration]);
        foreach ($errorData as $error) {
            $content .= '</br>' . "({$error['staff_info_id']}){$staffName[$error['staff_info_id']]} {$error['msg']}";
        }
        //发消息
        $send_message = [
            'staff_users'        => [$info->staff_info_id],
            'message_title'      => $title,
            'message_content'    => "<p style='font-size: 32px;'>" . $content . "</p>",
            'staff_info_ids_str' => $info->staff_info_id,
            'category'           => -1,
            'push_state'         => 1,
            'id'                 => time() . $info->staff_info_id . rand(1000000, 9999999),
        ];
        $hcm_rpc      = new ApiClient('hcm_rpc', '', 'add_kit_message', $info->lang);
        $hcm_rpc->setParams($send_message);
        $res = $hcm_rpc->execute();
        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::SUCCESS) {
            $this->getDI()->get('logger')->write_log([
                'function' => 'overtime sendMessage',
                'message'  => '消息发送失败',
                'params'   => $send_message,
                'result'   => $res,
            ]);
        }
        return true;
    }


    /**
     * 审批
     * @param $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateOsOt($paramIn)
    {
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $auditId = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason');
        if (!empty($reason)) {
            $reason = filter_param($reason);
        }
        $info = OutsourcingOvertimeModel::findFirst($auditId);
        if (empty($info)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
        $info = $info->toArray();
        //oa 审批处理中 不能操作
        if ($info['in_approval'] == OutsourcingOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('ot_in_approval_notice'));
        }
        $server = new ApprovalServer($this->lang, $this->timeZone);
        //校验当前审批人审批状态
        $flag = false;
        if ($paramIn['status'] == enums::$audit_status['approved']) {
            $flag = $server->approval($auditId, AuditListEnums::APPROVAL_TYPE_OVERTIME_OS, $staffId, $reason);
        }
        //驳回
        if ($paramIn['status'] == enums::$audit_status['dismissed']) {
            $flag = $server->reject($auditId, AuditListEnums::APPROVAL_TYPE_OVERTIME_OS, $reason, $staffId);
        }
        //撤销
        if ($paramIn['status'] == enums::$audit_status['revoked']) {
            //撤销验证
            $this->cancelCheck($info);
            if ($info['staff_info_id'] == $staffId) {//自己撤销
                $server->cancel($auditId, AuditListEnums::APPROVAL_TYPE_OVERTIME_OS, $reason, $staffId);
            } else {
                $server->approvalCancel($auditId, AuditListEnums::APPROVAL_TYPE_OVERTIME_OS, $reason, $staffId);
            }
        }
        return $this->checkReturn([]);
    }

    /**
     * @param $data
     * @return bool
     * @throws ValidationException
     */
    public function cancelCheck($data)
    {
        //已撤销的不能再撤销
        if (in_array($data['state'], [enums::APPROVAL_STATUS_CANCEL])) {
            throw new ValidationException('have canceled', enums::$ERROR_CODE['1000']);
        }
        //新增逻辑 每个月20号之后，不可以撤销所选日期为本月15号以及之前的申请数据。
        $tmp_20      = strtotime(date('Y-m-20'));
        $tmp_15      = strtotime(date('Y-m-16'));
        $current_tmp = time();
        $apply_tmp   = strtotime($data['date_at']);
        if ($current_tmp >= $tmp_20 && $apply_tmp < $tmp_15) {//已经过了20号
            throw new ValidationException($this->getTranslation()->_('2207'));
        }
        return true;
    }


    /**
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed
     * @throws ValidationException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $info = OutsourcingOvertimeModel::findFirst($auditId);
        if (empty($info)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staffRe     = new StaffRepository($this->lang);
        $staffInfo   = $staffRe->getStaffPosition($info->staff_info_id);
        $osStaffInfo = $staffRe->getStaffPosition($info->os_staff_id);

        //[2]组织详情数据
        $typeList = $this->getDetailOtType();

        $detailLists['apply_parson']     = "{$staffInfo['name']}({$staffInfo['staff_info_id']})";
        $detailLists['apply_department'] = $staffInfo['depart_name'];
        $detailLists['os_staff']         = "{$osStaffInfo['name']}({$osStaffInfo['staff_info_id']})";;
        $detailLists['os_staff_job_title'] = $osStaffInfo['job_name'];
        $detailLists['os_department']      = $osStaffInfo['depart_name'];
        $detailLists['os_overtime_date']         = $info->date_at;
        $detailLists['os_overtime_type']         = $typeList[$info->type] ?? '';
        $detailLists['start_time']         = $info->start_time;
        $detailLists['end_time']           = $info->end_time;
        $detailLists['duration']           = round($info->duration, 1);
        $detailLists['os_overtime_reason']       = $info->reason;
        $detailLists['ot_detail_6']        = $osStaffInfo['store_name'];
        $auditListRe                       = new AuditlistRepository($this->lang, $this->timeZone);

        $returnData['data']['detail'] = $this->format($detailLists);
        $data                         = [
            'title'       => $auditListRe->getAudityType(enums::APPROVAL_TYPE_OVERTIME_OS),
            'id'          => $info->id,
            'staff_id'    => $info->staff_info_id,
            'type'        => enums::APPROVAL_TYPE_OVERTIME_OS,
            'created_at'  => show_time_zone($info->created_at),
            'updated_at'  => show_time_zone($info->updated_at),
            'status'      => $info->state,
            'status_text' => $auditListRe->getAuditStatus('10' . $info->state),
            'serial_no'   => $info->serial_no ?? '',
        ];


        $returnData['data']['head'] = $data;
        return $returnData;
    }

    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true, true, true, true, true, false);
    }

    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        //获取加班详情
        $info = OutsourcingOvertimeModel::findFirst($auditId);
        if (empty($info)) {
            return [];
        }

        //新版 duration
        $new_duration = round($info->duration, 2);
        $param        = [
            [
                'key'   => "os_overtime_date",
                'value' => $info['date_at'],
            ],
            [
                'key'   => "os_overtime_type",
                'value' => $info['type'],
            ],
            [
                'key'   => "duration",
                'value' => $new_duration . 'h',
            ]
            ,
            [
                'key'   => "created_at",
                'value' => show_time_zone($info['created_at']),
            ],
        ];
        return $param;
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $detail = OutsourcingOvertimeModel::findFirst($auditId);
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                $detail->reject_reason = isset($extend['remark']) ? $extend['remark'] : '';
            }
            //最后审批人信息记录
            $detail->approval_id = $extend['staff_id'] ?? null;
            if(!empty($extend['staff_id'])){
                $approveInfo = HrStaffInfoModel::findFirst("staff_info_id={$extend['staff_id']}");
                $detail->approval_name = $approveInfo->name;
            }
            $detail->state       = $state;
            $detail->in_approval = OutsourcingOvertimeModel::NOT_IN_APPROVAL;

            if($state != Enums::APPROVAL_STATUS_APPROVAL){
                $detail->salary_state = OutsourcingOvertimeModel::SALARY_STATE_AUDIT_FAIL;
            }

            $detail->update();
        }
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    /**
     * 获取加班类型
     */
    public function getDetailOtType()
    {
        $ot_server = Tools::reBuildCountryInstance($this, [$this->lang, $this->timeZone]);
        $data      = $ot_server->getAllType($this->lang);
        return array_column($data, 'msg', 'code');
    }


    /**
     * OT提醒
     * @return int
     */
    public function isReadRule(): int
    {
        $uid                           = $this->userInfo['id'];
        $StaffConsentAgreementLogModel = StaffConsentAgreementLogModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND type = :type: ',
            'bind'       => [
                'staff_info_id' => $uid,
                'type'          => StaffConsentAgreementLogModel::TYPE_OS_OVERTIME_RULE,
            ],
        ]);

        if ($StaffConsentAgreementLogModel) {
            return 1;
        }
        $staffItemModel                = new StaffConsentAgreementLogModel();
        $staffItemModel->staff_info_id = $uid;
        $staffItemModel->type          = StaffConsentAgreementLogModel::TYPE_OS_OVERTIME_RULE;
        $staffItemModel->content       = 1;
        $staffItemModel->save();
        return 0;
    }

    //获取各个国家的 ot 规则弹窗
    public function getOtRuleText($param)
    {
        $staffRe   = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($this->userInfo['id']);
        if (empty($staffInfo)) {
            return '';
        }
        $countryServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timeZone]);
        return $countryServer->ruleConfirm($staffInfo);
    }

    //目前只 越南 其他国家 按原来走
    public function ruleConfirm($staffInfo)
    {
        return '';
    }


    //整理根据配置返回的 类型数组 对应的加班类型
    public function formatPermissionOvertimeType($dataList, $settingStr)
    {
        if (empty($settingStr)) {
            return [];
        }
        $typeList = str_replace('，', ',', $settingStr);
        $typeList = explode(',', $typeList);
        sort($typeList);
        $data   = array_column($dataList, null, 'code');
        $return = [];
        foreach ($typeList as $type) {
            $type     = (int)$type;
            $return[] = $data[$type];
        }
        return $return;
    }

    //获取 加班类型 对应的 小时枚举 每个人不一样 走规则配置
    public function getOsTypeDuration($data)
    {
        $position = HrStaffInfoPositionModel::find("staff_info_id = {$this->staffInfo['staff_info_id']}")->toArray();
        $position = empty($position) ? [] : array_column($position, 'position_category');
        $params   = [
            'w_f_condition_staff_id'           => $this->staffInfo['staff_info_id'],
            'w_f_condition_store_id'           => $this->staffInfo['sys_store_id'],
            'w_f_condition_job_title'          => $this->staffInfo['job_title'],
            'w_f_condition_sex'                => $this->staffInfo['sex'],
            'w_f_condition_state'              => $this->staffInfo['state'],
            'w_f_condition_job_title_grade_v2' => $this->staffInfo['job_title_grade_v2'],
            'w_f_condition_node_department_id' => $this->staffInfo['node_department_id'],
            'w_f_condition_hire_type'          => $this->staffInfo['hire_type'],
            'w_f_condition_category'           => $this->staffInfo['category'],
            'w_f_condition_probation'          => $this->staffInfo['status'] == HrProbationModel::STATUS_FORMAL ? 1 : 2,
            'w_f_condition_position_category'  => $position,
            'w_f_condition_nationality'        => $this->staffInfo['nationality'],
        ];

        // 配置 获取加班类型
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OS_OT_apply_rules')
            ->loadParameters($params)
            ->getConfig();

        $setting = $res['response_data'] ?? [];//字符串 逗号分割 类型
        $data    = $this->formatPermissionOvertimeType($data, $setting);

        //2-8小时 产品说 1-8小时
        $i             = 1;
        $limitDuration = [];
        while ($i <= 8) {
            $limitDuration[] = $i;
            $i               += 0.5;
        }
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OS_OT_Hours')
            ->loadParameters($params)
            ->getConfig();

        $durations = $res['response_data'] ?? '';
        //整理时长
        $durations = str_replace('，', ',', $durations);
        $durations = explode(',', trim($durations));
        sort($durations);
        foreach ($data as &$da) {
            $da['duration'] = [];
            foreach ($durations as $d) {
                if (!in_array($d, $limitDuration)) {
                    continue;
                }
                $row['time_hour'] = (float)$d;
                $row['time_text'] = "{$d}h";
                $da['duration'][] = $row;
            }
        }
        return $data;
    }

    //oa 系统 获取 加班列表页 三个月的数据
    public function rpcOtList($param)
    {
        $return = ['count' => 0, 'list' => []];
        if (empty($param['operate_id'])) {
            return self::checkReturn(['data' => $return]);
        }
        $addHour = $this->config->application->add_hour;

        //[2]获取列表数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['a' => AuditApplyModel::class]);
        $builder->join(HrStaffInfoModel::class, 'a.submitter_id = s.staff_info_id', 's');
        $builder->join(AuditApprovalModel::class, 'a.flow_id = p.flow_id and a.biz_type = 77', 'p');
        $builder->join(OutsourcingOvertimeModel::class, 'o.id = a.biz_value', 'o');
        $builder->join(HrStaffInfoModel::class, 'o.os_staff_id = os.staff_info_id', 'os');
        if (!empty($param['piece_id']) || !empty($param['region_id'])) {
            $builder->leftJoin(SysStoreModel::class, 'os.sys_store_id = store.id', 'store');
        }

        $builder = $this->rpcBuildCondition($builder, $param);
        //分页 总数
        $totalCount      = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();
        $return['count'] = (int)$totalCount->total;
        if (empty($return['count'])) {
            return self::checkReturn(['data' => $return]);
        }
        $builder->columns("o.id,os.staff_info_id,os.staff_info_id as os_staff_id,os.name,s.staff_info_id as operate_id,s.name as operate_name,a.final_approver,os.node_department_id,os.sys_department_id,
        os.sys_store_id as store_id,o.created_at,o.date_at,o.type,o.duration,a.updated_at,p.state approval_state,o.state,o.in_approval");

        $page   = $param['pageNum'] ?? 1;
        $size   = $param['pageSize'] ?? 20;
        $offset = $size * ($page - 1);
        $builder->limit($size, $offset);
        //排序
        $builder->orderBy("a.created_at desc");
        $data = $builder->getQuery()->execute()->toArray();


        //网点名称信息 store_name
        $storeIds    = array_column($data, 'store_id');
        $storeIds    = array_diff($storeIds, [null]);
        $storeIds    = array_values(array_unique($storeIds));
        $storeServer = new SysStoreServer($this->lang, $this->timeZone);
        $storeInfo   = $storeServer->batchStorePieceRegion($storeIds);

        $dept_ids = array_column($data, 'node_department_id');
        if (!empty($dept_ids)) {
            //部门名称 department_name
            $departmentData = SysDepartmentModel::find([
                'columns'    => 'id, name',
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $dept_ids],
            ])->toArray();
        }

        $departmentData = empty($departmentData) ? [] : array_column($departmentData, 'name', 'id');

        $otServer    = new OsOvertimeServer($this->lang, $this->timeZone);
        $otServer    = Tools::reBuildCountryInstance($otServer, [$this->lang, $this->timeZone]);
        $allOtType   = $otServer->getAllType($this->lang);
        $allOtType   = array_column($allOtType, 'msg', 'code');
        $auditListRe = new AuditlistRepository($this->lang, $this->timeZone);

        foreach ($data as &$da) {
            $storeId               = $da['store_id'];
            $da['operator']        = "{$da['operate_name']}({$da['operate_id']})";
            $da['store_name']      = $storeInfo[$storeId]['store_name'] ?? '';
            $da['department_name'] = $departmentData[$da['node_department_id']] ?? '';
            $da['region_name']     = $storeInfo[$storeId]['region_name'] ?? '';
            $da['piece_name']      = $storeInfo[$storeId]['piece_name'] ?? '';
            $da['created_at']      = date('Y-m-d H:i:s', strtotime($da['created_at']) + ($addHour * 3600));//申请时间
            $da['updated_at']      = date('Y-m-d H:i:s', strtotime($da['updated_at']) + ($addHour * 3600));//审批时间
            $da['type_text']       = $allOtType[$da['type']] ?? '';//ot类型
            $da['duration']        = $da['duration'] . 'h';//时长
            $da['state_text']      = $auditListRe->getAuditState($da['state']);//审批状态
            if ($da['in_approval'] == OutsourcingOvertimeModel::IN_APPROVAL) {
                $da['state_text'] .= '(' . $this->getTranslation()->_('in_approval') . ')';
            }

            //如果 审核通过 并且当前登陆人是最后审批人 可以撤销
            $da['is_cancel_button'] = false;
            if ($da['state'] == enums::$audit_status['approved'] && $da['final_approver'] == $param['operate_id']) {
                $da['is_cancel_button'] = true;
            }
        }

        $return['list'] = $data;
        return self::checkReturn(['data' => $return]);
    }

    //构造条件
    public function rpcBuildCondition($builder, $param)
    {
        $addHour = $this->config->application->add_hour;
        $builder->andWhere('p.approval_id = :approval_id:', ['approval_id' => $param['operate_id']]);//审批人
        //当前登陆人的待审批列表
        if (!empty($param['tab_type']) && $param['tab_type'] == 1) {
            $builder->andWhere('p.state = 1');
        } else {
            //已处理tab页 不展示 当前登陆人待审批数据
            $builder->andWhere('p.state != 1');
            if (!empty($param['state'])) {
                $builder->inWhere('o.state', $param['state']);//审批状态
            }
        }

        if (!empty($param['os_staff_id'])) {//外协工号
            $builder->andWhere('o.os_staff_id = :os_staff_id:', ['os_staff_id' => $param['os_staff_id']]);
        }

        //申请人工号
        if (!empty($param['staff_info_id'])) {//submitter_id
            $builder->andWhere('a.submitter_id = :submitter_id:', ['submitter_id' => $param['staff_info_id']]);//申请人
        }

        if (!empty($param['apply_time_start'])) {
            //转零时区
            $start = date('Y-m-d H:i:s', strtotime("{$param['apply_time_start']} -{$addHour} hour"));
            $end   = date('Y-m-d H:i:s', strtotime("{$param['apply_time_end']} -{$addHour} hour +1 day") - 1);
            $builder->betweenWhere('o.created_at', $start, $end);
        }
        //ot 类型
        if (!empty($param['type'])) {
            $builder->inWhere('o.type', $param['type']);
        }

        //ot 日期
        if (!empty($param['ot_date_start'])) {
            $builder->betweenWhere('o.date_at', $param['ot_date_start'], $param['ot_date_end']);
        }

        if (!empty($param['department_id'])) {
            //是否包含子部门
            if (!empty($param['is_sub_department'])) {
                $ids = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($param['department_id']);
            }
            if (empty($ids)) {
                $ids[] = $param['department_id'];
            }
            $builder->inWhere('os.node_department_id', $ids);//部门
        }

        if (!empty($param['store_id'])) {
            $builder->inWhere('os.sys_store_id', $param['store_id']);//网点id
        }

        if (!empty($param['piece_id'])) {
            $builder->inWhere('store.manage_piece', $param['piece_id']);//片区id
        }
        if (!empty($param['region_id'])) {
            $builder->andWhere('store.manage_region = :region_id:', ['region_id' => $param['region_id']]);//大区id
        }

        return $builder;
    }


    //所有加班类型展示 这里面不做权限判断
    public function getAllType($locale = '')
    {
        return [];
    }


}
