<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\RoyaltyRepository;

class RoyaltyServer extends BaseServer
{
    protected $re;
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone, $userInfo = [])
    {
        parent::__construct($lang);
        $this->royalty = new RoyaltyRepository($timezone);
        $this->staff = new StaffRepository();
        $this->department = new DepartmentRepository();
        $this->userInfo = $userInfo;
        $this->language = $lang;
        $this->timezone = $timezone;
    }
     /** 获取提成详情
     * @param array $paramIn
     * @return array
     */
    public function getRoyaltyInfo($paramIn = []){
        try {
            $t = $this->getTranslation();
            $userinfo = $this->processingDefault($paramIn, 'userinfo');
            $staffId = isset($userinfo['staff_id']) ? $userinfo['staff_id'] : '';
            if (empty($staffId)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }

            //角色校验权限
//            $staffRole = $this->department->staffRoles(['staff_id' => $staffId]);
//            $staffRoleArr = array_column($staffRole, "position_category");
//            $roleIdsData = [1, 2, 3, 18];
//            $position = array_intersect($roleIdsData, $staffRoleArr);
//            if (!$position) {
//                return $this->checkReturn(-3, $this->getTranslation()->_('8000'));
//            }

            //员工信息
            $staffInfo = $this->staff->checkoutStaffBi($staffId);
            //获取env开关 true=开启总部判断 默认false
            $headOffice = env("headOffice",false);
            if ($headOffice){
                //校验权限 网点账号、在职、停职、编制内的Flash Express员工
                if ($staffInfo['formal'] != 1 || $staffInfo['state'] == 2 || $staffInfo['sys_store_id'] == -1 || empty($staffInfo)){
                    return $this->checkReturn(-3,$t->_('8000'));
                }
            }else{
                //new 校验权限 所有账号、在职、停职、编制内的Flash Express员工
                if ($staffInfo['formal'] != 1 || $staffInfo['state'] == 2 || empty($staffInfo)){
                    return $this->checkReturn(-3,$t->_('8000'));
                }
            }


            $returnArr['staffInfo']['staff_id'] = isset($staffInfo['staff_info_id']) ? $staffInfo['staff_info_id'] : '';
            $returnArr['staffInfo']['name'] = isset($staffInfo['name']) ? $staffInfo['name'] : '';
            $returnArr['staffInfo']['mobile'] = isset($staffInfo['mobile']) ? $staffInfo['mobile'] : '';


            //提成详细
            $royaltyParam = [
                'staff_id' => $staffId,
            ];
            $royaltyData = $this->royalty->getRoyaltyInfo($royaltyParam);

            foreach ($royaltyData["UserRoyaltyList"] as $k=>$v){
                $royaltyData["UserRoyaltyList"][$k]['user_type_name'] = "";
                if ($v['user_type'] == 1){
                        $royaltyData["UserRoyaltyList"][$k]['user_type_name'] = "(". $t->_('royalty_type_c').")";
                   }elseif ($v['user_type'] == 2){
                    $royaltyData["UserRoyaltyList"][$k]['user_type_name'] = "(". $t->_('royalty_type_ka').")";
                   }
            }
            $royaltyData['monthRoyaltyTitle'] = " ".intval($royaltyData['monthRoyaltyNum']/100) . "฿";
            $returnArr['royaltyInfo'] = $royaltyData;
            return $this->checkReturn(['data' => ['dataList' => $returnArr]]);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('获取提成详情 getRoyaltyInfo error :'.$e->getMessage().' \n 参数:'.json_encode($paramIn),'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }
}