<?php

namespace FlashExpress\bi\App\Server;

use Couchbase\PhraseSearchQuery;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOperationModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\LogServer;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;

class InterviewServer extends BaseServer {

    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone) {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 面试列表
     * @param array $params Description
     * @return array Description
     */
    public function getList($params) {
        //默认为待反馈列表
        $params['type'] = (!isset($params['type']) && empty($params['type'])) ? 0 : $params['type'];
        //获取列表数据
        $list = (new InterviewRepository($this->timezone))->getInterviewList($params);

        if ($list) {
            $_yu = $this->getTranslation()->_('4604');
            $_anpai = $this->getTranslation()->_('4429');

            //当前时间
            $add_hour = $this->config->application->add_hour;
            $_now_time = strtotime(gmdate('Y-m-d H:i:s', time())) + $add_hour*3600;

            foreach ($list['data'] as $k => $v) {
                //待反馈列表 是否超时
                if ($params['type'] == 0) {
                    $_interview_time = strtotime($v["interview_time"]);
                    $is_writing = $_now_time - $_interview_time;
                    if ($is_writing > 0) {
                        $timeInfo = getTimeDifference($_now_time,$_interview_time);
                        $str = '';
                        if($timeInfo['day']>0){
                            $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_day'),$timeInfo['day']);
                        }
                        if($timeInfo['hour']>0){
                            $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_hour'),$timeInfo['hour']);
                        }
                        if($timeInfo['min']>0){
                            $str.=sprintf($this->getTranslation()->_('wait_time_feedback_interview_minutes'),$timeInfo['min']);
                        }
                        $list['data'][$k]["wating_feedback"]  =  $str;
                    }else{
                        $list['data'][$k]["wating_feedback"] = '';
                    }
                }
                
                //已反馈列表 返回面试结果
                if ($params['type'] == 1 ) {
                    if($v['operation_state'] == 1){
                        $list['data'][$k]['conclusion_type'] = 1;//已通过
                    }elseif($v['operation_state'] == 2){
                        $list['data'][$k]['conclusion_type'] = 0;//未通过
                    }else{
                        if($v['operation_state'] == 3){
                            $list['data'][$k]['conclusion_type'] = 2; //已取消
                        }
                    }
                }
                $list['data'][$k]['name'] = empty( $list['data'][$k]['name'])?  $list['data'][$k]['first_name_en'].' '.$list['data'][$k]['last_name_en'] : $list['data'][$k]['name'];

                //已取消列表
                if($params['type'] == 2){
                    $list['data'][$k]['conclusion_type'] = 2;//$v['status'];已取消
                }
                //简历提供人
                $create_id = $v['create_id'] ?: $v['staff_id'];
                $staff_info = (new HrStaffInfoServer())->getUserInfoByStaffInfoId($create_id);

                $name = $staff_info ? $staff_info->name : '';

                $list['data'][$k]['staff_operate'] = 'HR ' . $name .' '. $_yu . $v['created_at'] . $_anpai;
                //获取岗位信息
                $job_title = HrJobTitleModel::findFirst([
                            'conditions' => 'id = :id:',
                            'bind' => ['id' => $v['job_title']],
                ]);
                $list['data'][$k]['job_name'] = $job_title ? $job_title->job_name : ' ';

                //时间格式化
                $list['data'][$k]['info_created_at'] = isset($list['data'][$k]['info_created_at']) ? show_time_zone($list['data'][$k]['info_created_at']) : '';
                $list['data'][$k]['opt_cancel_at'] = isset($list['data'][$k]['opt_cancel_at']) ? show_time_zone($list['data'][$k]['opt_cancel_at']) : '';

                unset($list['data'][$k]['job_title']);
                unset($list['data'][$k]['updated_at']);
                unset($list['data'][$k]['staff_id']);
                unset($list['data'][$k]['resume_provider_id']);
            }
        }

        return $list ? $list : null;
    }

    /**
     * 面试详情
     * @param array $params
     */
    public function getDetail($params) {
        //面试详情
        $interview_detail = (new InterviewRepository($this->timezone))->getInterviewDetail($params);
        if(empty($interview_detail)){
            //记录日志
            $this->getDI()->get("logger")->write_log("面试详情-getDetail获取面试信息失败：".json_encode($params,JSON_UNESCAPED_UNICODE), "info");
        }

        $interview_detail = isset($interview_detail[0]) ? $interview_detail[0] : [];
        
        if ($interview_detail) {
            $interview_detail['sex'] = $this->getResumeSex($interview_detail['sex'],$interview_detail['call_name']) == 1 ? $this->getTranslation()->_('4900') : $this->getTranslation()->_('4901');
            $interview_detail['age'] = $interview_detail['date_birth'] ? DateHelper::howOld($interview_detail['date_birth']) : ' ';
            $interview_detail['email'] = $interview_detail['email'] ? $interview_detail['email'] : $this->getTranslation()->_('4122');

            //获取岗位信息
            $job_title = HrJobTitleModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $interview_detail['job_title']]
            ]);


            //前端改了状态
            if(isset($interview_detail['state'])){
                if($interview_detail['state'] == 0){
                    $interview_detail['conclusion_type'] = null;//未反馈
                    $interview_detail['interview_state'] = null;// todo
                }elseif ($interview_detail['state'] == 1){
                    $interview_detail['conclusion_type'] = 1; //通过
                    $interview_detail['interview_state'] = 1;
                }elseif($interview_detail['state'] == 2){
                    $interview_detail['conclusion_type'] = 0;//不通过
                    $interview_detail['interview_state'] = 3;

                }else{ //$interview_detail['state'] == 3
                    $interview_detail['conclusion_type'] = 2;//取消
                    $interview_detail['interview_state'] = null;//todo
                }
            }else{
                $interview_detail['conclusion_type'] = 2;//取消
                $interview_detail['interview_state'] = null;//todo
            }


            //面试不通过原因，从type_record表取
            $interview_detail['reason_list'] = [];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hipt.pass_type, hipt.pass_reason, hipt.pass_remark');
            $builder->from(['main' => HrInterviewInfoModel::class]);
            $builder->leftjoin(HrInterviewPassTypeRecordModel::class, 'main.interview_info_id=hipt.business_id and hipt.deleted=0', 'hipt');
            $builder->andWhere('hipt.business_type=:business_type: AND hipt.deleted=:deleted: AND hipt.out_type=:out_type: AND main.interview_id=:interview_id:',
                ['business_type'=>enums::INTERVIEW_PASS_BUSINESS_TYPE, 'deleted'=>enums::DELETED_NO, 'out_type'=>enums::INTERVIEW_OUT_TYPE_1, 'interview_id' => $params['interview_id']]);
            if(!empty($params['opt_id'])){
                $builder->andWhere('hipt.opt_id=:opt_id:', ['opt_id'=>$params['opt_id']]);
            }
            $reason_list = $builder->getQuery()->execute()->toArray();
            $out_type_list = $this->getOutInterviewListServer(1);
            if(!empty($reason_list)) {
                foreach ($reason_list as $key => $val) {
                    if ($val['pass_type'] == enums::INTERVIEW_PASS_TYPE_DEFAULT) {
                        $reason_list[$key]['pass_reason'] = $out_type_list[$val['pass_reason']] ?? '';
                    } else {
                        $reason_list[$key]['pass_type'] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_PREFIX . $val['pass_type']);
                        if($val['pass_reason']==enums::INTERVIEW_PASS_NO_REASON){
                            $reason_list[$key]['pass_reason'] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_PREFIX . $val['pass_type']);
                        }else {
                            $reason_list[$key]['pass_reason'] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_REASON_PREFIX . $val['pass_type'] . '_' . $val['pass_reason']);
                        }
                    }
                }
                $interview_detail['reason_list'] = $reason_list;
            }



            $interview_detail['job_name'] = $job_title ? $job_title->job_name : ' ';
            $interview_detail['hire_type_text'] = !empty($interview_detail['hire_type']) ? $this->getTranslation()->_('hire_type_'.$interview_detail['hire_type']) : '';

            //获取简历附件
            $resume_id = $interview_detail['cvid'];
            $repository = new ResumeRepository($this->timezone);

            $this->logger->write_log(sprintf("[interviewServer][getDetail] params : %s, resume id : %s",
                json_encode($params), $resume_id), "info");

            $annex = $repository->getAnnexList($resume_id);

            if($annex){
                $config = $this->getDI()->getConfig();
                $interview_detail['file_name'] = $annex['original_name'];
                $interview_detail['file_type'] = intval($annex['file_type']);
                $interview_detail['file_path'] = $config->application['img_prefix'] . $annex['object_key'];
            }else{
                $interview_detail['file_type'] = 0;
                $interview_detail['file_path'] = '';
                $interview_detail['file_name'] = '';
            }
    
    
            $interview_detail['operation_shop_name'] = '';

            if (!empty($interview_detail['operation_shop_id'])) {
                if ($interview_detail['operation_shop_id'] == -1) {
                    $interview_detail['operation_shop_name'] = enums::HEAD_OFFICE;
                } else {
                    //获取网点名称
                    $sysStoreInfo = SysStoreModel::findFirst([
                        'columns' => 'id,name',
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $interview_detail['operation_shop_id']]
                    ]);

                    $interview_detail['operation_shop_name'] = $sysStoreInfo->name ?? '';
                }
            }

            //interviewer_id
            $interview_detail['interviewer_name'] = '';
            if (!empty($interview_detail['interviewer_id'])) {
                $interviewerStaffInfo = StaffInfoModel::findFirst([
                    'conditions' => "id = :id:",
                    "bind"       => ['id' => $interview_detail['interviewer_id']],
                    "columns"    => 'id,name'
                ]);

                $interview_detail['interviewer_name'] = $interviewerStaffInfo->name ?? '';
                $interview_detail['interviewer_name'] .= '-'.$interview_detail['interviewer_id'];
            }

            //create_id
            $interview_detail['create_name'] = '';
            if (!empty($interview_detail['create_id'])) {
                
                $createStaffInfo = HrStaffInfoModel::findFirst([
                    'conditions' => "staff_info_id = :create_id:",
                    "bind"       => ['create_id' => $interview_detail['create_id']],
                    "columns"    => 'staff_info_id,name'
                ]);

                $interview_detail['create_name'] = $createStaffInfo->name ?? '';
                $interview_detail['create_name'] .= '-'.$interview_detail['create_id'];
            }

            //工作天数&轮休规则
            $working_day_rest_type_arr = !empty($interview_detail['working_day_rest_type']) ? explode(',', $interview_detail['working_day_rest_type']) : [];
            if(!empty($working_day_rest_type_arr)) {
                foreach ($working_day_rest_type_arr as $key => $value) {
                    $interview_detail['working_day_rest_type_items'][] = [
                        'key' => $value,
                        'value' => $this->getTranslation()->_('working_day_rest_type_' . $value)
                    ];
                }
            }
            $interview_detail['is_show_option'] = true;
            if (isset($interview_detail['conclusion_type']) && in_array($interview_detail['conclusion_type'],
                    [0, 1, 2])) {
                $interview_detail['is_show_option'] = false;
            }
            //从发给hr的消息进来的，不能面试反馈

            if (!empty($params['src']) && $params['src'] == 'to_hr') {
                $interview_detail['is_show_option'] = false;
            }


            unset($interview_detail['date_birth']);
            unset($interview_detail['job_title']);

            if (isCountry("TH")){
                $deServer = new DelinquencyServer($this->lang,$this->timeZone);
                [$list, $count] = $deServer->getDelinquencyList($interview_detail['identity'],[HrBlackGreyListModel::BEHAVIOR_TYPE_GREY]);
                $interview_detail['delinquency_list'] = $deServer->displayList($list);
            }
        }
        return $interview_detail ? $interview_detail : null;
    }

    /**
     * 取消面试
     * @param $params
     * @return bool
     */
    public function cancle($params) {
        try {
            //开启事物
            $db = $this->getDI()->get('db');
            $db->begin();
            $subscribe = HrInterviewSubscribeModel::findFirst([
                'conditions' => 'interview_id=:interview_id:',
                'bind'       => ['interview_id' => $params['interview_id']],
            ]);
            if (empty($subscribe)){
                throw new \Exception('cancel error 1'.json_encode($params, JSON_UNESCAPED_UNICODE));
            }
            $subscribe = $subscribe->toArray();
            $hr_hc = HrHcModel::findFirst(
                [
                    'columns' => 'job_title,department_id',
                    'conditions' => 'hc_id = :hc_id:',
                    'bind' => ['hc_id' =>$subscribe['hc_id']],
                ]
            );
            if (empty($hr_hc)){
                throw new \Exception('cancel hc error '.json_encode($params, JSON_UNESCAPED_UNICODE));
            }
            $hr_hc = $hr_hc->toArray();

            // 获取轮次
            $level = (new InterviewRepository($this->timezone))->selectLevel($params['interview_id']);

            // 判断是否一线
            $isFirstLineJob = (new SalaryServer($this->lang, $this->timezone))->isFirstLineJob($hr_hc['department_id'], $hr_hc['job_title']);
            if (!$isFirstLineJob){
                // 非一线 走面试回退逻辑
                $result = $this->getDI()->get('db')->updateAsDict(
                    'hr_interview_subscribe',
                    [
                        'interview_back' => HrInterviewSubscribeModel::INTERVIEW_BACK_YES,
                        'interview_back_cancel_type'   => ($params['cancel_type'] ?? 1),
                        'interview_back_cancel_reason' => ($params['cancel_reason'] ?? '')
                    ],
                    'interview_id = ' . $params['interview_id']
                );
                if ($result){
                    $conditions = 'interview_sub_id = :interview_sub_id: ';
                    $binds = ['interview_sub_id' => $params['subscribe_id']];
                    //兼容旧数据
                    if(isset($params['opt_id']) && !empty($params['opt_id'])){
                        $conditions.=  ' and id = :id:';
                        $binds['id'] = $params['opt_id'];
                    }

                    $operationData = HrInterviewOperationModel::findFirst([
                        'conditions' => $conditions,
                        'bind' => $binds,
                        'order'=>'id desc'
                    ]);

                    if($operationData){
                        $operationData->state = HrInterviewOperationModel::STATE_CANCEL;//取消
                        $operationData->cancel_at = gmdate('Y-m-d H:i:s');
                        if(!$operationData->save()){
                            throw new \Exception('cancel error 2'.json_encode($params, JSON_UNESCAPED_UNICODE));
                        }
                    }
                    //添加日志
                    (new LogServer())->addLog([
                        'module_id'    => $params['interview_id'],
                        'module_type'  => 1,
                        'module_level' => $level,
                        'action'       => 29,
                        'data_after'   => $subscribe,
                        'current_user_id'   => $params['current_user_id'],
                    ]);
                }else{
                    throw new \Exception('cancel error 3'.json_encode($params, JSON_UNESCAPED_UNICODE));
                }
            }else{
                // 一线 走终止面试逻辑
                //设置面试状态
                $cancelInterview = (new InterviewRepository($this->timezone))->cancelInterview($params);
                if (!$cancelInterview) {
                    $this->getDI()->get('logger')->write_log("取消面试:（" . json_encode($params) . "）操作记录不存在：", 'info');
                    return false;
                }
                //取消预约
                $cancelAppointment = (new InterviewRepository($this->timezone))->interviewAppointmentStatus($params);
                if (!$cancelAppointment) {
                    $this->getDI()->get('logger')->write_log("interviewServer_cancel: params:（" . json_encode($params) . "）:reson:".json_encode($cancelAppointment), 'notice');
                    throw new \Exception('cancel error'.json_encode($cancelAppointment, JSON_UNESCAPED_UNICODE));
                }
                //添加日志
                (new LogServer())->addLog([
                    'module_id' => $params['interview_id'],
                    'module_type' => 1,
                    'module_level' => $level,
                    'action' => 4,
                    'data_after' => $params,
                    'module_status' => 2, //不通过
                    'current_user_id' => $params['current_user_id'],
                ]);
            }

            //面试官详情
            $interviewer_info = (new HrStaffInfoServer())->getUserInfoByStaffInfoId($params['staff_id']);
            //面试详情
            $resume_info = (new InterviewRepository($this->timezone))->getInterviewDetail($params);
            if(empty($resume_info)){
                //记录日志
                $this->getDI()->get("logger")->write_log("取消面试-getInterviewDetail：".json_encode($params,JSON_UNESCAPED_UNICODE), "info");
            }
            //获取岗位信息
            $job_title = HrJobTitleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $resume_info[0]['job_title']]
            ]);

            $add_hour = $this->config->application->add_hour;
            $_now_time = strtotime(gmdate('Y-m-d H:i:s', time())) + $add_hour*3600;
            $cancel_date = date('Y-m-d',$_now_time);

            $name  = !empty($interviewer_info->nick_name) ? $interviewer_info->nick_name : $interviewer_info->name_en;

            $lang = (new StaffServer())->getLanguage($resume_info[0]['staff_id']);
            $t = $this->getTranslation($lang);
            //给HR发送取消通知
            $content = $t->_('cancel_interview_content');
            $remindMsageContent = str_replace(["{interviewer_name}", "{interviewer_id}", "{resume_name}","{resume_id}","{job_name}","{cancel_date}"], [$name,$interviewer_info->staff_info_id,$resume_info[0]['name'],$resume_info[0]['cvid'],$job_title->job_name ?? '',$cancel_date], $content);

            $pushSuccessParam = [
                'staff_info_id' => $resume_info[0]['staff_id'],
                'message_title' => $t->_('cancel_interview_title'),
                'message_content' => $remindMsageContent,
                'type' => 41, //面试
                'path' => 'message_list'
            ];
            $msgRes1 = (new PublicRepository())->sendPushToSubmitter($pushSuccessParam);
            $opt_id = $params['opt_id'] ?? 0;
            if($opt_id==0){
                $this->getDI()->get('logger')->write_log("cancle 发送取消面试通知-by 数据异常或旧数据不有opt_id字段：" .json_encode($params, JSON_UNESCAPED_UNICODE), 'notice');
            }
            $pushSuccessParam['message_content'] .="|||{$params['interview_id']},{$params['subscribe_id']},$opt_id";

            $msgRes = (new PublicRepository())->sendMessageToSubmitter($pushSuccessParam);

            $msgResLev = $msgRes && $msgRes1 ? 'info' : 'notice';

            $this->getDI()->get('logger')->write_log("发送取消面试通知-by：" . $resume_info[0]['staff_id'] . json_encode($pushSuccessParam, JSON_UNESCAPED_UNICODE), $msgResLev);

            $this->addHrInterviewRecord($params['interview_id'],HrInterviewRecordModel::STATE_CANCEL,['current_user_id' => $params['current_user_id']]);

            $db->commit();

            return true;
        } catch (\Exception $e) {
            //异常回滚
            $db->rollback();
            $this->getDI()->get('logger')->write_log("取消面试:（" . json_encode($params) . "）操作失败：" . $e->getMessage() . $e->getTraceAsString(), 'error');
            return false;
        }
    }


    /**
     * 面试反馈
     */
    public function addEvaluation($params) {
        $this->getDI()->get('logger')->write_log("面试反馈:" . json_encode($params,JSON_UNESCAPED_UNICODE) , 'info');

        /* 校验hc */
        $hcData = (new HcRepository($this->timezone))->checkHc($params);
        if ($hcData['state_code'] == 4 && $params['state'] != 3) {
            throw new BusinessException($this->getTranslation()->_('hc_abolished'));
        }

        //自由轮休添加限制
        if (isCountry('MY') && !empty($hcData['hire_type']) && !empty($params['working_day_rest_type'])) {
            $freeRestType = HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1;

            if (
                ($hcData['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT)
                &&
                ($params['working_day_rest_type'] != $freeRestType)
            ) {
                throw new BusinessException($this->getTranslation()->_('working_day_rest_type_select_error'));
            }
        }

        //校验offer
        $data = (new OfferRepository($this->timezone))->checkOffer([
            'interview_id' => $params['interview_id'],
            'status' => 1,
        ]);
        if ($data) {
            throw new BusinessException($this->getTranslation()->_('4012'));
        }
        //面试详情
        $resume_info = (new InterviewRepository($this->timezone))->getInterviewDetail($params);
        if (empty($resume_info)) {
            //记录日志
            $this->getDI()->get("logger")->write_log("面试反馈-getInterviewDetail：" . json_encode($params,
                    JSON_UNESCAPED_UNICODE));
            throw new BusinessException($this->getTranslation()->_('data_error'));
        }
        //未到面试时间，不能填写面试反馈
        if (!empty($resume_info[0]['interview_time']) && $resume_info[0]['interview_time'] > date('Y-m-d H:i:s')) {
            throw new BusinessException($this->getTranslation()->_('interview_feedback_too_early'));
        }

        // 面试轮次
        $level = (new InterviewRepository($this->timezone))->selectLevel($params['interview_id']);

        $params['level'] = (int)$level + 1;

        /* 存入信息 */
        $recordFeedback = (new InterviewRepository($this->timezone))->recordFeedback($params);

        // 验证
        if (!$recordFeedback) {
            throw new BusinessException($this->getTranslation()->_('4703'));
        }

        //给HR发送取消通知
        $interviewer_info = (new HrStaffInfoServer())->getUserInfoByStaffInfoId($params['staff_id']);

        //获取岗位信息
        $job_title = HrJobTitleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $resume_info[0]['job_title']]
        ]);

        $today = date('Y-m-d');

        $interviewer_info->name  = empty($interviewer_info->name) ? $interviewer_info->name_en : $interviewer_info->name;

        $msg_staff_id =  !empty($resume_info[0]['create_id'])? $resume_info[0]['create_id'] : $resume_info[0]['staff_id'];

        $lang = (new StaffServer())->getLanguage($msg_staff_id);
        $t = $this->getTranslation($lang);

        $result = $params['conclusion'] == 1 ? $t->_('interview_result_pass') : $t->_('interview_result_not_pass'); //是否通过
        $content = $t->_('interview_result_notice_content');
        $remindMsageContent = str_replace(["{interviewer_name}", "{interviewer_id}", "{resume_name}","{resume_id}","{job_name}","{today}","{result}"], [$interviewer_info->name,$interviewer_info->staff_info_id,$resume_info[0]['name'],$resume_info[0]['cvid'],$job_title->job_name ?? '',$today,$result], $content);

        $pushSuccessParam = [
            'staff_info_id' => $msg_staff_id,
            'message_title' => $t->_('interview_result_notice'),
            'message_content' => $remindMsageContent,
            'type' => 41, //面试
            'path' => 'message_list'
        ];
        //push 消息
        $msgRes1 = (new PublicRepository())->sendPushToSubmitter($pushSuccessParam);
        $this->getDI()->get('logger')->write_log("addEvaluation push-反馈面试消息:（" . json_encode($pushSuccessParam) . "）发送状态：".$msgRes1, 'info');

        $pushSuccessParam['message_content'] .="|||{$params['interview_id']},{$params['subscribe_id']},{$params['opt_id']}";

        $msgRes = (new PublicRepository())->sendMessageToSubmitter($pushSuccessParam);
        $this->getDI()->get('logger')->write_log("addEvaluation 站内信-反馈面试消息:（" . json_encode($pushSuccessParam) . "）发送状态：".$msgRes, 'info');

        // 数据返回
        $return['data']['interview_info_id'] = $recordFeedback;

        if ($params['conclusion'] == 1) { //通过
            $module_status = 1;
        }else{
            $module_status = 2;
        }
        //添加日志
        $return['data']['hr_log_id'] = (new LogServer())->addLog([
            'module_id' => $params['interview_id'],
            'module_type' => 1, //面试
            'module_level' => $params['level'],
            'module_status' => $module_status,
            'action' => 7,   //4取消  10 反馈
            'data_after' => $params,
            'current_user_id' => $params['current_user_id'],
        ]);
        if ($module_status == 1) { //通过
            $this->addHrInterviewRecord($params['interview_id'],HrInterviewRecordModel::STATE_PASS,['current_user_id' => $params['current_user_id']]);
        }else{
            $this->addHrInterviewRecord($params['interview_id'],HrInterviewRecordModel::STATE_NO_PASS,['current_user_id' => $params['current_user_id']]);
        }

        return $this->checkReturn($return);
    }

    /**
     * 淘汰原因
     * @returnType : 1 return array  0 return json
     */
    public function getOutInterviewListServer($returnType = 0) {
        $outInterviewData = [
            [
                'key' => 1,
                'name' => $this->getTranslation()->_('7529'),
            ],
            [
                'key' => 2,
                'name' => $this->getTranslation()->_('7530'),
            ],
            [
                'key' => 3,
                'name' => $this->getTranslation()->_('7531'),
            ],
            [
                'key' => 5,
                'name' => $this->getTranslation()->_('8117'),
            ],
            [
                'key' => 6,
                'name' => $this->getTranslation()->_('8118'),
            ],
            [
                'key' => 7,
                'name' => $this->getTranslation()->_('8119'),
            ],
            [
                'key' => 8,
                'name' => $this->getTranslation()->_('8120'),
            ],
            [
                'key' => 4,
                'name' => $this->getTranslation()->_('7532'),
            ],
        ];

        if($returnType==1){
            $arr = [];
            foreach ($outInterviewData as $k=>$v){
                $arr[$v['key']] =$v['name'];
            }
            return $arr;
        }
        return $outInterviewData;
    }

    /**
     * 获取性别
     * @param int $sex 简历中的性别
     * @param int $callName 简历中的称呼
     * @return int
     */
    public function getResumeSex($sex, $callName=1): int
    {
        if (isset($sex) && $sex) { //如果性别字段有值
            return intval($sex);
        } else { //性别字段无值，根据称呼字段判断
            if ($callName == 1) {
                return 1;
            } else {
                return 2;
            }
        }
    }

    /**
     * new 添加面试记录
     * @param $interview_id
     * @param $state
     * @param array $paramIn
     * @return bool
     */
    public function addHrInterviewRecord($interview_id, $state = HrInterviewRecordModel::STATE_DEFAULT, $paramIn = [])
    {
        $resume_id         = $paramIn['resume_id'] ?? 0;
        $interviewer_id    = $paramIn['interviewer_id'] ?? '';
        $interview_address = $paramIn['interview_address'] ?? '';
        $interview_time    = $paramIn['interview_time'] ?? '';
        $current_user_id    = $paramIn['current_user_id'] ?? '';
        if (!$resume_id || !$interviewer_id || !$interview_address || !$interview_time) {
            // 以上信息有一个没有传 就去查询
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("interview.interview_id,interview.resume_id,interview_subscribe.interviewer_id,interview_subscribe.detail_address,interview_subscribe.interview_time");
            $builder->from(['interview' => HrInterviewModel::class]);
            $builder->leftjoin(HrInterviewSubscribeModel::class,
                'interview.interview_id=interview_subscribe.interview_id', 'interview_subscribe');
            $builder->andWhere('interview.interview_id = :interview_id:', ['interview_id' => $interview_id]);
            $result         = $builder->getQuery()->execute()->getFirst();
            $interview_data = $result ? $result->toArray() : [];
            if (!$interview_data) {
                $this->getDI()->get('logger')->write_log("addHrInterviewRecord interview_data is null interview_id:" . $interview_id . ",state:" . $state . ",paramIn:" . json_encode($paramIn),
                    'error');
            }

            $resume_id         = $resume_id ? $resume_id : ($interview_data['resume_id'] ?? 0);
            $interviewer_id    = $interviewer_id ? $interviewer_id : ($interview_data['interviewer_id'] ?? 0);
            $interview_address = $interview_address ? $interview_address : ($interview_data['detail_address'] ?? '');
            $interview_time    = $interview_time ? $interview_time : ($interview_data['interview_time'] ?? '');
        }

        $interview_record_model                    = new HrInterviewRecordModel();
        $interview_record_model->interview_id      = $interview_id;
        $interview_record_model->resume_id         = $resume_id;
        $interview_record_model->operator_id       = $current_user_id;
        $interview_record_model->state             = $state;
        $interview_record_model->interviewer_id    = $interviewer_id;
        $interview_record_model->interview_address = $interview_address;
        $interview_record_model->interview_time    = $interview_time;
        return $interview_record_model->create();
    }
}
