<?php

namespace FlashExpress\bi\App\Server\Penalty;

use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;


/**
 * 迟到
 */
class PunchInPenaltyServer extends BasePenaltyServer
{

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang, $timezone);
    }

    public $initDataObj = null;

    /**
     * 上班打卡
     */
    public function initData($initData): PunchInPenaltyServer
    {
        $this->initDataObj = $initData;
        return $this;
    }

    /**
     * @throws BusinessException
     */
    public function handle(): bool
    {
        if (!$this->checkIsNeed()) {
            return true;
        }
        $seconds = $this->dealTime();
        $this->cancel($seconds);
        $seconds >= 60 && $this->makeData($seconds);
        return true;
    }

    public function cancel($seconds): bool
    {
        //有变换再撤销
        if(!empty($this->initDataObj->coming_late_penalty) && $seconds != $this->initDataObj->coming_late_penalty->early_late_seconds){
            $this->clearPenalty($this->initDataObj->coming_late_penalty) && $this->doReadMessage($this->initDataObj->coming_late_penalty) && $this->sendCancelMessage($this->initDataObj->coming_late_penalty) && $this->cancelAudit($this->initDataObj->coming_late_penalty);
        }
        return true;
    }


    public function checkIsNeed(): bool
    {
        if (!parent::checkIsNeed()) {
            return false;
        }
        if (empty($this->initDataObj->attendance_started_at) || empty($this->initDataObj->need_shift_start)) {
            return false;
        }
        return true;
    }

    /**
     * 迟到秒数
     * @return false|mixed
     */
    public function dealTime()
    {
        //1判断是否迟到;
        $punch_in_time      = strtotime(date('Y-m-d H:i:s', strtotime($this->initDataObj->attendance_started_at)));
        $need_punch_in_time = strtotime($this->initDataObj->need_shift_start);
        return floor(max($punch_in_time - $need_punch_in_time, 0));
    }

    /**
     * @throws BusinessException
     */
    public function makeData($seconds)
    {
        //生成处罚项目
        $penaltyInfo     = $this->dealPenaltyInfo($seconds);


        $t        = $this->getTranslation($this->initDataObj->staff_lang);
        $timeInfo = DateHelper::dealSecondsToHoursAndMinutes($seconds);
        $timeStr  = '';
        if ($timeInfo['hours'] > 0) {
            $timeStr .= $t->_('penalty_hour', ['x' => $timeInfo['hours']]);
        }
        if ($timeInfo['minutes'] > 0) {
            $timeStr .= $t->_('penalty_minutes', ['x' => $timeInfo['minutes']]);
        }
        $penaltyTime     = '';
        $penaltyTimeInfo = self::showPenalTime($penaltyInfo['early_late_ab_hour']);
        if ($penaltyTimeInfo['h'] > 0) {
            $penaltyTime .= $t->_('penalty_hour', ['x' => $penaltyTimeInfo['h']]);
        }
        if ($penaltyTimeInfo['day'] > 0) {
            $penaltyTime .= $t->_('penalty_day', ['x' => $penaltyTimeInfo['day']]);
        }

        $title = $t->_('coming_late_reminder');
        $content = $t->_('coming_late_reminder_content', ['time' => $timeStr]);
        $category        = -1;
        $category_code   = 0;
        $penaltyDetailId = 0;
        if ($penaltyInfo['penalty_money'] > 0) {
            $penaltyInfo['early_late_seconds'] = $seconds;
            $penaltyInfo['staff_info_id']      = $this->initDataObj->staff_info_id;
            $penaltyInfo['store_id']           = $this->initDataObj->staff_store_id;
            $penaltyInfo['job_title']          = $this->initDataObj->job_title;
            $penaltyInfo['attendance_date']    = $this->initDataObj->attendance_date;
            $penaltyInfo['state']              = HrPenaltyDetailModel::PENALTY_STATE_TAKE_EFFECT;
            $penaltyInfo['penalty_reason']     = HrPenaltyDetailModel::PENALTY_REASON_COMING_LATE;
            $penaltyInfo['penalty_date']       = date('Y-m-d');

            if (!empty($this->initDataObj->related_id)) {
                $penaltyInfo['id']           = $this->initDataObj->related_id;
                $penaltyInfo['created_at']   = $this->initDataObj->created_at;
                $penaltyInfo['updated_at']   = $this->initDataObj->created_at;
                $penaltyInfo['penalty_date'] = show_time_zone($this->initDataObj->created_at, 'Y-m-d');
                $penaltyInfo['remark']       = '事务问题修正';
            }

            $penaltyDetailId                   = $this->createPenalty([
                'staff_info_id'   => $this->initDataObj->staff_info_id,
                'attendance_date' => $this->initDataObj->attendance_date,
                'penalty_reason'  => HrPenaltyDetailModel::PENALTY_REASON_COMING_LATE,
            ], $penaltyInfo);

            if(!empty($this->initDataObj->related_id)){
                return true;
            }
            $title = $t->_('tardiness_reminder');
            $content = $t->_('tardiness_reminder_content',
                [
                    'attendance_date' => $this->initDataObj->attendance_date,
                    'time'            => $timeStr,
                    'penalty_time'    => $penaltyTime,
                    'money'           => $penaltyInfo['penalty_money'],
                ]);

            $category                          = MessageEnums::CATEGORY_SIGN;
            $category_code                     = MessageEnums::CATEGORY_SIGN_CODE_PENALTY_COMING_LATE;
        }

        $data = [
            "staff_users"        => [$this->initDataObj->staff_info_id],
            //提交人ID
            "message_title"      => $title,
            "message_content"    => $content,
            //类别
            "category"           => $category,
            //子类别
            "category_code"      => $category_code,
            "push_state"         => 1,
            "staff_info_ids_str" => $this->initDataObj->staff_info_id,
            //内容
            "related_id"         => $penaltyDetailId,
            "audit_status"       => 2,
            //内容
            "id"                 => time().$this->initDataObj->staff_info_id.rand(1000000, 9999999),

        ];
        $this->sendMessage($data);
    }


}