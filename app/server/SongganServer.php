<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/29/23
 * Time: 8:31 PM
 */

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\ActivityRecordModel;

class SongganServer extends ActivityServer
{
    protected $activityCode = ActivityRecordModel::CODE_SONGGAN;
    public function initData($param)
    {
        parent::initData($param);
        $this->activityDate = (new SettingEnvServer())->getSetVal('songgan_date');
    }

}