<?php

namespace FlashExpress\bi\App\Server;


//超时server
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;

class ApprovalOvertimeServer extends BaseServer
{
    private static $instance;

    /**
     * @var ApprovalServer
     */
    private $approval_server;

    /**
     * @var string
     */
    private $overtime_date;

    /**
     * @var array
     */
    private $audit_type;

    /**
     * 获取实例
     * @param $lang
     * @param $timezone
     * @return self
     */
    public static function getInstance($lang , $timezone): ApprovalOvertimeServer
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self($lang , $timezone);
        }
        return self::$instance;
    }

    /**
     * 初始化
     * @param array $params
     * @return ApprovalOvertimeServer
     */
    public function init(array $params = []): ApprovalOvertimeServer
    {
        $this->overtime_date   = $params['overtime_date'];
        $this->audit_type      = $params['audit_type'];
        $this->approval_server = new ApprovalServer($this->lang, $this->timeZone);
        return $this;
    }

    /**
     * 处理审批超时
     * @return void
     */
    public function handleAuditOvertime()
    {
        $auditList      = $this->getPendingAuditList($this->audit_type);
        $auditChunkList = array_chunk($auditList, CommonEnums::DEFAULT_CHUNK_NUMS);
        foreach ($auditChunkList as $chunk) {
            foreach ($chunk as $audit) {
                echo '超时关闭 => 参数：' . json_encode($audit) . PHP_EOL;
                $approvalResult = $this->approval_server->timeOut($audit['biz_value'], $audit['biz_type']);
                if ($approvalResult === false) {
                    //超时关闭失败
                    $this->logger->write_log([
                        'function' => 'ApprovalOvertimeServer -> handleAuditOvertime',
                        'message'  => '超时关闭失败',
                        'params'   => json_encode($audit),
                        'result'   => false,
                    ]);
                    echo '超时关闭失败结果: false' . PHP_EOL;
                } else {
                    //超时关闭失败
                    $this->logger->write_log([
                        'function' => 'ApprovalOvertimeServer -> handleAuditOvertime',
                        'message'  => '超时关闭失败',
                        'params'   => json_encode($audit),
                        'result'   => false,
                    ], 'info');
                    echo '超时关闭失败结果: true' . PHP_EOL;
                }
            }
        }
    }

    /**
     * 获取待审批数据
     * @param array $audit_type
     * @return array
     */
    private function getPendingAuditList($audit_type = []): array
    {
        if (empty($audit_type)) {
            $auditTypeList = AuditListEnums::getAllAuditTypes();
        } else {
            $auditTypeList = $audit_type;
        }

        return AuditApplyModel::find([
            'conditions' => 'biz_type in({audit_type:array}) and state = :state: and time_out <= :date:',
            'bind' => [
                'audit_type' => $auditTypeList,
                'state'      => enums::APPROVAL_STATUS_PENDING,
                'date'       => $this->overtime_date,
            ],
            'columns' => 'biz_type,biz_value',
        ])->toArray();
    }
}