<?php

namespace FlashExpress\bi\App\Server\Rpc;


use App\Country\Tools;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\FleetServer;

class FleetRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    public function getFleetList($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getFleetList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getFleetList($params);
            $logger->write_log("svc getFleetList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getFleetList 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getFleetList 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function lineCount($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc lineCount 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->lineCount($params);
            $logger->write_log("svc lineCount 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc lineCount 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc lineCount 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getFleetDetailByLineId($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getFleetDetailByLineId 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getFleetDetailByLineId($params);
            $logger->write_log("svc getFleetDetailByLineId 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getFleetDetailByLineId 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getFleetDetailByLineId 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getFleetDetailByAuditId($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getFleetDetailByAuditId 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getFleetDetailByAuditId($params);
            $logger->write_log("svc getFleetDetailByAuditId 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getFleetDetailByAuditId 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getFleetDetailByAuditId 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function editFleetInfo($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc editFleetInfo 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->editFleetInfo($params);
            $logger->write_log("svc editFleetInfo 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc editFleetInfo 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc editFleetInfo 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function auditFleet($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc auditFleet 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);

            //中控审批
            $params['super'] = true;
            //批量审核通过
            if(!empty($params['batch_audit_ids']) && $params['status'] == enums::APPROVAL_STATUS_APPROVAL){
                $data = $server->batchAuditFleet($params);
                $logger->write_log("svc auditFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                return $data;//里面 有 code msg data
            }else{
                $data   = $server->auditFleet($params);
                $logger->write_log("svc auditFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                return $data;
            }

        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc auditFleet 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc auditFleet 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getPendingFleet($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getPendingFleet 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getPendingFleet($params);
            $logger->write_log("svc getPendingFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getPendingFleet 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getPendingFleet 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getDemandNum($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getDemandNum 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getDemandNum($params);
            $logger->write_log("svc getDemandNum 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getDemandNum 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getDemandNum 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }


    public function saveFleetOptLog($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc saveFleetOptLog 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->saveFleetOptLog($params);
            $logger->write_log("svc saveFleetOptLog 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc saveFleetOptLog 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc saveFleetOptLog 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getFleetOptLogList($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getFleetOptLogList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->getFleetOptLogList($params);
            $logger->write_log("svc getFleetOptLogList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getFleetOptLogList 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getFleetOptLogList 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function abandonFleetLineByLineId($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc deleteFleet 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {

            $server = new FleetServer($locale['locale'], $this->timeZone);
            //新增 批量作废
            if(!empty($params['batch_line_ids'])){
                $data = $server->batch_abandon($params);
                $logger->write_log("svc auditFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                return $data;//里面 有 code msg data
            }else{

                $data   = $server->abandonFleetLineByLineId($params);
                $logger->write_log("svc deleteFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
                return $server->checkReturn(['data' =>$data]);
            }

        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc deleteFleet 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc deleteFleet 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getVanCourierAttendanceByAuditId($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getVanCourierAttendanceByAuditId 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {

            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data = $server->getVanCourierAttendanceByAuditId($params);
            $logger->write_log("svc getVanCourierAttendanceByAuditId 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $data;
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getVanCourierAttendanceByAuditId 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getVanCourierAttendanceByAuditId 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function findSysAttachment($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc findSysAttachment 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->findSysAttachment($params);
            $logger->write_log("svc findSysAttachment 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc findSysAttachment 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc findSysAttachment 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    //发送车线调度信息变更
    public function sendSchedulingChangeMessage($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc sendSchedulingChangeMessage 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = new FleetServer($locale['locale'], $this->timeZone);
            $data   = $server->sendSchedulingChangeMessage($params);
            $logger->write_log("svc sendSchedulingChangeMessage 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc sendSchedulingChangeMessage 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc sendSchedulingChangeMessage 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function createFleet($locale, $params): array
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc createFleet 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = Tools::reBuildCountryInstance(new FleetServer($locale['locale'], $this->timeZone),[$locale['locale'], $this->timeZone]);
            $data   = $server->addFleet($params);
            $logger->write_log("svc createFleet 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc createFleet 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc createFleet 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function getFleetPendingList($locale, $params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->write_log("svc getFleetPendingList 参数列表 locale:" . json_encode($locale, JSON_UNESCAPED_UNICODE) . ";param:" . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        try {
            $server = Tools::reBuildCountryInstance(new FleetServer($locale['locale'], $this->timeZone),[$locale['locale'], $this->timeZone]);
            $data   = $server->getFleetPendingList($params);
            $logger->write_log("svc getFleetPendingList 数据返回:" . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');
            return $server->checkReturn(['data' =>$data]);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::VALIDATE_ERROR) {
                $logger->write_log("svc getFleetPendingList 异常信息:" . $e->getMessage(), 'info');
            } else {
                $logger->write_log("svc getFleetPendingList 异常信息:" . $e->getMessage(), 'notice');
            }
            $return['data'] = [];
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }
}