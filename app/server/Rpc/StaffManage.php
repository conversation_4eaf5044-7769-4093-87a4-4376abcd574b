<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class StaffManage extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 管辖员工的HRBP
     * @param $locale
     * @param $params
     * @return string
     */
    public function findHRBP($locale, $params)
    {
        $staffInfo = (new StaffServer())->getStaffById($params['staff_info_id'], 'node_department_id,sys_store_id');
        return (new WorkflowServer($locale['locale'], $this->timeZone))->findHRBP($staffInfo['node_department_id'],
            ["store_id" => $staffInfo['sys_store_id']]);
    }

}