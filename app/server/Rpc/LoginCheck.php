<?php

namespace FlashExpress\bi\App\Server\Rpc;


use FlashExpress\bi\App\Server\LoginServer;

class LoginCheck extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

//
//    public function loginCheck($locale,$param){
//        header("X-Fle-Session-Id: xxxxx");
//        $loginServer = new LoginServer($locale['locale'], $this->timeZone);
//        $loginServer->loginCheckBy();
//    }
//
//
//    /**
//     * 根据token获取用户信息
//     * @throws BusinessException|ValidationException
//     */
//    public function getStaffInfoBYToken($x_fle_session_id,$device_id)
//    {
//        //验证token
//        $cache = $this->getDI()->get('redisLib');
//        [$time, $paramAuth, $staff_id] = explode('_', $x_fle_session_id);
//        $str  = $this->config->application->authenticate;
//        $auth = sha1(md5($time . $str . $staff_id));
//        if ($auth == $paramAuth && !empty($cache->get($auth))) {
//            //新增 互踢验证
//            $this->loginKick($staff_id, $device_id);
//            return $this->staffCheck($staff_id);
//        }
//
//        throw new BusinessException('SessionID expired (re_login)', 100112);
//    }

}

