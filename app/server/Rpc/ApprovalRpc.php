<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\BusinesstripServer;

class ApprovalRpc extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;

    /**
     * 系统驳回
     * @description 将待审批、审批已同意的审批驳回
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function systemReject($locale, $params): array
    {
        $server = new ApprovalServer($locale['locale'], $this->timeZone);
        $res    = $server->approvalSystemReject($params['audit_id'], $params['audit_type'],
            $params['reject_reason'], enums::SYSTEM_STAFF_ID, ['super' => 1]);
        return $server->checkReturn(['data' => $res]);
    }

    /**
     * 获取 审批流编号
     * @param $locale
     * @param $params
     * @return array
     */
    public function getRandomIdNo($locale, $params): array
    {
        $server = new ApprovalServer($locale['locale'], $this->timeZone);
        return $server->checkReturn(['data' => $this->getRandomId()]);
    }
}