<?php

namespace FlashExpress\bi\App\Server\Rpc;

use FlashExpress\bi\App\Server\BaseServer;

class RpcBaseServer extends BaseServer
{
    protected static $instance;
    public           $timeZone;

    public function __construct($timezone)
    {
        $this->timeZone = $timezone;
    }

    /**
     * @param $timezone
     * @return RpcBaseServer
     */
    public static function getInstance($timezone)
    {
        if (static::$instance === null) {
            static::$instance = new static($timezone);
        }
        return static::$instance;
    }
}