<?php

namespace FlashExpress\bi\App\Server\Rpc;

use App\Country\Tools;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Server\OsOvertimeServer;
use FlashExpress\bi\App\Server\OvertimeServer;

class Overtime extends RpcBaseServer
{
    //此变量不可省略，请勿删除
    protected static $instance = null;


    //当前登陆人的加班审批列表
    public function overtimeApprovalList($locale, $param)
    {
        try {
            $server = new OvertimeServer($locale['locale'], $this->timeZone);
            $data   = $server->rpcOtList($param);
            $this->logger->write_log("overtimeApprovalList ".json_encode($param), 'info');
            return $data;
        } catch (\Exception $e) {
            $this->logger->write_log("overtimeApprovalList 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }


    //详情页
    public function overtimeDetail($locale, $param)
    {
        try {
            $server = new OvertimeServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->getDetail($param['audit_id'], $param['operate_id'], 2);
            $this->logger->write_log("overtimeDetail ".json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS,'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("overtimeDetail 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    //最后审批人 撤销 审核通过的
    public function overtimeCancel($locale, $param)
    {
        try {
            $server = new OvertimeServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $param['staff_id'] = $param['operate_id'];
            $param['audit_id'] = $param['overtime_id'];
            $data   = $server->cancelV3($param);
            $this->logger->write_log("overtimeCancel ".json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS,'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("overtimeCancel 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }



    //当前登陆人的加班审批列表
    public function osOvertimeApprovalList($locale, $param)
    {
        try {
            $server = new OsOvertimeServer($locale['locale'], $this->timeZone);
            $data   = $server->rpcOtList($param);
            $this->logger->write_log("overtimeApprovalList ".json_encode($param), 'info');
            return $data;
        } catch (\Exception $e) {
            $this->logger->write_log("overtimeApprovalList 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    //最后审批人 撤销 审核通过的
    public function osOvertimeCancel($locale, $param)
    {
        try {
            $server = new OsOvertimeServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $param['staff_id'] = $param['operate_id'];
            $param['audit_id'] = $param['id'];
            $param['status']   = enums::$audit_status['revoked'];
            $data   = $server->updateOsOtUseLock($param);
            $this->logger->write_log("osOvertimeCancel ".json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS,'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("osOvertimeCancel 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }


    public function getOsOtType($locale,$param){
        try {
            $server = new OsOvertimeServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->getAllType($locale['locale']);
            $this->logger->write_log("getOsOtType svc ".json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS,'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("getOsOtType 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    public function addOsOvertime($locale,$param){
        try {
            $server = new OsOvertimeServer($locale['locale'], $this->timeZone);
            $server = Tools::reBuildCountryInstance($server, [$locale['locale'], $this->timeZone]);
            $data   = $server->addOsOvertime($param);
            $this->logger->write_log("addOsOvertime svc ".json_encode($param), 'info');
            return ['code' => ErrCode::SUCCESS,'msg' => '', 'data' => $data];
        } catch (\Exception $e) {
            $this->logger->write_log("addOsOvertime svc 异常信息:".$e->getMessage());
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }



}

