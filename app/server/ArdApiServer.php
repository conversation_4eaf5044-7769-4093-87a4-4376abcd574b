<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;

class ArdApiServer extends BaseServer
{
    /**
     * Shop部门 网点维度的揽派统计数据
     * @param $store_id
     * @param $begin_date
     * @param $end_date
     * @return mixed
     * @throws BusinessException
     */
    public static function getShopStoreStatisticsData($store_id, $begin_date, $end_date)
    {
        $fle_rpc             = new ApiClient("ard_api", '', 'deliverycount.get_shopu_project_by_store_date', 'en');
        $param['store_id']   = $store_id;
        $param['begin_date'] = $begin_date;
        $param['end_date']   = $end_date;
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (isset($res['result']['code']) && $res['result']['code'] != 1) {
            throw new BusinessException($res['msg']);
        }
        return $res['result']['data'];
    }

    /**
     * Shop部门 大区维度的揽派统计数据
     * @param $region_id
     * @param $begin_date
     * @param $end_date
     * @return mixed
     * @throws BusinessException
     */
    public static function getShopRegionStatisticsData($region_id, $begin_date, $end_date)
    {
        $fle_rpc             = new ApiClient("ard_api", '', 'deliverycount.get_shopu_project_by_region_date', 'en');
        $param['region_id']  = $region_id;
        $param['begin_date'] = $begin_date;
        $param['end_date']   = $end_date;
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();
        if (isset($res['error'])) {
            throw new BusinessException($res['error']);
        }
        if (isset($res['result']['code']) && $res['result']['code'] != 1) {
            throw new BusinessException($res['msg']);
        }
        return $res['result']['data'];
    }

}
