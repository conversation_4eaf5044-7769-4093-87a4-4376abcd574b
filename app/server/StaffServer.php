<?php

namespace FlashExpress\bi\App\Server;

use app\enums\LangEnums;
use FlashExpress\bi\App\Enums\JobGradeEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManagePieceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageRegionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreCategoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\StaffPhoneCheckLogModel;
use FlashExpress\bi\App\Models\backyard\StaffTodoListModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\bi\BiBaseModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleKaProfileModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\fle\StaffInfoPositionModel;
use FlashExpress\bi\App\Models\fle\StaffSigningStatusModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrOrganizationDepartmentRelationStoreRepository;
use FlashExpress\bi\App\Repository\InteriorGoodsRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use WebGeeker\Validation\Validation;

class StaffServer extends BaseServer
{
    // 展示选择辅导员的职位
    public static $show_job_title = [13,37,110,452,1930,1015,1000];
    public static $show_delivery_code_job_title = [];
    public static $counselor_department_id = [4];
    public const MANAGE_ORG_TYPE_DEPARTMENT = 1; //管辖部门
    public const MANAGE_ORG_TYPE_REGION = 2;     //管辖大区
    public const MANAGE_ORG_TYPE_PIECE = 3;      //管辖片区
    public const MANAGE_ORG_TYPE_STORE = 4;      //管辖网点
    public function __construct($lang = 'zh-CN', $timeZone='+07:00')
    {
        parent::__construct($lang, $timeZone);
        $this->department = new DepartmentRepository();
    }

    public function test($params)
    {
        return $params;
    }

    public function getStaffEmpId($staff_info_id)
    {
        $staff_info  = $this->getStaffInfo(['staff_info_id' => $staff_info_id]);
        if($staff_info['state'] != HrStaffInfoModel::STATE_ON_JOB){
            return false;
        }
        return $staff_info['emp_id'];
    }

    /**
     * 根据部门和职位查询在职的正式员工
     * @param $params
     * @return array
     */
    public function getStaffInfoByDepartmentJobTitle($params): array
    {

        if (empty($params)) {
            return [];
        }
        $result = [];
        foreach ($params as $param) {
            if (empty($param['node_department_id']) && empty($param['job_title'])) {
                continue;
            }
            $bind = [
                'node_department_id' => $param['node_department_id'],
                'formal'             => HrStaffInfoModel::FORMAL_1,
                'state'              => HrStaffInfoModel::STATE_1,
            ];
            $conditions = " node_department_id = :node_department_id: and formal = :formal: and state = :state: ";
            if(!empty($param['job_title'])){
                $bind['job_title'] = array_values($param['job_title']);
                $conditions .= " and job_title in ({job_title:array}) ";
            }

            $staffInfo = HrStaffInfoModel::find([
                'conditions' => $conditions,
                'bind'       => $bind,
                'columns'    => ['staff_info_id', " IF(mobile='' OR mobile IS NULL , mobile_company, mobile) as mobile", 'node_department_id', 'job_title'],
            ])->toArray();
            if (!empty($staffInfo)) {
                $result[] = $staffInfo;
            }
        }
        if(empty($result)){
            return [];
        }
        return array_merge(...$result);
    }


    /**
     * @description 获取指定工号的下级
     * @param $mangerId
     * @return array
     */
    public function getStaffInfoId($mangerId): array
    {
        $staffInfo = HrStaffItemsModel::find([
            'conditions' => 'value = :staff_info_id: and item = :item:',
            'bind'       => [
                'staff_info_id' => $mangerId,
                'item' => 'MANGER',
            ],
            'columns' => 'staff_info_id',
        ])->toArray();
        return array_column($staffInfo, 'staff_info_id');
    }

    /**
     * 找上级
     * @param $staffId
     * @return array
     */
    public function getStaffMangerId($staffId): array
    {
        $Sql       = "select `value` from `hr_staff_items` where `staff_info_id`=" . $staffId . " AND `item`='MANGER'";
        $data      = $this->getDI()->get('db_rby')->query($Sql);
        $data_list = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data_list;
    }
    /**
     * 1.上级正常，返回上级，2。如果上级离职，找原上级的上级【只有三级，不递归】,3.新上级入职hris更新后直接展示 ORDER BY id DESC"新的上级
     * @param $staffId
     * @return array
     */
    public function getHigherStaffId($staffId): array
    {
        try{
            $mangerArr['value'] = 0;
            $Sql       = "select `value` from `hr_staff_items` where `staff_info_id`=" . $staffId . " AND `item`='MANGER'  ORDER BY id DESC";
            $data      = $this->getDI()->get('db_rby')->query($Sql);
            $mangerId = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (empty($mangerId)) { //无上下级关系
                return $mangerArr;
            }

            if (in_array($mangerId['value'], explode(',', env('hr_fle_higher_staff')))) { //17245在bi系统不存在，如果上级是17245则直接返回
                return ['value' => $mangerId['value']];
            }

            $Sql       = "select * from `hr_staff_info` where `staff_info_id`=" . $mangerId['value'] ;
            $data      = $this->getDI()->get('db_rby')->query($Sql);
            $mangerInfo = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if( $mangerInfo['state'] != 1 ){
                $Sql       = "select `value` from `hr_staff_items` where `staff_info_id`=" . $mangerId['value'] . " AND `item`='MANGER'  ORDER BY id DESC";
                $data      = $this->getDI()->get('db_rby')->query($Sql);
                $mangerId = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
                return $mangerId ?  $mangerId : $mangerArr;
            }else{
                return $mangerId ?  $mangerId : $mangerArr;
            }
        }catch (Exception $e) {
            $this->wLog('error',$e->getMessage(),'>>>getHigherStaffId');
        }
        return  [];
    }

    /**
     * 修改上级
     * @param $staffId
     * @param $mangerId
     * @return bool
     */
    public function updateStaffMangerId($staffId, $mangerId): bool
    {

        $staffInfo = $this->getStaffMangerId($staffId);
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $time = gmdate("Y-m-d H:i:s", time() + $add_hour * 3600);
        if (empty($staffInfo)) {
            $data_list['staff_info_id'] = $staffId;
            $data_list['item']          = 'MANGER';
            $data_list['value']         = $mangerId;
            $data_list['created_at']    = $time;
            $data_list['updated_at']    = $time;
            $result = $this->getDI()->get('db')->insertAsDict(
                'hr_staff_items', $data_list
            );
        } else {
            $result = $this->getDI()->get('db')->updateAsDict(
                'hr_staff_items',
                [
                    'value' => $mangerId,
                    'updated_at' => $time,
                ],
                'staff_info_id = '.$staffId.' and item = "MANGER" '
            );
        }
        return $result;

    }

    /**
     * 获取员工基本信息接口
     * @param $staff_id
     */
    public function get_staff($staff_id){
        $staff_model = new StaffRepository($this->lang);
        $info = $staff_model->getStaffPosition($staff_id);

        if(!empty($info)){
            $info['job_number'] = $info['staff_info_id'];
            $info['entry_date'] = empty($info['hire_date']) ? '' : date('Y-m-d',strtotime($info['hire_date']));
            $info['leave_date'] = empty($info['leave_date']) ? '' : date('Y-m-d',strtotime($info['leave_date']));
            $info['department_id'] = $info['sys_department_id'];
            $info['store_id'] = $info['sys_store_id'];
            $info['position_id'] = $info['job_title'];
            $info['id_number'] = $info['identity'];
            $info['department_name'] = $info['depart_name'];

            //网点和总部
            $info['store_manager_id'] = $info['phone'] = $info['difficult_phone_first'] = $info['difficult_phone_second'] = '';
            if($info['sys_store_id'] != -1){
                $store_info = $staff_model->getStaffStoreInfo($info['sys_store_id']);
                $info['store_manager_id'] = empty($store_info) ? '' : $store_info['manager_id'];
                $info['phone'] = empty($store_info) ? '' : $store_info['phone'];
                $info['difficult_phone_first'] = empty($store_info) ? '' : $store_info['difficult_phone_first'];
                $info['difficult_phone_second'] = empty($store_info) ? '' : $store_info['difficult_phone_second'];

                // 区域 
                $info['store_area_name'] = enums::$store_areas[$store_info['province_manage_geography_code']] ?? '';
            }else{
                $store_info['name'] = 'Head Office';
            }
            $info['store_category'] = $store_info['category'] ?? 0;
            $info['store_name']     = empty($store_info) ? '' : $store_info['name'];
            $info['position_name']  = $info['job_name'];
            $info['hire_type_text']  = !empty($info['hire_type']) ? $this->getTranslation()->_('hire_type_'.$info['hire_type']) : '';
        }
        $return['data'] = $info;
        return $this->checkReturn($return);

    }

    /**
     * 查询员工是否为网点负责人
     */
    public function getStoreManager($store_id)
    {
        $retureData = "";
        $sql = "--
                select 
                    id,
                    category,
                    manager_id
                from sys_store
                where id = '{$store_id}'";
        $store = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($store) {
            //DC+SP
            $staffInfo = $this->getBranchSupervisor($store_id);
            $staffInfo = array_column($staffInfo, null, 'sys_store_id');

            //Shop
            $staffShopInfo = $this->getShopManager($store_id);
            $staffShopInfo = array_column($staffShopInfo, null, 'organization_id');

            switch ($store['category']) {
                case 1: //DC
                case 2: //SP
                    $retureData = $staffInfo[$store_id]['staff_info_id'] ?? "";
                    break;
                case 4: //SHOP(pickup-only) SHOP
                case 5: //SHOP(pickup&delivery) SHOP
                case 7: //USHOP SHOP
                    $retureData = $staffShopInfo[$store_id]['manager_id'] ?? "";
                    break;
                case 8: //HUB
                    $hubManagerList = env('hub_manager', '17377,25921,27424,27118,24513,23917,33489,27011,17139,35180');
                    $hubManagerList = explode(',', $hubManagerList);
                    $hub_manager = [
                        'TH01470301'    => ['name' => 'BKK', 'manager_id' => $hubManagerList[0]],
                        'TH02030204'    => ['name' => 'LAS', 'manager_id' => $hubManagerList[1]],
                        'TH56010102'    => ['name' => 'NO1', 'manager_id' => $hubManagerList[2]],
                        'TH68010201'    => ['name' => 'SO1', 'manager_id' => $hubManagerList[3]],
                        'TH27011602'    => ['name' => 'NE1', 'manager_id' => $hubManagerList[4]],
                        'TH49030503'    => ['name' => 'NO2', 'manager_id' => $hubManagerList[5]],
                        'TH37010701'    => ['name' => 'NE2', 'manager_id' => $hubManagerList[6]],
                        'TH71111201'    => ['name' => 'SO2', 'manager_id' => $hubManagerList[7]],
                        'TH38040201'    => ['name' => 'NE3', 'manager_id' => $hubManagerList[8]],
                        'TH47190703'    => ['name' => 'NO3', 'manager_id' => $hubManagerList[9]],
                    ];
                    $retureData = $hub_manager[$store_id]['manager_id'] ?? "";
                    break;
                default:
                    break;
            }
        }
        return $retureData;
    }
    /**
     * 获取DC/SP网点负责人
     */
    public function getBranchSupervisor($store_id = null)
    {
        if (empty($store_id)) {
            $where = "";
        } else {
            $where = " ss.id = '{$store_id}' and ";
        }

        $sql = "--
                select 
                   hsi.staff_info_id,
                   hsi.sys_store_id
                from hr_staff_info hsi
                left join sys_store ss on hsi.sys_store_id = ss.id
                where {$where}
                hsi.job_title = 16       -- DC/SP网点正主管
                and ss.category in (1,2) -- dc/sp
                and hsi.formal = 1       -- 在编
                and hsi.state = 1        -- 在职
                and hsi.is_sub_staff = 0 -- 不是子账号
                ";
        $staff_info = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $staff_info;
    }

    /**
     * 获取shop的负责人
     */
    public function getShopManager($store_id = null)
    {
        if (empty($store_id)) {
            $where = "";
        } else {
            $where = " ss.id = '{$store_id}' and ";
        }

        $sql = "--
                select 
                   ss.manager_id,
                   si.organization_id
                from staff_info si
                left join sys_store ss on si.organization_id = ss.id
                where {$where}
                ss.category in (4,5,7)  -- shop
                and si.formal = 1       -- 在编
                and si.state = 1        -- 在职
                and si.is_sub_staff = 0 -- 不是子账号
                group by ss.manager_id
                ";
        $staff_info = $this->getDI()->get('db_fle')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $staff_info;
    }

    /**
     * 用户详情
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public function getStaffById($staffId,$columns = '*'): array
    {
        $staffModel = HrStaffInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staffId],
        ]);
        return $staffModel ? $staffModel->toArray() : [];
    }

    /**
     * 同步hris员工信息rpc
     */
    public function syncHrStaff($params)
    {
        $staffId               = $params["staff_info_id"] ?? "";
        $departmentId          = $params["department_id"] ?? "";
        $jobTitle              = $params["job_title"] ?? "";
        $sysStoreId            = $params["sys_store_id"] ?? "";
        $directManager         = $params["direct_manager"] ?? "";
        $car_type              = $params["car_type"] ?? "";
        $operater              = $params["operater"] ?? 10000;
        $position_category     = $params["position_category"] ?? [];
        $vehicle_source        = $params["vehicle_source"] ?? 0;
        $rental_car_cteated_at = $params["vehicle_use_date"] ?? "";
        $working_day_rest_type = $params['working_day_rest_type'] ?? '';
        $type                  = $params['type'] ?? JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE;
        $jobTitleGrade         = $params['job_title_grade'] ?? 0;
        $projectNum            = $params['project_num'] ?? null;
        $hireType              = $params['hire_type'] ?? 0;
        $hireTimes             = $params['hire_times'] ?? 0;
        $vehicleTypeCategory   = $params['vehicle_type_category'] ?? 0;

        /**
         * staff_change_job
         */
        //调用hris提供的员工更新接口
        $ac = new ApiClient('hr_rpc', '', 'staff_change_job');
        // $ac = new ApiClient('hr_rpc', 'staff_change_job', '1231231','th');
        /**
            员工id ：staff_info_id 20857
            操作者id ：operater 10000
            部门id ：department_id 9
            职位 ：job_title 19
            网点id ：sys_store_id TH01010101
            角色（数组）： position_category []
            直线上级 ： direct_manager 20630
         */
        $ac->setParams(
            [
                "staff_info_id"         => $staffId,
                "department_id"         => $departmentId,
                "job_title"             => $jobTitle,
                "sys_store_id"          => $sysStoreId,
                "operater"              => $operater,
                "position_category"     => $position_category,
                "direct_manager"        => $directManager,
                "car_type"              => $car_type,
                "vehicle_source"        => $vehicle_source,
                "vehicle_use_date"      => $rental_car_cteated_at,
                "working_day_rest_type" => $working_day_rest_type,
                'type'                  => $type,
                'job_title_grade'       => $jobTitleGrade,
                'project_num'           => $projectNum,
                'hire_type'             => $hireType,
                'hire_times'            => $hireTimes,
                'vehicle_type_category' => $vehicleTypeCategory,
            ]
        );
        $return = $ac->execute();
        return $return ?? [];
    }

    /**
     * @param $staff_id
     * @return string
     */
    public function getLanguage($staff_id)
    {
        $arr = StaffAccountModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id: and equipment_type in ({equipment_types:array})',
            'bind'       => [
                'staff_info_id' => $staff_id,
                'equipment_types' => [
                    LangEnums::$equipment_type['kit'],
                    LangEnums::$equipment_type['backyard'],
                ],
            ],
            'order'      => 'updated_at desc',
            'columns'    => "accept_language",
        ]);
        if (empty($arr) || empty($arr->accept_language)) {
            $lang = getCountryDefaultLang();
        } else {
            $lang = substr($arr->accept_language, 0, 2);
        }
        return $lang;
    }

    /**
     * @param $staffIds
     * @return
     */
    public function getBatchStaffLanguage($staffIds)
    {
        if(empty($staffIds)) {
            return false;
        }
        $staffLanguage = StaffAccountModel::find([
            'conditions' => ' staff_info_id in ({staff_ids:array}) and equipment_type in ({equipment_types:array})',
            'bind' => ['staff_ids' => $staffIds, 'equipment_types' => [LangEnums::$equipment_type['kit'],LangEnums::$equipment_type['backyard']]],
            'order' => 'updated_at asc',
            'columns' => "staff_info_id, accept_language",
        ])->toArray();
        if($staffLanguage) {
            //array_column 当出现多个相同的index_key时，后面的会替换前面的，所以sql中用了 asc 的排序规则
            $staffLanguage = array_column($staffLanguage, 'accept_language', 'staff_info_id');
        }
        $staffLangs = [];
        foreach ($staffIds as $id) {
            $staffLangs[$id] = $staffLanguage[$id] ?? getCountryDefaultLang();
        }

        return $staffLangs;
    }

    /**
     * 员工转岗快递员职位成功后，给该员工发送车辆信息修改提醒消息
     * rpc -> bi
     * @param int $staff_info_id
     * @return mixed
     */
    public function sendCourierTransferVehicleMsg(int $staff_info_id)
    {
        if (empty($staff_info_id)) {
            return false;
        }

        $lang = $this->getLanguage($staff_info_id);
        $post_param = [
            'staff_id' => $staff_info_id,
        ];

        $bi_rpc = (new ApiClient('bi_rpc','','job_transfer_courier_send_msg', $lang));
        $bi_rpc->setParams($post_param);
        $return = $bi_rpc->execute();

        return !empty($return['code']) && $return['code'] == 1;
    }

    /**
     * 查找指定网点主管角色的人
     */
    public function getStoreManagerByRole($store_id): array
    {
        $builder = $this->modelsManager->createBuilder();
        $staffIds = $builder->columns('distinct (hsi.staff_info_id) as staff_info_id')
            ->from(['hsi' => HrStaffInfoModel::class])
            ->leftJoin(HrStaffInfoPositionModel::class, 'hsip.staff_info_id = hsi.staff_info_id', 'hsip')
            ->where('hsi.state = 1 and hsi.formal = 1 and hsip.position_category = 18')
            ->andWhere('hsi.sys_store_id = :store_id:', ['store_id' => $store_id])
            ->getQuery()->execute()->toArray();

        return array_column($staffIds, 'staff_info_id');
    }

    /**
     * 获取KA客户的所属网点
     */
    public function getKaProfileInfo(string $client_id) : array
    {
        $clientInfo = FleKaProfileModel::findFirst([
            'columns' => 'id as client_id,store_id,staff_info_id,project_manager_id,sales_assistant_id',
            'conditions' => 'id = :client_id:',
            'bind' => [
                'client_id' => $client_id,
            ],
        ]);

        if (isset($clientInfo) && $clientInfo) {
            return $clientInfo->toArray();
        } else {
            return [];
        }
    }


    /**
     * 在岗人员
     * 目前排除
     */
    public function WorkOnStaffs($paramIn, $date_at)
    {
        $staff_ids = $paramIn['staff_ids'];
        $staff_ids = explode(',',$staff_ids);
        $_staff_ids = $staff_ids;

        $setting_model = new BySettingRepository($this->lang);
        if($setting_model->get_setting('closeWorkOnStaffs')){ //设置个开关有性能问题直接吧这个开关打开就逃过逻辑
            return $_staff_ids;
        }
        //快速的吧字符数组转换成int数组
        //$staff_ids = json_decode('[' . join(',', $staff_ids) . ']', true);
        if(empty($staff_ids)){
            return [];
        }
        $Staff_Repository = new StaffRepository($this->lang);
        //1 当天休息的人 这里传过来都是网点的人所以只考虑6天班的人
        $data = $Staff_Repository->getWorkdays($staff_ids, $date_at);
        if(!empty($data)){
            //过滤剩下在岗的人
            $data = array_column($data,'staff_info_id');
            $_staff_ids = array_diff($staff_ids,array_values($data));
        }

        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        // 请假取值 优先打卡逻辑
        $leave_staffs = $att_model->getStaffSplit($staff_ids,$date_at,$date_at);
        if(!empty($leave_staffs)){
            $leave_staffs = array_column($leave_staffs,'staff_info_id');
            //总人数-请假的
            $_staff_ids = array_values(array_diff($_staff_ids,array_values($leave_staffs)));
        }
        // 打卡取值
        $att_list = $att_model->getDateInfoByStaffs($staff_ids, $date_at);
        if(!empty($att_list)){
            $att_list = array_values(array_column($att_list,'staff_info_id'));
            $_staff_ids = array_unique(array_merge($_staff_ids,$att_list));
        }
        //外勤打卡的 取待审批、审核通过的
        $ab_server = new AttendanceBusinessServer($this->lang, $this->timeZone);
        $ab_data = $ab_server->find_by_date_staffs($staff_ids,$date_at);
        if(!empty($ab_data)){
            $ab_data = array_values(array_column($ab_data,'staff_info_id'));
            $_staff_ids = array_unique(array_merge($_staff_ids,$ab_data));
        }
        $str = 'staff_ids '. $paramIn['staff_ids'] . 'getWorkdays '.json_encode($data) . 'getStaffSplit '.json_encode($leave_staffs) . 'getDateInfoByStaffs '.json_encode($att_list).'find_by_date_staffs '.json_encode($ab_data);
        $this->logger->write_log($str ,'info');

        $_staff_ids = array_values($_staff_ids);
        return $_staff_ids;
    }

    /**
     * 员工状态的查看+打卡情况
     */
    public function StaffListStatus($paramIn)
    {

        $staff_ids = $paramIn;
        //获取用户状态
        $staffArr = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staffs:array})',
            'bind'       => ['staffs' => $staff_ids],
            'columns'    => ['staff_info_id', 'wait_leave_state', 'state'],
        ])->toArray();
        //$staffArr = array_column($staffArr, null, 'staff_info_id');

        // 打卡取值
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        $att_list = $att_model->getDateInfoByStaffs($staff_ids, date("Y-m-d"));
        $data = [];
        if (!empty($att_list)) {
            $data = array_column($att_list,'staff_info_id');
        }

        foreach ($staffArr as $key => &$val) {
            $val['on_work'] = in_array($val['staff_info_id'],$data) ? 1:2 ;
        }
        return $staffArr ?? [];
    }

     /**
     * 员工状态的查看+打卡情况
     */
    public function StaffListLeave($paramIn,$date)
    {

        $staff_ids = explode(',',$paramIn);
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        // 请假取值 优先打卡逻辑
        $leave_staffs = $att_model->getStaffSplitByStatus($staff_ids,$date,$date,'2');
        return $leave_staffs ?? [];
    }

     /**
     * 员工状态打卡情况 给出上、下班
     */
    public function getStaffClockinInfo($paramIn)
    {
        $staff_id = $paramIn['staff_id'];
        $date = $paramIn['date'];
        $data = [];
        $data['started_at'] = 0;
        $data['end_at'] = 0;;
        if(empty($staff_id) || empty($date)){
            return $data;
        }
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        // 请假取值 优先打卡逻辑
        $leave_staffs = $att_model->getDateInfo($staff_id,$date);
        $data['started_at'] = empty($leave_staffs['start_data']) ? 0 :1;
        $data['end_at'] = empty($leave_staffs['end_data']) ? 0 :1;
        return $data ?? [];
    }

    /**
     * 清理打卡信息
     */
    public function clearStaffClockin($paramIn)
    {
        $staff_id = $paramIn['staff_id'];
        $date = $paramIn['date'];
        $type = $paramIn['type']; //1清除当天打卡 2 清除下班打卡
        if(empty($staff_id) || empty($date) || empty($type)){
            return false;
        }
        $insert = [];
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);

        if($type == 2 ){
            $insert['end_at'] = null;
            $insert['end_state'] = null;
            $flag = $att_model->updateStaffWorAttendance($staff_id,$date, $insert);
            if (!$flag) {
                return false;
            }else{
                return true;
            }
        }else{
            $this->clearimg($staff_id);
            return $att_model->clear_staff_work_attendance($staff_id,$date);
        }


    }

    /**
     * 清除员工底片
     */
    public function clearimg($staff_id, $userinfo = null)
    {
        $sql = "update staff_work_attendance_attachment
              set deleted = 1,
              operate_id = :operate_id,
              operate_name= :operate_name
              where staff_info_id = :staff_info_id and deleted = 0";
        $pdo = $this->getDI()->get('db');
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(":operate_id", 10000, \PDO::PARAM_INT);
        $stmt->bindValue(":operate_name", 'java svc', \PDO::PARAM_STR);
        $stmt->bindValue(":staff_info_id", $staff_id, \PDO::PARAM_INT);
        $staff_work_attendance = $stmt->execute();
        if ($staff_work_attendance) {
            return true;
        } else {
            throw new \Exception('更新失败');
        }

    }

    /**
     * 获取员工信息
     * @param array $paramIn 传入参数[staff_info_id 员工工号]
     * @return array
     */
    public function getStaffInfo(array $paramIn = [], $columns = '*'): array
    {
        //获取员工工号
        $staffInfoId = $this->processingDefault($paramIn, 'staff_info_id');

        //校验工号
        $validations = [
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id' => $staffInfoId,
            ],
            'columns' => $columns,
        ]);
        if (empty($staffInfo)) {
            return [];
        } else {
            $staffInfo = $staffInfo->toArray();
        }

        return $staffInfo;
    }

    /**
     * 获取最近30天入职员工工号
     * @param array $paramIn 传入参数[days 距今天数]
     * @return array
     */
    public function getStaffIdsByHasEntryDays($paramIn = []): array
    {
        //获取距今天数
        $days = $this->processingDefault($paramIn, 'days', 2, 30);
        $date = date('Y-m-d', strtotime("- {$days} days"));

        //近30天入职，且仍在职的仓管员和快递员工号（在编）
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrStaffInfoPositionModel::class, "hsi.staff_info_id = hsip.staff_info_id", "hsip");
        $builder->where('state = 1 and formal = 1');
        $builder->andWhere('hsi.hire_date >= :hire_date:', ['hire_date' => $date]);
        $builder->inWhere('hsip.position_category', [1,2]);
        $staffInfo = $builder->getQuery()->execute()->toArray();

        return array_values(array_unique(array_column($staffInfo, 'staff_info_id')));
    }



    /**
     * 创建加盟商
     */
    public function createCooperator(array $paramIn = []): bool
    {

        $validations = [
            "staff_id" => "Required|Int",
            "sys_store_id" => "Required|StrLenGeLe:0,50",
            "name" => "Required|StrLenGeLe:0,100",
            "mobile" => "Required|StrLenGeLe:0,12",
            "position_category" => "Required|Arr",
            "state" => "Required|IntIn:1",
            "formal" => "Required|IntIn:2",
            "hire_date" => "Required|Date",
            "staff_car_type" => "Required|StrIn:Bike",
        ];
        Validation::validate($paramIn, $validations);

        //获取员工工号
        $staff_id = $this->processingDefault($paramIn, 'staff_id');//工号
        $company_name_ef = $this->processingDefault($paramIn, 'company_name_ef');//合作商公司名称
        $formal = $this->processingDefault($paramIn, 'formal');//加盟商 2  合作社 3
        $hire_date = $this->processingDefault($paramIn, 'hire_date');//激活时间
        $mobile = $this->processingDefault($paramIn, 'mobile');//手机号
        $name = $this->processingDefault($paramIn, 'name');//姓名
        $position_category = $this->processingDefault($paramIn, 'position_category',3);//角色
        $staff_car_no = $this->processingDefault($paramIn, 'staff_car_no'); //车牌号
        $staff_car_type = $this->processingDefault($paramIn, 'staff_car_type');//Bike,Van,Tricycle,Car  车辆类型
        $sys_store_id = $this->processingDefault($paramIn, 'sys_store_id');//网点名称
        $state = $this->processingDefault($paramIn, 'state',2,1);//状态 1激活 2关闭
        $uuid = uuid();

        //校验工号

        $staffInfo = HrStaffInfoModel::findFirst(['conditions' => 'staff_info_id = :staff_info_id:',
                                                     'bind' => [
                                                         'staff_info_id' => $staff_id,
                                                     ]]);

        if(!empty($staffInfo)){
            throw new BusinessException('staff id already exist');
        }

        $db = HrStaffInfoModel::beginTransaction($this);
        try {
            $staffInfo = new HrStaffInfoModel();
            $staffInfo->staff_info_id = $staff_id;
            $staffInfo->name = $name;
            $staffInfo->company_name_ef = $company_name_ef;
            $staffInfo->formal = $formal;
            $staffInfo->hire_date = $hire_date;
            $staffInfo->mobile = $mobile;
            $staffInfo->state = $state;
            $staffInfo->sys_store_id = $sys_store_id;
            $staffInfo->uuid = $uuid;
            $saveStaffInfo = $staffInfo->save();

            $staffItem = new HrStaffItemsModel();
            $itemData = [
                ['item'=>'CAR_TYPE','value'=>$staff_car_type,'staff_info_id'=>$staff_id],
            ];
            if(!empty($staff_car_no)){
                $itemData[] =  ['item'=>'CAR_NO','value'=>$staff_car_no,'staff_info_id'=>$staff_id];
            }
            $saveItem = $staffItem->batch_insert($itemData,'db');
            $staffPosition = new HrStaffInfoPositionModel();

            if(is_array($position_category) && !empty($position_category)){
                foreach ($position_category as $value) {
                    $positionData[] = ['staff_info_id'=>$staff_id,'position_category'=>$value];
                }
                $savePosition = $staffPosition->batch_insert($positionData,'db');
            }
            $db->commit();
            return true;
        }catch (Exception $e){
            $db->rollBack();
            throw $e;
        }
    }


    /**
     * 已入职员工DC officer, bike courier, van courier,boat courier 查询是否有辅导员
     * @param $paramIn
     * @return mixed
     */
    public function getStaffJobTitleInstructorList($paramIn) {
        $staffId = $paramIn['user']['staff_id'];

        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 30);
        $pageNum  = $this->processingDefault($paramIn, 'page_num', 2, 1);

        //获取当前用户管辖信息
        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);

        $where['staff_id'] = $staffId;
        $where['store_ids'] = $storeIds;
        $where['dept_ids'] = $deptIds;
        $where['pageSize'] = $pageSize;
        $where['pageNum'] = $pageNum;
        $total = $this->getInstructorQuery($where, true);
        $count = !empty($total) ? intval($total['count']) : 0;

        $network_dep_ids = $this->getNetWorkInfo();
        $showJobTitle = $this->getShowJobTitle();

        $list = $this->getInstructorQuery($where);
        foreach ($list as $key => $value) {
            $list[$key]['hire_date'] = date('Y-m-d',strtotime($value['hire_date']));
            //Bike Courier, DC Officer, Van Courier, Boat Courier
            if(empty($value['instructor_id']) && in_array($value['job_title'], $showJobTitle) && in_array($value['node_department_id'], $network_dep_ids)) {
                $list[$key]['is_add_instructor'] = 1;
            } else {
                $list[$key]['is_add_instructor'] = 0;
            }
        }

        $result = [
            'dataList' => $list,
            'pagination' => [
                "count"     => $count,
                "pageCount" => ceil($count / $pageSize),
                "pageNum"   => intval($pageNum),
                "pageSize"  => $pageSize,
            ],
        ];

        return $result;
    }

    /**
     * 获取Network部门清单--TH ph
     * @return array
     */
    public function getNetWorkInfo()
    {
        return (new DepartmentRepository())->getDepartmentChildInfo(static::$counselor_department_id);//network
    }

    /**
     * 获取指定用户管辖信息
     * @param $staffId
     * @return array
     */
    public function getManagerInfo($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staffId:',
            'bind'       => ['staffId' => $staffId],
            'columns' => 'sys_store_id',
        ]);

        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        [$deptIds, $storeIds] = $this->getStaffCheckData($staffId);
        if($staffInfo['sys_store_id'] != -1) {//不是总部网点，则查自己所在网点
            $storeIds[] = $staffInfo['sys_store_id'];
        }
        return [$deptIds, $storeIds];
    }

    /**
     * 查询已入职信息query
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getInstructorQuery($params, $isCount = false)
    {
        $columns = 'h.staff_info_id,name,h.job_title,h.sys_department_id,h.node_department_id,h.sys_store_id,h.instructor_id,h.hire_date,j.job_name';
        if($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['h' => HrStaffInfoModel::class]);
        $builder->join(HrEntryModel::class,'h.staff_info_id = e.staff_id','e');
        $builder->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j');
        //原逻辑--暂时保留。
//        $builder->where('h.job_title IN (13,110,452,37) AND is_sub_staff = 0 AND h.formal = 1 AND h.state = 1');
        $builder->where('is_sub_staff = 0 AND h.formal = 1 AND h.state = 1');

        $stringWhere = '';
        $where['manger'] = $params['staff_id'];//上级
        if(!empty($params['store_ids'])) {
            $stringWhere .= ' or h.sys_store_id in ({storeIds:array})';
            $where['storeIds'] = $params['store_ids'];
        }
        if(!empty($params['dept_ids'])) {
            $stringWhere .= ' or h.node_department_id in ({dept_ids:array})';
            $where['dept_ids'] = $params['dept_ids'];
        }
        $builder->andWhere('h.manger = :manger: ' . $stringWhere, $where);
        $builder->andWhere('h.hire_date >= :hire_date: ', ['hire_date' => date("Y-m-d 00:00:00",strtotime("-7 day"))]);//获取近7天数据

        if($isCount){
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->limit($params['pageSize'], ($params['pageNum'] - 1) * $params['pageSize']);
        $builder->orderBy('hire_date desc');
        return $builder->getQuery()->execute()->toArray();

    }

    public function getStaffCheckData($staffId)
    {
        $deptIds = (new DepartmentRepository())->getStaffOrganizationInfo($staffId);
        $storeIds = (new SysStoreServer())->getStoreByManager($staffId);
        return [$deptIds, $storeIds];
    }


    /**
     * 获取 未有辅导员员工数据
     * @param $paramIn
     * @return mixed
     */
    public function getStaffJobTitleInstructorCount($paramIn)
    {
        $staffId      = $paramIn['user']['staff_id'];
        $is_three_day = $paramIn['is_three_day'] ?? false;

        //获取当前用户管辖信息
        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);
        $network_dep_ids = $this->getNetWorkInfo();
        $showJobTitle    = $this->getShowJobTitle();

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as count');
        $builder->from(['h' => HrStaffInfoModel::class]);

        // 泰国存在通过winhr导入个人代理的情况，即：不走正常的招聘流程
        // 所以泰国需要排掉这种途径的人 [!!!!! 这里与列表的逻辑要一致 !!!!!]
        $builder->join(HrEntryModel::class,'h.staff_info_id = e.staff_id','e');
        $builder->where('h.formal = 1 AND h.state = 1 AND h.is_sub_staff = 0 and h.instructor_id is null');

        //入职日期
        $date = date('Y-m-d 00:00:00', strtotime("-7 days")); //默认7天内
        if ($is_three_day) {
            $date = date('Y-m-d 00:00:00', strtotime("-3 days"));
        }
        $builder->andWhere('hire_date >= :date:', ['date' => $date]);

        //权限
        $conditions           = '';
        $bindParams['manger'] = $staffId;
        if(!empty($storeIds)) {
            $conditions .= ' or h.sys_store_id in ({storeIds:array})';
            $bindParams['storeIds'] = $storeIds;
        }
        if(!empty($deptIds)) {
            $conditions .= ' or h.node_department_id in ({dept_ids:array})';
            $bindParams['dept_ids'] = $deptIds;
        }
        $builder->andWhere('h.manger = :manger:' . $conditions, $bindParams);

        //配置
        if ($network_dep_ids) {
            $builder->andWhere('h.node_department_id in ({node_department_id:array})', ['node_department_id' => $network_dep_ids]);
        }
        if ($showJobTitle) {
            $builder->andWhere('h.job_title IN ({job_title:array})', ['job_title' => $showJobTitle]);
        }

        if (isCountry('MY')) {
            $builder->andWhere('h.hire_type != :hire_type:', ['hire_type' => HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT]);
        }

        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->count);
    }

    /**
     * 添加辅导员
     * @param $paramIn
     * @return array
     */
    public function addStaffInstructor($paramIn)
    {
        try {
            $staff_info_id = $paramIn['staff_info_id'];
            $instructor_id = $paramIn['instructor_id'];//辅导员id
            $instructor_info = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,instructor_id,hire_date',
                'conditions' => 'staff_info_id = :staff_info_id: AND state = 1 AND formal = 1',
                'bind' => [
                    'staff_info_id' => $instructor_id,
                ],
            ]);

            if(empty($instructor_info)) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('add_instructor_error_1')));//辅导员不存在
            }

            $staff_info = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,instructor_id,hire_date',
                'conditions' => 'staff_info_id = :staff_info_id: AND state = 1 AND formal = 1',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);

            if(empty($staff_info)) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('add_instructor_error_2')));//需要添加的员工不存在
            }

            if(!empty($staff_info['instructor_id'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('add_instructor_error_3')));
            }

            $ac = new ApiClient('hr_rpc', '', 'update_staff_instructor', $this->lang);
            $ac->setParams([
                'staff_info_id' => $staff_info_id,
                'instructor_id' => $instructor_id,
                'operatorId' => $paramIn['user']['id'],
            ]);
            $ret = $ac->execute();

            if($ret["result"]['code'] == 1) {
                $this->sendAddInstructorMsg($staff_info_id, $instructor_id);
                return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log('update_staff_instructor:'.$e->getMessage().'--行号：'.$e->getLine());
            return $this->checkReturn(['msg' => $this->getTranslation()->_('5002')]);
        }
    }

    public function sendAddInstructorMsg($staffId, $instructorId)
    {
        $logger = $this->getDI()->get('logger');
        $staffLang = $this->getBatchStaffLanguage([$staffId, $instructorId]);
        $staffs = HrStaffInfoModel::find([
            'conditions' => ' staff_info_id in ({staff_ids:array})',
            'bind' => ['staff_ids' => [$staffId, $instructorId]],
        ])->toArray();
        $staffs = array_column($staffs, null, 'staff_info_id');

        $id = time() . $staffId. rand(1000000, 9999999);
        $param['staff_users'] = [$staffId];//数组 多个员工id
        $param['message_title'] = sprintf($this->getTranslationByLang($staffLang[$staffId])->_('add_instructor_staff_msg_title'), $staffs[$instructorId]['name']);
        $param['message_content'] = addslashes("<div style='font-size: 30px'>" .  sprintf($this->getTranslationByLang($staffLang[$staffId])->_('add_instructor_staff_msg_content'), $staffs[$instructorId]['name']) . "</div>");
        $param['staff_info_ids_str'] = $staffId;
        $param['id'] = $id;
        $param['category'] = -1;
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $staffLang[$staffId]));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        $logger->write_log('add_instructor_staff_msg_title ' . json_encode($param, JSON_UNESCAPED_UNICODE) . ' result ' . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');

        $id = time() . $instructorId . rand(1000000, 9999999);
        $param['staff_users'] = [$instructorId];//数组 多个员工id
        $param['message_title'] = sprintf($this->getTranslationByLang($staffLang[$instructorId])->_('add_instructor_msg_title'), $staffs[$staffId]['name']);
        $param['message_content'] = addslashes("<div style='font-size: 30px'>" . sprintf($this->getTranslationByLang($staffLang[$instructorId])->_('add_instructor_msg_content'), $staffs[$staffId]['name']). "</div>");
        $param['staff_info_ids_str'] = $instructorId;
        $param['id'] = $id;
        $param['category'] = -1;
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $staffLang[$instructorId]));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        $logger->write_log('add_instructor_msg_title ' . json_encode($param, JSON_UNESCAPED_UNICODE) . ' result ' . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');
    }

    /**
     * 员工到岗详情 (泰国已经入职的详情调用)
     * @param $paramIn
     * @return array
     */
    public function getStaffEntryDetail($paramIn)
    {
        $staff_info_id = $paramIn['staff_info_id'];
        $builder       = $this->modelsManager->createBuilder();
        $staff_detail  = $builder->columns('
                            h.staff_info_id,
                            h.name as staff_name,
                            h.job_title,
                            h.sys_department_id,
                            h.node_department_id,
                            h.sys_store_id,
                            h.instructor_id,
                            h.hire_date,
                            h.hire_type,
                            h.identity,
                            h.mobile,
                            h.sex,
                            h.week_working_day,
                            h.rest_type,
                            j.job_name,
                            d.name as department_name,
                            s.name as store_name')
            ->from(['h' => HrStaffInfoModel::class])
            ->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j')
            ->leftJoin(SysDepartmentModel::class, 'h.node_department_id = d.id', 'd')
            ->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's')
            ->where('h.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id])
            ->getQuery()
            ->getSingleResult();
        $result        = [];
        if (empty($staff_detail)) {
            return $result;
        }


        $staff_detail = $staff_detail->toArray();
        //班次
        $staff_shift_detail = HrStaffShiftModel::findFirst([
            //'columns' => 'staff_info_id,start,end,shift_type,shift_id',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_detail['staff_info_id'],
            ],
        ]);
        if (!empty($staff_shift_detail)) {
            $staff_shift_detail = $staff_shift_detail->toArray();

            $key        = 'shift_' . strtolower($staff_shift_detail['shift_type']);
            $shift_name = $this->getTranslation()->_($key);

            $staff_detail['shift_id']        = $staff_shift_detail['shift_id'];
            $staff_detail['shift_type']      = $staff_shift_detail['shift_type'];
            $staff_detail['shift_type_text'] = $shift_name;
            $staff_detail['shift_text']      = $staff_shift_detail['start'] . ' - ' . $staff_shift_detail['end'];
        }

        //辅导员
        $staff_detail['instructor_name'] = '';
        if (!empty($staff_detail['instructor_id'])) {
            $instructor_detail = HrStaffInfoModel::findFirst([
                'columns'    => 'staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,instructor_id,hire_date',
                'conditions' => 'staff_info_id = :staff_info_id: AND state = 1 AND formal = 1',
                'bind'       => [
                    'staff_info_id' => $staff_detail['instructor_id'],
                ],
            ]);
            if (!empty($instructor_detail)) {
                $instructor_detail->toArray();
                $staff_detail['instructor_name'] = $instructor_detail['name'];
            } else {
                $staff_detail['instructor_name'] = '';
            }
        }

        $network_dep_ids = $this->getNetWorkInfo();
        //是否展示辅导员
        $is_show_counselor = 0;
        if (in_array($staff_detail['node_department_id'], $network_dep_ids) && in_array($staff_detail['job_title'],
                self::$show_job_title)) {
            $is_show_counselor = 1;
        }

        //网点主管
        $store_manager_detail = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id,name,job_title,sys_department_id,node_department_id,sys_store_id,instructor_id,hire_date',
            'conditions' => 'sys_store_id = :sys_store_id: AND state = 1 AND formal = 1 AND job_title = 16 AND is_sub_staff = 0',
            'bind'       => [
                'sys_store_id' => $staff_detail['sys_store_id'],
            ],
        ]);
        if (!empty($store_manager_detail)) {
            $store_manager_detail               = $store_manager_detail->toArray();
            $staff_detail['store_manager_id']   = $store_manager_detail['staff_info_id'];
            $staff_detail['store_manager_name'] = $store_manager_detail['name'];
        }

        switch ($staff_detail['sex']) {
            case 0:
                $sex_title = $this->getTranslation()->_('4902');
                break;
            case 1:
                $sex_title = $this->getTranslation()->_('4900');
                break;
            case 2:
                $sex_title = $this->getTranslation()->_('4901');
                break;
            default:
                $sex_title = $this->getTranslation()->_('4902');
        }

        $staff_item_list = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,item,value',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_detail['staff_info_id'],
            ],
        ])->toArray();
        $age             = '';
        $avator          = '';
        if (!empty($staff_item_list)) {
            $staff_item_list = array_column($staff_item_list, 'value', 'item');
            $staff_birthday  = $staff_item_list['BIRTHDAY'] ?? '';
            if (!empty($staff_birthday) && strtotime($staff_birthday)) {
                if ((int)date("Y") < (int)date('Y', strtotime($staff_birthday))) {
                    $age = (int)date("Y") - (int)(date('Y', strtotime($staff_birthday)) - 543);
                } else {
                    $age = (int)date("Y") - (int)date('Y', strtotime($staff_birthday));
                }
            }

            $profile_object_key = $staff_item_list['PROFILE_OBJECT_KEY'] ?? '';
            $img_prefix         = env("img_prefix",
                "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
            if (!empty($profile_object_key)) {
                $avator = $img_prefix . $profile_object_key;
            }
        }

        //获取到岗确认操作人信息
        $operate_name = $this->getOperationInfo($staff_info_id);

        $resumeInfo = (new ResumeServer())->getResumeInfoByStaffId($staff_info_id);

        $identity_personal_verification_status_text = isset(HrResumeModel::$identity_validate_status_by_text[$resumeInfo['identity_validate_status']]) ? $this->getTranslation()->_(HrResumeModel::$identity_validate_status_by_text[$resumeInfo['identity_validate_status']]) : '';
        $result = [
            'name'                  => $staff_detail['staff_name'],
            'sys_store_id'          => $staff_detail['sys_store_id'],
            'store_name'            => $staff_detail['store_name'],
            'department_id'         => $staff_detail['node_department_id'],
            'department_name'       => $staff_detail['department_name'],
            'job_title_id'          => $staff_detail['job_title'],
            'job_title_name'        => $staff_detail['job_name'],
            'identity'              => $staff_detail['identity'],
            'mobile'                => $staff_detail['mobile'],
            'sex'                   => $staff_detail['sex'],
            'hire_type'             => $staff_detail['hire_type'],
            'sex_title'             => $sex_title,
            'age'                   => $age,
            'store_manager_id'      => $staff_detail['store_manager_id'] ?? '',
            'store_manager_name'    => $staff_detail['store_manager_name'] ?? '',
            'avator'                => $avator,
            'hire_date'             => date('Y-m-d', strtotime($staff_detail['hire_date'])),
            'shift_id'              => $staff_detail['shift_id'] ?? 0,
            'shift_type'            => $staff_detail['shift_type'] ?? '',
            'shift_type_text'       => $staff_detail['shift_type_text'] ?? '',
            'shift_text'            => $staff_detail['shift_text'] ?? '',
            'is_show_counselor'     => $is_show_counselor,
            'instructor_id'         => $staff_detail['instructor_id'],
            'instructor_name'       => $staff_detail['instructor_name'],
            'operator_name'         => $operate_name,
            'working_day_rest_name' => $staff_detail['week_working_day'] && $staff_detail['rest_type'] ? $this->getTranslation()->_('working_day_rest_type_' . $staff_detail['week_working_day'] . $staff_detail['rest_type']):'',
            'size'                  =>  (new InteriorGoodsRepository())->getGoodsSize($staff_info_id)['size'],
            'job_title'             => $staff_detail['job_title'],
            'identity_personal_verification_status'       => $resumeInfo['identity_validate_status'],
            'identity_personal_verification_status_text'  => $identity_personal_verification_status_text,
            'identity_code_url'     => $resumeInfo['identity_code_url'] ?? '',//身份证图片
            'hand_identity_url'     => !empty($resumeInfo['hand_identity_url']) ? $resumeInfo['hand_identity_url'] : '',//身份证打码图片,//手持身份证照片(仅ph 有 其他国家返回空)
        ];
        // https://flashexpress.feishu.cn/wiki/SbiIwNq3YiCfdKkjeYccklxKnGe
        // 前端用这个结构
        $result['userinfo']['staff_id'] = $paramIn['user']['staff_id'] ?? '';
        $result['userinfo']['name']     = $paramIn['user']['name'] ?? '';
        
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 查询用户基础信息
     * @param $params
     * @return array
     */
    public function getStaffBusinessData($params): array
    {
        $staffInfoId = $params['staff_info_id'] ?? 0;

        $returnData = [
            'staffInfoId'   => (string)$staffInfoId,
            'promptSigning' => false,
        ];

        //非马来直接返回
        if (!isCountry('MY')) {
            return $returnData;
        }

        //用户不存在
        if (empty($staffInfoId)) {
            return $returnData;
        }

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId,'hire_type');

        if (empty($staffInfo) || !in_array( $staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            return $returnData;
        }

        //查询合同
        $data = StaffSigningStatusModel::findFirst([
            'conditions' => 'staff_info_id=:staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffInfoId
            ],
            'order' => 'id DESC',
        ]);

        if (!$data) {
            return  $returnData;
        }

        $data = $data->toArray();

        if (!$data['signing_state']) {
            $returnData['promptSigning'] = true;
        }

        return $returnData;
    }

    /**
     * 获取到岗确认操作人信息
     * @param $staffId
     * @return string
     */
    public function getOperationInfo($staffId)
    {
         $operate_name = '';
         $resumeRecommendRepository = new ResumeRecommendRepository();
         $entryInfo = $resumeRecommendRepository->getEntryInfo($staffId);

         if(empty($entryInfo)) {
             return $operate_name;
         }
         $binds = [
             'interview_id' => $entryInfo['interview_id'],
             'module_type' => HrLogModel::$log_module_type['entry'],
             'action' => HrLogModel::$log_option['confirmed'],
             'module_status' => HrLogModel::$log_status['arrived'],
         ];
         $conditions = ' module_id = :interview_id: and module_type = :module_type: and action = :action: and module_status = :module_status:';
         $logInfo = $resumeRecommendRepository->getLogOneInfo($conditions, $binds);

         if($logInfo) {
             $operateInfo = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                "bind"       => ['staff_info_id' => $logInfo['staff_info_id']],
                "columns"    => 'staff_info_id,name',
             ]);
             $operateInfo = !empty($operateInfo) ? $operateInfo->toArray() : [];
             $operate_name = '(' . $logInfo['staff_info_id'] .')' . $operateInfo['name'] ?? '';
         }
         return $operate_name;
    }
    
    // 产品让强转一下 电话 姓名 性别 入职日期 职位 这几项优先取值逻辑 并不让影响winhr 只改by
    public function replace_entry_data($data)
    {
        $job_title = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(),
            'job_name', 'id');
        $resume_ids = array_column($data, 'resume_id');
        $staff_ids = array_column($data, 'staff_id');
        $entry_data = [];
        $staff_data = [];
        if (!empty($resume_ids)){
            $entry_data = HrEntryModel::find([
                'columns'    => 'record_entry,resume_id',
                'conditions' => ' resume_id in ({resume_id:array}) ',
                'bind'       => [
                    'resume_id' => $resume_ids,
                ],
            ])->toArray();
            $entry_data = $entry_data ? array_column($entry_data, 'record_entry','resume_id') : [];
        }
        if (!empty($staff_ids)){
            $staff_data = HrStaffInfoModel::find([
                'conditions' => ' staff_info_id in ({staff_info_id:array}) ',
                'bind'       => [
                    'staff_info_id' => $staff_ids,
                ],
            ])->toArray();
            $staff_data = $staff_data ? array_column($staff_data, null,'staff_info_id') : [];
        }
        foreach ($data as $k => $v) {
            if (!empty($v['staff_id'])){
                if (isset($data[$k]['name'])){
                    $data[$k]['name'] = !empty($staff_data[$v['staff_id']]['name']) ? $staff_data[$v['staff_id']]['name'] : ($v['name'] ?? '');
                }
                if (isset($data[$k]['sex'])){
                    $data[$k]['sex'] = !empty($staff_data[$v['staff_id']]['sex']) ? $staff_data[$v['staff_id']]['sex'] : ($v['sex'] ?? '');
                }
                if (isset($data[$k]['entry_date'])){
                    $data[$k]['entry_date'] = !empty($staff_data[$v['staff_id']]['hire_date']) ? date('Y-m-d',strtotime($staff_data[$v['staff_id']]['hire_date'])) : ($v['entry_date'] ?? '');
                }
                if (isset($data[$k]['work_time'])){
                    $data[$k]['work_time'] = !empty($staff_data[$v['staff_id']]['hire_date']) ? date('Y-m-d',strtotime($staff_data[$v['staff_id']]['hire_date'])) : ($v['work_time'] ?? '');
                }
                if (isset($data[$k]['position_id'])){
                    $data[$k]['position_id'] = !empty($staff_data[$v['staff_id']]['job_title']) ? $staff_data[$v['staff_id']]['job_title'] : ($v['position_id'] ?? '');
                    $data[$k]['position_name'] = !empty($job_title[$v['position_id']]) ? $job_title[$v['position_id']] : ($v['position_name'] ?? '');
                }
                if (isset($data[$k]['mobile'])){
                    $data[$k]['mobile'] = !empty($staff_data[$v['staff_id']]['mobile']) ? $staff_data[$v['staff_id']]['mobile'] : ($v['mobile'] ?? '');
                }
                if (isset($data[$k]['phone'])){
                    $data[$k]['phone'] = !empty($staff_data[$v['staff_id']]['mobile']) ? $staff_data[$v['staff_id']]['mobile'] : ($v['phone'] ?? '');
                }
            }else{
                $record_entry = $entry_data[$v['resume_id']] ? json_decode($entry_data[$v['resume_id']], true) : [];
                if (isset($data[$k]['name'])){
                    $data[$k]['name'] = !empty($record_entry['name']) ? $record_entry['name'] : ($v['name'] ?? '');
                }
                if (isset($data[$k]['sex'])){
                    $data[$k]['sex'] = !empty($record_entry['sex']) ? $record_entry['sex'] : ($v['sex'] ?? '');
                }
                if (isset($data[$k]['entry_date'])){
                    $data[$k]['entry_date'] = !empty($record_entry['hire_date']) ? date('Y-m-d',strtotime($record_entry['hire_date'])) : ($v['entry_date'] ?? '');
                }
                if (isset($data[$k]['work_time'])){
                    $data[$k]['work_time'] = !empty($record_entry['hire_date']) ? date('Y-m-d',strtotime($record_entry['hire_date'])) : ($v['work_time'] ?? '');
                }
                if (isset($data[$k]['position_id'])){
                    $data[$k]['position_id'] = !empty($record_entry['job_title']) ? $record_entry['job_title'] : ($v['position_id'] ?? '');
                    $data[$k]['position_name'] = !empty($job_title[$v['position_id']]) ? $job_title[$v['position_id']] : ($v['position_name'] ?? '');
                }
                if (isset($data[$k]['mobile'])){
                    $data[$k]['mobile'] = !empty($record_entry['mobile']) ? $record_entry['mobile'] : ($v['phone'] ?? '');
                }
                if (isset($data[$k]['phone'])){
                    $data[$k]['phone'] = !empty($record_entry['mobile']) ? $record_entry['mobile'] : ($v['phone'] ?? '');
                }
            }
        }
        return $data;
    }

    /**
     * 已入职员工列表
     * @param $paramIn
     * @return mixed
     */
    public function getEntryList($paramIn)
    {
        $staffId = $paramIn['staff_id'];

        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 30);
        $pageNum  = $this->processingDefault($paramIn, 'page_num', 2, 1);

        //获取当前用户管辖信息
        [$deptIds, $storeIds] = $this->getManagerInfo($staffId);

        $where['staff_id'] = $staffId;
        $where['status'] = $paramIn['status'];
        $where['store_ids'] = $storeIds;
        $where['dept_ids'] = $deptIds;
        $where['pageSize'] = $pageSize;
        $where['pageNum'] = $pageNum;
        $total = $this->getEntryQuery($where, true);
        $count = !empty($total) ? intval($total['count']) : 0;
        $list = $this->getEntryQuery($where);

        $job_title = array_column((new SysServer($this->lang, $this->timeZone))->getJobTitleList(), 'job_name', 'id');

        $network_dep_ids = $this->getNetWorkInfo();
        $instructorInfoId = [];
        $staff_data = [];
        if($paramIn['status'] == 1 && !empty($list)) {//已入职
            $staffIds = array_column($list, 'staff_id');
            $instructor_info = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind' => [
                    'staff_ids' => $staffIds,
                ],
            ])->toArray();
            if($instructor_info) {
                $instructorInfoId = array_column($instructor_info, 'instructor_id','staff_info_id');
                $staff_data = array_column($instructor_info, null,'staff_info_id');
            }
        }
        //获取展示辅导员的职位
        $showJobTitle             = $this->getShowJobTitle();
        //获取展示派件码的职位
        $showDeliveryCodeJobTitle = $this->getShowDeliveryCodeJobTitle();
        //根据网点获取派件码
        //已入职的情况，职位、网点已入职后的职位、网点为准
        $staffInfoIds     = array_values(array_unique(array_column($list, 'staff_id')));
        $staffInfoList    = (new HrStaffInfoServer())->getUserInfoByStaffInfoIds($staffInfoIds,
            'staff_info_id,sys_store_id,hire_type,job_title');
        $storeIds         = array_column($staffInfoList, 'sys_store_id');
        $staffInfoMap     = array_column($staffInfoList, 'sys_store_id', 'staff_info_id');
        $staffHireTypeMap = array_column($staffInfoList, 'hire_type', 'staff_info_id');
        $staffJobTitleMap = array_column($staffInfoList, 'job_title', 'staff_info_id');
        $deliveryCodeList = $this->getDeliveryCodeByStoreIds($storeIds);
        $deliveryCodeArrayList = array_column($deliveryCodeList, 'deliveryCodes', 'storeId');
        $deliveryCodeList = array_column($deliveryCodeList, 'bindLimit', 'storeId');

        $list = (new StaffServer($this->lang))->replace_entry_data($list);
        foreach ($list as $key => $value) {
            
            $list[$key]['position_name'] = $job_title[$value['position_id']] ?? '';
            $list[$key]['work_time'] = $paramIn['status'] == 1 ? $value['entry_date'] : $value['work_time'];

            //is_add_instructor用于前端展示“选择辅导员按钮”
            if($paramIn['status'] == 1 && empty($instructorInfoId[$value['staff_id']]) &&
                in_array($value['position_id'], $showJobTitle) &&
                in_array($value['department_id'], $network_dep_ids)) {
                $list[$key]['is_add_instructor'] = 1;
            } else {
                $list[$key]['is_add_instructor'] = 0;
            }

            //马来兼职个人代理不展示辅导员
            if (isCountry('MY') && $value['staff_hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
                $list[$key]['is_add_instructor'] = 0;
            }

            unset($list[$key]['record_entry']);
            //获取所属网点
            $tmpStoreId = $staffInfoMap[$value['staff_id']] ?? '';
            if (empty($tmpStoreId)) {
                continue;
            }

            //is_add_delivery_code用于前端展示“选择派送码”
            //若TA在WHR为雇佣类型=个人代理，且职位=bike courier[13] / van courier[110] / tricycle courier[1000]，
            //且有派送码可选的员工成功点击到岗确认，BY到岗确认-已入职列表显示【选择派送码】按钮
            if(isCountry('PH') && $paramIn['status'] == 1 &&
                isset($staffJobTitleMap[$value['staff_id']]) && in_array($staffJobTitleMap[$value['staff_id']], $showDeliveryCodeJobTitle) &&
                isset($staffHireTypeMap[$value['staff_id']]) && $staffHireTypeMap[$value['staff_id']] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR &&
                empty($value['delivery_code']) &&
                isset($deliveryCodeList[$tmpStoreId]) && is_numeric($deliveryCodeList[$tmpStoreId]) &&
                $deliveryCodeList[$tmpStoreId] > 0 &&
                isset($deliveryCodeArrayList[$tmpStoreId]) && count($deliveryCodeArrayList[$tmpStoreId]) > 0
            ) {
                $list[$key]['is_add_delivery_code'] = 1;
            } else {
                $list[$key]['is_add_delivery_code'] = 0;
            }

        }

        return [
            'dataList' => $list,
            'pagination' => [
                "count"     => $count,
                "pageCount" => ceil($count / $pageSize),
                "pageNum"   => intval($pageNum),
                "pageSize"  => $pageSize,
            ],
        ];
    }

    /**
     * 获取展示 辅导员的职位
     * @return array
     */
    public function getShowJobTitle(): array
    {
        return static::$show_job_title;
    }

    /**
     * 获取展示 派送码的职位
     * @return array
     */
    public function getShowDeliveryCodeJobTitle(): array
    {
        return static::$show_delivery_code_job_title;
    }

    /**
     * 获取展示 派送码配置
     * @return array
     */
    public function getDeliveryCodeByStoreIds($store_ids)
    {
        return [];
    }

    /**
     * 获取展示 派送码配置
     * @return array
     */
    public function getDeliveryCode($store_id)
    {
        return [];
    }

    /**
     * 获取入职信息query
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getEntryQuery($params, $isCount = false)
    {
        $columns = [
            'hr.id as resume_id',
            'he.hire_type as staff_hire_type',
            'he.staff_id',
            "DATE_FORMAT(CONVERT_TZ(he.entry_date,'+00:00','{$this->timeZone}'),'%Y-%m-%d') AS entry_date",
            "DATE_FORMAT(hio.work_time,'%Y-%m-%d') AS work_time",
            'he.record_entry',
            'hc.hire_type',
            'hc.hc_id',
            'he.delivery_code',
        ];

        if($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['he' => HrEntryModel::class]);
        $builder->leftJoin(HrResumeModel::class, 'he.resume_id = hr.id', 'hr');
        $builder->leftJoin(HrHcModel::class, 'he.hc_id = hc.hc_id', 'hc');
        $builder->leftJoin(HrInterviewOfferModel::class, 'he.interview_offer_id = hio.id', 'hio');
        $builder->where('he.status = :status: ', ['status' => $params['status']]);

        $stringWhere = '';

        //已入职
        if ($params['status'] == 1) {
            $where['manger'] = $params['staff_id'];//上级
            if(!empty($params['store_ids'])) {
                $stringWhere .= ' or hsi.sys_store_id in ({storeIds:array})';
                $where['storeIds'] = $params['store_ids'];
            }
            if(!empty($params['dept_ids'])) {
                $stringWhere .= ' or hsi.node_department_id in ({dept_ids:array})';
                $where['dept_ids'] = $params['dept_ids'];
            }
            $builder->join(HrStaffInfoModel::class, 'he.staff_id = hsi.staff_info_id', 'hsi');
            $builder->andWhere('hsi.manger = :manger: ' . $stringWhere, $where);

            if(!$isCount) {
                $columns[] = 'hsi.name';
                $columns[] = 'hsi.first_name';
                $columns[] = 'hsi.last_name';
                $columns[] = 'hsi.sys_store_id as worknode_id';
                $columns[] = 'hsi.node_department_id as department_id';
                $columns[] = 'hsi.job_title as position_id';
            }
            $builder->andWhere('hsi.hire_date >= :hire_date: ', ['hire_date' => date("Y-m-d 00:00:00",strtotime("-7 day"))]);//获取近7天数据


        } else {
            $where['manger'] = $params['staff_id'];//上级
            if(!empty($params['store_ids'])) {
                $stringWhere .= ' or hc.worknode_id in ({storeIds:array})';
                $where['storeIds'] = $params['store_ids'];
            }
            if(!empty($params['dept_ids'])) {
                $stringWhere .= ' or hc.department_id in ({dept_ids:array})';
                $where['dept_ids'] = $params['dept_ids'];
            }
            $builder->andWhere('he.manager = :manger: ' . $stringWhere, $where);

            if(!$isCount) {
                $columns[] = 'hr.name';
                $columns[] = 'hr.first_name';
                $columns[] = 'hr.last_name';
                $columns[] = 'hc.worknode_id';
                $columns[] = 'hc.department_id';
                $columns[] = 'hio.position_id';
            }
            $builder->andWhere('hio.work_time >= :hire_date: ', ['hire_date' => date("Y-m-d 00:00:00",strtotime("-7 day"))]);//获取近7天数据

        }
        $builder->columns($columns);

        if($isCount){
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->limit($params['pageSize'], ($params['pageNum'] - 1) * $params['pageSize']);
        $builder->orderBy('he.created_at desc');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * @description:判断工号是否为可显示账号设置的工号
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/15 17:44
     */
    public function  isShowAccountSettings($staff_id=0){
        if (env('break_away_from_ms')) {
            return false;
        }

        //开关  0 为 关闭 1 为打开  打开时 调用 java 接口
        $is_show_account_settings = (new SettingEnvServer())->getSetValFromCache('is_show_account_settings');
        if (empty($is_show_account_settings)){
            return false;
        }

        //https://note.youdao.com/ynoteshare/index.html?id=30bed7ea07cc4c4cc39cb4936130e4c0&type=note&_time=*************
        $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.StaffAuthSvc','showLogout', $this->lang);
        $fle_rpc->setParams(['staff_id'=>$staff_id]);
        $fle_return = $fle_rpc->execute();
        return $fle_return['result'] ?? false;
    }

    /**
     * @description 获取员工详情
     * @param int $staffId 指定工号
     * @return array
     */
    public function getStaffInfoById($staffId)
    {
        $builder = $this->getStaffInfoBuilder($staffId);
        $staff_detail = $builder->columns('
            h.staff_info_id,
            h.job_title,
            h.node_department_id,
            h.sys_department_id,
            h.sys_store_id,
            h.name as staff_name,
            h.mobile,
            h.state,
            h.wait_leave_state,
            h.leave_date,
            h.hire_type,
            j.job_name,
            d.name as department_name,
            s.name as store_name')->getQuery()->getSingleResult();

        return $staff_detail ?? [];
    }

    /**
     * @description 获取builder
     * @param $staffId
     * @return mixed
     */
    private function getStaffInfoBuilder($staffId)
    {
        $builder = $this->modelsManager->createBuilder();
        return $builder->from(['h' => HrStaffInfoModel::class])
            ->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j')
            ->leftJoin(SysDepartmentModel::class, 'h.node_department_id = d.id', 'd')
            ->leftJoin(SysDepartmentModel::class, 'h.sys_department_id = sd.id', 'sd')
            ->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's')
            ->where('h.staff_info_id = :staff_info_id:', ['staff_info_id' => $staffId]);
    }

    /**
     * @description 获取员工详情自定义字段
     * @param $staffId
     * @param $specColumns
     * @return array
     */
    public function getStaffInfoSpecColumns($staffId, $specColumns): array
    {
        $builder = $this->getStaffInfoBuilder($staffId);
        $staff_detail = $builder->columns($specColumns)->getQuery()->getSingleResult();

        return $staff_detail ? $staff_detail->toArray(): [];
    }

    /**
     * 根据工号获取员工详情
     * @param $staff_info_ids
     * @return array
     */
    public function getStaffInfoList($staff_info_ids): array
    {
        if (empty($staff_info_ids)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $staff_list = $builder->columns('
                            h.staff_info_id,
                            h.job_title,
                            h.formal,
                            h.is_sub_staff,
                            h.state,
                            h.wait_leave_state,
                            h.node_department_id,
                            h.sys_department_id,
                            h.sys_store_id,
                            h.name as staff_name,
                            j.job_name,
                            d.name as department_name,
                            s.name as store_name,
                            h.hire_type')
            ->from(['h' => HrStaffInfoModel::class])
            ->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j')
            ->leftJoin(SysDepartmentModel::class, 'h.node_department_id = d.id', 'd')
            ->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's')
            ->inWhere('h.staff_info_id', $staff_info_ids)
            ->getQuery()->execute()->toArray();

        $staff_list = array_column($staff_list, null, 'staff_info_id');
        return $staff_list;
    }



    /**
     * 根据工号获取员工部门详情
     * @param $staff_info_ids
     * @return array
     */
    public function getStaffDepartmentInfoList($staff_info_ids): array
    {
        $builder = $this->modelsManager->createBuilder();
        $staff_list = $builder->columns('
                            h.staff_info_id,
                            h.name as staff_name,   
                            d.name as department_name '
        )
            ->from(['h' => HrStaffInfoModel::class])
            ->leftJoin(SysDepartmentModel::class, 'h.node_department_id = d.id', 'd')
            ->inWhere('h.staff_info_id', $staff_info_ids)
            ->getQuery()->execute()->toArray();

        return array_column($staff_list, null, 'staff_info_id');
    }



    public function downLoadUrl($staffId)
    {

        return [
            'download' => $this->getShortUrl(env('sign_url') . "/#/download/app"),
            'reset_password' => $this->getShortUrl(env('sign_url') . "/#/resetPass?staff_id=" . base64_encode($staffId)),
        ];
    }

    /**
     * @description: 获取员工列表 在编,在职
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/9 17:24
     */
    public function getStaffList( $params ) {
        //查询数据
        $columns = $params['columns'] ?? 'staff_info_id,name';
        $page_num    = $params['page_num'] ?? 1;
        $page_size    = $params['page_size'] ?? 1000;
        $offset  = $page_size * ($page_num - 1);
        //查询条件
        //在编 非子账号
        $conditions = "formal in ({formal:array})  AND is_sub_staff = :is_sub_staff: AND state = :state: ";
        $bind       = ['formal' => [HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN], 'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0,'state'=>HrStaffInfoModel::STATE_1];

        //手机号
        $mobile = $params['mobile'] ?? '';

        if ($mobile) {
            $conditions             .= " AND  (mobile = :mobile: or mobile_company = :mobile_company: ) ";
            $bind['mobile']         = $mobile;
            $bind['mobile_company'] = $mobile;
        }

        $list = HrStaffInfoModel::find([
                                             'conditions' => $conditions,
                                             'bind'       => $bind,
                                             'columns'    => $columns,
                                             'limit'      => $page_size,
                                             'offset'     => $offset,
                                         ])->toArray();
        $couInfo = HrStaffInfoModel::findFirst([
                                                     'columns' => 'count(1) as cou',
                                                     'conditions' => $conditions,
                                                     'bind'       => $bind,
                                                 ]);
        $result['data'] = [
            'items'    => $list,
            'paginate' => [
                'total_count' => $couInfo->cou ?? 0,
                'page_num'    => (string) $page_num,
                'page_size'   => (string) $page_size,
            ],
        ];

        return $this->checkReturn($result);

    }

    /**
     * 获取员工信息
     * @param array $paramIn 传入参数[staff_info_id 员工工号]
     * @return array
     */
    public function getStaffInfoV2(array $paramIn = []): array
    {
        //获取员工工号
        $staffInfoId = $this->processingDefault($paramIn, 'staff_info_id');

        //校验工号
        $validations = [
            "staff_info_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //查询
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id,hsi.job_title,hsi.node_department_id,group_concat(hsip.position_category) as position_category,hsi.name as staff_info_name,hsi.mobile");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrStaffInfoPositionModel::class, "hsip.staff_info_id = hsi.staff_info_id", "hsip");
        $builder->andWhere('hsi.staff_info_id = :staff_id:', ['staff_id' => $staffInfoId]); //申请人列表，没有搜索申请人工号功能
        $builder->groupby('hsi.staff_info_id');
        $staffInfo = $builder->getQuery()->getSingleResult();
        if (empty($staffInfo)) {
            return [];
        }
        $staffInfo = $staffInfo->toArray();

        $model = new FleSysDepartmentModel();
        $networkDeptIds = $model->getSpecifiedDeptAndSubDept($staffInfo['node_department_id']);
        $staffInfo['department_ids'] = array_map('intval', $networkDeptIds);
        $staffInfo['position_category'] = array_map('intval', explode(',', $staffInfo['position_category']));
        $staffInfo['staff_info_id'] = intval($staffInfo['staff_info_id']);
        $staffInfo['node_department_id'] = intval($staffInfo['node_department_id']);
        $staffInfo['job_title'] = intval($staffInfo['job_title']);

        return $staffInfo;
    }
    /**
     * 员工搜索
     * @param $params
     * @return array
     */
    public function searchStaffList($params) {
        $search_name = $params['search_name'] ?? '';
        $list = (new StaffRepository())->searchStaffList($search_name);
        if(!empty($list)) {
            foreach ($list as $key => $value) {
                $list[$key]['value'] = $value['staff_info_id'] . ' ' . $value['name'];
            }
        }
        return $list;
    }



    public function getStaffJurisdiction($staff_info_id, $type = 1)
    {
        $all_id = HrStaffManageStoreModel::$all_id; // 勾选了 all  大区  片区 网点
        //查询管辖部门
        $staff_departments = HrStaffManageDepartmentModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'department_id,is_include_sub',
        ])->toArray();

        $departments         = []; // 部门 id
        $is_include_sub_deps = []; //需要找子部门的  id
        //查询出来哪些需要找子部门
        foreach ($staff_departments as $v) {
            $departments[] = $v['department_id'];
            if (!empty($v['is_include_sub'])) {
                //需要查询子部门的 id
                $is_include_sub_deps[] = $v['department_id'];
            }
        }
        if ($is_include_sub_deps) {
            //获取需要查询子部门的部门
            $is_include_sub_departments = SysDepartmentModel::find([
                'conditions' => ' id IN ({ids:array}) ',
                'bind'       => ['ids' => $is_include_sub_deps],
                'columns'    => 'id,ancestry_v3',
            ])->toArray();
            $dept_lists                 = [[]]; //这样子的结构 [[1,3],[3,4]]
            //查询子部门
            foreach ($is_include_sub_departments as $ancestry_v3) {
                $dept_list    = SysDepartmentModel::find([
                    'conditions' => ' ancestry_v3 like :chain: ',
                    'bind'       => [
                        'chain' => "{$ancestry_v3['ancestry_v3']}/%",
                    ],
                    'columns'    => 'id,ancestry_v3',
                ])->toArray();
                $dept_lists[] = array_column($dept_list, 'id');
            }
            $dept_lists  = array_merge(...$dept_lists);
            $departments = array_merge($departments, $dept_lists);
        }


        //查询大区
        $staff_regions = HrStaffManageRegionModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,region_id',
        ])->toArray();
        $regions       = !empty($staff_regions) ? array_column($staff_regions, 'region_id') : [];
        if (in_array($all_id, $regions)) {
            //all  所有大区
            $region  = SysManageRegionModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $regions = array_column($region, 'id');
        }

        //查询片区
        $staff_pieces = HrStaffManagePieceModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,piece_id',
        ])->toArray();
        $pieces       = !empty($staff_pieces) ? array_column($staff_pieces, 'piece_id') : [];
        if (in_array($all_id, $pieces)) {
            //all  所有片区
            $piece  = SysManagePieceModel::find([
                'columns'    => 'id,name',
            ])->toArray();
            $pieces = array_column($piece, 'id');
        }
        //查询网点
        $staff_stores = HrStaffManageStoreModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,store_id',
        ])->toArray();
        $stores       = !empty($staff_stores) ? array_column($staff_stores, 'store_id') : [];
        //查询网点类型
        $store_categories = HrStaffManageStoreCategoryModel::find([
            'conditions' => ' staff_info_id  = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => ['staff_info_id' => $staff_info_id, 'type' => $type],
            'columns'    => 'staff_info_id,store_category',
        ])->toArray();

        $store_categories = !empty($store_categories) ? array_column($store_categories, 'store_category') : [];

        return [
            'departments'      => array_values(array_unique(array_filter($departments))),
            'stores'           => array_values(array_unique(array_filter($stores))),
            'regions'          => array_values(array_unique(array_filter($regions))),
            'pieces'           => array_values(array_unique(array_filter($pieces))),
            'store_categories' => array_values(array_unique(array_filter($store_categories))),
        ];
    }

    /**
     * 获取指定员工的管辖网点
     * @param $staff_info_id
     * @return array
     */
    public function getStaffManageStore($staff_info_id): array
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staffManageStore = HrStaffManageStoreModel::find([
            'conditions' => ' staff_info_id = :staff_info_id: and deleted = 0 and type = :type:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'type'          => 1,
            ],
            'columns'    => 'store_id',
        ])->toArray();
        return array_column($staffManageStore, 'store_id');
    }

    public function getStaffTodoList($params): array
    {
        $staff_info                            = $this->getStaffInfoV2(['staff_info_id' => $params['staffInfo']['staff_id']]);
        $data['staff_info']['mobile']          = strval($staff_info['mobile']);
        $data['staff_info']['staff_inf_id']    = strval($params['staffInfo']['staff_id']);
        $data['is_need_mobile_authentication'] = false;
        return $data;
        if (!isCountry('PH') || RUNTIME != 'pro') {
        }

        //快递员、仓管 才需要验证
        if (!array_intersect($params['staffInfo']['positions'],
            [RolesModel::ROLE_COURIER, RolesModel::ROLE_DC_OFFICE])) {
            return $data;
        }
        $data['is_need_mobile_authentication'] = !$this->checkStaffTodoListIsCompleted($params['staffInfo']['staff_id'],
            StaffTodoListModel::BIZ_TYPE_MOBILE_VERIFY);
        return $data;
    }

    /**
     * 验证是否完成待办
     * @param $staff_info_id
     * @param $biz_type
     * @param string $biz_value
     * @return bool
     */
    public function checkStaffTodoListIsCompleted($staff_info_id, $biz_type, string $biz_value = ''): bool
    {
        $condition = 'staff_info_id = :staff_info_id: and biz_type = :biz_type: and is_deleted = 0';
        $bind      = ['staff_info_id' => $staff_info_id, 'biz_type' => $biz_type];
        if (!empty($biz_value)) {
            $condition         .= ' AND biz_value = :biz_value:';
            $bind['biz_value'] = $biz_value;
        }

        $data = StaffTodoListModel::findFirst([
            'conditions' => $condition,
            'bind'       => $bind,
        ]);
        return !empty($data);
    }

    /**
     * @param $params
     * @return bool
     * @throws ValidationException
     * @throws BusinessException
     */
    public function verifyMobile($params)
    {

        $staff_info_id = $params['staffInfo']['staff_id'];
        $staff_info = $this->getStaffInfoV2(['staff_info_id' => $staff_info_id]);
        //没抛异常就是验证通过了
        (new SmsServer($this->lang, $this->timeZone))->checkSmsCode($params['biz_type'], $params['mobile'],
            $params['verify_code']);


        //号码变更 同步 hris
        if ($params['mobile'] != $staff_info['mobile']) {
            $isExist = HrStaffInfoModel::count(
                [
                    'conditions' => 'mobile = :mobile: and state in (1,3) and is_sub_staff = 0 and staff_info_id != :staff_info_id:',
                    'bind'       => ['mobile'=>$params['mobile'],'staff_info_id'=>$staff_info_id],
                ]
            );
            if($isExist){
                throw new BusinessException($this->getTranslation()->_('mobile_existed'));
            }
            //更新员工信息手机号
            $personInfoServer = new PersoninfoServer($this->lang, $this->timeZone);
            $syncResult = $personInfoServer->updatePersonInfobymobile([
                'staff_id' => $staff_info_id,
                'mobile'   => $params['mobile'],
            ]);
            if ($syncResult['code'] != 1){
                throw new BusinessException($syncResult['msg']);
            }
        }

        //落表
        $staffTodoListModel = StaffTodoListModel::findFirst(([
            'conditions' => 'staff_info_id = :staff_info_id: and biz_type = :biz_type: and is_deleted = 0',
            'bind'       => ['staff_info_id' => $staff_info_id, 'biz_type' => StaffTodoListModel::BIZ_TYPE_MOBILE_VERIFY],
        ]));
        if (!$staffTodoListModel) {
            $staffTodoListModel                = new StaffTodoListModel();
            $staffTodoListModel->staff_info_id = $staff_info_id;
            $staffTodoListModel->biz_type      = StaffTodoListModel::BIZ_TYPE_MOBILE_VERIFY;
            $staffTodoListModel->biz_value     = $params['mobile'];
            $staffTodoListModel->save();
        }
        return true;
    }

    /**
     * 获取符合条件的员工人数
     * @param $params
     * @return int
     */
    public function getStaffCount($params)
    {
        $count1 = HrStaffInfoModel::count(
            [
                'conditions' => "formal in ({formal:array}) and is_sub_staff=:is_sub: and state in ({state:array}) and wait_leave_state=:wait_state: and sys_store_id=:store_id: and job_title in ({job_title:array})",
                'bind' => [
                    'formal' => [enums::$staff_formal['organization'],enums::$staff_formal['internship']],
                    'is_sub'=> enums::$staff_is_sub['no'],
                    'state' => [enums::$service_status['incumbency'],enums::$service_status['suspension']],
                    'wait_state' => enums::$staff_wait_leave_state['no'],
                    'store_id' => $params['store_id'],
                    'job_title' => $params['job_title'],
                ],
            ]
        );
        $count2 = $this->modelsManager->createBuilder()
            ->columns('count(*) as cnt')
            ->from(['t1'=>HrEntryModel::class])
            ->leftJoin(HrHcModel::class,'t1.hc_id=t2.hc_id','t2')
            ->where("t1.status=2 and t1.deleted=0 and 
            t2.worknode_id=:store_id: and t2.job_title in ({job_title:array})",['store_id'=>$params['store_id'],'job_title'=>$params['job_title']])
            ->getQuery()->getSingleResult();

        return $count1+ $count2['cnt'];
    }

    /**
     * @description 大区负责人 或者片区负责人 获取大区//片区下的网点
     * @param $staff_info_id
     * @return array
     */
    public function getManageOrganizationByStaffInfoId($staff_info_id): array
    {
        $hrOrganization = new HrOrganizationDepartmentRelationStoreRepository($this->timeZone);
        //部门负责人
        $manageDepartmentIds = $this->getManageDepartmentList($staff_info_id);
        $manageSubDepartmentIds = $hrOrganization->getManageSubDepartmentList($manageDepartmentIds);
        $manageDepartment = $hrOrganization->getManageOrganizationByRelationInfo($manageSubDepartmentIds, self::MANAGE_ORG_TYPE_DEPARTMENT);

        //大区负责人
        $manageRegionIds = $this->getManageRegionsList($staff_info_id);
        $manageRegion = $hrOrganization->getManageOrganizationByRelationInfo($manageRegionIds, self::MANAGE_ORG_TYPE_REGION);

        //片区负责人
        $managePieceIds = $this->getManagePiecesList($staff_info_id);
        $managePiece = $hrOrganization->getManageOrganizationByRelationInfo($managePieceIds, self::MANAGE_ORG_TYPE_PIECE);

        //网点负责人
        $manageStoresIds = $this->getManageStoresList($staff_info_id);
        $manageStore = $hrOrganization->getManageOrganizationByRelationInfo($manageStoresIds, self::MANAGE_ORG_TYPE_STORE);

        return [
            $manageSubDepartmentIds,
            $this->getManageDepartmentStores($manageDepartment, $manageRegion, $managePiece, $manageStore),
        ];
    }

    /**
     * @description 获取管理的部门，管理的网点
     * @param $manageDepartment
     * @param $manageRegion
     * @param $managePiece
     * @param $manageStore
     * @return array
     */
    private function getManageDepartmentStores($manageDepartment, $manageRegion, $managePiece, $manageStore): array
    {
        $storeIds = array_merge(array_column($manageDepartment, 'store_id'),
            array_column($manageRegion, 'store_id'),
            array_column($managePiece, 'store_id'),
            array_column($manageStore, 'store_id'));
        return array_values(array_unique($storeIds));
    }

    /**
     * 获取员工管理的大区
     * @param $staff_info_id
     * @return array
     */
    public function getManageDepartmentList($staff_info_id): array
    {
        $manageInfo = SysDepartmentModel::find([
            'conditions' => 'manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 目前马来在用
     * 获取银行卡信息
     * @param $staff_info_id
     * @return array
     */
    public function getBankInfo($staff_info_id): array
    {
        $returnData = [
            'bank_type'     => '0',
            'bank_no'       => '',
            'bank_no_name'  => '',
            'bank_flow_img' => '',
            'is_full'       => false,
        ];
        //查看支援信息 换成主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($staff_info_id);
        if ($supportStaffInfo) {
            $staff_info_id = $supportStaffInfo['staff_info_id'];
        }


        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: ',
            'bind'       => ['staff_info_id' => $staff_info_id],
            'columns'    => 'staff_info_id,bank_type,bank_no',
        ]);

        if ($staffInfo) {
            $returnData['bank_type'] = $staffInfo->bank_type;
            $returnData['bank_no']   = strval($staffInfo->bank_no);
        }

        //持卡人
        $staffItem = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and item = 'BANK_NO_NAME'",
            'bind'       => ['staff_info_id' => $staff_info_id],
        ]);
        if ($staffItem) {
            $returnData['bank_no_name'] = $staffItem->value;
        }

        $annexInfo = HrStaffAnnexInfoModel::findFirst(
            [
                'conditions' => "staff_info_id = :staff_info_id: and type = :type:",
                'bind'       => ['staff_info_id' => $staff_info_id, 'type' => HrStaffAnnexInfoModel::TYPE_BANK_CARD],
            ]
        );
        if ($annexInfo) {
            $returnData['bank_flow_img'] = $annexInfo->annex_path_front ?? '';
        }

        if (!empty($returnData['bank_no']) && !empty($returnData['bank_no_name'])) {
            $returnData['is_full'] = true;
        }
        return $returnData;
    }

    /**
     * 更新员工银行卡信息
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveBankInfo($staff_info_id, $params): bool
    {
        $params['bank_no_name']  = trim($params['bank_no_name'] ?? '');
        $params['bank_no']       = trim($params['bank_no'] ?? '');
        $params['bank_flow_img'] = trim($params['bank_flow_img'] ?? '');

        (new SysServer($this->lang, $this->timeZone))->bankNoVerify($params['bank_type'],$params['bank_no']);

        //查看支援信息 换成主账号
        $supportStaffInfo = (new AttendanceRepository($this->lang,
            $this->timeZone))->getSupportInfoBySubStaff($staff_info_id);
        if ($supportStaffInfo) {
            $staff_info_id = $supportStaffInfo['staff_info_id'];
        }

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staff_info_id);

        //持卡人
        $staffItem = (new HrStaffItemsModel())->getOneByStaffId($staff_info_id,'BANK_NO_NAME');

        $data                 = [];
        $data['bank_type']    = $staffInfo['bank_type'] ?? 0;
        $data['bank_no']      = $staffInfo['bank_no'] ?? '';
        $data['bank_no_name'] = $staffItem['value'] ?? '';
        $isUpdate             = false;

        //银行流水
        $annexInfo = HrStaffAnnexInfoModel::findFirst(
            [
                'conditions' => "staff_info_id = :staff_info_id: and type = :type:",
                'bind'       => ['staff_info_id' => $staff_info_id, 'type' => HrStaffAnnexInfoModel::TYPE_BANK_CARD],
            ]
        );

        $data['bank_flow_img'] = $annexInfo->annex_path_front ?? '';

        //格式化url 返回baseName
        $dbBankFlowImg = trim(getBaseName($data['bank_flow_img']));
        $inBankFlowImg = getBaseName($params['bank_flow_img']);


        if ((intval($data['bank_type']) != intval($params['bank_type']))
            || (trim($data['bank_no']) != $params['bank_no'])
            || (trim($data['bank_no_name']) != $params['bank_no_name'])
            || ($dbBankFlowImg != $inBankFlowImg)
        ) {
            //银行卡信息
            $attributes['bank_no']       = $params['bank_no'];
            $attributes['bank_no_name']  = $params['bank_no_name'];
            $attributes['bank_type']     = $params['bank_type'];
            $attributes['staff_info_id'] = $staff_info_id;
            $hr_rpc                      = (new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang));
            $hr_rpc->setParams($attributes);
            $result = $hr_rpc->execute();

            if ($result['result']['code'] != 1) {
                throw new BusinessException(implode('', $result['result']['msg']));
            }

            $isUpdate = true;
        }

        $db = BackyardBaseModel::beginTransaction($this);

        try {
            if ($isUpdate) {
                $annexInfo = HrStaffAnnexInfoModel::findFirst(
                    [
                        'conditions' => "staff_info_id = :staff_info_id: and type = :type:",
                        'bind'       => ['staff_info_id' => $staff_info_id, 'type' => HrStaffAnnexInfoModel::TYPE_BANK_CARD],
                    ]
                );

                if (empty($annexInfo)) {
                    $annexInfo                = new HrStaffAnnexInfoModel();
                    $annexInfo->type          = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
                    $annexInfo->staff_info_id = $staff_info_id;
                }

                //待上传
                if (empty($params['bank_flow_img'])) {
                    $annexInfo->audit_state = null;
                } else {
                    $annexInfo->audit_state = HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED;
                }

                $annexInfo->annex_path_front = $params['bank_flow_img'];
                $annexInfo->save();
            }

            $entryInfo = HrEntryModel::findFirst([
                'conditions' => "staff_id = :staff_info_id: ",
                'bind'       => ['staff_info_id' => $staff_info_id],
                'for_update' => true,
            ]);
            //更新入职表
            if (!empty($entryInfo) && !empty($entryInfo->record_entry)) {
                $record_entry_new                 = json_decode($entryInfo->record_entry, true);
                $record_entry_new['bank_no_name'] = $params['bank_no_name'];
                $record_entry_new['bank_type']    = $params['bank_type'];
                $record_entry_new['bank_no']      = $params['bank_no'];
                $entryInfo->record_entry          = json_encode($record_entry_new, JSON_UNESCAPED_UNICODE);
                $entryInfo->updated_at            = new \Phalcon\Db\RawValue('updated_at');
                $entryInfo->save();
                $this->logger->write_log(['record_entry' => $record_entry_new], 'info');
            }

            //更新合同信息
            $attributes['bank_no']       = $params['bank_no'];
            $attributes['bank_no_name']  = $params['bank_no_name'];
            $attributes['bank_type']     = $params['bank_type'];
            $attributes['staff_info_id'] = $staff_info_id;
            $hr_rpc                      = (new ApiClient('winhr_rpc', '', 'updateContractData', $this->lang));
            $hr_rpc->setParams($attributes);
            $result = $hr_rpc->execute();

            if (empty($result['result']['code']) || $result['result']['code'] != 1) {
                throw new BusinessException($result['result']['msg']);
            }

            $db->commit();

        }catch (Exception $e){
            $db->rollback();
            throw  $e;
        }
        return true;
    }

    public function getDeliveryCodeInfo()
    {
        return [];
    }
    /**
     * 获取员工管理的大区
     * @param $staff_info_id
     * @return array
     */
    public function getManageRegionsList($staff_info_id): array
    {
        $manageInfo = SysManageRegionModel::find([
            'conditions' => 'manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 获取员工管理的片区
     * @param $staff_info_id
     * @return array
     */
    public function getManagePiecesList($staff_info_id): array
    {
        $manageInfo = SysManagePieceModel::find([
            'conditions' => 'manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 获取员工管理的网点
     * @param $staff_info_id
     * @return array
     */
    public function getManageStoresList($staff_info_id): array
    {
        $manageInfo = SysStoreModel::find([
            'conditions' => 'manager_id = :manager_id: and state = 1',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    public function getManageSubDepartmentList($department_ids)
    {

    }

    /**
     * 员工是否是大区、片区、网点负责人
     * @param $staff_info_id
     * @param array $region_id
     * @param array $piece_id
     * @param array $store_id
     * @return bool
     */
    public function checkStaffManageInfo($staff_info_id, array $region_id, array $piece_id, array $store_id): bool
    {
        if ($region_id) {
            $is_region_manage = SysManageRegionModel::findFirst([
                'conditions' => "id in ({id:array}) and manager_id = :manager_id:",
                "bind"       => [
                    "manager_id" => $staff_info_id,
                    "id"         => array_values($region_id),
                ],
            ]);
        }
        if ($piece_id) {
            $is_piece_manage = SysManagePieceModel::findFirst([
                'conditions' => "id in ({id:array}) and manager_id = :manager_id:",
                "bind"       => [
                    "manager_id" => $staff_info_id,
                    "id"         => array_values($piece_id),
                ],
            ]);
        }
        if ($store_id) {
            $is_store_manage = SysStoreModel::findFirst(
                [
                    'conditions' => "id in ({id:array}) and manager_id = :manager_id:",
                    "bind"       => [
                        "manager_id" => $staff_info_id,
                        "id"         => array_values($store_id),
                    ],
                ]
            );
        }
        return !empty($is_region_manage) || !empty($is_piece_manage) || !empty($is_store_manage);
    }

    /**
     * 获取管辖范围
     * @param $staff_info_id
     * @param $submitter_id
     * @return mixed
     */
    public function getManagement($staff_info_id, $submitter_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id,
            ss.manager_id as s_manager_id,
            smp.manager_id as p_manager_id,
            smr.manager_id as r_manager_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class,'hsi.sys_store_id = ss.id','ss');
        $builder->leftJoin(SysManageRegionModel::class,'ss.manage_region = smr.id','smr');
        $builder->leftJoin(SysManagePieceModel::class,'smp.id = ss.manage_piece','smp');
        $builder->andWhere('hsi.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        $builder->andWhere('smr.manager_id = :manager_id: or smp.manager_id = :manager_id: or ss.manager_id =:manager_id:', ['manager_id' => $submitter_id]);
        $stafInfo = $builder->getQuery()->execute()->toArray();

        if ($stafInfo) {
            $stafInfo = current($stafInfo);
        }
        return $stafInfo;
    }


    /**
     * 验证是否某天是主播
     * @param $staffInfo
     * @param $date_at
     * @return bool
     * @throws Exception
     */
    public function checkIsLiveJob($staffInfo, $date_at):bool
    {
        if(empty($staffInfo['job_title']) || empty($staffInfo['staff_info_id'])){
            throw new Exception('参数错误 job_title or staff_info_id is empty');
        }

        //主播职位
        $transfer = HrStaffTransferModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
            'bind'       => [
                'staff_id' => $staffInfo['staff_info_id'],
                'date_at'  => $date_at,
            ],
        ]);

        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position', ',');
        if (!empty($transfer)) {
            return in_array($transfer->job_title, $liveJobId);
        }
        return in_array($staffInfo['job_title'], $liveJobId);
    }

    /**
     * 根据证件号获取员工ID
     * @param $identity
     * @return array
     */
    public function getStaffInfoByIdentity($identity): array
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "identity = :identity: and state in ({state:array})  and formal in ({formal:array}) and is_sub_staff = 0",
            'bind'       => [
                'identity' => $identity,
                'state'    => [HrStaffInfoModel::STATE_ON_JOB,HrStaffInfoModel::STATE_SUSPENSION],
                'formal'   => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            ],
            'columns'    => ['staff_info_id', 'name','identity'],
        ]);
        if (!empty($staffInfo)) {
            $info =  $staffInfo->toArray();
            $info['staff_info_id'] = intval($info['staff_info_id']);
            return  $info;
        }
        return [];
    }


    /**
     * 员工数据权限
     * @param $staffId
     * @return array[]
     */
    public function StaffDataPurviewV2($staffId)
    {
        $hr_rpc = (new ApiClient('hr_rpc', '', 'shift-purview-v2', 'zh-CN'));
        $hr_rpc->setParams(['staff_info_id' => $staffId]);
        $result = $hr_rpc->execute();
        $this->logger->info("purview_{$staffId} " . json_encode($result,true));
        if (!empty($result) && $result['code'] == 0) {
            return $result['body'];
        }
        return ['stores'=>[-100],'departments'=>[-100],'flash_home_departments'=>[-100],'staff_ids'=>[-100]];
    }

    /**
     * 指定工号管辖范围
     * @param $staffId
     * @param string $type
     * @return array
     * @throws \Exception
     */
    public function dominDepartmentsAndStores($staffId, $type = HrStaffManageDepartmentModel::TYPE_STAFF)
    {
        $relations = $this->getStaffJurisdiction($staffId, $type);

        $result = [
            "flash_home_departments" => $relations['departments'] ?? [],
            "stores"                 => $relations['stores'] ?? [],
        ];

        //判断是否查询 网点类型 -- 片区-- 大区
        //网点里有-2 就不用查 大区 片区 网点类型了
        if (in_array(SysStoreModel::ALL_STORE_PERMISSION, $relations['stores'])) {
            return $result;
        }

        $regionIds       = $relations['regions'] ?? [];
        $pieceIds        = $relations['pieces'] ?? [];
        $storeCategories = $relations['store_categories'] ?? [];
        $conditions      = $bind = [];
        if (!empty($regionIds)) {
            $conditions[]       = ' manage_region in ({region_ids:array}) ';
            $bind['region_ids'] = $regionIds;
        }
        if (!empty($pieceIds)) {
            $conditions[]      = ' manage_piece in ({piece_ids:array}) ';
            $bind['piece_ids'] = $pieceIds;
        }
        if (!empty($storeCategories)) {
            $conditions[]             = ' category in ({store_categories:array}) ';
            $bind['store_categories'] = $storeCategories;
        }

        if (!empty($conditions) && !empty($bind)) {
            $stores           = SysStoreModel::find([
                'conditions' => implode(' or ', $conditions),
                'bind'       => $bind,
            ])->toArray();
            $result['stores'] = array_values(array_unique(array_merge($result['stores'], array_column($stores, 'id'))));
        }

        return $result;
    }

    /**
     * 给flashlink提供员工数据
     * @param $page_num
     * @return array
     */
    public function getListForFlashLink($page_num): array
    {
        $page_size = 2000;
        return HrStaffInfoModel::find([
            'columns'    => 'staff_info_id as id, name, state, node_department_id as organization_id',
            'conditions' => " formal in (1,4) and sys_store_id = '-1' ",
            'order'      => 'staff_info_id asc',
            'offset'     => ($page_num - 1) * $page_size,
            'limit'      => $page_size,
        ])->toArray();

    }

    /**
     * 验证密码
     * @param $staff_info_id
     * @param $password
     * @return bool
     */
    public function validatePassword($staff_info_id, $password): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }

        $re   = new StaffRepository($this->lang);
        $info = $re->checkoutStaff($staff_info_id);
        if (empty($info)) {
            return false;
        }
        if ($info['state'] != HrStaffInfoModel::STATE_ON_JOB) {
            return false;
        }

        $hash = new PasswordHash(10, false);
        return $hash->checkPassword($password, $info['encrypted_password']);
    }

    /**
     * 是否是lnt 员工
     * @param $staff_info_id
     * @return bool
     */
    public function isLntStaff($staff_info_id): bool
    {
        if (!isCountry('MY')) {
            return false;
        }
        $staffInfo  = $this->getStaffInfo(['staff_info_id' => $staff_info_id], 'contract_company_id');
        $lntCompany = (new SettingEnvServer())->getSetVal('lnt_company_ids', ',');
        return in_array($staffInfo['contract_company_id'], $lntCompany);
    }


    public static function isLntCompany($staff_info_id){
        $ids = (new SettingEnvServer())->getSetValFromCache('lnt_company_ids', ',');
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id,contract_company_id',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        if(!empty($staffInfo) && in_array($staffInfo->contract_company_id, $ids)){
            return true;
        }
        return false;
    }

    public static function isLntCompanyByInfo($staffInfo){
        $ids = (new SettingEnvServer())->getSetValFromCache('lnt_company_ids', ',');
        if(!empty($staffInfo) && in_array($staffInfo['contract_company_id'], $ids)){
            return true;
        }
        return false;
    }

}
