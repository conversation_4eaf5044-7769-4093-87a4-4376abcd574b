<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/1/5
 * Time: 2:36 PM
 */


namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftOperateLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffDefaultRestDayModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingSplitModel;

class WorkdayServer extends BaseServer
{

    /**
     * @param $type
     * @param $data
     * @param array $extend ['before' => 'xxx','after' => 'xxx', 't_key' => 'xxx']
     * @return bool
     * @throws \Exception
     */
    public function addShiftLog($type, $data, $extend = [])
    {
        $insert['staff_info_id'] = $data['staff_info_id'];
        $insert['date_at']       = $data['date_at'];
        $insert['operate_id']    = $data['operate_id'];
        $insert['edit_type']     = $type;
        $insert['remark']        = $extend['t_key'];//翻译key
        if (in_array($type, [
            HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST,
            HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT,
            HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT,
        ])) {
            $insert['remark'] = $extend['before'] . '->' . $extend['after'];//3类型 翻译key 4，5 只有值
        }
        $model = new HrStaffShiftOperateLogModel();
        $model->create($insert);
        return true;
    }

    /**
     * @param $type
     * @param $data ['staffId' => ['operate_id' => xxx, ['dates' => ['date1','date2']]]];
     * @param array $extend ['before' => 'xxx','after' => 'xxx', 't_key' => 'xxx']
     * @return bool
     * @throws \Exception
     */
    public function addShiftLogBatch($type, $data, $extend = [])
    {
        if (empty($data)) {
            return true;
        }

        $insert = [];
        foreach ($data as $staffId => $item) {
            $operateId = $item['operate_id'] ?: 10000;
            $dateList  = $item['dates'] ?? [];

            foreach ($dateList as $date) {
                $row['staff_info_id'] = $staffId;
                $row['date_at']       = $date;
                $row['operate_id']    = $operateId;
                $row['edit_type']     = $type;
                $row['remark']        = $extend['t_key'] ?? '';//翻译key
                if (in_array($type, [
                    HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST,
                    HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT,
                    HrStaffShiftOperateLogModel::EDIT_TYPE_PRESET_SHIFT,
                ])) {
                    $row['remark'] = $extend['before'] . '->' . $extend['after'];//3类型 翻译key 4，5 只有值
                }
                $insert[] = $row;
            }
        }
        $model = new HrStaffShiftOperateLogModel();
        $model->batch_insert($insert);
        return true;
    }


    //到岗确认 加日志
    public function entryAdd($param)
    {
        //没有这个就不加
        if (empty($param['default_rest_day_date'])) {
            return true;
        }
        //加默认休日志 default_rest_day_date ["2"]
        $logData['staff_info_id'] = $param['new_id'];//新生成的工号
        $logData['operate_id']    = $param['staff_id'];//操作人
        $logData['date_at']       = date('Y-m-d');//默认休没有具体日期 取当前操作时间
        $extend['before']         = '';
        $extend['after']          = implode('', $param['default_rest_day_date']);

        $this->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST, $logData, $extend);
        return true;
    }

    //菲律宾 自由轮休 限制打卡弹窗 https://flashexpress.feishu.cn/docx/LRwnd57B2owa0gxkX9KckXXenue
    public function checkWorkdaySetting($staffInfo)
    {
        $t = $this->getTranslation();
        //是否是周1，2，3
        $week = date('w');
        $weekArr = [1, 2, 3];
        if(get_runtime() == 'dev'){
            $weekArr = [1,2,3,4,5];
        }
        if (!in_array($week, $weekArr)) {
            return [];
        }
        //是否符合配置
        $settingEnv = (new SettingEnvServer())->listByCode(['White_List_SetFreeRest', 'SetFreeRest_Position']);
        $settingEnv = array_column($settingEnv, 'set_val', 'code');
        $white      = empty($settingEnv['White_List_SetFreeRest']) ? [] : explode(',',
            $settingEnv['White_List_SetFreeRest']);
        $job        = empty($settingEnv['SetFreeRest_Position']) ? [] : explode(',',
            $settingEnv['SetFreeRest_Position']);
        //不限制打卡 白名单 White_List_SetFreeRest
        if (!empty($white) && in_array($staffInfo['staff_id'], $white)) {
            return [];
        }

        //是否再配置职位范围内 SetFreeRest_Position
        if(empty($job)){
            return [];
        }
        if (!in_array($staffInfo['job_title'], $job)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->join(HrJobTitleModel::class, 'j.id = s.job_title', 'j');
        $builder->andWhere("s.manger = :staff_id:", ['staff_id' => $staffInfo['staff_id']]);
        $builder->andWhere("s.sys_store_id != '-1'");
        $builder->andWhere("s.state = 1 and formal in (1,4) and is_sub_staff = 0");
        $builder->andWhere("s.week_working_day = :week_working_day:",
            ['week_working_day' => HrStaffInfoModel::WEEK_WORKING_DAY_FREE]);
        $builder->columns(
            "s.staff_info_id,s.name,j.job_name, s.sys_store_id as id,s.job_title, s.hire_date"
        );
        $allStaff = $builder->getQuery()->execute()->toArray();
        //下级员工 在职 并且是自由轮休 可能有多个网点
        if (empty($allStaff)) {
            return [];
        }
        $freeSetting = $this->getWorkdaySetting($allStaff);
        $staffIds    = array_column($allStaff, 'staff_info_id');
        //本周一
        $nextMonday = $this->getNextMonday();
        $thisMonday = date('Y-m-d', strtotime("{$nextMonday} -7 day"));

        $weekStart = weekStart();
        $weekEnd   = weekEnd();
        $restDays  = HrStaffWorkDayModel::find([
            'columns'    => 'staff_info_id,date_at',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at >= :weekStart: and date_at <= :weekEnd:',
            'bind'       => ['ids' => $staffIds, 'weekStart' => $weekStart, 'weekEnd' => $weekEnd],
        ])->toArray();
        $staffRest = [];
        if(!empty($restDays)){
            foreach ($restDays as $r){
                $staffRest[$r['staff_info_id']][] = $r['date_at'];
            }
        }

        $msg         = $t->_('free_workday_dialog');
        foreach ($allStaff as $staff) {
            $restCount = empty($staffRest[$staff['staff_info_id']]) ? 0 : count($staffRest[$staff['staff_info_id']]);
            //默认休息日列 自由轮休显示文案
            $staffKey = "{$staff['id']}_{$staff['job_title']}";
            if (empty($freeSetting[$staffKey])) {
                continue;
            }
            //入职日期 大于 等于 本周一
            if (strtotime($staff['hire_date']) >= strtotime($thisMonday)) {
                continue;
            }
            if ($freeSetting[$staffKey]['days_num'] > $restCount) {
                $middleStr[] = "{$staff['name']} ({$staff['staff_info_id']}) {$staff['job_name']} " . $t->_('workday_setting_notice',
                        ['days_num' => $freeSetting[$staffKey]]['days_num']);
            }
        }
        //都符合条件
        if (empty($middleStr)) {
            return [];
        }
        array_unshift($middleStr, $msg);

        $middleStr[] = $t->_('free_workday_dialog2');
        $middleStr = implode("\r\n", $middleStr);
        $return = [
            'business_type'     => 'un_remittance',
            'remittance_detail' => [
                'dialog_status'      => 1,//0不弹，1弹
                'dialog_msg'         => $middleStr,
                'dialog_must_status' => 1,// 0 能跳过 1不能跳过
                'is_ces_tra'         => 0,
                'init_business_type' => '',
                'dialog_data'        => null,//定制数据 弹窗显示用
            ],
        ];

        return $return;
    }

    public function getWorkdaySetting($data)
    {
        $storeIds = array_column($data, 'id');//所有网点id
        $jobs     = array_column($data, 'job_title');//所有职位
        $storeIds = array_values(array_diff($storeIds, ['-1']));
        if(empty($storeIds)){
            return [];
        }
        $today    = date('Y-m-d');
        $builder  = $this->modelsManager->createBuilder();
        $builder->from(['w' => WorkdaySettingModel::class]);
        $builder->join(WorkdaySettingSplitModel::class, 'w.id = s.origin_id', 's');
        $builder->inWhere("w.store_id", $storeIds);
        $builder->inWhere("s.job_title", $jobs);
        $builder->andWhere("w.is_delete = 0");
        $builder->andWhere("w.effect_date <= :today:", ['today' => $today]);
        $builder->columns(
            "concat(w.store_id,'_',s.job_title) as u_key,w.days_num, w.effect_date"
        );
        $builder->orderBy('w.id asc');
        $allSetting = $builder->getQuery()->execute()->toArray();

        $allSetting = empty($allSetting) ? [] : array_column($allSetting, null, 'u_key');
        return $allSetting;
    }

    //自由轮休 根据网点和职位获取对应配置天数 给个人代理用 取最新的一条 不判断生效日期
    public function getFreeLeaveConfig($store_id, $job_title)
    {
        if (empty($store_id) || empty($job_title)) {
            return [];
        }
        if($store_id == enums::HEAD_OFFICE_ID){
            return [];
        }
        //获取数据 取最新一条配置 产品定的
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.origin_id,w.store_id,s.job_title,w.days_num');
        $builder->from(['w' => WorkdaySettingModel::class]);
        $builder->leftJoin(WorkdaySettingSplitModel::class, 'w.id = s.origin_id', 's');
        $builder->andWhere('w.store_id = :store_id:', ['store_id' => $store_id]);
        $builder->andWhere('s.job_title = :job_title:', ['job_title' => $job_title]);
        $builder->andWhere('w.is_delete = 0');
        $builder->orderBy('w.id desc');
        $data = $builder->getQuery()->getSingleResult();
        if (empty($data)) {
            return [];
        }
        return $data->toArray();
    }

    //根据当前时间 或者 参数日期 获取下周一
    public function getNextMonday($date = '')
    {
        // 创建一个 DateTime 对象，表示当前日期
        $currentDate = new \DateTime($date);
        // 获取当前日期的星期几（1 表示星期一，7 表示星期日）
        $currentDayOfWeek = $currentDate->format('N');
        // 计算距离下周一还有多少天
        if ($currentDayOfWeek == 1) {
            // 如果今天是星期一，直接加 7 天
            $daysToAdd = 7;
        } else {
            // 否则计算到下周一的天数
            $daysToAdd = 8 - $currentDayOfWeek;
        }
        // 添加天数，得到下周一的日期
        $nextMonday = $currentDate->modify("+{$daysToAdd} days");
        // 输出下周一的日期
        return $nextMonday->format('Y-m-d');
    }

}