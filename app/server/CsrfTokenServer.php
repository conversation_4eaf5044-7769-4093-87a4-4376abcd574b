<?php
/**
 *CsrfTokenServer.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/12 0012 11:31
 */

namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\Server\BaseServer;

class CsrfTokenServer extends BaseServer
{
    private $csrf_cache_key_timeout;
    private $csrf_cache_key_prefix;

    public function __construct(string $csrfCacheKeyPrefix = 'interior_goods_order_from_csrf_token', int $timeout = 300)
    {
        parent::__construct();
        $this->csrf_cache_key_timeout = $timeout;
        $this->csrf_cache_key_prefix  = $csrfCacheKeyPrefix;
    }

    /**
     * 获取表单唯一标识
     * Created by: Lqz.
     * @return string
     * CreateTime: 2020/8/12 0012 11:43
     */
    public function getCsrfToken()
    {
        $redis        = $this->getDI()->get('redisLib');
        $csrfToken    = uniqid();
        $csrfCacheKey = $this->csrf_cache_key_prefix . $csrfToken;
        $res = $redis->set($csrfCacheKey, 0, $this->csrf_cache_key_timeout);
        if(!$res){
            throw new \LogicException('Set CsrfToken fialed!');
        }
        return $csrfToken;
    }

    /**
    * 相同的数据在一定的时间内只能提交一次
    * @Date: 6/22/22 8:55 PM
    * @author: peak pan
    * @return:
    **/
    public function checkCsrfRepeat(string $csrfCacheKey)
    {
        $redis        = $this->getDI()->get('redisLib');
        $csrfCacheKey = $this->csrf_cache_key_prefix . $csrfCacheKey;
        if ($redis->exists($csrfCacheKey)) {
            return true;
        }
        $res = $redis->set($csrfCacheKey,$csrfCacheKey, $this->csrf_cache_key_timeout);
        if($res){
            return false;
        }
    }


    /**
     * 检测数据是否重复提交
     * Created by: Lqz.
     * @param string $csrfToken
     * @return bool|string[]
     * CreateTime: 2020/8/12 0012 11:43
     */
    public function checkCsrfToken(string $csrfToken)
    {
        $redis        = $this->getDI()->get('redisLib');
        $csrfCacheKey = $this->csrf_cache_key_prefix . $csrfToken;
        if (!$redis->exists($csrfCacheKey)) {
            return [
                'msg' => 'The csrf_token is invalid!'
            ];
        }
        $value = $redis->incr($csrfCacheKey);
        if ($value !== 1) {
            return [
                'msg' => 'The data has been submitted!'
            ];
        }
        return true;
    }

}
