<?php
namespace FlashExpress\bi\App\Enums;

final class RedisEnums
{
    const BY_STAFF_SESSION_ID = 'sys:by:staff_session_id:%s';
    const TABLE_HR_JOB_TITLE = 'hr_job_title';
    const TABLE_SYS_PROVINCE = 'sys_province';
    const TABLE_SYS_STORE = 'sys_store';
    const TABLE_SYS_DEPARTMENT = 'sys_department';

    //员工登录状态验证缓存
    const SYS_STAFF_SESSION_KEY = 'sys:backyard:staff_session_id:%s';

    const DELAY_MESSAGE = 'v133_3del33ay33_message_%s';//延迟完成消息带截止日期的

    /**
     * 翻译缓存以及二级缓存
     */
    const TRANSLATE_KEY = 'backyard_translate_data';
    const TRANSLATE_KEY_INNER = 'backyard_translate_data_inner';
    /**
     * 翻译过期时间
     */
    const TRANSLATE_KEY_EXPIRE = 3600 * 24 * 15;
    const TRANSLATE_KEY_INNER_EXPIRE =  3600 * 24 * 20;

    // 离职资产待审批
    const LEAVE_ASSETS_NOT_APPROVE_NUM = 'leave_assets_not_approve_num_v1';
    //考勤处罚
    const LIST_ATTENDANCE_PENALTY = 'attendance_penalty';
    //延迟队列
    const LIST_ATTENDANCE_PENALTY_DAILY = 'attendance_penalty_daily';
    //公司解约个人代理
    const LIST_COMPANY_TERMINATION_CONTRACT = 'company_termination_contract';
    //bi处罚申诉
    const LIST_BI_PENALTY_DAILY = 'bi_penalty_daily';
    //bi虚假里程
    const LIST_BI_FALSE_MILES_DAILY = 'bi_false_miles_daily';
    // fle注册加盟商
    const LIST_FLE_FRANCHISEES = 'fle_franchisees';
    //马来 离职确认信函
    const LIST_MY_REGION_CONFIRM_PDF = 'my_region_confirm_pdf';
    //马来 签署完合同发送减免short notice pdf
    const LIST_MY_REDUCTION_SHORT_NOTICE_PDF = 'my_reduction_short_notice_pdf';
    //转个人代理到岗通知OA
    const LIST_CHANGE_TO_IC_NOTICE_OA = 'change_to_ic_notice_oa';
    //马来 快速返聘为lnt员工薪资结构
    const LIST_REHIRE_LNT = 'list_rehire_lnt';

    //泰国高级主管同步考勤到虚拟账号
    const LIST_SYNC_ATTENDANCE_TO_VIRTUAL_STAFF = 'sync_attendance_to_virtual_staff';
    /**
     * 外协上班打卡记录所在外协公司
     */
    const SYNC_ATTENDANCE_FOR_COMPANY_NAME_EF = 'sync_attendance_for_company_name_ef';
    //消息已读数统计
    const MESSAGE_READ_CONT = 'message:read_count';
    //答题消息log
    const MESSAGE_QA_LOG = 'message:qa_log';
    /**
     * 上班打卡相关
     */
    const CLOCK_KEY_EXPIRE = 10 * 60;
    const CLOCK_KEY = 'backyard_clock_in';

    //异步同步外协工单管理
    const REDIS_SYNC_OS_ORDER = 'sync_os_order';

    //异步写入日志
    const REDIS_ASYNC_JOB_TRANSFER = 'async_job_transfer';

    //异步回调
    const REDIS_ASYNC_CALLBACK = 'async_callback';

    //是否显示签字提醒
    const REDIS_IS_SHOW_SIGN_REMINDER = 'show_sign_reminder';
}