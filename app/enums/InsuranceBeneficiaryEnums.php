<?php

namespace app\enums;

use FlashExpress\bi\App\Enums\BaseEnums;

class InsuranceBeneficiaryEnums extends BaseEnums
{
    //保险受益人与本人关系 1父亲 2母亲 3配偶 4孩子 5其他亲属关系
    const RELATION_FATHER = 1;//父亲
    const RELATION_MOTHER = 2;//母亲
    const RELATION_SPOUSE = 3;//配偶
    const RELATION_CHILD = 4;//孩子
    const RELATION_OTHER = 5;//其他亲属

    public static $relation_list = [
        self::RELATION_FATHER => 'insurance_beneficiary_relation_1',
        self::RELATION_MOTHER => 'insurance_beneficiary_relation_2',
        self::RELATION_SPOUSE => 'insurance_beneficiary_relation_3',
        self::RELATION_CHILD => 'insurance_beneficiary_relation_4',
        self::RELATION_OTHER => 'insurance_beneficiary_relation_5',
    ];

    const INSURANCE_BENEFICIARY_UNSUBMITTED = 0; //保险受益人信息待提交
    const INSURANCE_BENEFICIARY_SUBMITTED = 1; //保险受益人已提交

    public static function getCodeTxtMap($lang = '', $code = '')
    {
    }
}