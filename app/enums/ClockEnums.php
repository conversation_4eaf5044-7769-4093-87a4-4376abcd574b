<?php

namespace FlashExpress\bi\App\Enums;

use app\enums\LangEnums;

class ClockEnums extends BaseEnums
{
    //拦截类型
    const DISPLAY_TYPE_ALTER = 'alert';
    const DISPLAY_TYPE_PAGE  = 'page';

    //按钮拦截类型 1 关闭  2 跳转  3 调用api
    const BUTTON_TYPE_CLOSE  = 1;
    const BUTTON_TYPE_PAGE   = 2;
    const BUTTON_TYPE_API    = 3;

    const BUSINESS_TYPE_REMITTANCE_DIALOG     = 'remittance_dialog';
    const BUSINESS_TYPE_REMITTANCE_DIALOG_BTN = 'clock_remittance_dialog_btn';
    const BUSINESS_TYPE_AGREEMENT_SIGN_BTN    = 'clock_agreement_sign_btn';
    const BUSINESS_TYPE_AGREEMENT_SIGN_MSG    = 'clock_agreement_sign_msg';
    const BUSINESS_TYPE_AGREEMENT_SIGN_TITLE  = 'clock_agreement_sign_title';

    public static $business_types = [
        self::BUSINESS_TYPE_REMITTANCE_DIALOG => 'remittance_dialog',
    ];

    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        $txt = self::$code ?? '';
        return $txt;
    }
}
