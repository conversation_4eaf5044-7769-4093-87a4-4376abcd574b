<?php

namespace FlashExpress\bi\App\Enums;

use app\enums\LangEnums;

class JobTransferConfirmEnums extends BaseEnums
{
    public const CONFIRM_STATE_INVALID = 0;        //已失效（含历史数据+撤销数据）
    public const CONFIRM_STATE_PENDING_CONFIRM = 1;//待确认
    public const CONFIRM_STATE_CONFIRM_PASS = 2;   //确认通过
    public const CONFIRM_STATE_CONFIRM_REJECT = 3; //确认驳回
    public const CONFIRM_STATE_CONFIRM_CANCEL = 4; //确认撤销
    public const CONFIRM_STATE_OVER_TIME = 5;      //确认超时

    //转岗确认单类型
    const CONFIRM_TYPE_GRADE_PROMOTION = 1; //职级提升
    const CONFIRM_TYPE_ONLY_JOB_TITLE_CHANGED = 2; //仅职位变更
    const CONFIRM_TYPE_OTHER = 3; //其他

    public static function getAllValidConfirmState(): array
    {
       return [
           self::CONFIRM_STATE_PENDING_CONFIRM,
           self::CONFIRM_STATE_CONFIRM_PASS,
           self::CONFIRM_STATE_CONFIRM_REJECT,
           self::CONFIRM_STATE_CONFIRM_CANCEL,
           self::CONFIRM_STATE_OVER_TIME,
       ];
    }

    public static function getCodeTxtMap($lang = '')
    {
        return [
            self::CONFIRM_STATE_PENDING_CONFIRM => 'confirm_state_pending',
            self::CONFIRM_STATE_CONFIRM_PASS    => 'confirm_state_pass',
            self::CONFIRM_STATE_CONFIRM_REJECT  => 'confirm_state_reject',
            self::CONFIRM_STATE_CONFIRM_CANCEL  => 'confirm_state_cancel',
            self::CONFIRM_STATE_OVER_TIME       => 'confirm_state_overtime'
        ];
    }
}