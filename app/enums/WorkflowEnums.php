<?php


namespace FlashExpress\bi\App\Enums;


class WorkflowEnums extends BaseEnums
{

    const WORKFLOW_EXCEPTION_TYPE_APPROVAL = 1; //指定工号审批人离职
    const WORKFLOW_EXCEPTION_TYPE_HANDOVER = 2; //转交指定人员离职
    const WORKFLOW_EXCEPTION_TYPE_CC = 3; //抄送指定人员离职

    const RECREATE_WORKFLOW = 1; //重新创建

    const DEPARTMENT_MANAGER_BELONG_DEPARTMENT = 0;  //所属部门负责人
    const DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_1 = 1; //一级部门负责人
    const DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_2 = 2; //二级部门负责人
    const DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_3 = 3; //三级部门负责人
    const DEPARTMENT_MANAGER_DEPARTMENT_LEVEL_4 = 4; //四级部门负责人
    const DEPARTMENT_MANAGER_BU_LEVEL = 101; //所属BU负责人
    const DEPARTMENT_MANAGER_C_LEVEL = 150; //所属C-level负责人

    const OPTION_YES = 1; //是
    const OPTION_NO = 2; //否

    const WORKFLOW_CREATE_MODEL_NORMAL = 1; //正常创建审批流
    const WORKFLOW_CREATE_MODEL_DELAY_STAGE_1 = 2; //延时创建审批流 一阶段(仅创建apply数据)
    const WORKFLOW_CREATE_MODEL_DELAY_STAGE_2 = 3; //延时创建审批流 二阶段(创建审批流)
    const WORKFLOW_CREATE_MODEL_EDIT = 4; //修改审批

    //延时审批状态
    const WORKFLOW_DELAY_CREATE_STATE_DEFAULT = 0; //非延时审批流
    const WORKFLOW_DELAY_CREATE_STATE_PENDING = 1; //待创建延时审批流
    const WORKFLOW_DELAY_CREATE_STATE_HAS_CREATED = 2; //已创建延时审批流
    const WORKFLOW_DELAY_CREATE_STATE_CANCELED = 3; //撤销创建延时审批流

    /**
     *
     * @param $lang
     * @param string $code
     * @return string
     */
    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        return self::$code ?? '';
    }
}
