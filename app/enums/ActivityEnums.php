<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/30/23
 * Time: 2:11 PM
 */

namespace FlashExpress\bi\App\Enums;


class ActivityEnums extends BaseEnums
{
    const STATE_IS_ACTIVITY = 1;//需要弹窗 今天是活动日或3天内
    const STATE_HAD = 2;//已经弹过
    const STATE_DELAY_ACTIVITY = 3;//延时弹窗 今天是活动日 等打上班卡之后弹
    const STATE_NOT_ACTIVITY = 4;//不需要弹窗 今天不是活动日(记录表为空时候)

    const ACTIVITY_BIRTHDAY = 'birthday';//生日活动
    const ACTIVITY_ANNIVERSARY = 'anniversary';//入职满周年活动
    const ACTIVITY_FASTING = 'fasting';//开斋节
    const ACTIVITY_SONGGAN = 'songgan';//宋干节

    //消息类型对应活动
    public static $categoryToCode = [
        MessageEnums::MESSAGE_CATEGORY_CODE_BIRTHDAY    => self::ACTIVITY_BIRTHDAY,
        MessageEnums::MESSAGE_CATEGORY_CODE_ANNIVERSARY => self::ACTIVITY_ANNIVERSARY,
        MessageEnums::MESSAGE_CATEGORY_CODE_FASTING => self::ACTIVITY_FASTING,
        MessageEnums::MESSAGE_CATEGORY_CODE_SONGGAN => self::ACTIVITY_SONGGAN,
    ];


    const CLICK_PAGE = 1;//首页点击
    const CLICK_DOWNLOAD = 2;//下载点击
    const CLICK_SHARE = 3;//分享点击
    public static $clickType = [
        self::CLICK_PAGE => 'click_page',
        self::CLICK_DOWNLOAD => 'click_download',
        self::CLICK_SHARE => 'click_share',

    ];

    const IMG_WIDTH = 750;
    const IMG_HEIGHT = 800;


    //英文 缩写 一共7周年 翻译key
    public static $enOrders = [
        1 => 'first',
        2 => 'second',
        3 => 'third',
        4 => 'fourth',
        5 => 'fifth',
        6 => 'sixth',
        7 => 'seventh',
        8 => 'eighth',
        9 => 'ninth',
        10 => 'tenth',
    ];


    //对应国家要指定字体 改成这个字体了 Prompt-Medium.ttf
    public static $fontFamily = [
        'en' => 'Prompt Medium',
        'th' => 'Prompt Medium',
        'zh' => 'Noto Sans SC',
        'id' => 'Prompt Medium',
        'vi' => 'Noto Sans',
        'lo' => 'Noto Sans Lao',

    ];

    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        return [];
    }

}