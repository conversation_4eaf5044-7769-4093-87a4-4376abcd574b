<?php

namespace FlashExpress\bi\App\Enums;



use FlashExpress\bi\App\library\enums;

class AuditListEnums extends BaseEnums
{
    //排序
    const LIST_SORT_DESC = 1; // id desc
    const LIST_SORT_ASC = 2;  // id asc
    const LIST_AUDIT_TIME_DESC = 3;                    // 我的审批已处理  audit_time desc
    const LIST_AUDIT_TIME_ASC = 4;                     // 我的审批已处理 audit_time asc

    //日期formatter
    const DATETIME_FORMATTER_DEFAULT = 1;              // 日期Y-m-d H:s:i
    const DATETIME_FORMATTER_TIMESTAMP = 2;            // 时间戳

    //审批类型
    const APPROVAL_TYPE_AT = 1;                        //补卡
    const APPROVAL_TYPE_ICAT = 75;                     //泰国个人代理补卡
    const APPROVAL_TYPE_LE = 2;                        //请假
    const APPROVAL_TYPE_LH = 3;                        //LH(废弃)
    const APPROVAL_TYPE_OVERTIME = 4;                  //加班
    const APPROVAL_TYPE_OVERTIME_UNPAID = 62;          //个人代理类型员工的加班
    const APPROVAL_TYPE_OVERTIME_OS = 77;              //加班（外协）
    const APPROVAL_TYPE_HC = 6;                        //HC
    const APPROVAL_TYPE_WMS = 9;                       //物料
    const APPROVAL_TYPE_BT = 10;                       //出差
    const APPROVAL_TYPE_VEHICLE = 11;                  //车辆里程
    const APPROVAL_TYPE_FLEET = 12;                    //加班车
    const APPROVAL_TYPE_RN = 13;                       //离职
    const APPROVAL_TYPE_OS = 14;                       //外协
    const APPROVAL_TYPE_ASSETS = 16;                   //个人资产
    const APPROVAL_TYPE_REPORT = 17;                   //举报
    const APPROVAL_TYPE_JT = 18;                       //转岗
    const APPROVAL_TYPE_PUBLIC_ASSETS = 19;            //公共资产
    const APPROVAL_TYPE_JT_OA = 20;                    //转岗申请OA渠道
    const APPROVAL_TYPE_FD_NETWORK = 21;               //运营产品-network(废弃)
    const APPROVAL_TYPE_SALARY = 22;                   //薪资审批
    const APPROVAL_TYPE_ATT_BUS = 23;                  //出差打卡
    const APPROVAL_TYPE_HC_BUDGET = 24;                //HC预算
    const APPROVAL_TYPE_FD_SALES_PMD = 25;             //运营产品-sales/Project Managerment(废弃)
    const APPROVAL_TYPE_FD_SHOP = 26;                  //运营产品-shop(废弃)
    const APPROVAL_TYPE_ADJUST_ROLE = 27;              //增减角色审批
    const APPROVAL_TYPE_CLAIMER = 30;                  //网点理赔
    const APPROVAL_TYPE_FUEL = 31;                     //油费补贴
    const APPROVAL_TYPE_YCBT = 32;                     //黄牌项目出差
    const APPROVAL_TYPE_MESSAGE = 33;                  //消息审批
    const APPROVAL_TYPE_PA = 34;                       //kit 处罚申诉
    const APPROVAL_TYPE_OFFER_SIGNATURE = 35;          //offer签字
    const APPROVAL_TYPE_OPR = 36;                      //外协特殊价格
    const APPROVAL_TYPE_GO = 37;                       //外出申请
    const APPROVAL_TYPE_GO_CI = 38;                    //外出打卡审批 go out clock in
    const APPROVAL_TYPE_SAS = 39;                      //网点申请支援 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SASS = 40;                     //员工申请支援网点 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SYSTEM_CS = 41;                //外部审核- 众包
    const APPROVAL_TYPE_ABNORMAL_EXPENSE = 42;         //异常费用
    const APPROVAL_TYPE_ASSET_V2 = 46;                 //OA资产申请
    const APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT = 47; //异常费用-单次运费调整
    const APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST = 48;  //外部审核- 司机黑名单
    const APPROVAL_TYPE_WMS_V2    = 50; //OA耗材申请
    const APPROVAL_TYPE_OUTSOURCING_OT  = 51;          //外协员工加班审批
    const APPROVAL_TYPE_HUB_OS_AT = 52;                //外协员工补卡审批
    const APPROVAL_TYPE_HR_PENALTY_APPEAL = 54;        //HR处罚申诉
    const APPROVAL_TYPE_QUITCLAIM = 56;                //Quitclaim审核申请
    const APPROVAL_TYPE_MILEAGE = 49;  //外部审核- 虚假里程
    const APPROVAL_TYPE_VEHICLE_APPROVAL = 55;         //车辆变更审批
    const APPROVAL_TYPE_RENEW_CONTRACT = 58; //续签合同

    const APPROVAL_TYPE_REINSTATEMENT = 59;
    // 停职审批
    const APPROVAL_TYPE_STOP = 60;
    const APPROVAL_TYPE_CANCEL_CONTRACT = 63;// 个人代理离职
    const APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT = 68;// 公司解约个人代理

    const APPROVAL_HIRE_TYPE_CHANGE = 64;//转型个人代理申请
    const APPROVAL_TYPE_JT_STAGE_TWO = 65; //转岗转入

    // OA 仓库信息变更审批
    const APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS = 66;// 状态变更审批
    const APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE = 67;// 网点变更审批
    const APPROVAL_TYPE_OA_AGENCY_PAYMENT = 71;//代理支付

    const APPROVAL_TYPE_SCHOOL_PLAN_SEND = 78; //培训发布学习计划
    const APPROVAL_TYPE_WORK_HOME   = 81; //居家办公 审批 跟出差打卡一样
    const APPROVAL_TYPE_CS_PAYMENT   = 82; //外部审批 众包结算审批

    // OA审批业务集合
    public static $oa_biz_type_item_by_common_audit = [
        self::APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS,
        self::APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE,
        self::APPROVAL_TYPE_OA_AGENCY_PAYMENT

    ];
    const APPROVAL_TYPE_JT_SPECIAL = 70; //批量特殊转岗


    const APPROVAL_TYPE_REEMPLOYMNET = 61;//重新雇佣申请
    const APPROVAL_TYPE_ADVANCE_FUEL = 69;//预支邮费申请
    const APPROVAL_TYPE_SUSPEND_WORK = 72;//个人代理暂停接单申请
    const APPROVAL_TYPE_IC_RENEWAL = 74;//个人代理续约合同


    const APPROVAL_TYPE_SICK_CERTIFICATE = 73;//泰国病假 材料申请
    const APPROVAL_TYPE_FRANCHISEES = 76;// 注册加盟商审批流
    const APPROVAL_TYPE_FLEET_GENERATE_RECORDS = 79;//汽运-生成流水
    const APPROVAL_TYPE_SUSPENSION = 80;//停职申请


    public static function getCodeTxtMap($lang)
    {
    }


    /**
     * @description 获取审批筛选分组,用于BY列表展示的下拉筛选
     * @return array[]
     */
    public static function getAuditFilterGroup(): array
    {
        return [
            'audit_group_attendance' => [ //考勤
                self::APPROVAL_TYPE_AT,
                self::APPROVAL_TYPE_ICAT,
                self::APPROVAL_TYPE_LE,
                self::APPROVAL_TYPE_OVERTIME,
                self::APPROVAL_TYPE_OVERTIME_OS,
//                self::APPROVAL_TYPE_OVERTIME_UNPAID,
                self::APPROVAL_TYPE_SICK_CERTIFICATE,
            ],
            'audit_group_hr' => [ //人事
                self::APPROVAL_TYPE_HC,
                self::APPROVAL_TYPE_REEMPLOYMNET,
                self::APPROVAL_TYPE_SALARY,
                self::APPROVAL_TYPE_OFFER_SIGNATURE,
                self::APPROVAL_TYPE_RENEW_CONTRACT,
                self::APPROVAL_TYPE_BT,
                self::APPROVAL_TYPE_ATT_BUS,
                self::APPROVAL_TYPE_WORK_HOME,
                self::APPROVAL_TYPE_GO,
                self::APPROVAL_TYPE_GO_CI,
                self::APPROVAL_TYPE_YCBT,
                self::APPROVAL_TYPE_RN,
                self::APPROVAL_TYPE_REPORT,
                self::APPROVAL_TYPE_OS,
                self::APPROVAL_TYPE_JT,
                self::APPROVAL_TYPE_ADJUST_ROLE,
                self::APPROVAL_TYPE_SAS,
                self::APPROVAL_TYPE_SASS,
                self::APPROVAL_TYPE_OPR,
                self::APPROVAL_TYPE_HR_PENALTY_APPEAL,
                self::APPROVAL_TYPE_HUB_OS_AT,
                self::APPROVAL_TYPE_OUTSOURCING_OT,
                self::APPROVAL_TYPE_REINSTATEMENT,
                self::APPROVAL_TYPE_STOP,
                self::APPROVAL_TYPE_CANCEL_CONTRACT,
                self::APPROVAL_HIRE_TYPE_CHANGE,
                self::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
                self::APPROVAL_TYPE_JT_SPECIAL,
                self::APPROVAL_TYPE_SUSPEND_WORK,
                self::APPROVAL_TYPE_IC_RENEWAL,//个人代理续约合同
                self::APPROVAL_TYPE_SUSPENSION,//停职申请
            ],
            'audit_group_network_operation' => [ //网点运营
                self::APPROVAL_TYPE_VEHICLE,
                self::APPROVAL_TYPE_FLEET,
                self::APPROVAL_TYPE_FUEL,
                self::APPROVAL_TYPE_PA,
                self::APPROVAL_TYPE_CLAIMER,
                self::APPROVAL_TYPE_SYSTEM_CS,
                self::APPROVAL_TYPE_ABNORMAL_EXPENSE,
                self::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST,
                self::APPROVAL_TYPE_CS_PAYMENT,
            ],
            'audit_group_logistics' => [ //后勤
                self::APPROVAL_TYPE_WMS,
                self::APPROVAL_TYPE_ASSETS,
                self::APPROVAL_TYPE_PUBLIC_ASSETS,
                self::APPROVAL_TYPE_ASSET_V2,
                self::APPROVAL_TYPE_WMS_V2,
                self::APPROVAL_TYPE_ADVANCE_FUEL,
            ],
            'audit_group_others' => [ //其他
                self::APPROVAL_TYPE_MESSAGE,
                self::APPROVAL_TYPE_QUITCLAIM,
                self::APPROVAL_TYPE_MILEAGE,
                self::APPROVAL_TYPE_FRANCHISEES,
                //self::APPROVAL_TYPE_VEHICLE_APPROVAL, //注释原因FMS审批
            ],
        ];
    }

    /**
     * 获取全部审批类型
     * @return array
     */
    public static function getAllAuditTypes(): array
    {
        $auditTypeList = [];
        $groups = self::getAuditFilterGroup();
        foreach ($groups as $group) {
            $auditTypeList = array_merge($auditTypeList, array_values($group));
        }
        return $auditTypeList;
    }

    /**
     * 获取在BY审批列表展示的审批类型
     * @return array
     */
    public function getAuditType()
    {
        $auditTypeList = [];
        $groups = self::getAuditFilterGroup();
        foreach ($groups as $group) {
            $auditTypeList = array_merge($auditTypeList, array_values($group));
        }
        return $auditTypeList;
    }


    /**
     * @description 获取全量固化审批流类型
     * @return array
     */
    public static function getTotalPersistentWorkflowType(): array
    {
        return [
            self::APPROVAL_TYPE_JT,
            self::APPROVAL_TYPE_JT_OA,
        ];
    }

    /**
     * @description 需要在BY审批列表里，显示超时关闭倒计时的审批类型
     * @return array
     */
    public static function getAuditTypeOfLimitApprovalTime(): array
    {
        return [
            self::APPROVAL_TYPE_AT => 'all',
            self::APPROVAL_TYPE_ICAT => 'all',
            self::APPROVAL_TYPE_LE => 'all',
            self::APPROVAL_TYPE_LH => 'all',
            self::APPROVAL_TYPE_OVERTIME =>'all',
            self::APPROVAL_TYPE_OVERTIME_UNPAID =>'all',
            self::APPROVAL_TYPE_BT => '0',
            self::APPROVAL_TYPE_GO => '0',
            self::APPROVAL_TYPE_HR_PENALTY_APPEAL => 'all',
            self::APPROVAL_TYPE_REINSTATEMENT => 'all',
            self::APPROVAL_TYPE_REPORT => 'all',
            self::APPROVAL_TYPE_SUSPEND_WORK => 'all',
            self::APPROVAL_TYPE_OVERTIME_OS => 'all',
        ];
    }

    /**
     * @description 在创建审批时，获取可以部分对接可视化的审批类型
     * @return array
     */
    public static function getPartialVisualizeApprovalType(): array
    {
        return [
            self::APPROVAL_TYPE_LE,
            self::APPROVAL_TYPE_OVERTIME,
        ];
    }

    /**
     * @description 获取仅对外提供审批服务，业务表不在BY的审批类型
     * @return array
     */
    public static function getAuditTypeOfOnlyProvideService(): array
    {
        return [
            self::APPROVAL_TYPE_FLEET_GENERATE_RECORDS,   //汽运-生成流水
            self::APPROVAL_TYPE_SYSTEM_CS,                //众包审批
            self::APPROVAL_TYPE_ASSET_V2,                 //资产申请
            self::APPROVAL_TYPE_ABNORMAL_EXPENSE,         //异常费用类型
            self::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT, //异常费用-单次运费调整
            self::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST,  //司机黑名单
            self::APPROVAL_TYPE_WMS_V2,                   //耗材申请
            self::APPROVAL_TYPE_MILEAGE,                  //虚假里程
            self::APPROVAL_TYPE_VEHICLE_APPROVAL,         //车辆变更审批
            self::APPROVAL_TYPE_VEHICLE,                  //补里程
            self::APPROVAL_TYPE_STOP,                     //停职审批
            self::APPROVAL_TYPE_WAREHOUSE_CHANGE_STATUS,  //仓库状态变更审批
            self::APPROVAL_TYPE_WAREHOUSE_CHANGE_STORE,   //仓库网点变更审批
            self::APPROVAL_TYPE_OA_AGENCY_PAYMENT,//代理支付
            self::APPROVAL_TYPE_FRANCHISEES,//注册加盟商审批流
            self::APPROVAL_TYPE_SCHOOL_PLAN_SEND,         //培训系统审批
            self::APPROVAL_TYPE_CS_PAYMENT,         //众包结算审批
        ];
    }

    /**
     * @description 待审批短息提醒对应的审批类型
     * @return array
     */
    public static function getPendingMessageTypeList(): array
    {
        return [
            self::APPROVAL_TYPE_AT,
            self::APPROVAL_TYPE_ICAT,
            self::APPROVAL_TYPE_LE,
            self::APPROVAL_TYPE_OVERTIME,
            self::APPROVAL_TYPE_HC,
            self::APPROVAL_TYPE_WMS,
            self::APPROVAL_TYPE_BT,
            self::APPROVAL_TYPE_VEHICLE,
            self::APPROVAL_TYPE_FLEET,
            self::APPROVAL_TYPE_RN,
            self::APPROVAL_TYPE_OS,
            self::APPROVAL_TYPE_ASSETS,
            self::APPROVAL_TYPE_REPORT,
            self::APPROVAL_TYPE_JT,
            self::APPROVAL_TYPE_PUBLIC_ASSETS,
            self::APPROVAL_TYPE_SALARY,
            self::APPROVAL_TYPE_ATT_BUS,
            self::APPROVAL_TYPE_ADJUST_ROLE,
            self::APPROVAL_TYPE_CLAIMER,
            self::APPROVAL_TYPE_FUEL,
            self::APPROVAL_TYPE_YCBT,
            self::APPROVAL_TYPE_MESSAGE,
            self::APPROVAL_TYPE_PA,
            self::APPROVAL_TYPE_OFFER_SIGNATURE,
            self::APPROVAL_TYPE_OPR,
            self::APPROVAL_TYPE_GO,
            self::APPROVAL_TYPE_GO_CI,
            self::APPROVAL_TYPE_SAS,
            self::APPROVAL_TYPE_SASS,
            self::APPROVAL_TYPE_SYSTEM_CS,
            self::APPROVAL_TYPE_ABNORMAL_EXPENSE,
            self::APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST,
            self::APPROVAL_TYPE_HUB_OS_AT,
            self::APPROVAL_TYPE_STOP,
            self::APPROVAL_TYPE_JT_SPECIAL,
            self::APPROVAL_TYPE_SUSPEND_WORK,
        ];
    }

    //审批列表 特殊审批 图标
    public static $subTitleTips = [
        enums::WF_ACTION_CREATE_CANCEL => 'cancel_tips',
        enums::WF_ACTION_EDIT_RECREATE => 'edit_tips',
    ];


    /**
     * @description 不需要push 的审批类型
     * @return int[]
     */
    public static function getAuditTypeWithOutPush()
    {
        return [
            AuditListEnums::APPROVAL_TYPE_JT_OA,
            AuditListEnums::APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT,
            AuditListEnums::APPROVAL_TYPE_FLEET_GENERATE_RECORDS,
        ];
    }

    /**
     * @description 发送by push的审批类型
     * @return int[]
     */
    public static function getAuditTypePushJumpToOa()
    {
        return array_merge(self::$oa_biz_type_item_by_common_audit, [AuditListEnums::APPROVAL_TYPE_HC_BUDGET]);
    }
}
