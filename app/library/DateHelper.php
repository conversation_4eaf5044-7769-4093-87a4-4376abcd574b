<?php

/**
 * 分页页码
 */

namespace FlashExpress\bi\App\library;

use DateTime;
use DateTimeZone;

class DateHelper {



    public static function DateRange($BeginDate,$EndDate,$format='Y-m-d')
    {
        $DateRange = [];
        while ($BeginDate<=$EndDate)
        {
            $DateRange[] = date($format,$BeginDate);
            $BeginDate = strtotime('+1 day',$BeginDate);
        }
        return $DateRange;
    }
    //这个星期的星期一
// @$timestamp ，某个星期的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function this_monday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $monday_date = date('Y-m-d', $timestamp-86400*date('w',$timestamp)+(date('w',$timestamp)>0?86400:-/*6*86400*/518400));
            if($is_return_timestamp){
                $cache[$id] = strtotime($monday_date);
            }else{
                $cache[$id] = $monday_date;
            }
        }
        return $cache[$id];

    }

//这个星期的星期天
// @$timestamp ，某个星期的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function this_sunday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $sunday = self::this_monday($timestamp) + /*6*86400*/518400;
            if($is_return_timestamp){
                $cache[$id] = $sunday;
            }else{
                $cache[$id] = date('Y-m-d',$sunday);
            }
        }
        return $cache[$id];
    }

//上周一
// @$timestamp ，某个星期的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last_monday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $thismonday = self::this_monday($timestamp) - /*7*86400*/604800;
            if($is_return_timestamp){
                $cache[$id] = $thismonday;
            }else{
                $cache[$id] = date('Y-m-d',$thismonday);
            }
        }
        return $cache[$id];
    }

//上个星期天
// @$timestamp ，某个星期的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last_sunday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $thissunday = self::this_sunday($timestamp) - /*7*86400*/604800;
            if($is_return_timestamp){
                $cache[$id] = $thissunday;
            }else{
                $cache[$id] = date('Y-m-d',$thissunday);
            }
        }
        return $cache[$id];

    }

//这个月的第一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式

    public static function month_firstday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp),1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

//这个月的最后一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式

    public static function month_lastday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $lastday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp),date('t',$timestamp),date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($lastday);
            }else{
                $cache[$id] = $lastday;
            }
        }
        return $cache[$id];
    }

//上个月的第一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function lastmonth_firstday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-1,1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

//上个月的最后一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function lastmonth_lastday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $lastday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-1, date('t',self::lastmonth_firstday($timestamp)),date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($lastday);
            }else{
                $cache[$id] =  $lastday;
            }
        }

        return $cache[$id];
    }

    //上3个月的第一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last3month_firstday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-3,1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

//上3个月的最后一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last3month_lastday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $lastday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-3, date('t',self::last3month_firstday($timestamp)),date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($lastday);
            }else{
                $cache[$id] =  $lastday;
            }
        }
        return $cache[$id];
    }
    //上2个月的第一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last2month_firstday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-2,1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

//上2个月的最后一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last2month_lastday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $lastday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-2, date('t',self::last2month_firstday($timestamp)),date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($lastday);
            }else{
                $cache[$id] =  $lastday;
            }
        }
        return $cache[$id];
    }

    //上6个月的第一天
// @$timestamp ，某个月的某一个时间戳，默认为当前时间
// @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last6month_firstday($timestamp = 0, $is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-6,1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }




    //上x个月的第一天 20181102
    // @$timestamp ，某个月的某一个时间戳，默认为当前时间
    // @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    // $x ,上x个月的x，如：1
    public static function last_x_month_firstday($timestamp = 0, $is_return_timestamp=true,$x=1){
        static $cache ;
        $id = $timestamp.$is_return_timestamp.$x;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $firstday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-$x,1,date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($firstday);
            }else{
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

    //上x个月的最后一天 20181102
    // @$timestamp ，某个月的某一个时间戳，默认为当前时间
    // @is_return_timestamp ,是否返回时间戳，否则返回时间格式
    public static function last_x_month_lastday($timestamp = 0, $is_return_timestamp=true,$x=1){
        static $cache ;
        $id = $timestamp.$is_return_timestamp.$x;
        if(!isset($cache[$id])){
            if(!$timestamp) $timestamp = time();
            $lastday = date('Y-m-d', mktime(0,0,0,date('m',$timestamp)-$x, date('t',self::last_x_month_firstday($timestamp,true,$x)),date('Y',$timestamp)));
            if($is_return_timestamp){
                $cache[$id] = strtotime($lastday);
            }else{
                $cache[$id] =  $lastday;
            }
        }
        return $cache[$id];
    }


    /**
     * 秒转小时与分钟
     * @param $seconds
     * @return int[]
     */
    public static function dealSecondsToHoursAndMinutes($seconds)
    {
        $result = [
            'hours'=>0,
            'minutes'=>0,
        ];
        if ($seconds >= 3600) {
            $result['hours'] = floor($seconds/3600);
            $seconds = $seconds%3600;
        }
        if ($seconds >= 60) {
            $result['minutes'] = floor($seconds/60);
        }
        return $result;
    }
    
    
    /* 根据年月日计算年龄
    * @param $birth
    * @return mixed
    */
    public static function howOld($birth) {
        [$birthYear, $birthMonth, $birthDay] = explode('-', $birth);
        [$currentYear, $currentMonth, $currentDay] = explode('-', date('Y-m-d'));
        $age = $currentYear - $birthYear - 1;
        if($currentMonth > $birthMonth || $currentMonth == $birthMonth && $currentDay >= $birthDay)

            $age++;
        return$age;

    }

    /**
     * 获取指定日期内的指定周几
     * @param $start_date
     * @param $end_date
     * @param array $N 1（表示星期一）到 7（表示星期天）
     * @return array
     */
    public static function getRangeDateFixedDate($start_date,$end_date,$N = []): array
    {
        $data = [];
        while($start_date <= $end_date){
            if(in_array(date('N',$start_date),$N)){
                $data[] = date('Y-m-d',$start_date);
            }
            $start_date +=3600*24;
        }
        return $data;
    }

    /**
     * @param null $dt
     * @param string $format
     * @return string
     * @throws Exception
     */
    public static function localToUtc($dt=null,$format='Y-m-d H:i:s')
    {
        $tz = date_default_timezone_get();
        if (is_null($dt)){
            $dt = 'now';
        }
        $dt = new DateTime($dt, new DateTimeZone($tz));
        $dt->setTimezone(new DateTimeZone('UTC'));
        return $dt->format($format);
    }

    /**
     * @param null $dt
     * @param string $format
     * @return string
     * @throws Exception
     */
    public static function utcToLocal($dt=null,$format='Y-m-d H:i:s')
    {
        $tz = date_default_timezone_get();
        if (is_null($dt)){
            $dt = 'now';
        }
        $dt = new DateTime($dt, new DateTimeZone('UTC'));
        $dt->setTimezone(new DateTimeZone($tz));
        return $dt->format($format);
    }

    /**
     * 计算间隔天数
     * @param $begin_date
     * @param $end_date
     * @return false|int
     * @throws \Exception
     */
    public static function calcDays($begin_date, $end_date)
    {
        // 创建两个 DateTime 对象
        $date1 = new DateTime($begin_date);
        $date2 = new DateTime($end_date);

        // 计算日期差异
        $interval = $date1->diff($date2);

        return $interval->days;
    }
}