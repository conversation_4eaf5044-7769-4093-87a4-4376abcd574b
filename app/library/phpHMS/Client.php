<?php
namespace phpHMS;

use GuzzleHttp;

/**
 * <AUTHOR>
 */
class Client implements ClientInterface
{
    const DEFAULT_GOOGLE_API_URL = 'https://fcm.googleapis.com/fcm/send',
        DEFAULT_HUAWEI_API_URL = 'https://push-api.cloud.huawei.com/v1/%s/messages:send';

    /** @var string */
    private $apiKey;

    /** @var string */
    private $proxyApiUrl;

    /** @var GuzzleHttp\ClientInterface */
    private $guzzleClient;

    /** @var string */
    private $apiId;

    /** @var string */
    private $deviceType;

    public function injectHttpClient(GuzzleHttp\ClientInterface $client)
    {
        $this->guzzleClient = $client;
    }

    /**
     * set your client device type here
     *
     * @throws \UnexpectedValueException
     * @param integer $deviceType 1:google, 2:HuaWei
     * @return $this
     */
    public function setDeviceType($deviceType)
    {
        if (!in_array($deviceType,[1,2]))
            throw new \UnexpectedValueException('currently phpHMS only supports 1:google or 2:HuaWei device type');

        $this->deviceType = $deviceType;
        return $this;
    }

    /**
     * add your server api key here
     *
     * @param string $apiKey
     *
     * @return \phpHMS\Client
     */
    public function setApiKey($apiKey)
    {
        $this->apiKey = $apiKey;
        return $this;
    }

    /**
     * add your server api key here
     *
     * @param integer $apiId
     *
     * @return \phpHMS\Client
     */
    public function setApiId($apiId)
    {
        $this->apiId = $apiId;
        return $this;
    }

    /**
     * people can overwrite the api url with a proxy server url of their own
     *
     * @param string $url
     *
     * @return \phpHMS\Client
     */
    public function setProxyApiUrl($url)
    {
        $this->proxyApiUrl = $url;
        return $this;
    }

    /**
     * sends your notification to the google servers and returns a guzzle repsonse object
     * containing their answer.
     *
     * @param Message $message
     *
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\RequestException
     */
    public function send(Message $message)
    {
        return $this->guzzleClient->post(
            $this->getApiUrl(),
            [
                'headers' => [
                    'Authorization' => sprintf($this->deviceType === 2 ? 'Bearer %s' : 'key=%s', $this->apiKey),
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($message),
            ]
        );
    }

    private function getApiUrl()
    {
        return isset($this->proxyApiUrl)
            ? $this->proxyApiUrl
            : ($this->deviceType === 2 ? sprintf(self::DEFAULT_HUAWEI_API_URL,$this->apiId) : self::DEFAULT_GOOGLE_API_URL);
    }
}
