<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 3/9/22
 * Time: 3:02 PM
 */


namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Controllers\MilesController as GlobalController;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\MobileHelper;
use FlashExpress\bi\App\Server\MilesServer;
use FlashExpress\bi\App\Server\SettingEnvServer;


class MilesController extends GlobalController{

    /**
     * 里程上报 创建
     * 参数：
     *
     * val mileage_date: String, val kilometres: Long, val mileage_record_type: Int, val mileage_images: List<ImageKey>, val change_car: Int
     */
    public function createAction(){
        $staff_info_id = $this->userinfo['id'];
        $param = $this->paramIn;
        //增加拦截验证
        $setVal = (new SettingEnvServer())->getSetVal('punch_out_switch');
        $server = new MilesServer($this->lang, $this->timezone);
        if(!empty($setVal) && $param['mileage_record_type'] == $server::MILES_END_TYPE){
            $check = $this->mile_check_punch_out($param);
            if(!empty($check) && $check['code'] == '-3'){
                //里程 fbi 那边返回-3 了 客户端区分不开
                $check['code'] = '-4';
                return $this->jsonReturn($check);
            }
        }

        $this->logger->write_log("miles_report {$staff_info_id} create request ".json_encode($param,JSON_UNESCAPED_UNICODE), 'info');
        $param['user_info'] = $this->userinfo;

        $ac = new ApiClient('ard_api', '', 'mileage.add_mileage',$this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("miles_report {$staff_info_id} create request ".json_encode($param,JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');

        if(!empty($ac_result) && isset($ac_result['result'])){
            return $this->ajax_fle_return($ac_result['result']['msg'], $ac_result['result']['code'], $ac_result['result']['data']);
        }

        return $this->ajax_fle_return('', 0, null);
    }


    /**
     * 获取里程信息
     * GET请求
    参数：
    mileageDate 	打卡时间 字符串格式
     */
    public function get_infoAction(){

        $param = $this->request->get();
        $param['date'] = $this->request->get('mileageDate');
        $param['staff_id'] = $this->userinfo['id'];
        $this->logger->write_log("miles_report {$param['staff_id']} get_info request ".json_encode($param,JSON_UNESCAPED_UNICODE), 'info');
        $param['user_info'] = $this->userinfo;

        $ac = new ApiClient('ard_api', '', 'mileage.get_info',$this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("staff {$param['staff_id']} get_info request ".json_encode($param,JSON_UNESCAPED_UNICODE)." res ".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');

        if(isset($ac_result['result']['code'])){
            return $this->ajax_fle_return($ac_result['result']['msg'], $ac_result['result']['code'], $ac_result['result']['data']);
        }
        
        return $this->ajax_fle_return('error', 0, null);
    }


    //判断版本号 旧版本 走原来逻辑 不中转  后来fbi 那边说做兼容 先放着
    protected function route_flag(){

        $a = MobileHelper::getUserAgent();
        var_dump($a);exit;
    }


    // 新增接口 上传里程 视频 中转调 fbi
    public function upload_videoAction(){
        $staff_info_id = $this->userinfo['id'];
        $param = $this->paramIn;
        $this->logger->write_log("miles_report {$staff_info_id} upload_video request ".json_encode($param,JSON_UNESCAPED_UNICODE), 'info');
        $param['user_info'] = $this->userinfo;

        $ac = new ApiClient('ard_api', '', 'mileage.upload_video',$this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();
        $this->logger->write_log("miles_report {$staff_info_id} upload_video request ".json_encode($param,JSON_UNESCAPED_UNICODE)." res:".json_encode($ac_result,JSON_UNESCAPED_UNICODE)." ", 'info');

        if(!empty($ac_result) && isset($ac_result['result'])){
            return $this->ajax_fle_return($ac_result['result']['msg'], $ac_result['result']['code'], $ac_result['result']['data']);
        }

        return $this->ajax_fle_return('', 0, null);
    }

    public function check_change_carAction()
    {
        $param             = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['id'];
        $ac = new ApiClient('ard_api', '', 'mileage.check_change_car', $this->lang);
        $ac->setParams($param);
        $ac_result = $ac->execute();

        if (!empty($ac_result) && isset($ac_result['result'])) {
            return $this->ajax_fle_return($ac_result['result']['msg'], $ac_result['result']['code'],
                $ac_result['result']['data']);
        }
        return $this->ajax_fle_return('', 0, null);
    }

}