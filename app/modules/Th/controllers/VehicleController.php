<?php
namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\Th\Server\VehicleServer;
use FlashExpress\bi\App\Enums\VehicleInfoEnums;
use Exception;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;

class VehicleController extends Controllers\ControllerBase
{
    /**
     * @var array
     */


    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 车辆信息，枚举列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleAction()
    {
        $returnArr['data'] = [];

        $staff_info_id = $this->userinfo['id'];
        $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);

        if ($checkStaff['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
            if (empty($supportStaffInfo)){
                $this->jsonReturn($this->checkReturn($returnArr));
            }
            $staff_info_id = $supportStaffInfo['staff_info_id'];
            $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        }

        if(empty($checkStaff) || $checkStaff['formal'] != 1){
            $this->jsonReturn($this->checkReturn($returnArr));
        }

        // 非van/bike职位，无权限访问数据
        if ( array_key_exists($checkStaff['job_title'], VehicleInfoEnums::JOB_TITLE_ITEM) ) {
            $infoArr = (new VehicleServer($this->lang, $this->timezone))->getVehicleInfoS(["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']]);

            //如果车辆信息不存在输出类型(判断废弃，同时返回)
            $enumArr = (new VehicleServer($this->lang, $this->timezone))->enumVehicleS($checkStaff);

            $returnArr['data'] = $infoArr + $enumArr;
        }

        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 创建车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoAction()
    {
        try {

            //[1]入参和验证
            $paramIn = trim_array($this->paramIn);
            $userinfo = $this->userinfo;

            $checkStaff = (new StaffRepository())->checkoutStaffBi($userinfo['id']);

            $this->getDI()->get('logger')->write_log(['paramIn'=>$paramIn,'userinfo'=>$userinfo,'checkStaff'=>$checkStaff], 'info');

            $staff_info_id = $this->userinfo['id'];
            if ($checkStaff['is_sub_staff'] == 1){
                // 子账号需切到主账号 获取信息
                $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
                if (empty($supportStaffInfo)){
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
                }
                $staff_info_id = $supportStaffInfo['staff_info_id'];
                $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
            }

            if(empty($checkStaff) || $checkStaff['formal'] != 1){
                $this->getDI()->get('logger')->write_log('kit_add_vehicle_info : '.json_encode(array_merge($userinfo,$checkStaff), JSON_UNESCAPED_UNICODE), 'notice');
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('staff_vehicle_notice_1')));
            }

            // 职位权限校验: 仅限 VehicleInfoEnums::JOB_TITLE_ITEM 列出的职位可操作
            if ( !array_key_exists($checkStaff['job_title'], VehicleInfoEnums::JOB_TITLE_ITEM) ) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
            }

            if (!is_array($paramIn['driving_licence_img_item']) && !empty($paramIn['driving_licence_img_item'])) {
                $paramIn['driving_licence_img_item'] = json_decode($paramIn['driving_licence_img_item']);
            }

            // 公共验证字段
            $validations = [
                'vehicle_source' => 'Required|IntIn:1,2,3|>>>:vehicle_source' . $this->getTranslation()->_('miss_args'),
                'vehicle_start_date' => 'IfIntEq:vehicle_source,2|Required|Date|>>>:vehicle_start_date' . $this->getTranslation()->_('miss_args'),
                //"plate_number" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7130'),
                "engine_number" => "Required|StrLenGeLe:10,17|>>>:" . $this->getTranslation()->_('vehicle_info_0002'),
                'license_location' => 'Required|StrLenGeLe:1,512|>>>:license_location' . $this->getTranslation()->_('miss_args'),
                "registration_certificate_img" => "Required|StrLenGeLe:1,255|>>>:registration_certificate_img" . $this->getTranslation()->_('miss_args'),
                "vehicle_img" => "Required|StrLenGeLe:1,200|>>>:" . $this->getTranslation()->_('7134'),
                "insurance_policy_number" => "Required|StrLenGeLe:1,50|>>>:insurance_policy_number" . $this->getTranslation()->_('miss_args'),
                "insurance_start_date" => "Required|Date|>>>:insurance_start_date" . $this->getTranslation()->_('miss_args'),
                "insurance_end_date" => "Required|Date|>>>:insurance_end_date" . $this->getTranslation()->_('miss_args'),
                "vehicle_tax_expiration_date" => "Required|Date|>>>:vehicle_tax_expiration_date" . $this->getTranslation()->_('miss_args'),
                "vehicle_tax_certificate_img" => "Required|StrLenGeLe:1,255|>>>:vehicle_tax_certificate_img" . $this->getTranslation()->_('miss_args'),
                "driver_license_type" => "Required|IntIn:1,2,3,4,5,6,7,8,9,10,11,12,13,14,100|>>>:driver_license_type" . $this->getTranslation()->_('miss_args'),
                "driver_license_type_other_text" => "IfIntEq:driver_license_type,100|Required|StrLenGeLe:1,200|>>>:driver_license_type_other_text" . $this->getTranslation()->_('miss_args'),
                "driver_license_number" => "Required|StrLenGeLe:1,128|>>>:driver_license_number" . $this->getTranslation()->_('miss_args'),
                "driver_license_start_date" => "Required|Date|>>>:driver_license_start_date" . $this->getTranslation()->_('miss_args'),
                "driver_license_end_date" => "Required|Date|>>>:driver_license_end_date" . $this->getTranslation()->_('miss_args'),
                "driving_licence_img_item" => "Required|ArrLen:2|>>>:" . $this->getTranslation()->_('vehicle_info_0008'),
            ];

            $config  = UC('vehicleInfo');;
            // van职位 特有字段验证
            if (in_array($checkStaff['job_title'],VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
                $vehicle_size = implode(',',array_keys(VehicleServer::getVehicleSize(false)));
                $other_validations = [
                    "vehicle_brand"      => "Required|IntIn:" . implode(',', array_column($config['vehicle_brand'],
                            'value')) . "|>>>:vehicle_brand" . $this->getTranslation()->_('miss_args'),
                    "vehicle_brand_text" => "IfIntEq:vehicle_brand,8|Required|StrLenGeLe:1,200|>>>:vehicle_brand_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model"      => "Required|Int|>>>:vehicle_model" . $this->getTranslation()->_('miss_args'),
                    "vehicle_model_text" => "IfIntEq:vehicle_model,100|Required|StrLenGeLe:1,200|>>>:vehicle_model_text" . $this->getTranslation()->_('miss_args'),
                    "vehicle_size"       => "Required|IntIn:$vehicle_size|>>>:vehicle_size" . $this->getTranslation()->_('miss_args'),
                    "buy_date"           => "Required|Date|>>>:buy_date" . $this->getTranslation()->_('miss_args'),

                ];
                $about_oil_card_validations = [
                    "oil_type" => "Required|IntIn:".implode(',',array_keys($config['oil_type']))."|>>>:oil_type" . $this->getTranslation()->_('miss_args'),
                    "oil_company" => "IntIn:0,1,2,3|>>>:oil_company" . $this->getTranslation()->_('miss_args'),   //油卡企业
                    'oil_img' => 'StrLenGeLe:0,255|>>>:oil_img' . $this->getTranslation()->_('error_message'),  //油卡图片
                    "oil_number" => "IfIntNe:oil_company,0|Required|Regexp:/^\d{10,20}$/|>>>:" . $this->getTranslation()->_('fuel_manage_is_oil_number'),   //油卡号
                ];

                if ($checkStaff['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID || $checkStaff['job_title'] ==  VehicleInfoEnums::JOB_EV_COURIER_TITLE_ID) {
                    $about_oil_card_validations = [];
                }

                $validations = array_merge($validations, $other_validations,$about_oil_card_validations);
            }

            $this->validateCheck($paramIn, $validations);

//            if (empty($paramIn['plate_number']) ||
//                (!preg_match("/^(\d?)([กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ]{2})([0-9]{1,4})$/u",$paramIn['plate_number']) &&
//                !preg_match("/^([0-9]{2})-([0-9]{4})$/u",$paramIn['plate_number']))) {
//                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7130')));
//            }

            // 验证车牌号码的规则：泰文辅音+数字 共计7位
            if (!preg_match("/^[กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ0-9-]{1,7}$/u",$paramIn['plate_number'])) {
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('plate_number_th_error')));
            }

            //购买日期验证
            if ($paramIn['buy_date'] > date("Y-m-d")) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('7131')));
            }

            if ($paramIn['insurance_end_date'] < $paramIn['insurance_start_date']) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0004')));
            }

            if ($paramIn['driver_license_end_date'] < $paramIn['driver_license_start_date']) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0005')));
            }

            $returnArr = (new VehicleServer($this->lang, $this->timezone))->addVehicleInfoS($paramIn, ["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']],$this->userinfo['id']);

            $this->getDI()->get('logger')->write_log('kit_add_vehicle_info, 返回结果: '.json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');
        } catch (\Exception $e) {
            throw $e;
        }
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


}
