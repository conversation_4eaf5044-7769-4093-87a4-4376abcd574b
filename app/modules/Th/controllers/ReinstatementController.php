<?php

namespace FlashExpress\bi\App\Modules\Th\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\Th\Server\ReinstatementServer;

class ReinstatementController extends BaseController
{
    /**
     * 提交复职申请
     * @return void
     */
    public function addRequestAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, [
            'staff_info_id'      => "Required|Int",
            'reason_explanation' => "Required|StrLenGeLe:10,500",
            'attach'             => "ArrLenGeLe:0,5",
            'attach[*]'          => "Url",
        ]);
        $params['user_info'] = $this->userinfo;

        $server = new ReinstatementServer($this->lang, $this->timezone);
        $result = $server->addRequestUseLock($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 审批操作
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;
        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED && (empty($params['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        $returnArr = (new ReinstatementServer($this->lang, $this->timezone))->auditUseLock($params, $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }


    /**
     * 获取停职恢复在职员工工号信息
     * @return void
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function getStaffInfoAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, [
            'staff_info_id' => "Required|Int",
        ]);
        $params['user_info'] = $this->userinfo;

        $server = new ReinstatementServer($this->lang, $this->timezone);
        $result = $server->getStaffInfo($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}