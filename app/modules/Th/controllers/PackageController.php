<?php
namespace FlashExpress\bi\App\Modules\Th\Controllers;
use FlashExpress\bi\App\Enums\PackageEnums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Th\Server\PackageServer;
use FlashExpress\bi\App\Modules\Th\Server\PackageReturnServer;
use Exception;
/**
 * 包材领用控制器
 * Class PackageController
 */
class PackageController extends BaseController
{
    protected $paramIn;
    protected $packageServer;
    protected $packageReturnServer;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->packageServer = new PackageServer($this->lang, $this->timezone);
        $this->packageReturnServer = new PackageReturnServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    /**
     * 获取订单枚举
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47473
     */
    public function getOrderStatusAction()
    {
        try {
            $result = $this->packageServer->getOrderStatus();
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("getOrderStatusAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取申请人申请列表或管理员审批列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47491
     */
    public function orderListAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                'type' => 'IntIn:'.PackageEnums::TYPE_APPLY.','.PackageEnums::TYPE_ADMIN.'|>>>:type'. $this->getTranslation()->_('miss_args'),//类型，1申请人，2审批人
                'status' => 'IntIn:'.PackageEnums::PACKAGE_ORDER_STATUS_SUBMIT.','.PackageEnums::PACKAGE_ORDER_STATUS_CONFIRMED.','.PackageEnums::PACKAGE_ORDER_STATUS_REJECTED.','.PackageEnums::PACKAGE_ORDER_STATUS_CANCEL.'|>>>:status'. $this->getTranslation()->_('miss_args'),//状态
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//页码
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),//每页条数
            ];
            //过滤非必需参数
            $paramIn = $this->packageServer->handleParams($paramIn, ['status','type']);
            //验证
            $this->validateCheck($paramIn, $validations);
            $paramIn['page_num'] = $this->processingDefault($paramIn, 'page_num', 2, PackageEnums::PAGE_NUM);
            $paramIn['page_size'] = $this->processingDefault($paramIn, 'page_size', 2,PackageEnums::PAGE_SIZE);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->packageServer->getOrderList($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("orderListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取启用的包材清单列表
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47401
     */
    public function listAction()
    {
        try {
            $list = $this->packageServer->getPackageCategoryAndSkuList();
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("listAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取申请包材枚举（用途、防重提交）等
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47593
     */
    public function getApplyEnumsAction()
    {
        try {
            $result = $this->packageServer->getApplyEnums();
            return $this->returnJson(ErrCode::SUCCESS,'ok',$result);
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("getApplyEnumsAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 申请包材
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47575
     */
    public function applyAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = [
                "csrf_token" => "Required|Str|>>>:csrf token error",
                'use'=>'Required|IntIn:'.PackageEnums::PACKAGE_ORDER_USE_FREE.','.PackageEnums::PACKAGE_ORDER_USE_SALE.','.PackageEnums::PACKAGE_ORDER_USE_FOLLOW.','.PackageEnums::PACKAGE_ORDER_USE_REPAIR.','.PackageEnums::PACKAGE_ORDER_USE_RETURN.','.PackageEnums::PACKAGE_ORDER_USE_OTHER.'|>>>:use'. $this->getTranslation()->_('miss_args'),//用途
                'use_remark'=>'IfIntEq:use,'.PackageEnums::PACKAGE_ORDER_USE_OTHER.'|Required|Str|StrLenGeLe:10,500|>>>:use_remark'. $this->getTranslation()->_('miss_args'),//用途其他备注
                'use_phone' => 'IfIntEq:use,'.PackageEnums::PACKAGE_ORDER_USE_FREE.'|Required|Str|StrLenGeLe:10,100|>>>:use_phone'. $this->getTranslation()->_('miss_args'),//用途免费发放给客户的客户电话
                'sku_list'=> 'Required|Arr|ArrLenGe:1|>>>:sku list'. $this->getTranslation()->_('miss_args'),//批量申请包材
                'sku_list[*]'    => 'Required|Obj',
                'sku_list[*].sku_id'  => 'Required|IntGe:1|>>>:sku_id'. $this->getTranslation()->_('miss_args'), //skuID
                'sku_list[*].apply_num'=> 'Required|IntGeLe:1,9999|>>>:apply_num'. $this->getTranslation()->_('miss_args'), //申请数量
            ];
            //验证
            $this->validateCheck($paramIn, $validations);
            $data = $this->packageServer->setLockConf(3)->applyUseLock($paramIn, $this->userinfo);
            $this->jsonReturn($this->checkReturn(array('data' => $data)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }   catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("applyAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 获取申请人申请包材或管理员审批包材详情
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47599
     */
    public function detailAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageServer->getValidateOrderCode();
            $validations = [
                'type' => 'IntIn:'.PackageEnums::TYPE_APPLY.','.PackageEnums::TYPE_ADMIN.'|>>>:type'. $this->getTranslation()->_('miss_args')//类型，1申请人，2审批人
            ];
            //过滤非必需
            $paramIn = $this->packageServer->handleParams($paramIn, ['type']);
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->packageServer->getDetail($paramIn);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("detailAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 包材申请撤回
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47617
     */
    public function applyCancelAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageServer->getValidateOrderCode();
            //验证
            $this->validateCheck($paramIn, $validations_order_code);
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $result = $this->packageServer->applyCancel($paramIn);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("applyCancelAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 扫一扫
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47653
     */
    public function scanAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageServer->getValidateOrderCode();
            //验证
            $this->validateCheck($paramIn, $validations_order_code);

            if(substr($paramIn['order_code'],0,2)==PackageEnums::PACKAGE_RETURN_ORDER_PREFIX){
                $result = $this->packageReturnServer->scanCode($paramIn, $this->userinfo);
            }else {
                $result = $this->packageServer->scanCode($paramIn, $this->userinfo);
            }

            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("scanAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 管理员确认详情页
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47605
     */
    public function confirmDetailAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations = $this->packageServer->getValidateOrderCode();
            //验证
            $this->validateCheck($paramIn, $validations);
            $list = $this->packageServer->getConfirmDetail($paramIn, $this->userinfo);
            $this->jsonReturn($this->checkReturn(array('data' => $list)));
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("confirmDetailAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 管理员确认员工申请
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47665
     */
    public function confirmAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageServer->getValidateOrderCode();
            $validations = [
                'sku_list'=> 'Required|Arr|ArrLenGe:1|>>>:sku list'. $this->getTranslation()->_('miss_args'),//批量申请包材
                'sku_list[*]' => 'Required|Obj',
                'sku_list[*].sku_id'  => 'Required|IntGe:1|>>>:sku_id'. $this->getTranslation()->_('miss_args'), //skuID
                'sku_list[*].confirm_num'=> 'Required|IntGe:0|>>>:confirm_num'. $this->getTranslation()->_('miss_args'), //确认数量
            ];
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $result = $this->packageServer->confirm($paramIn, $this->userinfo);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("confirmAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }

    /**
     * 管理员驳回员工申请
     * @api https://yapi.flashexpress.pub/project/93/interface/api/47659
     */
    public function rejectAction()
    {
        try {
            //参数定义
            $paramIn = $this->paramIn;
            //参数校验
            $validations_order_code = $this->packageServer->getValidateOrderCode();
            $validations = [
                'reason' => 'Required|Str|StrLenGeLe:10,500|>>>:reason'. $this->getTranslation()->_('miss_args'),//驳回原因
            ];
            //验证
            $this->validateCheck($paramIn, array_merge($validations_order_code, $validations));
            $result = $this->packageServer->reject($paramIn, $this->userinfo);
            return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("rejectAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}
