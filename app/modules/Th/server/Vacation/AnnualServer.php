<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 9/28/22
 * Time: 4:55 PM
 */

namespace FlashExpress\bi\App\Modules\Th\Server\Vacation;


use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveExtendModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Server\LeaveServer;
use FlashExpress\bi\App\Server\ProbationServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\Vacation\AnnualServer as GlobalServer;


class AnnualServer extends GlobalServer implements LeaveInterface
{

    public $cycleInfo;//对应 入职日期 的申请周期信息

    public $invalidDates = [];//申请区间 超出有效期的 日期list
    public $invalidDays  = 0;//申请区间超出有效期的天数 存在半天 情况 不能count

    public $effectiveDates    = [];//申请区间在有效期内的日期list
    public $effectiveDays     = 0;//在有效期内的 天数
    public $oldEffectiveDates = [];//旧规则 能用22年额度的日期list
    public $oldEffectiveDays  = 0;//能用22年的天数


    //操作扣减额度用
    public $currentApplyDays = 0;//当前周期 申请天数
    public $lastApplyDays    = 0;//上个周期 申请天数
    public $oldApplyDays     = 0;//旧规则 22年 占用的天数


    public $pointDate             = '';//新规则 开始时间 （上线时间）
    public $formatPointDate       = '';//22年额度 失效时间 圆点分割 展示用
    public $invalidDate2022       = '';//22年额度 失效时间 横线分割
    public $formatInvalidDate2022 = '';//22年 失效时间 远点分割 翻译用

    public $isSvc = 0;//是否是 rpc 调用

    public function handleCreate($param)
    {
        //初始化数据
        $this->initData($param);

        //逻辑验证
        $this->businessCheck();

        //format 数据
        $this->dataFormat();

        //保存
        $this->dataSave();

        return $this->auditId;
    }

    public function handleSearch($param)
    {
        //初始化数据
        $this->initSearch($param);

        $this->getOldDate();

        //获取额度
        $this->cycleInfo = $this->get_cycle();
        $this->getLimitDays();


        //整理成 day_sub  day_limit
        return $this->formatLimitDays();
    }


    public function detailList($param)
    {
        //初始化数据
        $this->initSearch($param);

        //获取22年旧规则额度
        $this->getOldDate();

        //有可能是 hcm 调用
        $this->isSvc = empty($param['is_svc']) ? 0 : 1;

        //周期信息
        $this->cycleInfo = $this->get_cycle();
        //额度信息
        $this->getLimitDays();

        //整理成列表 详情信息
        return $this->formatDetail();
    }


    protected function initData($param)
    {
        parent::initData($param);

        //--------定制
        $this->getOldDate();
        //年假周期
        $this->cycleInfo = $this->get_cycle();

        //获取额度
        $this->getLimitDays();

        //根据有效期拆分对应天数和日期
        $this->initSides();
    }


    //针对 22年过渡期配置的 2个日期 新逻辑 开始时间和 22年 额度失效时间
    protected function getOldDate()
    {
        $envModel    = new SettingEnvServer();
        $setting_val = $envModel->getSetVal('point_date');
        if (!empty($setting_val)) {
            $this->pointDate       = $setting_val;//时间节点 新旧交替日 11-01
            $this->invalidDate2022 = date('Y-m-d', strtotime("{$setting_val} +89 day"));//旧规则失效时间 01-29
            $this->formatPointDate = date('Y.m.d', strtotime($setting_val));//圆点 日期 展示用 11.01
            $this->formatInvalidDate2022 = date('Y.m.d', strtotime("{$setting_val} +89 day"));//旧规则失效时间 02-01 翻译用
        }
    }


    public function getLimitDays()
    {
        $return['limit'] = $return['sub'] = 0.0;
        $today           = date('Y-m-d');

        $this->limitDays = $return;
        //没周期
        if (empty($this->cycleInfo)) {
            $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} -1  没有额度", 'info');
            return;
        }

        //没权限
        if (!$this->applyPermission()) {
            $this->isPermission = false;
            $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} -2  没有额度", 'info');
            return;
        }

        //没转正 并且 不是待离职 返回0 泰国 没转正也要展示出来记录
        if (!$this->hireCheck()) {
            $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} -3  没有额度", 'info');
            return;
        }
        //工具 放开离职员工限制
        if(empty($this->paramModel['is_bi']) && empty($this->paramModel['is_svc']) && $this->staffInfo['state'] != 1){
            $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} -4  没有额度", 'info');
            return ;
        }

        //有额度
        $cycles[] = $this->cycleInfo['cycle'];

        //是否获取上周期
        if ($this->cycleInfo['cycle'] > 1 && $today < $this->cycleInfo['invalid_day']) {
            $cycles[] = $this->cycleInfo['cycle'] - 1;
        }

        //定制逻辑 如果在22年额度有效期内 要取22年的额度
        if ($today <= $this->invalidDate2022) {
            $cycles[] = 2022;
        }

        //正好是 周期最后一天的展示用到了下周起的初始化
        $cycle_last_date = date('Y-m-d', strtotime("{$this->cycleInfo['count_day']} -1 day"));
        if($today == $cycle_last_date){
            $cycles[] = $this->cycleInfo['cycle'] + 1;
        }

        $data = StaffLeaveRemainDaysModel::find([
            'columns'    => 'staff_info_id,leave_type,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycles:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycles'     => $cycles,
            ],
        ])->toArray();

        foreach ($data as $remain_info) {
            //当前周期
            if ($remain_info['year'] == $this->cycleInfo['cycle']) {
                $return['sub']   = half_num($remain_info['days']);
                $return['limit'] = half_num($remain_info['freeze_days']);
            }
            //上个周期剩余
            if ($remain_info['year'] == ($this->cycleInfo['cycle'] - 1)) {
                $return['last_limit'] = half_num($remain_info['freeze_days']);
                $return['last_sub']   = half_num($remain_info['days']);
            }
            //旧版 2022年的额度 信息
            if ($remain_info['year'] == 2022) {
                $return['old_sub']   = half_num($remain_info['days']);
                $return['old_limit'] = half_num($remain_info['freeze_days']);
            }

            //存在新周期的情况 当天是周期 最后一天 超额的天数 已经挪到了下周期 要单独处理
            if ($remain_info['year'] == ($this->cycleInfo['cycle'] + 1)) {
                $return['next_sub']   = half_num($remain_info['days']);
                $return['next_limit'] = half_num($remain_info['freeze_days']);
            }
        }

        $this->getDI()->get('logger')->write_log("{$this->staffInfo['staff_info_id']} 额度信息 ".json_encode($return),
            'info');
        $this->limitDays = $return;
    }

    public function businessCheck()
    {
        //parent check
        $this->publicValidate();

        //没权限 返回异常 bi 工具不限制
        if (!$this->applyPermission()) {
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //工具 放开离职员工限制
        if(empty($this->paramModel['is_bi']) && $this->staffInfo['state'] != 1){
            throw new ValidationException($this->getTranslation()->_('2107'));
        }

        //没有额度记录 异常数据验证
        if (empty($this->limitDays) || !isset($this->limitDays['limit'])) {
            $this->logger->write_log("年假额度异常 {$this->staffInfo['staff_info_id']} ".json_encode($this->limitDays),
                'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }

        //没转正 不让申请 验证
        if (!empty($this->leaveProperty->is_forbidden)) {
            if (!$this->hireCheck()) {
                $message = str_replace('leave_type', $this->getTranslation()->_($this->leaveProperty->language_key),
                    $this->getTranslation()->_('probation_limit'));
                throw new ValidationException($message);
            }
            //针对工具 放开这个逻辑限制 电话沟通 石阳
            if (empty($this->paramModel['is_bi']) && !empty($this->staffInfo['formal_at']) && $this->paramModel['leave_start_time'] < $this->staffInfo['formal_at']) {
                $message = $this->getTranslation()->_('probation_before_limit');
                throw new ValidationException($message);
            }
        }

        //没有周期
        if (empty($this->cycleInfo)) {
            throw new ValidationException($this->getTranslation()->_('need hire date'));
        }


        //请明年非有效期假期不能请 转换为 不能申请超出当前周期 的失效日期
        $nextInvalidDate = date('Y-m-d', strtotime("{$this->cycleInfo['invalid_day']} +1 year"));
        if ($nextInvalidDate <= $this->paramModel['leave_end_time']) {
            throw new ValidationException($this->getTranslation()->_('next_invalid_notice'));
        }

        //新需求 have days 新增 预发放额度 并且只能申请 发放日期之后的 才能用预发放额度
        $need_days = $this->thisNum + $this->nextNum;//申请总天数

        //说明跳过休息日之后 用0天
        if (empty($need_days)) {
            throw new ValidationException('day off for the apply date');
        }

        //请假日期必须大于等于当前日期
        if (empty($this->paramModel['is_bi'])) {
            $this->timeValidate(0, 100, '1018');
        }

        //验证额度 够不够
        $today = date('Y-m-d');
        //要用到 22年额度的 情况 过了这个日期的过渡期之后 就走正常逻辑
        if ($today <= $this->invalidDate2022 && $this->limitDays['old_sub'] > 0) {
            $flag = $this->oldCheckDays();
        } else {
            //过渡期之后 正常 额度验证
            $flag = $this->checkDays();
        }

        if ($flag <= 0) {//额度不够
            $this->getDI()->get('logger')->write_log([
                'staff_id' => $this->staffInfo['staff_info_id'],
                'flag'     => $flag,
                '请年假额度不够',
            ], 'info');
            throw new ValidationException($this->getTranslation()->_('leave_limit'));
        }
    }

    //整理数据 数据结构 audit, split, img
    protected function dataFormat()
    {
        //先保存 主表 拿audit id
        $this->saveAudit();

        //拆分表
        $this->formatSplitData();
    }

    //保存 各种相关表 数据
    public function dataSave()
    {
        //保存 申请 上传图片
        $this->saveImgData();
        //保存 拆分表记录
        $this->saveSplitData();
        //额度更新 当前周期 和上个周期
        $this->saveRemainData();
    }

    //撤销 返还额度 操作
    public function returnRemainDays($auditId, $staffInfo,$extend = [])
    {

        $this->staffInfo = $staffInfo;
        $this->cycleInfo = $this->get_cycle();
        //split 对应的 占用的 对应周期的 额度
        $splitInfo = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}");
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo));
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();

            //针对 过渡期 周期最后一天 特殊处理 如果撤销的是已经挪到下周期的额度 需要修改extend表数据
            if($remain->year > $this->cycleInfo['cycle'] && $remain->year < 2000){//当前周期 小于要撤销的额度周期 并且 非自然年
                $this->extendEdit($remain->year,$needBackDays);
            }
        }
    }

    //特殊处理 超额天数 情况很少 过了 22年 2月份 这代码就没用了
    protected function extendEdit($cycle,$needBackDays){
        $extData = StaffLeaveExtendModel::find([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at in ({cycles:array})',
            'bind' => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycles' => [$this->cycleInfo['cycle'],$cycle],
            ]
        ]);
        if(empty($extData->toArray())){
            return;
        }
        //2个周期 都加上对应的天数
        foreach ($extData as $item){
            $item->left_all_days += $needBackDays;
            $item->update();
        }
        return ;
    }


    //申请权限
    protected function applyPermission()
    {
        //新增权限判断
        $flag = $this->leaveServer->leavePermission($this->staffInfo);
        if(!$flag){
            return false;
        }

        //  年假规则适用于：“正式员工”、“月薪制特殊合同工” 其他 类型都不能申请
        if ($this->staffInfo['formal'] != 1 //正式
            || !in_array($this->staffInfo['hire_type'],
                [HrStaffInfoModel::HIRE_TYPE_1, HrStaffInfoModel::HIRE_TYPE_2, HrStaffInfoModel::HIRE_TYPE_3])) {
            return false;
        }
        return true;
    }

    //根据失效日期 把时间区间拆分成多个
    public function initSides()
    {
        $invalidDate = $this->cycleInfo['invalid_day'];//实效日期分割
        $today       = date('Y-m-d');

        foreach ($this->leaveSplitInfo as $v) {
            $add_step = ($v['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5;
            if ($v['date_at'] < $invalidDate) {//可以用上个周期 和当前周期
                $this->effectiveDates[] = $v['date_at'];
                $this->effectiveDays    += $add_step;
            }
            if ($v['date_at'] >= $invalidDate) {//可以用当前周期
                $this->invalidDates[] = $v['date_at'];
                $this->invalidDays    += $add_step;
            }

            //过渡期
            if ($today <= $this->invalidDate2022 && $v['date_at'] <= $this->invalidDate2022) {
                $this->oldEffectiveDates[] = $v['date_at'];
                $this->oldEffectiveDays    += $add_step;
            }
        }
    }

    //验证额度 是否够用方法
    protected function checkDays($subDays = null)
    {
        $need_days  = $this->thisNum + $this->nextNum;//申请总天数
        $start_date = $this->paramModel['leave_start_time'];
        $end_date   = $this->paramModel['leave_end_time'];
        $have_days  = $this->limitDays['sub'];
        if (!is_null($subDays)) {//22年 过渡期用
            $have_days = $subDays;
        }

        if (!isset($this->limitDays['last_limit'])) {//没有上周期额度 直接用当前
            if ($have_days < $need_days) {
                return -1;
            }
            return 1;
        }

        //有 去年额度
        $last_year_days = $this->limitDays['last_sub'] ?? 0;

        //如果在有效期 但是 申请日期不在 有效期不能用 剩余额度
        if ($start_date >= $this->cycleInfo['invalid_day']) {
            if ($have_days < $need_days) {
                throw new ValidationException($this->getTranslation()->_('my_leave_out_invalid'));
            }
            return 1;
        }

        if ($start_date < $this->cycleInfo['invalid_day'] && $end_date >= $this->cycleInfo['invalid_day']) {//在失效期两边
            //取失效期前有几天 与 上个周期剩余 比较 取小 !! 需要排除 ph
            $invalid_sub    = $this->effectiveDays;
            $last_year_days = $invalid_sub > $last_year_days ? $last_year_days : $invalid_sub;//去年剩余 与要申请的可以用去年额度的天数 取最小 去年的额度 超出的日期不能用
            $this->getDI()->get('logger')->write_log([
                'last_days' => $last_year_days,
                'have_days' => $have_days,
                'staff_id'  => $this->staffInfo['staff_info_id'],
            ], 'info');
            $have_days = $have_days + $last_year_days;
            if ($have_days < $need_days) {
                throw new ValidationException($this->getTranslation()->_('my_leave_out_invalid'));
            }
        }
        //都小于 失效期 把去年额度 加上就行了
        if ($end_date < $this->cycleInfo['invalid_day']) {
            $have_days += $last_year_days;
            if ($have_days < $need_days) {
                return -2;
            }
        }

        //总体额度 够用 验证通过
        if ($have_days < $need_days) {
            return -3;
        }
        return 1;
    }


    //22年逻辑验证
    protected function oldCheckDays()
    {
        $have_days = $this->limitDays['sub'];//本次请假 能使用的额度
        //整理文档 https://flashexpress.feishu.cn/docx/PyX9dY7djo60eNxXNKEcAJKdnZd
        if ($this->oldEffectiveDays > 0 && $this->limitDays['old_sub'] > 0) {
            $oldActDays = ($this->oldEffectiveDays > $this->limitDays['old_sub']) ? $this->limitDays['old_sub'] : $this->oldEffectiveDays;
            //如果 能用22年额度天数 等于本次申请天数 用22年总数 防止 当前额度负数 导致不能申请的情况
            if($this->oldEffectiveDays == ($this->thisNum + $this->nextNum)){
                $oldActDays = $this->limitDays['old_sub'];
            }
            $have_days  += $oldActDays;
            return $this->checkDays($have_days);
        }
        return $this->checkDays();
    }


    //获取每个员工的周期 和 失效日期 和 结算日期 90天失效
    public function get_cycle($staffInfo = [], $date_at = '')
    {
        //任务 直接调用
        if (!empty($staffInfo)) {
            $this->staffInfo = $staffInfo;
        }

        if (empty($this->staffInfo['hire_date'])) {
            return '';
        }

        $return = [];

        //入职日期加13月 的1号为结算日 以后每个结算日 加一年 改了 按天算
        $hire_date   = date('Y-m-d', strtotime($this->staffInfo['hire_date']));
        $first_count = date('Y-m-d', strtotime("{$hire_date} +1 year"));
        $today       = date('Y-m-d');
        if (!empty($date_at)) {
            $today = $date_at;
        }

        $return['cycle'] = 1;//默认周期 为1 每满一个周期 +1
        if ($today < $first_count) {//还没 满第一个周期
            $return['count_day']   = $first_count;
            $return['invalid_day'] = date('Y-m-d', strtotime("{$hire_date} +90 day"));
            return $return;
        }
        $count_day = $first_count;
        while ($today >= $count_day) {
            $count_day = date('Y-m-d', strtotime("{$count_day} +1 year"));
            $return['cycle']++;
        }

        //结算日 加 6个月
        $invalid_day = date('Y-m-d', strtotime("{$count_day} -1 year +90 day"));

        $return['count_day']   = $count_day;//结算日
        $return['invalid_day'] = $invalid_day;//结余 失效日

        return $return;
    }


    //by 或者 hcm 显示额度 整理
    protected function formatLimitDays()
    {
        $day_limit = $this->limitDays['limit'];
        $day_sub   = $this->limitDays['sub'];
        $return    = [];

        //假期合度 失效期描述文字
        $showText = '';

        //过渡期 加上22年剩余额度
        if (date('Y-m-d') <= $this->invalidDate2022) {
            $day_sub += $this->limitDays['old_sub'] ?? 0;
            $day_limit += $this->limitDays['old_limit'] ?? 0;

            //如果22年有剩余 固定显示语句提示语
            if (!empty($this->limitDays['old_sub']) && $this->limitDays['old_sub'] > 0) {
                $date = date('Y-m-d',strtotime("{$this->pointDate} +90 day"));
                $showText .= $this->getTranslation()->_('annual_2022_text',
                    ['start_2022' => $this->formatPointDate, 'invalid_2022' => $date]);
            }
        }

        //过渡期 用超额的 挪过来 出现了 下周期
        if(isset($this->limitDays['next_sub'])){
            $day_sub += $this->limitDays['next_sub'];
        }

        //没有上周期的额度
        if (!isset($this->limitDays['last_limit'])) {
            $return['day_limit'] = "{$day_limit}";//总额度
            $return['day_sub']   = "{$day_sub}";//剩余额度 有可能是负数
            $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
            if(!empty($showText)){
                $showText .= $this->getTranslation()->_('annual_end_text');
                $return['show_text'] = $showText;
            }
            return $return;
        }

        //有上周期
        $return['day_sub'] = bcadd($day_sub, $this->limitDays['last_sub'], 1);//分子 今年剩余额度 + 去年剩余额度
        $return['day_limit'] = bcadd($day_limit, $this->limitDays['last_limit'], 1);//分母 今年 + 去年
        //过渡期间 特别短 又走到下个周期了 但是上个周期 的开始时间 在 节点之前
        $end_current = date('Y-m-d', strtotime("{$this->cycleInfo['count_day']} -1 year -1 day"));//上个周期的 结算日前一天
        $invalid_current = date('Y.m.d', strtotime("{$this->cycleInfo['invalid_day']}"));//上个周期失效日期
        //有效期 提醒  如果 额度为0 就不提醒了
        if ($this->limitDays['last_sub'] > 0) {
            $add = $this->cycleInfo['cycle'] - 2;//有上周期额度 必定大于2
            $start_current = date('Y-m-d', strtotime("{$this->staffInfo['hire_date']} +{$add} year"));
            $showText = empty($showText) ? '' : $showText . '<br/>';
            if($start_current < $this->pointDate){
                $end_current = str_replace('-','.',$end_current);
                $showText .= $this->getTranslation()->_('annual_current_text',
                    ['date_between' => "{$this->formatPointDate}-{$end_current}", 'invalid_current' => $invalid_current]);
            }else {//正常周期 的提示语
                $showText .= $this->getTranslation()->_('annual_invalid_text',
                    ['invalid_date' => date('Y.m.d',strtotime($this->cycleInfo['invalid_day']))]);
            }
        }

        if(!empty($showText)){
            $showText .= $this->getTranslation()->_('annual_end_text');
            $return['show_text'] = $showText;
        }

        $this->getDI()->get('logger')->write_log(['formatLimitDays' => $return], 'info');
        return $return;
    }


    //整理 员工 额度 详情信息
    public function formatDetail()
    {
        //没有权限
        if(!$this->isPermission){
            return ['detail_list' => [], 'is_permission' => $this->isPermission];
        }
        $detailData = [];

        //有区间的 都展示出来 只不过 额度 没转正显示0  产品说的
        $hireFlag = $this->hireCheck();
        //当天日期 判断 额度是否失效
        $today = date('Y-m-d');
        $todayPoint = date('Y.m.d');

        $remainHistory = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and (year <= :cycle: or year > 2000)',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycle' => $this->cycleInfo['cycle'],
            ],
            'order'      => 'year desc',
        ])->toArray();


        if (empty($remainHistory)) {
            return ['detail_list' => [], 'is_permission' => $this->isPermission];
        }

        //查询扩展表 非当前周期的 额度显示用到 left_all_days
        $extCycle = array_column($remainHistory,'year');
        $extInfo = StaffLeaveExtendModel::find([
            'column' => 'year_at,left_all_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year_at in ({extCycle:array})',
            'bind' => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'extCycle' => $extCycle,
            ]
        ])->toArray();
        $extInfo = empty($extInfo) ? [] : array_column($extInfo,'left_all_days','year_at');
        //当前周期 最后一天 处理临界情况
        $cycle_last_date = date('Y-m-d', strtotime("{$this->cycleInfo['count_day']} -1 day"));
        $isLastDay = $today == $cycle_last_date;

        //处理 历史额度记录
        foreach ($remainHistory as $h) {
            if ($h['year'] == 2022) {//22年的数据 放到最后面
                //已经失效了 非rpc 不显示
                if(!$this->isSvc && $today > $this->invalidDate2022){
                    continue;
                }

                $pointTime  = $this->pointDate;
                $oldEnd     = date('Y.m.d', strtotime("{$pointTime} -1 day"));
                $betweenStr = '2022.01.01-'.$oldEnd;

                $used_day = half_num($h['leave_days']);
                //如果 固化剩余是负数 总申请额度 要加上 这个负数的绝对值
                if(isset($extInfo[2022]) && $extInfo[2022] < 0){
                    $used_day .= '+' . abs($extInfo[2022]);
                }

                $history2022['date_between'] = $betweenStr;
                $history2022['in_all']       = $hireFlag ? half_num($h['freeze_days']) : '0.0';
                $history2022['used_day']     = $used_day;
                $history2022['left_day']     = $hireFlag ? half_num($h['days']) : '0.0';
                $history2022['actual_left']  = $hireFlag ? $h['days'] : '0.00000';
                $history2022['invalid_date'] = $this->invalidDate2022 >= $today ? $this->formatInvalidDate2022 : $this->getTranslation()->_('hasinvalid');
                $history2022['invalid_flag'] = $hireFlag && $this->invalidDate2022 >= $today ?  false: true;
                $history2022['cycle']        = $h['year'];
            } else {
                //正常过渡期
                $betweenStr          = $this->getBetweenStr($h['year']);
                $invalidDate         = date('Y.m.d',
                    strtotime("{$this->staffInfo['hire_date']} +{$h['year']} year +89 day"));//失效日期 是结束日期 +89天 算上当天是90天
                if(!$this->isSvc && $todayPoint > $invalidDate){
                    continue;
                }

                $used_day = half_num($h['leave_days']);
                $leftDays = half_num($h['days']);
                $actualDays = $h['days'];
                //如果 固化剩余是负数 总申请额度 要加上 这个负数的绝对值 当前周期 不操作加减
                if(isset($extInfo[$h['year']]) && $extInfo[$h['year']] < 0 && ($h['year'] != $this->cycleInfo['cycle'] || $isLastDay)){
                    if($isLastDay){
                        $used_day += abs($extInfo[$h['year']]);
                        $leftDays += $extInfo[$h['year']];
                        $actualDays += $used_day;
                    }else{
                        $used_day .= '+' . abs($extInfo[$h['year']]);
                    }
                }
                $row['date_between'] = $betweenStr;
                $row['in_all']       = $hireFlag ? half_num($h['freeze_days']) : '0.0';
                $row['used_day']     = $used_day;
                $row['left_day']     = $hireFlag ? half_num($leftDays) : '0.0';
                $row['actual_left']  = $hireFlag ? $actualDays : '0.00000';
                $row['invalid_date'] = $invalidDate >= $todayPoint ? $invalidDate : $this->getTranslation()->_('hasinvalid');
                $row['invalid_flag'] = $hireFlag && $invalidDate >= $todayPoint ?  false: true;
                $row['cycle']        = $h['year'];
                $detailData[]        = $row;
            }
        }

        //22年的 放最后
        if(!empty($history2022)){
            $detailData[] = $history2022;
        }
        return ['detail_list' => $detailData, 'is_permission' => $this->isPermission];
    }

    //获取对应周期的 开始结束时间 并且和 交替日 比较
    protected function getBetweenStr($cycle)
    {
        $lastCycle  = $cycle - 1;
        $cycleStart = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$lastCycle} year"));//开始
        $cycleEnd   = date('Y.m.d', strtotime("{$this->staffInfo['hire_date']} +{$cycle} year -1 day"));//周期结束

        //由于 存在 22年过渡期 判断区间时间 和 22年结束节点时间 取最大值
        $cycleStart = ($cycleStart < $this->formatPointDate) ? $this->formatPointDate : $cycleStart;
        $cycleEnd   = ($cycleEnd < $this->formatPointDate) ? $this->formatPointDate : $cycleEnd;
        $betweenStr = "{$cycleStart}-{$cycleEnd}";
        return $betweenStr;
    }


    /**
     *
     * 员工 职级对应 应有额度
     * @param $staff_info
     * @return array|int|mixed
     */
    public function getShouldDays($staff_info)
    {
        //如果是 月薪或日薪 hire type 非1 只有固定6天 https://flashexpress.feishu.cn/docx/Cs2DdqlnsokfWQxmDgScZVo1nEf
        if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_1){
            return 6;
        }

        $grade = $staff_info['job_title_grade_v2'];
        if (empty($grade) || $grade <= 14) {
            return 7;
        }
        if ($grade > 14 && $grade <= 16) {
            return 9;
        }
        if ($grade >= 17) {
            return 12;
        }

        return 7;
    }

    //年假额度 最大上限
    public function getMaxDays($staff_info)
    {
//        //年假最大额度改为15天了 https://flashexpress.feishu.cn/docx/Cs2DdqlnsokfWQxmDgScZVo1nEf 又他妈改回来了
//        return 15;

        $grade = $staff_info['job_title_grade_v2'];
        if (empty($grade) || $grade < 19) {
            return 15;
        } else {
            return 17;
        }
    }


    //计算 满一年以上员工 额外增加的年假天数
    public function overOneYear($staff_info)
    {
        if (empty($staff_info['hire_date'])) {
            return 0;
        }

        //如果是合同工 不额外增加年假
        if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_1){
            return 0;
        }

        $cycleInfo = $this->get_cycle($staff_info);
        //没有周期 信息
        if (empty($cycleInfo)) {
            return 0;
        }

        //返回周期 -1 天
        return $cycleInfo['cycle'] - 1;//满一年 加一天
    }



    //整理 扣减额度 标记 对应周期
    protected function formatSplitData()
    {
        //需要标记 用到去年剩余额度的
        $isLastFlag = isset($this->limitDays['last_sub']) && $this->limitDays['last_sub'] > 0 && $this->paramModel['leave_start_time'] < $this->cycleInfo['invalid_day'];
        $isOldFlag  = isset($this->limitDays['old_sub']) && $this->limitDays['old_sub'] > 0 && $this->paramModel['leave_start_time'] <= $this->invalidDate2022;
        $lastSub    = $this->limitDays['last_sub'] ?? 0;
        $oldSub     = $this->limitDays['old_sub'] ?? 0;
        $need2022   = $needLastYear = 0;
        // 拼接 拆分表 归属年 year_at 字段
        $add_row = [];//出现不够减情况 额外新增一条  merge到 insert
        foreach ($this->leaveSplitInfo as $k => $in) {
            $duration                             = ($in['type'] == StaffAuditLeaveSplitModel::SPLIT_TYPE_0) ? 1 : 0.5;//这天用额 1天 或者 半天
            $this->leaveSplitInfo[$k]['year_at']  = $this->cycleInfo['cycle'];//初始化周期 都为当前周期
            $this->leaveSplitInfo[$k]['audit_id'] = $this->auditId;

            //优先标记 22年
            if ($isOldFlag && $oldSub > 0 && in_array($in['date_at'], $this->oldEffectiveDates)) {
                //还够减
                if ($oldSub - $duration >= 0) {
                    $this->leaveSplitInfo[$k]['year_at'] = 2022;
                    $oldSub                              = $oldSub - $duration;
                    $need2022                            += $duration;
                    continue;
                } else {//拆分 两半
                    $this->leaveSplitInfo[$k]['type']    = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                    $this->leaveSplitInfo[$k]['year_at'] = 2022;
                    //拼接剩下半天 标记归属年 有可能是 last 或者 current
                    $cycleFlag = $this->cycleInfo['cycle'];
                    if($lastSub > 0){
                        $cycleFlag = $this->cycleInfo['cycle'] - 1;
                        $lastSub -= 0.5;
                        $needLastYear += 0.5;
                    }
                    $item      = $this->splitHalfItem($in, $cycleFlag);
                    $add_row[] = $item;
                    $oldSub    = 0;//减没了
                    $need2022  += 0.5;
                    continue;
                }
            }

            //其次标记 上周期
            if ($isLastFlag && $lastSub > 0 && in_array($in['date_at'], $this->effectiveDates)) {
                if ($lastSub - $duration >= 0) {//还够减
                    $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'] - 1;
                    $lastSub                             = $lastSub - $duration;
                    $needLastYear                        += $duration;
                    continue;
                } else {
                    $this->leaveSplitInfo[$k]['type']    = StaffAuditLeaveSplitModel::SPLIT_TYPE_1;
                    $this->leaveSplitInfo[$k]['year_at'] = $this->cycleInfo['cycle'] - 1;

                    //拼接剩下半天 标记归属年 为今年 merge
                    $item         = $this->splitHalfItem($in, $this->cycleInfo['cycle']);
                    $add_row[]    = $item;
                    $lastSub      = 0;//减没了
                    $needLastYear += 0.5;
                    continue;
                }
            }
        }
        //把拆分的 合并一起
        if (!empty($add_row)) {
            $this->leaveSplitInfo = array_merge($this->leaveSplitInfo, $add_row);
        }

        $this->lastApplyDays = $needLastYear;
        $this->oldApplyDays  = $need2022;
        //总数 减去 上一年下一年 剩下的就是当前额度占用天数
        $this->currentApplyDays = $this->thisNum + $this->nextNum - $needLastYear - $need2022;
        if ($this->currentApplyDays < 0) {
            $this->currentApplyDays = 0;
        }
    }

    //申请操作 额度表 model  分 三个周期 model 去年 今年 或者 旧规则 2022年
    protected function saveRemainData()
    {
        $conditionCycle = [];

        if (!empty($this->currentApplyDays)) {
            $conditionCycle[] = $this->cycleInfo['cycle'];
        }

        if (!empty($this->lastApplyDays)) {
            $conditionCycle[] = $this->cycleInfo['cycle'] - 1;
        }

        if (!empty($this->oldApplyDays)) {
            $conditionCycle[] = 2022;
        }

        if (empty($conditionCycle)) {
            return;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycles:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_1,
                'cycles'     => $conditionCycle,
            ],
        ]);

        if (empty($remainData->toArray())) {
            return;
        }

        //操作 对应额度字段
        foreach ($remainData as $remain) {
            if ($remain->year == $this->cycleInfo['cycle']) {
                //当前周年
                $remain->days       = $remain->days - $this->currentApplyDays;
                $remain->leave_days = $remain->leave_days + $this->currentApplyDays;
            }

            //上周期
            if ($remain->year == $this->cycleInfo['cycle'] - 1) {
                $remain->days       = $remain->days - $this->lastApplyDays;
                $remain->leave_days = $remain->leave_days + $this->lastApplyDays;
            }

            //旧规则 2022年
            if ($remain->year == 2022) {
                $remain->days       = $remain->days - $this->oldApplyDays;
                $remain->leave_days = $remain->leave_days + $this->oldApplyDays;
            }

            $remain->update();
        }
    }

    //额度 占用 一半一半的情况
    protected function splitHalfItem($in, $cycle)
    {
        $item['year_at']       = $cycle;
        $item['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $item['date_at']       = $in['date_at'];
        $item['type']          = StaffAuditLeaveSplitModel::SPLIT_TYPE_2;
        $item['audit_id']      = $this->auditId;
        return $item;
    }


    //是否转正
    protected function hireCheck()
    {
        //定制逻辑 所有国家 入职在这个时间点之前 都视为转正
        if ($this->staffInfo['hire_date'] < '2020-06-13 00:00:00') {
            return true;
        }

        //转正状态
        if ($this->staffInfo['status'] == ProbationServer::STATUS_FORMAL) {
            return true;
        }

        //新增 入职 大于等于 180天 也放开入口
        $today = date('Y-m-d');
        $limitDate = date('Y-m-d',strtotime("{$this->staffInfo['hire_date']} +179 day"));
        if($today >= $limitDate){
            return true;
        }

        return false;
    }


}