<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\SysStoreCateEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Modules\Th\enums\OvertimeEnums;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AssetWorkOrderServer;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\AuditExtendServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\AuditServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\KpiServer;
use FlashExpress\bi\App\Server\OsPriceServer;
use FlashExpress\bi\App\Server\OutsourcingOTServer;
use FlashExpress\bi\App\Server\ProbationTargetServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffHikServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SyncServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\CheckPunchOutServer;



class AuditServer extends GlobalBaseServer
{

    public static $c_days = 20;//c 级别 20天

    public const LEAVE_WORKFLOW_ID = 9 ;// 请假审批流 id
    public const LEAVE_WORKFLOW_ID_VIEW = 49 ;// 可视化请假审批流 id

    const PAID_LEAVE_PRE_APPLICATION_DAYS = 2; //带薪事假提前申请天数
    public $newVersionType = [enums::LEAVE_TYPE_1, enums::LEAVE_TYPE_19, enums::LEAVE_TYPE_4];//改版新结构类型

    public $yearTypes = [enums::LEAVE_TYPE_38];//泰国定制额度

    public $paramBean = [];


    /**
     * 补卡添加
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function reissueCardAdd($paramIn = [], $userinfo)
    {
        //原逻辑
        if (!in_array($userinfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            return parent::reissueCardAdd($paramIn, $userinfo);
        }

        //个人代理 的补卡逻辑 泰国定制
        $this->paramBean = $paramIn;
        //检验工号 是否存在 hrs
        $staff_re                           = new StaffRepository($this->lang);
        $this->staffInfo                    = $staff_re->getStaffPosition($this->paramBean['staff_id']);
        $this->paramBean['audit_reason']    = strip_tags(addcslashes(stripslashes($this->paramBean['audit_reason']),
            "'"));
        $this->paramBean['attendance_type'] = 2;//个人代理只能有下班补卡
        $reissueCardDate                    = $this->processingDefault($this->paramBean, 'reissue_card_date');
        if ($this->paramBean['day_type'] == StaffAuditModel::DAY_TYPE_NEXT) {
            $this->paramBean['reissue_card_date'] = date('Y-m-d H:i:s', strtotime("{$reissueCardDate} +1 day"));
        }
        //验证
        $return = $this->icCheckReissue();
        if ($return !== true) {
            return $this->checkReturn(['data' => $return]);
        }
        //入库
        $this->saveReissueApproval();
        return $this->checkReturn([]);
    }

    public function icCheckReissue()
    {
        if (empty($this->staffInfo)) {
            throw new ValidationException('invalid staff id');
        }

        if ($this->staffInfo['is_sub_staff'] == 1) {
            throw new ValidationException($this->getTranslation()->_('sub_staff_disable'));
        }
        $dayType = intval($this->paramBean['day_type']);//当日 次日
        if (empty($this->paramBean['date_at']) || empty($dayType)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $thisDay = $this->paramBean['date_at'];
        $nextDay = date('Y-m-d', strtotime("{$this->paramBean['date_at']} +1 day"));
        //当日有下班卡 不能申请补卡
        $attendanceInfo = StaffWorkAttendance::find([
            'columns'    => 'staff_info_id,attendance_date, started_at,end_at',
            'conditions' => 'staff_info_id = :staff_id: and attendance_date in ({dates:array})',
            'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'dates' => [$thisDay, $nextDay]],
        ])->toArray();
        $attendanceInfo = empty($attendanceInfo) ? [] : array_column($attendanceInfo, null, 'attendance_date');

        //出差打卡
        $tripAttendanceInfo = StaffAuditReissueForBusinessModel::find([
            'columns'    => 'staff_info_id, attendance_date, start_time, end_time',
            'conditions' => 'staff_info_id = :staff_id: and attendance_date in ({dates:array})',
            'bind'       => ['staff_id' => $this->staffInfo['staff_info_id'], 'dates' => [$thisDay, $nextDay]],
        ])->toArray();
        $tripAttendanceInfo = empty($tripAttendanceInfo) ? [] : array_column($tripAttendanceInfo, null,
            'attendance_date');

        //当日没有上班卡 也不能申请
        if (empty($attendanceInfo[$thisDay]['started_at']) && empty($tripAttendanceInfo[$thisDay]['start_time'])) {
            throw new ValidationException($this->getTranslation()->_('ic_no_start_notice'));
        }
        //当日已经有下班卡 也不能申请
        if (!empty($attendanceInfo[$thisDay]['end_at']) || !empty($tripAttendanceInfo[$thisDay]['end_time'])) {
            throw new ValidationException($this->getTranslation()->_('ic_have_end_notice'));
        }

        //已经申请补卡 不能再次申请
        $exist = StaffAuditModel::findFirst([
            'conditions' => 'audit_type = 1 and attendance_date = :date_at: and staff_info_id = :staff_id: and status in ({status:array})',
            'bind'       => [
                'date_at'  => $thisDay,
                'staff_id' => $this->staffInfo['staff_info_id'],
                'status'   => [enums::$audit_status['panding'], enums::$audit_status['approved']],
            ],
        ]);

        if (!empty($exist)) {
            throw new ValidationException($this->getTranslation()->_('ic_reissue_exist'));
        }

        //取配置
        $envModel     = new SettingEnvServer();
        $setting_code = [
            'ic_reissue_days',
            'ic_reissue_times',
        ];
        $setting_val  = $envModel->listByCode($setting_code);
        $setting_val = empty($setting_val) ? [] : array_column($setting_val, 'set_val', 'code');

        //个人代理 还有个 申请时间的限制
        $limitDays = $setting_val['ic_reissue_days'] ?? 0;
        $limitNum  = $setting_val['ic_reissue_times'] ?? 0;
        //补卡次数 不能超过 配置
        $punchMonthCardNum = (new AuditRepository($this->lang))->getAttendanceCarMonthData($this->paramBean);
        if ($limitNum != -1 && $punchMonthCardNum >= $limitNum) {//-1 不限制 为空 或者配置0 都限制
            throw new ValidationException($this->getTranslation()->_('ic_over_limit_times',['num' => $limitNum]));
        }
        //日期不能超过配置天数
        if ($limitDays > 0) {
            $today     = date('Y-m-d');
            $n         = $limitDays - 1;//配置的包含今天 所以要减去1
            $limitDate = date('Y-m-d', strtotime("-{$n} days"));
            if ($this->paramBean['date_at'] > $today || $this->paramBean['date_at'] < $limitDate) {
                throw new ValidationException($this->getTranslation()->_('date_error'));
            }
        }
        $addHour = $this->config->application->add_hour;
        //获取班次
        $shiftServer                 = new HrShiftServer();
        $shift_info                  = $shiftServer->getShiftInfos($this->staffInfo['staff_info_id'], [$nextDay]);
        //补卡时间 不能 超过 第二天的上班卡
        $nextStart = empty($attendanceInfo[$nextDay]['started_at']) ? ($tripAttendanceInfo[$nextDay]['start_time'] ?? '') : $attendanceInfo[$nextDay]['started_at'];
        if (!empty($nextStart)) {
            $nextStart = date('Y-m-d H:i:s', strtotime("{$nextStart}") + ($addHour * 3600));
            if ($this->paramBean['reissue_card_date'] > $nextStart) {
                throw new ValidationException($this->getTranslation()->_('ic_before_next_day'));
            }
        }else{//没有打卡时间 跟班次时间比
            $shiftStart = $shift_info[$nextDay]['start_datetime'] ?? '';
            if(!empty($shiftStart) && $this->paramBean['reissue_card_date'] > $shiftStart){
                throw new ValidationException($this->getTranslation()->_('ic_before_next_day'));
            }
        }

        //补卡时间不能 早于当天的上班卡
        $thisStart = empty($attendanceInfo[$thisDay]['started_at']) ? ($tripAttendanceInfo[$thisDay]['start_time'] ?? '') : $attendanceInfo[$thisDay]['started_at'];
        if (!empty($thisDay)) {
            $thisStart = date('Y-m-d H:i:s', strtotime("{$thisStart}") + ($addHour * 3600));
            if ($this->paramBean['reissue_card_date'] < $thisStart) {
                throw new ValidationException($this->getTranslation()->_('ic_after_this_day'));
            }
        }

        //java 接口 有件数 不能申请
        $checkServer = new CheckPunchOutServer($this->lang, $this->timezone);
        $res         = $checkServer->check_ic_from_java($this->paramBean['staff_id'], $this->paramBean['date_at']);
        if (!empty($res['num']) || !empty($res['abnormalNum'])) {
            return ['code' => -10090, 'msg' => $this->getTranslation()->_('attendance_punch_ic_notice_h5',['date_at' => $this->paramBean['date_at'],'num' => $res['num'] + $res['abnormalNum']]), 'data' => $res];
        }
        return true;
    }

    //个人代理 保存业务表 和审批流
    public function saveReissueApproval()
    {
        $auditType = enums::$audit_type['AT'];
        if(in_array($this->staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $auditType = enums::$audit_type['ICAT'];
        }

        //3天超时
        $insert['serial_no']         = $this->getID();
        $extend['time_out']          = date('Y-m-d 00:00:00', strtotime('+3 day'));
        $insert['staff_info_id']     = $this->staffInfo['staff_info_id'];
        $insert['reissue_card_date'] = $this->paramBean['reissue_card_date'];
        $insert['attendance_type']   = $this->paramBean['attendance_type'];
        $insert['audit_reason']      = $this->paramBean['audit_reason'];
        $insert['attendance_date']   = date('Y-m-d', strtotime($this->paramBean['date_at']));
        $insert['status']            = enums::$audit_status['panding'];
        $insert['audit_type']        = StaffAuditModel::AUDIT_TYPE_REISSUE;//新审批类型
        $insert['time_out']          = $extend['time_out'];
        $insert['source_type']       = StaffAuditModel::SOURCE_TYPE_UNPAID;//来源 个人代理

        $db = StaffAuditModel::beginTransaction($this);
        try {
            $db->insertAsDict("staff_audit", $insert);
            $audit_id = $db->lastInsertId();
            //插入图片
            $imagePathArr = $this->paramBean['image_path'];
            if (!empty($imagePathArr)) {
                $img_model = new StaffAuditImageModel();
                foreach ($imagePathArr as $k => $v) {
                    $img_clone = clone $img_model;
                    $row       = [
                        'audit_id'   => $audit_id,
                        'image_path' => $v,
                    ];
                    $img_clone->create($row);
                }
            }
            $rst = (new ApprovalServer($this->lang, $this->timezone))->create($audit_id, $auditType,
                $this->staffInfo['staff_info_id'], null, $extend);
            if (!$rst) {
                $this->logger->write_log("reissueCardAdd log " . json_encode($rst, JSON_UNESCAPED_UNICODE), 'info');
                $db->rollBack();
                throw new \Exception('audit staff insert reissueCardAdd  workflow fail ' . $rst);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $this->getDI()->get("logger")->write_log('reissue_card msg ' . $e->getMessage() . " file " . $e->getFile() . " line " . $e->getLine() . $e->getTraceAsString(),
                "error");
            return $this->checkReturn(-3, $this->getTranslation()->_('1006'));
        }
    }


    /**
     * 获取权限
     * @param array paramIn
     * @return array
     */
    public function getListPermission($paramIn = [])
    {
        //补卡
        $result['AT'] = $this->isMakeUpPermission() ? 1 : 2; //默认存在
        //泰国个人代理补卡
        $result['ICAT'] = $this->isUnpaidMakeUpPermission() ? 1 : 2; //默认存在
        //请假
        $result['LE'] = $this->isLeavePermission() ? 1 : 2;     //默认存在
        $result['LE_UNPAID'] = $this->unPaidLeavePermission() ? 1 : 2;//个人代理请假类型
        //病假证明权限
        $result['sick_certificate'] = $this->isLeavePermission() ? 1 : 2;

        //LH 移除
        //$result['LH'] = $this->checkLcDataPosition($paramIn) == true ? 1 : 2;
        //新耗材申请上线老的耗材入口移除
        //$result['Wms'] = $this->getWmsPermission($paramIn) == true ? 1 : 2;
        //修改里程权限
        $result['Mile'] = $this->getMilePermission($paramIn) == true ? 1 : 2;
        //加班-所有人都可以申请OT
        $result['OT'] = $this->getOTPermission($paramIn) == true ? 1 : 2;
        //出差
        $result['Trip'] = $this->getTripPermission($paramIn) == true ? 1 : 2;
        //车辆里程
        $result['Vehicle'] = $this->getVehiclePermission($paramIn) == true ? 1 : 2;
        //HC
        $result['HC'] = $this->checkHcDataRole($paramIn) == true ? 1 : 2;
        //补申请
        $result['apply'] = $this->getApplyPermission($paramIn) == true ? 1 : 2;
        //加班车
        $result['fleet'] = $this->getFleetPermission($paramIn) == true ? 1 : 2;
        //离职-所有人都可以申请离职
        $result['resign'] = $this->isResignPermission() ? 1 : 2;
        //解约
        $result['cancel_contract'] = $this->checkIsShowCancelContract() ? 1 : 2;
        //解约个人代理
        $result['company_termination_contract'] = $this->isCompanyTerminationContractPermission() ? 1 : 2;

        //外协员工
        $result['OS'] = $this->getOSPermission($paramIn) == true ? 1 : 2;
        //举报
        $result['Report'] = $this->getReportPermission($paramIn) == true ? 1 : 2;
        //到岗确认
        $result['Confirm'] = $this->getEntryConfirmPermission($paramIn) == true ? 1 : 2;
        //工资条pdf
        $result['salary'] = $this->salary_pdf_permission($paramIn) == true ? 1 : 2;
        //油费补贴
        $result['fuel_subsidy'] = $this->getFuelSubsidyPermission($paramIn) == true ? 1 : 2;
        //在职证明
        $result['on_job'] = $this->on_job_pdf_permission($paramIn) == true ? 1 : 2;
        //工资证明
        $result['payroll'] = $this->on_job_pdf_permission($paramIn) == true ? 1 : 2;
        //离职资产确认
        $result['resign_as'] = $this->getResignAssetPermision($paramIn) == true ? 1 : 2;
        //转岗
        $result['TF'] = $this->getJobtransferPermission($paramIn) == true ? 1 : 2;
        //抄送列表
        $result['CC'] = 1;
        //黄牌项目出差
        $result['yc_Trip'] =  2;//关闭入口 https://flashexpress.feishu.cn/wiki/R7VBwtiL1ilPSGkQgXQczZcAnbg
        // 销售CRM
        $result['Sales_CRM'] = $this->getSalesCRMPermission($paramIn) == true ? 1 : 2;

        //工服购买排除外协员工
        $result['InteriorOrder'] = $this->getInteriorOrdersPermission($paramIn) == true ? 1 : 2;
        //为员工增减角色审批
        $result['AR'] = $this->getAdjustRolePermission($paramIn) == true ? 1 : 2;
        //by 总部员工申请 油费报销 关联oa预算
        $result['fuel_budget']  = $paramIn['organization_type'] == 2 ? 1 : 2;
        $result['my_interview'] = (new InterviewRepository($this->timezone))->myInterview($paramIn) == true ? 1 : 2;
        //年底促销
        $result['FD_YES'] = $this->getFreightDiscYESPermission($paramIn) == true ? 1 : 2;
        //外协特殊价格审批
        $result['os_price'] = (new OsPriceServer($this->timezone))->permission($paramIn) == true ? 1 : 2;

        //网点支援
        $result['SAS']  = $this->getSASPermission($paramIn) ? 1 : 2;
        $result['SASS'] = $this->getSASSPermission($paramIn) ? 1 : 2;
        //领用包材
        $result['package'] = 1;

        //我的简历
        $resumeRecommendNum   = (new ResumeRecommendRepository())->getShowTotal($paramIn);
        $result['rr_is_show'] = !empty($resumeRecommendNum['is_show']) ? 1 : 2;

        //异常事件申请
        $result['ABE'] = $this->getAbnormalEventPermission($paramIn) ? 1 : 2;
        // 新增客户线索提报入口 add to zmma
        $result['Hand_in'] = (true === $this->isOutletsDirector($paramIn)) ? 1 : 2;
        //新版资产申请权限
        $result['NAS'] = $this->getNewAssetPermission($paramIn) ? 1 : 2;

        //申请耗材
        $result['apply_consumables'] = $this->getConsumablesPermission($paramIn) ? 1 : 2;

        //行政工单入口
        $result['xz_order'] = $this->getAdministrationOrderPermission($paramIn) ? 1 : 2;

        //资产工单-申请权限-入口判断
        $result['asset_work_order'] = (true === (new AssetWorkOrderServer())->getAssetWorkOrderPermission($paramIn)) ? 1 : 2;
        //审批-人事-KPI目标
        $result['kpi'] = (new KpiServer())->getKpiPermission($paramIn) ? 1 : 2;

        // 外协员工加班申请入口
        $result['outsourcing_ot'] = (true === (new OutsourcingOTServer($this->lang,$this->timezone))->isHubFenBoManger($paramIn)) ? 1 : 2;

        //恢复在职申请入口权限
        $result['reinstatement'] = (new ReinstatementServer($this->lang, $this->timezone))->getReinstatementPermission($paramIn) ? 1 : 2;

        //海康人脸照片入口权限
        $result['hik_face_input'] = (new StaffHikServer($this->lang, $this->timezone))->getHikInputPermission($paramIn) ? 1 : 2;
        //quick offer
        $result['quick_offer'] = $this->quickOfferPermission() ? 1 : 2;
        //OA文件夹
        $result['oa_folder'] = $this->OAFolderPermission() ? 1 : 2;
        // 暂停个人代理接单入口权限 
        $result['agent_suspend_work'] = $this->getAgentSuspendWorkPermission($paramIn) ? 1 : 2;
        //hcm入口
        $result['HCM'] = $this->isShowHCMPermission() ? 1 : 2;

        //试用期目标
        $result['probation_target'] = (new ProbationTargetServer($this->lang, $this->timezone))->getMenuPermission($paramIn) ? 1 : 2;

        //过滤外协员工权限
        $paramIn['permission_list'] = $result;
        $result                     = $this->outsourcingPermissionFilter($paramIn);


        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 正式员工就有
     * @param $paramIn
     * @return bool
     */
    public function getAdministrationOrderPermission($paramIn)
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        return $this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_1;
    }


    public function isOutletsDirector ($paramIn) {

        if(2 == $paramIn['organization_type']) {
            // 总部的人没有权限
            return false;
        }

        // 根据 staff_id 查询hr_staff_info表, 拿到一级部门id

        // 4 -> Network Management、13 -> Retail Management、34 -> Network Bulky
        if (empty($this->staffInfo) || (!empty($this->staffInfo['sys_department_id']) && !in_array($this->staffInfo['sys_department_id'],[4,13,34]))) {
            // 不属于网点4，13，34 部门的也没有权限
            return false;
        }

        // 角色
        $intersectResult = array_intersect([
            enums::$roles['DOT_ADMINISTRATOR'], // 网点主管
            enums::$roles['COURIER']            // 快递员
        ], $paramIn['positions']);

        if (empty($intersectResult)) {
            // 没拿到交集 表示角色不是网点主管也不是快递员
            return false;
        }
        return true;
    }

    /**
     * 校验年终促销活动状态
     * @param $paramIn
     * @return bool
     */
    public function getFreightDiscYESPermission($paramIn)
    {

        $_now_time = time();
        //
        if (!isCountry()|| $_now_time > strtotime('2022-02-28 23:59:59')) {
            return false;
        }

        $roles     = $this->processingDefault($paramIn, 'positions', 3);
        $organizationId = $this->processingDefault($paramIn, 'organization_id', 2);
        //'业务员','销售经理','销售总监','KA 经理','KA销售','KA 总监',大区经理  片区经理  超管  区域经理
        if(array_intersect($roles, [12,13,19,45,69,82,56,57,99,21])){
            return true;
        }

        $store     = (new SysStoreServer())->getStoreByid($organizationId);
        //SHOP部门的网点经理、网点主管
        if (array_intersect($roles, [3,18]) && in_array($store['category'], [4,5,7])) {
            return true;
        }

        //network NW部门的网点经理、网点主管
        if (array_intersect($roles, [3,18]) && in_array($store['category'], [1,2,10])) {
            return true;
        }

        return false;
    }

    /**
     * 获取资产申请权限 和 我的tab  公共资产显示
     * @param array paramIn
     * @param boolean $isAudit 是否是申请权限
     * @param boolean $isOnlyWhiteList 是否只判断白名单
     * @return boolean
     */
    public function getASPermission($paramIn = [], $isOnlyWhiteList = false, $isAudit = true)
    {
        if ($isAudit === false) { // 不是判断申请权限 (可持有 可转交权限)

            return true;
        }

        if ($isOnlyWhiteList) {
            // 泰国取消白名单的限制
            return false;
        }
        // 开始判断申请权限
        $staffId = $paramIn['staff_id'];
        $staff_re = new StaffRepository($this->lang);
        $info = $staff_re->getStaffPositionv3($staffId);
        if ($info['organization_type'] == 2) {
            // 总部都可以申请
            return true;
        }
        //pdc判断
        if(in_array($info['category'],SysStoreCateEnums::PDC_CATE_CODE)){
            if (in_array($info['job_title'], [
                enums::$job_title['branch_supervisor'],//16
                enums::$job_title['district_manager'],//269
                enums::$job_title['regional_manager'],//79
                enums::$job_title['network_support_officer'],//556
                enums::$job_title['network_support_supervisor'],//555
                enums::$job_title['network_support_manager'],//554
            ])) {
                return true;
            }
        }

        //判断职位
	    if (in_array($info['job_title'], [
		    enums::$job_title['hub_supervisor'],
		    enums::$job_title['hub_manager'],
		    enums::$job_title['freight_hub_outbound_supervisor'],
		    enums::$job_title['freight_hub_admin_officer'],
		    enums::$job_title['freight_hub_manager'],
		    enums::$job_title['onsite_supervisor'],
		    enums::$job_title['onsite_admin_officer'],
		    enums::$job_title['shop_supervisor'],
		    enums::$job_title['area_manager'],
		    enums::$job_title['shop_support_section_manager'],
		    enums::$job_title['branch_supervisor'],
		    enums::$job_title['district_manager'],
		    enums::$job_title['regional_manager'],
		    enums::$job_title['network_support_officer'],
		    enums::$job_title['network_support_supervisor'],
		    enums::$job_title['network_support_manager'],
            enums::$job_title['cdc_supervisor'],

	    ])) {
		    return true;
	    }
        //判断部门
	    if (in_array($info['department_id'], $this->getDepartmentSubset(enums::$department['hub_headquarter']))
	        &&
	        in_array($info['job_title'], [enums::$job_title['hub_admin_manager'],
	                                      enums::$job_title['hub_admin_supervisor'],
	                                      enums::$job_title['hub_admin_officer'],])
	    ) {
		    return true;
	    }
	    if (in_array($info['department_id'], $this->getDepartmentSubset(enums::$department['shop_support']))
	        &&
	        in_array($info['job_title'], [enums::$job_title['hub_admin_manager'],
	                                      enums::$job_title['hub_admin_supervisor'],
	                                      enums::$job_title['hub_admin_officer'],])
	    ) {
		    return true;
	    }

        /**
         * @doc 需求地址 https://l8bx01gcjr.feishu.cn/docs/doccnYUL2u3XKhcQrP3kXDUbnFf
         * 当前登录的员工属于泰国Flash Fullfillment[20001]的公司时，且其职位是Warehouse Operations Director[914]或Warehouse supervisor[158]或Warehouse Manager[916]可以申请和持有资产。
         * 之后的审批流审批节点和原有的保持一致。
         */
        if (
            $this->isBelongCompany($info['department_id'], 20001) && in_array($info['job_title'], [enums::$job_title['warehouse_operations_director'], enums::$job_title['warehouse_supervisor'], enums::$job_title['warehouse_manager']])
            ||
            $this->isBelongCompany($info['department_id'], 40001) && $info['job_title'] == enums::$job_title['operations_director']
        ) {
            return true;
        }
        return false;
    }

	/**
	 * @description:根据部门查询子集
	 *
	 * @param $department_id  部门 id
	 *
	 * @return     :data [] 数组
	 * <AUTHOR> L.J
	 * @time       : 2021/8/18 21:03
	 */
	public function getDepartmentSubset($department_id){
		$dept_list = [];
		$dept_detail = SysDepartmentModel::findFirst(['conditions' => "id = :id: and deleted = 0",
		                                              'bind'       => [
			                                              'id' => $department_id
		                                              ],
		                                              'columns'    => 'id,ancestry_v3']);
		if(!empty($dept_detail)) {
			$ancestry_v3 = empty($dept_detail->ancestry_v3) ? $dept_detail->id : $dept_detail->ancestry_v3;
			$dept_list = SysDepartmentModel::find([
				                                      'conditions' => ' ancestry_v3 like :ancestry: or id = :id: ',
				                                      'bind' => [
					                                      'ancestry' => $ancestry_v3.'/%',
					                                      'id' =>  $dept_detail->id,
				                                      ],
				                                      'columns' => ['id'],
			                                      ])->toArray();
			$dept_list = array_column($dept_list,'id','id');
		}
		return $dept_list;
	}

    /**
     * 转岗-申请权限
     * @param array $paramIn
     * @return bool
     */
    public function getJobtransferPermission(array $paramIn = []): bool
    {
        $staffId = $paramIn["staff_id"];

        //部门负责人
        $staffServer = new StaffServer($this->lang, $this->timezone);
        $manageDepartmentIds = $staffServer->getManageDepartmentList($staffId);
        if (!empty($manageDepartmentIds)) {
            return true;
        }

        //大区负责人
        $manageRegionIds = $staffServer->getManageRegionsList($staffId);
        if (!empty($manageRegionIds)) {
            return true;
        }

        //片区负责人
        $managePieceIds = $staffServer->getManagePiecesList($staffId);
        if (!empty($managePieceIds)) {
            return true;
        }

        //网点负责人
        $manageStoresIds = $staffServer->getManageStoresList($staffId);
        if (!empty($manageStoresIds)) {
            return true;
        }
        $underManageStaff = (new HrStaffItemsModel())->getOneByValue($staffId, 'MANGER');
        if (!empty($underManageStaff)) {
            return true;
        }

        return false;
    }


    //返回ph list
    public function get_holidays($staff_info)
    {
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $data = $leave_server->ph_days($staff_info);
        if(!empty($data))
            return array_column($data,'day');
        return array();
    }



	/**
	 * 获取销售CRM权限
	 * @param mixed $paramIn
	 * @doc https://l8bx01gcjr.feishu.cn/docs/doccnPxTa5v78XVc6Q2Hq6C1xOe
	 */
	public function getSalesCRMPermission($paramIn): bool
	{
		$staffId = $paramIn["staff_id"];
		$department_id = $paramIn["department_id"];
		//CRM 白名单 crm_white_list = 57776
		$senv = (new SettingEnvServer())->getSetVal('crm_white_list');
		if(!empty($senv) && in_array($staffId,explode(',',$senv))){
			return true;
		}

		if (empty($staffId) || empty($department_id)) {
			return false;
		}

		// 有权限的部门IDS
		$permission_department_ids = [];
        //获取拥有权限的部门 crm_dep_white_list
        $dep_env = (new SettingEnvServer())->getSetVal('crm_dep_white_list');
        $dep_env = explode(',', $dep_env);
        foreach ($dep_env as $departmentId) {
            $departmentIds = (new SysDepartmentModel())->getSpecifiedDeptAndSubDept($departmentId);
            $permission_department_ids = array_merge($permission_department_ids,$departmentIds);
        }

		return in_array($department_id, $permission_department_ids);
	}


	/**
	 * 获取黄牌项目出差权限
	 * @param array $paramIn
	 * @return boolean
	 */
	public function getYCTripPermission($paramIn = []) {

		if (!isCountry()) {
			return false;
		}
        if (empty($this->staffInfo)) {
            return false;
        }

		//Network部门的van courier(110)、branch supervisor(16)可以申请
        //  - 调整：Network Managem、Network Bulky及下级部门的van courier、branch supervisor职位可以申请 https://l8bx01gcjr.feishu.cn/docs/doccnRp0rPc2rVMo8FpCKlz3k1c#
        if (in_array($this->staffInfo['sys_department_id'], [
                enums::$department['Network Management'],
                enums::$department['Network Bulky'],
            ]) && in_array($this->staffInfo['job_title'],
                [enums::$job_title['branch_supervisor'], enums::$job_title['van_courier']])) {
            return true;
        }

		return false;
	}



	/**
	 * 请假添加
	 * @Access  public
	 * @Param   request
	 * @Return  array
	 *
	 * ！！！ 涉及表 staff_audit 主表 staff_audit_leave_split 拆分表
	 */
	public function leaveAdd($paramIn = [])
	{
		//[1]参数定义
		$staffId        = $this->processingDefault($paramIn, 'staff_id', 2);
		$leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
		$leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
		$leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
		$leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
		$leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
		$auditReason    = $this->processingDefault($paramIn, 'audit_reason');
		$imagePathArr   = $this->processingDefault($paramIn, 'image_path');
        $paidLeaveReason= $this->processingDefault($paramIn, 'paid_leave_reason', 2);
		$auditReason    = strip_tags(addcslashes(stripslashes($auditReason), "'"));

		$db = StaffAuditModel::beginTransaction($this);
		$serialNo  = $this->getRandomId();

		//[2]用户校验
		$staff_model = new StaffRepository();
		$staffData   = $staff_model->getStaffPosition($staffId);
		if (empty($staffData)) {
			return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
		}

        if ($staffData['is_sub_staff'] == 1) {
            return $this->checkReturn(-3, $this->getTranslation()->_('sub_staff_disable'));
        }
        if ($this->checkStaffFormal($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('os_or_franchisee_staff_disable'));
        }

		$leave_day = $paramIn['leave_day'] = $this->conversionTime(array_merge(['type' => 2], $paramIn));

		//请假日期 就是休息日区间 无需申请 假期类型包括 年假,事假,婚嫁,出家假,个人培训假（不带薪）,病假,绝育手术假,公司培训假,家人去世假。
		if ($leave_day == 0)
			return $this->checkReturn(-3, $this->getTranslation()->_('day off for the apply date'));

        //-- staff holidays between start and end
        $leave_server = new LeaveServer($this->lang,$this->timezone);
        $holidays = $paramIn['holidays'] = $leave_server->staff_off_days($staffId, $leaveStartTime, $leaveEndTime);

        if ($leaveType == enums::LEAVE_TYPE_2 && empty($paramIn['is_bi'])) {

            //带薪事假申请时间限制调整（仅BY调整，HCM不变）
            //https://flashexpress.feishu.cn/docx/CtPGdB2KjojlQ0xB7PIcTu1KnOe

            //校验时间
            if ($leaveEndTime < $leaveStartTime) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1032'));
            }

            $workdays       = 0;
            $currentDate    = date("Y-m-d", strtotime("+1 day"));
            $leaveStartData = date("Y-m-d", strtotime($leaveStartTime . "-1 day"));
            if ($currentDate >= $leaveStartData) {
                throw new ValidationException($this->getTranslation()->_('1032'));
            }
            $prefixHolidays = $leave_server->staff_off_days($staffId, $currentDate, $leaveStartData);
            while ($currentDate <= $leaveStartData && $workdays < self::PAID_LEAVE_PRE_APPLICATION_DAYS) {
                if (!in_array($currentDate, $prefixHolidays)) {
                    $workdays += 1;
                }
                $currentDate = date("Y-m-d", strtotime($currentDate . "+1 day"));
            }
            if ($workdays < self::PAID_LEAVE_PRE_APPLICATION_DAYS) {
                throw new ValidationException($this->getTranslation()->_('1032'));
            }
        }

		//[3]校验选择时间内是请假记录
		$checkData = $this->checkLeaveDataByDate($paramIn);

		$this->getDI()->get("logger")->write_log("leave params ".json_encode($paramIn,JSON_UNESCAPED_UNICODE) .' check res:'.json_encode($checkData,JSON_UNESCAPED_UNICODE),'info');

		if (isset($checkData['code']) && $checkData['code'] == 0) {
			return $this->checkReturn(-3, $checkData['msg']);
		}

        //新增验证班次时间二次确认提示 https://flashexpress.feishu.cn/docx/WqJcd6xFNoctnux4qPEcKTZcnkg
        if (empty($paramIn['is_bi']) && empty($paramIn['is_submit'])){
            if($staffData['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                $leave_server->checkShiftLeave($staffId,$paramIn);
            }
        }

		//判断是否跨天 保存 拆分表 staff_audit_leave_split
		$insert_param['leave_type'] = $leaveType;
		$insert_param['start_time'] = $leaveStartTime;
		$insert_param['end_time'] = $leaveEndTime;
		$insert_param['start_type'] = $leaveStartType;
		$insert_param['end_type'] = $leaveEndType;
		$insert_param['holidays'] = $holidays;
		$insert_param['sub_day'] = $this->sub_day;

        $r = $leave_server->format_leave_insert($staffId, $insert_param);
        if ($r['code'] == 1) {
            $insert = $r['data'];
        } else {
            return $r;
        }

        $audit_model = new AuditRepository($this->lang);
		//操作 各种定制类型 假期的 额外操作
		{
            if($leaveType == enums::LEAVE_TYPE_15 && !empty($paramIn['is_bi'])){//替换轮休 工具操作时候用
                $check_param['operator'] = $paramIn['operator'] ?? 0;
                $check_param['staff_id'] = $staffId;
                $check_param['date'] = date('Y-m-d',strtotime($leaveStartTime));
                $this->leave_for_workday($check_param);
            }else if(in_array($leaveType,$this->one_time)){
                //增加 一次性额度扣减
                $remain_model = new StaffLeaveRemainDaysModel();
                $remain_row['staff_info_id'] = $staffId;
                $remain_row['leave_type'] = $leaveType;
                $remain_row['days'] = 0;
                $remain_row['leave_days'] = $leave_day;
                $flag = $remain_model->create($remain_row);
                $this->getDI()->get("logger")->write_log("leave_one_time {$staffId} {$flag}" . json_encode($remain_row),'info');
            }else if(in_array($leaveType,$this->one_send)){
                //一次性发放额度 先查询 有没有额度 如果没有额度 不让申请 需要在hris 那边初始化
                $remain_info = StaffLeaveRemainDaysModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                    'bind' => ['staff_info_id' => $staffId,'leave_type' => $leaveType],
                ]);

                //如果没有 说明没初始化成功 提示错误
                if(empty($remain_info))
                    return $this->checkReturn(-3, 'init failed');

                //操作额度
                $remain_info->days -= $leave_day;
                $remain_info->leave_days += $leave_day;
                $remain_info->updated_at = gmdate('Y-m-d H:i:s');
                $remain_info->update();

            }else if($leaveType == enums::LEAVE_TYPE_2){//带薪事假 等可以预申请 要标记 第二年的额度 -- todo 以后这块逻辑改成除了上面定制类型 其他假期 都走这扣除
                //如果申请去年的 按当年算 申请明年的 按明年算

                $insert_param['need_num'] = 1;
                $num_arr = $leave_server->format_leave_insert($staffId,$insert_param);
                //扣除额度
                $p['this_year'] = $num_arr['data']['this_num'];
                $p['next_year'] = $num_arr['data']['next_num'];
                $p['leave_type'] = $leaveType;
                $leave_server->update_leave_days($staffId,enums::YEAR_ADD,$p);

            }
		}

		$status = 1;
		if (!empty($paramIn['is_bi'])){
			$status = 2;
			$auditReason .= "|system_tool_add";
		}
        $timeOut = date('Y-m-d 00:00:00',strtotime('+3 day'));
		//[4]请假插入
		try {
			$insetData = [
				'staff_info_id'    => $staffId,
				'leave_type'       => $leaveType,
				'leave_start_time' => $this->assemblyData($leaveStartTime, 1, $leaveStartType),
				'leave_start_type' => $leaveStartType,
				'leave_end_time'   => $this->assemblyData($leaveEndTime, 2, $leaveEndType),
				'leave_end_type'   => $leaveEndType,
				'audit_reason'     => $auditReason,
				'status'           => $status,
				'audit_type'       => enums::$audit_type['LE'],
				'leave_day'        => $leave_day,
				'serial_no'        => (!empty($serialNo) ? 'LE' . $serialNo : NULL),
                'paid_leave_reason'=> !empty($paidLeaveReason) ? $paidLeaveReason: 0,
                'time_out'         => $timeOut,
			];

			//不同国家 存在 子类型 产假等
			if(!empty($paramIn['sub_type'])){
				$insetData['template_comment'] = json_encode(array("leave_{$leaveType}_key" => intval($paramIn['sub_type'])));
			}
			$insetData['source_type'] = (in_array($staffData['hire_type'], HrStaffInfoModel::$agentTypeTogether)) ? 1 : 0;

			$db->insertAsDict("staff_audit", $insetData);
			$auditId = $db->lastInsertId();
			if (!$auditId) {
				throw new \Exception("插入staff_audit 失败" . json_encode($insetData, JSON_UNESCAPED_UNICODE));
			}
			if($insert && is_array($insert)) {
				foreach ($insert as &$v) {
					$v['audit_id'] = $auditId;
				}
				$result = (new BaseRepository())->batch_insert("staff_audit_leave_split", $insert);
				if (!$result) {
					throw new \Exception("插入staff_audit_leave_split 失败" . json_encode($insert, JSON_UNESCAPED_UNICODE));
				}
			}
			if (empty($paramIn['is_bi'])) {

				$extend = $this->getWorkflowExtend($insetData, $staffData);
				$extend['time_out'] = $timeOut;
                $workflow_id = $this->getWorkflowId($staffData);
                $res = (new ApprovalServer($this->lang, $this->timezone))->create($auditId, enums::$audit_type['LE'], $staffId,null,$extend,'',$workflow_id);
				if (!$res) {
					throw new \Exception('audit staff insert leaveAdd workflow fail' . $res);
				}
			}
			$db->commit();
		} catch (\Exception $e) {
			$this->getDI()->get("logger")->write_log("leaveaddworkflow error:". $e->getMessage() . " " . $e->getTraceAsString());
			$db->rollBack();
			return $this->checkReturn(-3, $this->getTranslation()->_('1009'));
		}

        //工具过来的 需要同步处罚
        if(!empty($paramIn['is_bi'])){
            //请假
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params = [
                'staff_id'         => $staffId,
                'leave_start_date' => $insetData['leave_start_time'],//带 时分秒的
                'leave_end_date'   => $insetData['leave_end_time'],
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
        }

		//插入图片
		if (!empty($imagePathArr)) {
			foreach ($imagePathArr as $k => $v) {
				$insertImgData = [
					'audit_id'   => $auditId,
					'image_path' => $v
				];
                $audit_model->auditImgInsert($insertImgData);
			}
		}

		$return['data'] = array('leave_day' => $paramIn['leave_day']);
		return $this->checkReturn($return);
	}


    //新版 计算剩余年假 只计算 type  1 和 2
    /**
     * 获取剩余假期天数
     * @param $param
     */
    public function get_left_holidays($param)
    {

        $showType = [enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_2,enums::LEAVE_TYPE_19];
        //hcm  调用
        if(!empty($param['is_svc']) && $param['is_svc'] == 1){
            $data =  $this->rpcHolidaysNum($param);
            return $this->checkReturn(['data' => $data]);
        }

        $staff_id     = intval($param['staff_id']);
        //获取性别 员工信息 job title name
        $staff_info = $param['user_info'];
        //所有假期类型
        $data = $this->type_book();

        $data = array_column($data, null, 'code');
        //根据员工属性 获取对应权限类型
        $data = $this->staffLeaveType($param['user_info'], $data);

        //是否可以今天请假，1代表可以申请今天，0代表不能申请今天
        $canleavetoday = (new SettingEnvServer())->getSetVal('canleavetoday');

        $leave_server = new LeaveServer($this->lang, $this->timezone);
        foreach ($data as $k => &$da) {
            $type = intval($da['code']);
            //需要在请假页面显示天数的假期类型 1 和类型2
            if ($type == enums::LEAVE_TYPE_1) {//年假类型需判断职位
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            } else if ($type == enums::LEAVE_TYPE_2) {//带薪是假 显示剩余
                $r = $leave_server->get_personal_days($staff_info);
                $da['day_limit'] = $r['day_limit'];
                $da['day_sub']   = $r['day_sub'];
                $da['day_sum']   = $r['day_sum'];
            } else if ($type == enums::LEAVE_TYPE_19) {
                $da              = array_merge($da, $leave_server->getVacationDays($staff_id, $type));
            }

            //泰国病假 定制 如果有剩余额度 返回带薪 底色是黄的  没额度 展示灰的
            if($type == enums::LEAVE_TYPE_38){
                $sickInfo          = $leave_server->getVacationDays($staff_id,$type);
                $da['color'] = ($sickInfo['day_sub'] > 0) ? enums::LEAVE_COLOR_YELLOW : enums::LEAVE_COLOR_GRAY;
            }

            if(in_array($type,$showType)){
                //改类型 前端用
                $da['day_limit'] = empty($da['day_limit']) ? '0' : (string)$da['day_limit'];
                $da['day_sub'] = empty($da['day_limit']) ? '0' : (string)$da['day_sub'];
            }

            //仅能选择今天之后的日期（canleavetoday=0不含今天）
            if($type == enums::LEAVE_TYPE_40) {
                $da['start_day_leave'] = !empty($canleavetoday) && $canleavetoday == self::CAN_LEAVE_TODAY ? date('Y-m-d') : date('Y-m-d', strtotime("+1 day"));
            }
        }

        $data = $leave_server->explain_read_flag($staff_info,$data);
        return $this->checkReturn(['data' => $data]);
    }

    public function rpcHolidayBody($permissionData, $leave_server)
    {
        foreach ($permissionData as $k => &$da) {
            $type = intval($da['code']);
            //额度显示 需要排除的 类型
            if (in_array($type, [enums::LEAVE_TYPE_15, enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
                unset($permissionData[$type]);
                continue;
            }

            //需要在请假页面显示天数的假期类型
            if (in_array($type, $this->newVersionType)) {//改版后调用
                $da = array_merge($da, $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]));
                continue;
            }

            if ($type == enums::LEAVE_TYPE_2) {//带薪是假 显示剩余
                $r               = $leave_server->get_personal_days($this->staffInfo);
                $da['day_limit'] = $r['day_limit'];
                $da['day_sub']   = $r['day_sub'];
                continue;
            }
            //病假 特殊处理 要弄一个超额 18 的进来
            if ($type == enums::LEAVE_TYPE_38) {
                $sickInfo = $leave_server->getVacationDays($this->staffId, $type, ['is_svc' => 1]);

                $paid['day_limit'] = $sickInfo['day_limit'];
                $paid['day_sub']   = $sickInfo['day_sub'];
                $da                = array_merge($da, $paid);
                //把超额病假 塞进去
                $this->unpaidInfo = $sickInfo['unpaid_info'] ?? [];
                continue;
            }

            //一次性假期
            if (!empty($this->oneTypes) && in_array($type, $this->oneTypes)) {
                $da['day_limit'] = $da['day_sub'] = $this->limit_array[$type] ?? 0;//分母 应有总额度

                //one time 类型 看有没有 没有就是没申请过 是全额
                if (in_array($type, $this->one_time) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = 0;
                }
                if (in_array($type, $this->one_send) && !empty($this->remainData[$type])) {
                    $da['day_sub'] = half_num($this->remainData[$type]['days']);
                }

                if ($da['day_sub'] < 0) {
                    $da['day_sub'] = 0;
                }
                continue;
            }
            //按年发放 固化的额度
            if (in_array($type, $this->yearTypes)) {
                $da['day_limit'] = $this->limit_array[$type] ?? 0;
                $da['day_sub']   = empty($this->remainData[$type]) ? 0 : half_num($this->remainData[$type]['days']);
                continue;
            }

            if (!in_array($type, $this->limit_types)) {
                $da['text'] = 'no_limit';//没有 unset掉 并且 额度表没类型 说明 是不限制额度
            }

            //需要计算的类型
            $da['day_limit'] = $this->limit_array[$type] ?? 0;//分母 应有总额度
            $sum             = empty($this->sum_days[$type]['num']) ? 0 : $this->sum_days[$type]['num'];//已用的 前端没用 不限时了
            $da['day_sub']   = $da['day_limit'] - $sum;//分子 今年剩余额度

            if ($da['day_sub'] < 0) {
                $da['day_sub'] = 0;
            }
        }
        return $permissionData;
    }


    //根据员工工号属性 获取对应能申请的假期类型
    public function staffLeaveType($staff_info,$data = []){
        $staff_id = $staff_info['staff_info_id'];
        $leave_server = new LeaveServer($this->lang, $this->timezone);
        //新增权限判断
        $permission = $leave_server->leavePermission($staff_info);
        if(!$permission){
            return [];
        }

        if(empty($data)){
            $data = $this->type_book();
        }
        //实习生类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            $data = $this->practise_type_book();
        }

        //外协类型
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0){
            $data = $this->os_type_book();
        }
        //无底薪员工
        if($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            $data = $this->hire_un_paid_book(enums::LEAVE_TYPE_40);
        }
        $data = array_column($data, null, 'code');

        if (empty($staff_info['sex']) ||  $staff_info['sex'] == 1) {
            //男性过滤掉 产检，产假 和女性特殊假
            unset($data[4],$data[17],$data[21],$data[22]);
        } else {
            //女性过滤掉 陪产假，国家军训假，出家假
            unset($data[5],$data[6],$data[enums::LEAVE_TYPE_11]);
        }
        //实习生不展示带薪事假
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_INTERN){
            unset($data[2]);
        }

        unset($data[15]);
        //泰国 新冠相关假期 根据名单判断是否显示
        $covid_25_flag = $leave_server->leave_25_off($staff_id);
        if (!$covid_25_flag) {
            unset($data[25]);
        }
        $covid_26_flag = $leave_server->leave_26_off($staff_id);
        if (!$covid_26_flag) {
            unset($data[26]);
        }
        //无底薪员工
        if($staff_info['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            unset($data[40]);
        }

        // 国籍与工作所在地不一致的员工， 会有跨国探亲假 如果数据表没有 触发发放操作
        $nationServer = new InternationalServer($this->lang,$this->timezone);
        if(!$nationServer->applyPermission($staff_info)){
            unset($data[19]);
        }

        return $data;
    }


    //网点申请支援权限
    public function getSASPermission($paramIn) {
        //高级主管
        if ($this->staffInfo['job_title'] == HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR) {
            return true;
        }

        //1. 申请职位权限：District Manager、Area Manager、DC Supervisor；取数：OA组织架构的部门负责人
        $staffInfoId = $paramIn['staff_id'];
        $manageStore = SysStoreModel::find([
            'conditions' => 'manager_id = :staff_id: and state = 1 and category IN (1,10,13)',
            'bind'       => ['staff_id' => $staffInfoId],
        ])->toArray();

        $manageRegionAll = SysManageRegionModel::find([
            'conditions' => 'deleted = 0 and type IN(1,3)'
        ]);
        $region_arr = $manageRegionAll->toArray();
        $region_ids = array_column($region_arr, 'id');
        $region_manager_ids = array_column($region_arr, 'manager_id');

        //2-片区负责人
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0 and manage_region_id in ({manage_region_ids:array})',
            'bind'       => ['staff_id' => $staffInfoId, 'manage_region_ids' => $region_ids],
        ])->toArray();

        //3-大区负责人
        if(in_array($staffInfoId, $region_manager_ids)) {
            $manageRegion = true;
        } else {
            $manageRegion = false;
        }

        return !empty($manageStore) || !empty($managePiece) || $manageRegion ;
    }

    /**
     * 员工申请支援网点有多处改动 请搜索关键词【申请支援改动点】
     * todo 申请支援改动点(3-1)
     * @param $paramIn
     * @return bool
     */
    //员工申请支援网点权限
    public function getSASSPermission($paramIn) {
        //1. Tricycle Courier、Van Courier、Bike Courier、DC Supervisor：只能看到和提交对应职位的支援申请
        $job_title = $this->processingDefault($paramIn, 'job_title', 2);
        if(in_array($job_title, [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['dc_officer'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['regional_manager'],
            enums::$job_title['district_manager'],
            enums::$job_title['assistant_branch_supervisor'],
            enums::$job_title['cdc_officer'],
            enums::$job_title['cdc_supervisor'],
            HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR,
        ])) {
            return true;
        }
        return false;
    }

    /**
     * 获取异常时间权限
     * @param $paramIn
     * @return bool
     */
    public function getAbnormalEventPermission($paramIn): bool
    {
        return false; //暂时屏蔽入口
        $staff_info_id = $paramIn['staff_id'];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_id:",
            'bind'       => [
                'staff_id' => $staff_info_id
            ]
        ]);

        $model = new FleSysDepartmentModel();
        $transportationDeptIds = $model->getSpecifiedDeptAndSubDept(enums::$department['Transportation']);
        $customerServiceDeptIds = $model->getSpecifiedDeptAndSubDept(enums::$department['Customer Service']);

        //Transportation部门下职位Transportation Supplier Management Officer
        //Customer Service部门下职位Claim Specialistx
        if (!empty($staffInfo) && (
            in_array($staffInfo->node_department_id, $transportationDeptIds) && $staffInfo->job_title == 749 ||
            in_array($staffInfo->node_department_id, $customerServiceDeptIds)   && $staffInfo->job_title == 467)
        ) {
            return true;
        }

        return false;
    }

    public function setATProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal ){
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind'       => ['audit_id' => $auditId]
            ]);
            if (empty($auditDetail)) {
                throw new \Exception('audit info error');
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind'       => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : ''
            ];
            $auditEditFlag = $this->re['audit']->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1014'));
            }
            if ( $state == enums::$audit_status['approved']) {
                //回调修改打卡时间
                //新需求 回写 班次到打卡表 徐华伟
                $shiftServer = new HrShiftServer();
                $shift_data  = $shiftServer->getShiftInfos($auditDetail['staff_info_id'], [$auditDetail['attendance_date']]);

                $updateData = [
                    'attendance_type'   => $auditDetail['attendance_type'],
                    'attendance_date'   => $auditDetail['attendance_date'],
                    'reissue_card_date' => $auditDetail['reissue_card_date'],
                    'staff_info_id'     => $auditDetail['staff_info_id'],
                    'shift_start'       => $shift_data[$auditDetail['attendance_date']]['start'] ?? '',
                    'shift_end'         => $shift_data[$auditDetail['attendance_date']]['end'] ?? '',
                    'shift_id'          => $shift_data[$auditDetail['attendance_date']]['shift_id'] ?? 0,

                ];

                $this->re['audit']->editWorkAttendance($updateData);

                //https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
                //请假补卡审批通过同步bi，重算处罚数据
                $sync_server = new SyncServer($this->lang, $this->timezone);
                $params = [
                    'staff_id'     => $auditDetail['staff_info_id'],
                    'recheck_date' => $auditDetail['attendance_date'],
                    'type'         => $auditDetail['attendance_type'],
                ];
                $sync_server->sync_fbi_attendance($params, 'abnormal.staff_clock_in_reset');
                //个人代理的补卡下班 要通知fbi 重算
                if($auditDetail['source_type'] == StaffAuditModel::SOURCE_TYPE_UNPAID && $auditDetail['attendance_type'] == StaffAuditModel::ATTENDANCE_TYPE_FIRST_LOW){
                    $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
                    $attendanceServer->sendIcOffCard($auditDetail['staff_info_id'],$auditDetail['attendance_date'],$auditDetail['reissue_card_date']);
                }
            }
        }
        return true;
    }

    public function setLEProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 是最终审批
            $auditDetail = StaffAuditModel::findFirst([
                'conditions' => ' audit_id = :audit_id: ',
                'bind' => ['audit_id' => $auditId]
            ]);
            if(empty($auditDetail)){
                throw new \Exception($this->getTranslation()->_('1014'));
            }
            $auditDetail = $auditDetail->toArray();
            if (isset(self::$paramIn['staff_id'])) {
                $staff = HrStaffInfoModel::findFirst([
                    'conditions' => ' staff_info_id = :staff_id: ',
                    'bind' => ['staff_id' => self::$paramIn['staff_id']]
                ]);
                if ($staff) {
                    $staff = $staff->toArray();
                }
            }
            if (!$extend['super']){
                $state = $this->transformState($state,$extend['is_cancel'] ?? 0);
            }
            $updateData = [
                'status'        => $state,
                'audit_id'      => $auditId,
                'reject_reason' => self::$paramIn['reject_reason'] ?? '',
                'approver_id'   => self::$paramIn['staff_id'] ?? 0,
                'approver_name' => isset($staff) && $staff ? $staff['name'] : ''
            ];
            $auditRe = new AuditRepository($this->lang);
            $auditEditFlag = $auditRe->auditEditStatus($updateData);
            if (!$auditEditFlag) {
                throw new \Exception($this->getTranslation()->_('1014'));
            }

            //获取员工信息
            $staff_model = new StaffRepository($this->lang);
            $staff_info = $staff_model->getStaffPosition($auditDetail['staff_info_id']);
            $leave_server = new LeaveServer($this->lang, $this->timezone);

            //撤销个人代理请假（不接单通知）bi处罚用
            if ($state == enums::$audit_status['revoked'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_40) {
                $bi_rpc = (new ApiClient('bi_rpcv2', '', 'abnormal.staff_leave_revoke', $this->lang));
                $bi_rpc->setParams(['audit_id' => $auditId]);
                $bi_return = $bi_rpc->execute();
                if (!isset($bi_return['result'], $bi_return['result']['code']) || $bi_return['result']['code'] !=1) {
                    $this->logger->write_log(['abnormal.staff_leave_revoke 返回结果失败',$auditId]);
                }
            }
            //不接单通知，提交通过后发送消息给 上级+上上级
            if ($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_40) {
                $this->sendMessageToManager($auditDetail);
            }
            //泰国 定制病假 新型病假审核通过 同步更新 子记录
            if($state == enums::APPROVAL_STATUS_APPROVAL && $auditDetail['leave_type'] == enums::LEAVE_TYPE_38){
                $sickServer = new SickServer($this->lang,$this->timezone);
                $sickServer->updateSickState($auditDetail['staff_info_id'],$auditId);
            }
            //带薪病假的 历史数据 撤销操作 要操作额度表
            if($state != enums::APPROVAL_STATUS_APPROVAL && $auditDetail['leave_type'] == enums::LEAVE_TYPE_3) {
                $sickServer = new SickServer($this->lang,$this->timezone);
                $sickServer->returnHistorySick($auditId,$staff_info);
            }
            //审核通过 休息日
            if($state == enums::$audit_status['approved'] && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15){
                $check_param['staff_id'] = $auditDetail['staff_info_id'];
                $check_param['date'] = date('Y-m-d',strtotime($auditDetail['leave_start_time']));
                $this->leave_for_workday($check_param);
            }
            //撤销休息日
            if ($state == enums::$audit_status['revoked']  && $auditDetail['leave_type'] == enums::LEAVE_TYPE_15) {
                $ext_server = new AuditExtendServer($this->lang, $this->timezone);
                $del_param['staff_info_id'] = $auditDetail['staff_info_id'];
                $del_param['date_at'] = date('Y-m-d', strtotime($auditDetail['leave_start_time']));
                $del_param['operate_id'] = $extend['operate_id'] ?? 0;
                $ext_server->cancel_for_leave($del_param);
            }
            //跨国探亲假 和其他假期 非审核通过 操作返还 目前其他类型 只有 带薪事假 陆续兼容
            if (in_array($auditDetail['leave_type'], [enums::LEAVE_TYPE_2]) && $state != enums::$audit_status['approved']) {
                $p['leave_type'] = $auditDetail['leave_type'];
                $p['audit_id'] = $auditId;
                $leave_server->update_leave_days($auditDetail['staff_info_id'],enums::YEAR_RE_BACK,$p);
            }
            //一次性的假期 one time  one send 需要操作staff_leave_remaining_days 表
            if($state != enums::$audit_status['approved'] ){
                if(in_array($auditDetail['leave_type'],$this->one_time)){
                    //delete remain
                    StaffLeaveRemainDaysModel::find([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ])->delete();
                }
                if(in_array($auditDetail['leave_type'],$this->one_send)){
                    //update remain
                    $remain_info = StaffLeaveRemainDaysModel::findFirst([
                        'conditions' => "staff_info_id = :staff_info_id: and leave_type = :leave_type:",
                        'bind' => ['staff_info_id' => $auditDetail['staff_info_id'], 'leave_type' => $auditDetail['leave_type']]
                    ]);
                    if(!empty($remain_info)){
                        $leave_days = $auditDetail['leave_day'];
                        $remain_info->days += $leave_days;
                        $remain_info->leave_days -= $leave_days;
                        $remain_info->updated_at = gmdate('Y-m-d');
                        $remain_info->update();
                    }
                }

                //通用返还额度
                if(in_array($auditDetail['leave_type'],[enums::LEAVE_TYPE_1,enums::LEAVE_TYPE_38,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4])){
                    $leave_server->cancelVacation($auditDetail,$staff_info,$state, $extend);
                }
            }
            //https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
            //请假补卡审批通过同步bi，重算处罚数据
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                //请假
                $sync_server = new SyncServer($this->lang, $this->timezone);
                $params = [
                    'staff_id'         => $auditDetail['staff_info_id'],
                    'leave_start_date' => $auditDetail['leave_start_time'],
                    'leave_end_date'   => $auditDetail['leave_end_time'],
                ];
                $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
            }
            //驳回 超时 发消息push
            $this->sendMessage($auditDetail,$state);
        }
        return true;
    }



    public function getWorkflowExtend($auditData, $staffData)
    {
        $extend['flow_code'] = 'season_1';
        if (in_array($auditData['leave_type'], [enums::LEAVE_TYPE_25, enums::LEAVE_TYPE_26])) {
            $extend['flow_code'] = 'covid';
            $extend['store_id'] = $staffData['sys_store_id'] ?? '';
        }
        return $extend;
    }

    /**
     * @description:获取请假审批流 id
     * @param string $node_department_id
     * @return int   workflow_id
     * @author: L.J
     * @time: 2022/12/13 14:46
     * 新需求 全部接入可视化 https://flashexpress.feishu.cn/docx/VtOmdswjvoCj7SxKipfcfUJanDh
     */
    public function getWorkflowId($staffData)
    {
        return self::LEAVE_WORKFLOW_ID_VIEW;
        $node_department_id = $staffData['node_department_id'] ?? '';
        if (empty($node_department_id)) {
            return self::LEAVE_WORKFLOW_ID;
        }
        //这几个部门包含子部门的走 可视化审批流 49  Flash Commerce、Flash Money、Flash Pay
        //F Commerce[50001] 、Flash Money 30001、Flash Pay  60001
        $dep_ids = [50001, 30001, 60001, 444, 26];
        //获取其子部门
        $SysDepartmentModel = new SysDepartmentModel();
        foreach ($dep_ids as $dep_id) {
            $deptIds = $SysDepartmentModel->getSpecifiedDeptAndSubDept($dep_id);
            $dep_ids = array_merge($dep_ids, $deptIds);
        }
        if (in_array($node_department_id, $dep_ids)) {
            return self::LEAVE_WORKFLOW_ID_VIEW;
        }
        return self::LEAVE_WORKFLOW_ID;
    }


    /**
     * 获取耗材权限
     * @param array paramIn
     * @return boolean
     */
    public function getConsumablesPermission($paramIn = [])
    {
        if (empty($this->staffInfo)) {
            return false;
        }
        // 开始判断申请权限
        $bool     = false;
        if ($this->staffInfo['organization_type'] == 2) {
            return true;
        }
        //权限限制
        $key_code = enums::MATERIAL_WMS_OPEN_RULE;
        $wms_open_rule =  (new StaffRepository())->getOaSettingEnvAuthority($key_code);
        if (!empty($wms_open_rule)) {
            $rule = json_decode($wms_open_rule, true);
            if (!empty($rule['job_ids']) && in_array($this->staffInfo['job_title'], explode(',', $rule['job_ids']))) {
                $bool = true;
            }
        }
        return $bool;
    }


    /**
     * 换算时间
     * 新需求 需根据员工属性 扣除休息日 和ph  6天-找轮休  5天 找周六日
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTime($paramIn = [])
    {
        //[1]参数定义
        //leave type 1 上午 2 下午
        $leaveStartTime = $this->processingDefault($paramIn, 'leave_start_time');
        $leaveStartType = $this->processingDefault($paramIn, 'leave_start_type');
        $leaveEndTime   = $this->processingDefault($paramIn, 'leave_end_time');
        $leaveEndType   = $this->processingDefault($paramIn, 'leave_end_type');
        $leaveType      = $this->processingDefault($paramIn, 'leave_type', 2);
        $type           = $this->processingDefault($paramIn, 'type', 2, 1);//类型 1外部调用 2内部调用
        $staff_id       = $paramIn['staff_id'];


        //[2]换算时间
        if ($leaveEndTime < $leaveStartTime || ($leaveEndTime == $leaveStartTime) && $leaveEndType < $leaveStartType) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }

        //泰国病假 计算请假天数
        if($leaveType == enums::LEAVE_TYPE_38){
            $sickServer = new SickServer($this->lang,$this->timezone);
            [$leave_day,$over_day] = $sickServer->formatLeaveDays($paramIn);
            if ($type == 1) {
                $returnData['data']['day'] = $leave_day;
                //新增加 泰国病假超出30天 返回提示
                if($over_day < 0){
                    $returnData['data']['over_day'] = abs($over_day);
                }
                //新增是否需要人脸比对字段
                $returnData['data']['is_compare'] = false;
                if($leaveStartTime == date('Y-m-d')){
                    $returnData['data']['is_compare'] = true;
                }
                return $this->checkReturn($returnData);
            }
            return $leave_day;
        }


        $leave_day = 0;
        $key       = $start_date = date('Y-m-d', strtotime($leaveStartTime));
        $end_date  = date('Y-m-d', strtotime($leaveEndTime));
        //获取该员工 请假区间的 休息日 和 ph
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        $holidays    = $leaveServer->staff_off_days($staff_id, $leaveStartTime, $leaveEndTime);
        while ($key <= $end_date) {
            if (in_array($key, $holidays) && in_array($leaveType, $this->sub_day)) {
                $key = date("Y-m-d", strtotime("+1 day", strtotime($key)));
                continue;
            }
            $add = 1;
            if ($key == $start_date && $leaveStartType != 1) {
                $add = 0.5;
            }
            if ($key == $end_date && $leaveEndType != 2) {
                $add = 0.5;
            }

            $leave_day += $add;
            $key       = date("Y-m-d", strtotime("+1 day", strtotime($key)));
        }

        if ($type == 1) {
            $returnData['data']['day'] = $leave_day;
            return $this->checkReturn($returnData);
        }
        return $leave_day;
    }


    //本国家 历史存在过的假期类型 列表页显示 历史数据用
    public function type_book_history()
    {
        $data = [
            [
                'code' => '15',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2017')
            ],
            [
                'code' => '16',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2018')
            ],
            [
                'code' => '1',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2003')
            ],
            [
                'code' => '2',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2004'),
                'need_img' => 1
            ],
            [
                'code' => '12',
                'type' => 0,
                'msg'  => $this->getTranslation()->_('2014')
            ],
//            [
//                'code' => '13',
//                'type' => 1,
//                'msg'  => $this->getTranslation()->_('2015')
//            ],
            [
                'code' => '38',//新病假类型 把 带薪病假(3) 和不带薪病假(18) 合并为这一个 历史的 带薪病假 刷成38了
                'type' => 0,
                'msg'  => $this->getTranslation()->_('2005')
            ],
            [
                'code' => '4',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2006')
            ],
            [
                'code' => '5',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2007') //'陪产假',
            ],
            [
                'code' => '17',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2020') //'产检',
            ],
            [
                'code' => '6',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2008')
            ],
            [
                'code' => '7',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2009')
                ,'need_img' => 1
            ],
            [
                'code' => '8',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2010')
            ],
            [
                'code' => '9',
                'type' => 2,
                'msg'  => $this->getTranslation()->_('2011')
            ],
            [
                'code' => '10',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2012')
            ],
            [
                'code' => '11',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2013')
            ],
//            [
//                'code' => '14',
//                'msg'  => $this->getTranslation()->_('2016')
//            ],
            [
                'code' => '18',
                'type' => 2,
                'msg'  => $this->getTranslation()->_('2021')
            ],
            [
                'code' => '19',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('2022')
            ],

            [
                'code' => '25',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('leave_25')
                ,'need_img' => 1
            ],
            [
                'code' => '26',
                'type' => 1,
                'msg'  => $this->getTranslation()->_('leave_26')
                ,'need_img' => 1
            ],

        ];

        $data[] = [
            'code' => '40',
            'type' => 2,
            'msg'  => $this->getTranslation()->_('leave_40')
        ];
        return $data;
    }

    /**
     * 获取薪资周期
     * @param $now
     * @return array
     */
    public function getPayCycle($now = null)
    {
        $timestamp = isset($now) ? strtotime($now):time();
        if (date('j', $timestamp) >= 24){
            $start = date("Y-m-24",$timestamp);
            $end = date("Y-m-23",strtotime("next month",$timestamp));
        }else{
            $start = date("Y-m-24",strtotime("previous month",$timestamp));
            $end = date("Y-m-23",$timestamp);
        }

        return [$start, $end];
    }


}

