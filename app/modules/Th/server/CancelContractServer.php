<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\JobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\CancelContractServer as BaseCancelContractServer;
use FlashExpress\bi\App\Server\MaterialAssetServer;
use FlashExpress\bi\App\Server\StaffServer;

class CancelContractServer extends BaseCancelContractServer
{


    /**
     * @throws BusinessException
     */
    protected function validation($staffInfo, $paramIn): bool
    {
        $leaveDate = $this->processingDefault($paramIn, 'leave_date');
        //校验离职日期小于签署合同日期
        if (strtotime($leaveDate) < strtotime($staffInfo['hire_date'])) {
            throw new BusinessException($this->getTranslation()->_('18830_err_msg_001'));
        }
        return true;
    }


    protected function fullOtherInsertData($auditId, $paramIn): bool
    {
        //插入签名图片
        $imagePathArr[] = $this->processingDefault($paramIn, 'sign_url');

        if (!$imagePathArr) {
            throw new ValidationException('sign url empty!');
        }

        if (!empty($imagePathArr)) {
            $insertImgData = [];
            foreach ($imagePathArr as $image) {
                $insertImgData[] = [
                    'id'         => $auditId,
                    'image_path' => $image,
                ];
            }
            $this->public->batchInsertImgs($insertImgData, 'RESIGN_AUDIT');
        }
        return true;
    }


    public function genSummary(int $auditId, $user)
    {
        $info = StaffResignModel::findFirst([
            "resign_id = :resign_id:",
            "bind" => [
                "resign_id" => $auditId,
            ],
        ]);
        if (!empty($info)) {
            $info  = $info->toArray();
            $param = [
                [
                    'key'   => "th_agent_contract_end_day",
                    'value' => $info['leave_date'],
                ],
            ];
        }
        return $param ?? [];
    }


    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $result                                   = $this->getResignDetail(['resign_id' => $auditId]);
        $staff_info                               = (new StaffRepository())->checkoutStaffById($result['submitter_id']);
        $detailLists                              = [
            'apply_parson' => sprintf('%s ( %s )', $staff_info['name'] ?? '', $staff_info['staff_info_id'] ?? ''),
        ];
        $detailLists['th_agent_last_work_date'] = $result['last_work_date'];
        $detailLists['th_agent_contract_end_day'] = $result['leave_date'];
        if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            $detailLists['agent_reason'] = !empty($result['reason']) ? $this->getTranslation()->_('resign_reason_'.$result['reason']) : '';
            $detailLists['agent_remark'] = $result['remark'] ?? '';
        }
        $detailLists['th_agent_staff_sign'] = $result['image_path'] ?? [];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        //撤销状态，需要显示撤销原因
        if ($result['status'] == enums::$audit_status['revoked']) {
            $detailLists = array_merge($detailLists, ["cancel_reason" => $result['cancel_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        //当前登陆人 信息 个人代理要换文案
        $staffRe = new StaffRepository($this->lang);
        $userInfo = $staffRe->getStaffPosition($user);

        $data                       = [
            'title'         => $this->getTranslation()->_("th_workflow_type_63"),
            'id'            => $result['resign_id'],
            'staff_id'      => $staff_info['staff_info_id'],
            'type'          => (string)AuditListEnums::APPROVAL_TYPE_CANCEL_CONTRACT,
            'created_at'    => $result['created_at'],
            'updated_at'    => $result['updated_at'],
            'approvalLevel' => 0,
            'status_text'   => (new AuditlistRepository($this->lang,
                $this->timezone))->getAuditStatus('10' . $result['status']),
            'serial_no'     => $result['serial_no'] ?? '',
            'user_hire_type'=> $userInfo['hire_type'] ?? 0,
        ];
        $returnData['data']['head'] = $data;
        $staffLeaveInfo             = $this->getStaffLeaveInfo(['staff_info_id' => $result['submitter_id']]);
        if ($result['asset_tag'] == enums::RESIGN_ASSET_TAG_NEW) {
            $new_assets_server = new MaterialAssetServer($this->lang, $this->timezone);

            if (in_array($result['status'], [enums::$audit_status['approved'], enums::$audit_status['timedout']])) {
                $new_assets                             = $new_assets_server->getAuditLeaveAssets(['resign_id' => $result['resign_id']]);
                $staffLeaveInfo['assets_process_state'] = $new_assets['assets_process_state'];
                $staffLeaveInfo['assets']               = $new_assets['assets'];
            } else {
                $result                   = $new_assets_server->getAssetsDetailByStaffId([
                    'staff_id'  => $result['submitter_id'],
                    'page_ize'  => 1000,
                    'page_size' => 1000,
                    'page_num'  => 1,
                ]);
                $staffLeaveInfo['assets'] = $result['data']['items'];
            }
        }
        $returnData['data']['extend']  = $staffLeaveInfo ?? [];
        $returnData['data']['confirm'] = [];
        return $returnData;
    }

    /**
     * 获取操作按钮显示规则
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(false, false, false, false, false, false);
    }
}