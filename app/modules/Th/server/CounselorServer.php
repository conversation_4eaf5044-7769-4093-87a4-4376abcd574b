<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Modules\Th\library\Enums\CommonEnums;
use FlashExpress\bi\App\Server\CounselorServer AS GlobalBaseServer;

class CounselorServer extends GlobalBaseServer
{
    //Bike Courier[13]/<PERSON> Co<PERSON>[110]/Boat Courier[452]/EV Courier[1930]
    protected $frontLineJobTitles = [
        CommonEnums::JOB_TITLE_BIKE_COURIER,
        CommonEnums::JOB_TITLE_VAN_COURIER,
        CommonEnums::JOB_TITLE_EV_VAN_COURIER,
        CommonEnums::JOB_TITLE_VAN_COURIER_PROJECT,
        CommonEnums::JOB_TITLE_BOAT_COURIER,
        CommonEnums::JOB_TITLE_TRICYCLE_COURIER,
    ];

    //DC Officer[37]&Assistant Branch Supervisor[451]
    protected $frontLineJobNonTitles = [
        CommonEnums::JO<PERSON>_TITLE_DC_OFFICER,
        CommonEnums::JO<PERSON>_TITLE_ASSISTANT_BRANCH_SUPERVISOR,
    ];

    protected $branchWorkStaffIds = [CommonEnums::JOB_TITLE_BRANCH_SUPERVISOR];

    protected $seniorWorkStaffIds = [CommonEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR];

    /**
     * 获取职位集合
     * @param $staffJobTitle
     * @return array
     */
    public function getSearchJobTitles($staffJobTitle)
    {
        $staffJobTitle = intval($staffJobTitle);

        if (in_array($staffJobTitle,$this->frontLineJobTitles)) {
            return $this->frontLineJobTitles;
        }

        if ($staffJobTitle == CommonEnums::JOB_TITLE_DC_OFFICER) {
            return $this->frontLineJobNonTitles;
        }

        return [];
    }
}