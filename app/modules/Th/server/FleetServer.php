<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\FleetServer as GlobalBaseServer;



class FleetServer extends GlobalBaseServer
{


    /**
     * 完善线路网点信息
     * @param $t
     * @param $auditType
     * @param $startStore
     * @param $endStore
     * @param $storeIds
     * @return array
     * @throws ValidationException
     */
    protected function fullApplyStoreInfo($t, $auditType, $startStore, $endStore, $storeIds): array
    {
        if (!empty($storeIds)) {
            //检验是否存在重复值
            if (count($storeIds) != count(array_unique($storeIds))) {
                throw new ValidationException($t->_('err_msg_store_repeat'));
            }

            //校验网点个数
            if (count($storeIds) > self::MAX_STORE_NUM || count($storeIds) < self::MIN_STORE_NUM) {
                throw new ValidationException($t->_('err_msg_store_too_much'));
            }

            $startStore = array_shift($storeIds);
            $endStore   = array_pop($storeIds);

            if (!empty($storeIds)) {
                //获取全部申请网点的缩写
                $storeInfos = SysStoreModel::find([
                    'conditions' => "state = 1 and id IN ({store_ids:array})",
                    'bind'       => [
                        'store_ids' => $storeIds,
                    ],
                    'columns'    => 'id,name,short_name',
                ])->toArray();
                if (!empty($storeInfos)) {
                    $shortNameList = array_column($storeInfos, 'short_name', 'id');
                    $shortName     = array_map(function ($v) use ($shortNameList) {
                        return $shortNameList[$v] ?? "";
                    }, $storeIds);
                    $shortName     = implode('-', $shortName);
                }

                //途径网点
                $viaStoreIds = $storeIds;
            }
        }

        return [$startStore, $endStore, $viaStoreIds ?? [], $shortName ?? ''];
    }




}
