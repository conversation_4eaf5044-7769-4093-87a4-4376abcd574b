<?php

namespace FlashExpress\bi\App\Modules\Th\Server\Osm;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeDetailModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeOrderModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\Osm\OutsourcingOTServer as GlobalBaseServer;

class OutsourcingOTServer extends GlobalBaseServer
{
    /**
     * sql 查询
     * @param array $params
     * @param array $columns
     * @param bool $isCount
     * @return array
     */
    public function getOutsourcingOTQuery(array $params, array $columns = [], bool $isCount = false): array
    {
        if (empty($params)) {
            return [];
        }
        $str_column = empty($columns) ? '*' : implode(',', $columns);
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns($str_column);
        $builder->from(['o' => HubOutsourcingOvertimeModel::class]);
        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns($str_column);
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        if (empty($params['is_all'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->orderBy('o.id DESC');
        return $builder->getQuery()->execute()->toArray();
    }
    /**
     * 生成查询条件
     * @param object $builder
     * @param array $params
     * @return object
     */
    public function getBuilderWhere(object $builder, array $params): object
    {
        // osm状态
        if (isset($params['osm_state']) && !empty($params['osm_state'])) {
            $builder->andWhere('o.osm_state = :osm_state:', ['osm_state' => (int)$params['osm_state']]);
        }

        // 外协公司id
        if (isset($params['company_id']) && !empty($params['company_id'])) {
            $builder->leftjoin(HubOutsourcingOvertimeOrderModel::class, "oo.hub_outsourcing_overtime_id=o.id", "oo");
            $builder->leftjoin(HrOutsourcingOrderModel::class, "oo.outsourcing_order_serial_no=hoo.serial_no", "hoo");

            $builder->andWhere('hoo.out_company_id = :company_id:',
                ['company_id' => (int)$params['company_id']]);
        }

        // 加班日期
        if (isset($params['ot_date']) && !empty($params['ot_date'])) {
            $builder->andWhere('o.ot_date = :ot_date:', ['ot_date' => $params['ot_date']]);
        }

        if (isset($params['start_time']) && !empty($params['start_time']) && isset($params['end_time']) && !empty($params['end_time'])) {
            // 1、db中的开始时间落在了传入时间区间的中间
            // 2、db中的结束时间落在了传入时间区间的中间
            // 3、传入的时间区间吧db的开始和结束时间包裹起来了
            // 4、db的时间区间把传入的时间区间包裹起来了
            if (isset($params['source']) && $params['source'] == 'order') {
                $builder->andWhere('(o.start_time >= :start_time: AND o.start_time < :end_time:)
            OR (o.end_time > :start_time: AND o.end_time <= :end_time:)
            OR (o.start_time >= :start_time: AND o.end_time <= :end_time:) 
            OR (o.start_time <= :start_time: AND o.end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            } else {
                $builder->andWhere('(o.start_time >= :start_time: AND o.start_time <= :end_time:)
            OR (o.end_time >= :start_time: AND o.end_time <= :end_time:)
            OR (o.start_time >= :start_time: AND o.end_time <= :end_time:) 
            OR (o.start_time <= :start_time: AND o.end_time >= :end_time:)',
                    ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
            }
        }

        // 审批状态
        if (isset($params['apply_state']) && is_array($params['apply_state']) && !empty($params['apply_state'])) {
            $builder->andWhere('o.apply_state IN ({apply_state:array})', ['apply_state' => $params['apply_state']]);
        }

        return $builder;
    }
}