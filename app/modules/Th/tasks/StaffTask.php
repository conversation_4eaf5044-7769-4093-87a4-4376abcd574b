<?php

namespace FlashExpress\bi\App\Modules\Th\Tasks;

use app\enums\LangEnums;
use app\models\fle\StaffAccount;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;

use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WhiteListServer;
use JsonRPC\Client;
use function GuzzleHttp2\uri_template;

class StaffTask extends \BaseTask{

    /**
     * 泰国
     * 短信缺卡提醒
     *
     * @param $params
     * 每天泰国时间中午12点跑
     * 时间 staff_id
     */
    public function clock_in_messageAction($params)
    {
        $date = date('Y-m-d', strtotime("-1 days"));
        if (isset($params[0])) {
            $date = $params[0];
        }
        $params_staff_ids = [];
        if (isset($params[1])) {
            $params_staff_ids = explode(',', $params[1]);
        }

        $page = 1;
        $pageSize = 100;

        $start_date = date('Y-m-d',strtotime($date . ' -1 days'));

        //获取打卡白名单
        $hcm_rpc_result = (new WhiteListServer())->attendanceList(['start_date'=>$start_date,'end_date'=>$date]);

        if (isset($hcm_rpc_result['error'])) {
            $this->logger->write_log('获取白名单失败!,重新跑任务吧 ' . $hcm_rpc_result['error'], 'error');
            exit();
        }

        $attendance_white_list = $hcm_rpc_result['result']['data'] ?? [];

        do {
            if ($params_staff_ids) {

                $attendanceDatas = AttendanceDataV2Model::find([
                    'columns' => 'staff_info_id, group_concat(stat_date) as stat_date',
                    'conditions' => ' staff_info_id in ({staff_ids:array}) and stat_date in ({stat_date:array}) and AB != 0 ',
                    'bind' => [
                        'staff_ids' => $params_staff_ids,
                        'stat_date' => [$date, $start_date]
                    ],
                    'group' => 'staff_info_id',
                    'offset' => ($page - 1) * $pageSize,
                    'limit' => $pageSize

                ])->toArray();
            } else {

                $attendanceDatas = AttendanceDataV2Model::find([
                    'columns' => 'staff_info_id, group_concat(stat_date) as stat_date',
                    'conditions' => ' stat_date in ({stat_date:array}) and AB != 0 ',
                    'bind' => [
                        'stat_date' => [$date, date('Y-m-d', strtotime($date . ' -1 days'))]
                    ],
                    'group' => 'staff_info_id',
                    'offset' => ($page - 1) * $pageSize,
                    'limit' => $pageSize

                ])->toArray();
            }

            $staffIds = array_column($attendanceDatas, 'staff_info_id');

            if ($staffIds) {
                $staffAccount = StaffAccountModel::find([
                    'conditions' => ' staff_info_id in ({staff_ids:array}) and equipment_type in ({equipment_types:array}) order by updated_at asc',
                    'bind' => ['staff_ids' => $staffIds, 'equipment_types' => [LangEnums::$equipment_type['kit'], LangEnums::$equipment_type['backyard']]]
                ])->toArray();
                $staffAccount = array_column($staffAccount, null, 'staff_info_id');

                $staffsInfo = HrStaffInfoModel::find(['conditions'=> "staff_info_id in  ({staff_info_id:array}) and state = 1 ",
                    'bind'=>['staff_info_id'=> $staffIds ],
                    'columns' => 'mobile,staff_info_id,week_working_day,sys_store_id,hire_type',
                ])->toArray();
                $staffsInfo = array_column($staffsInfo, null, 'staff_info_id');

                $staffAuditReissueForBusinesses = StaffAuditReissueForBusinessModel::find([
                    'conditions' => ' staff_info_id in ({staff_ids:array}) and attendance_date in ({dates:array}) ',
                    'bind' => [
                        'staff_ids' => $staffIds,
                        'dates' => [$date, date('Y-m-d', strtotime($date . ' -1 days'))]
                    ]
                ])->toArray();
                $staffAuditReissueForBusinessesGrpByStaffId = [];
                foreach ($staffAuditReissueForBusinesses as $staffAuditReissueForBusiness) {
                    $staffAuditReissueForBusinessesGrpByStaffId[$staffAuditReissueForBusiness['staff_info_id']][$staffAuditReissueForBusiness['attendance_date']] = $staffAuditReissueForBusiness;
                }

                foreach ($attendanceDatas as $attendanceData) {

                    if (isset($staffAccount[$attendanceData['staff_info_id']])) {
                        if (in_array($staffAccount[$attendanceData['staff_info_id']]['accept_language'], ['th', 'th-CN'])) {
                            $this->lang = 'th';
                        } else {
                            $this->lang = $staffAccount['accept_language'] ?? 'th';
                        }
                    } else {
                        $this->lang = 'th';
                    }
                    if (!isset($staffsInfo[$attendanceData['staff_info_id']]) || !$staffsInfo[$attendanceData['staff_info_id']]['mobile'] || $staffsInfo[$attendanceData['staff_info_id']]['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                        continue;
                    }

                    $dates = $this->revertDate($attendanceData['stat_date'], $staffAuditReissueForBusinessesGrpByStaffId[$attendanceData['staff_info_id']] ?? [],$attendance_white_list,$attendanceData['staff_info_id']);
                    $dates = array_values(array_filter($dates));

                    if ($dates) {

                        if (substr($this->lang, 0, 2) == 'zh') {

                            $message_content = sprintf($this->getTranslation()->_('clock_in_alter_message'), implode(', ', $dates));
                        } else {
                            if (count($dates) == 2) {

                                $message_content = sprintf($this->getTranslation()->_('clock_in_alter_message_2'), $dates[0], $dates[1]);
                            } else {

                                $message_content = sprintf($this->getTranslation()->_('clock_in_alter_message_1'), $dates[0]);
                            }
                        }

                        $param = ['mobile' => $staffsInfo[$attendanceData['staff_info_id']]['mobile'], 'msg' => $message_content, 'type'=>3 ,'code'=> $this->lang, "delay"=> 0];


                        $this->send_message($param);
                    }
                }
            }

            $staffIds = [];
            $page++;
        } while ($attendanceDatas);

    }


    private function revertDate($date, $staffAuditReissueForBusiness,$attendance_white_list=[],$staff_info_id=0) {
        $staffAuditReissueForBusinessDates = array_column($staffAuditReissueForBusiness, 'attendance_date');

        $dates = explode(',', $date);
        $returnDates = [];
        foreach ($dates as $date) {
            if (!in_array($date, $staffAuditReissueForBusinessDates)
                && !in_array($staff_info_id, $attendance_white_list[$date]['type_paid_locally'])
                && !in_array($staff_info_id,$attendance_white_list[$date]['type_not_paid_locally'])
            ) {

                $returnDates[] = date('d/m/Y', strtotime($date));
            }
        }

        return $returnDates;
    }


    public function send_push($param)
    {
        if(empty($param))
            return false;
        $i = 2;
        $logger   = $this->getDi() -> get('logger');
        $fle_rpc = (new ApiClient('bi_rpc','','push_to_staff', $this->lang));

        while ($i) {
            try {

                $fle_rpc->setParams($param);
                $ret = $fle_rpc->execute();


                if ($ret) {
                    $logger->write_log('员工号：' . $param['staff_info_id'] . '发送push成功', 'debug');
                    break;
                } else {
                    $logger->write_log('员工号：' . $param['staff_info_id'] . '发送push失败', 'error');
                    $i--;
                }
                //endregion
            } catch (Exception $e) {
                //region 记录日志
                $log    = array(
                    'file'  => $e->getFile(),
                    'line'  => $e->getLine(),
                    'code'  => $e->getCode(),
                    'msg'   => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                );
                $res    = ['request' => $param, 'response' => '', 'exception' => $log];
                $logger = $this->getDi() -> get('logger');
                $logger->write_log(json_encode($res, JSON_UNESCAPED_UNICODE));
                //endregion
                $i--;
            }
        }
        return true;
    }


    public function send_message($param){
        if(empty($param))
            return false;
        // 获取当前语言
        $locale = $this->lang;

        // 实例化jsonRPC接口
        $url = env('api_send_sms','http://192.168.0.230:8003/rpc/sms/');
        $client = new Client($url, false);

        $data_params = [['locale' => $locale,'src' => 'by:客户提示短信'], $param];

        try {

            // 获取Api回调数据
            $result = $client->execute('send', $data_params);
            //region 记录日志

            //追加日志
            $this->logger->write_log(sprintf("[staff task][send_message] rpc request : %s, response : %s", json_encode($data_params), json_encode($result)), "info");

            if(isset($result) && !empty($result)){
                return true;
            }

            if(isset($result['error']) && !empty($result['error'])){
                $logger = $this->getDI()->get('logger');
                $logger->write_log('client send rpc fail:'.$result['error']['message'], 'error');
                return false;
            }
            //endregion
        } catch (Exception $e) {

            //region 记录日志
            $log = array(
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $res = ['request' => $data_params, 'response' => '', 'exception' => $log];
            $logger = parent::$DI->get('logger');
            $logger->write_log(json_encode($res, JSON_UNESCAPED_UNICODE), 'info');
            //endregion
        }
        return false;
    }



}


