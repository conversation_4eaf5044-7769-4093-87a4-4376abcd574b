<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 3/9/22
 * Time: 3:02 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\Controllers\MilesController as BaseMilesController;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Server\MilesServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class MilesController extends BaseMilesController
{

    /**
     * 上传里程 视频
     * @return Response|ResponseInterface|null
     * @throws ValidationException
     */
    public function upload_videoAction()
    {
        $param              = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $service = new MilesServer($this->timezone, $this->lang);
        $res     = $service->milesLogicV2('mileage.upload_video', $param);
        return $this->jsonReturn($this->checkReturn($res));
    }

    /**
     * 验证车牌号
     * @throws ValidationException
     */
    public function check_plate_numberAction()
    {
        $param             = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];
        $service           = new MilesServer($this->timezone, $this->lang);
        $res               = $service->milesLogicV2('mileage.check_plate_number', $param);
        return $this->jsonReturn($this->checkReturn($res));
    }

    /**
     * 获取车牌号
     * @throws ValidationException
     */
    public function get_plate_numberAction()
    {
        $param             = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];
        $service           = new MilesServer($this->timezone, $this->lang);
        $res               = $service->milesLogicV2('mileage.get_plate_number', $param);
        return $this->jsonReturn($this->checkReturn($res));
    }
    /**
     * 里程上报 创建
     * @throws ValidationException
     */
    public function createAction()
    {
        $param = $this->paramIn;
        //增加拦截验证
        $setVal = (new SettingEnvServer())->getSetVal('punch_out_switch');
        $server = new MilesServer($this->lang, $this->timezone);
        if (!empty($setVal) && $param['mileage_record_type'] == $server::MILES_END_TYPE) {
            $check = $this->mile_check_punch_out($param);
            if (!empty($check) && $check['code'] == '-3') {
                //里程 fbi 那边返回-3 了 客户端区分不开
                $check['code'] = '-4';
                return $this->jsonReturn($check);
            }
        }
        $param['user_info'] = $this->userinfo;
        $service            = new MilesServer($this->timezone, $this->lang);
        $res                = $service->addMileageData($param);
        return $this->jsonReturn($this->checkReturn($res));
    }


    /**
     * 获取里程信息
     * @throws ValidationException
     */
    public function get_infoAction()
    {
        $params         = $this->request->get();
        $params['date'] = $this->request->get('mileageDate');;
        $params['staff_id']  = $this->userinfo['id'];
        $params['user_info'] = $this->userinfo;
        $service             = new MilesServer($this->timezone, $this->lang);
        $res                 = $service->getMilesInfo($params);
        return $this->jsonReturn($this->checkReturn($res));
    }


}