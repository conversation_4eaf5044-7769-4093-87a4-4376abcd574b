<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\WorkPassModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Modules\Ph\Server\WorkPassServer;
use Exception;
use FlashExpress\bi\App\Server\SettingEnvServer;

class WorkPassController extends BaseController
{

    const limit = 3;
    const redis_prefix = 'work_pass_';

    public function initialize()
    {
        parent::initialize();
        $this->server = ['workpass' => new WorkPassServer($this->lang, $this->timezone)];
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    //获取workpass
    public function getPassAction()
    {
        try {
            $paramIn['type'] = $this->request->get('type'); //当前登陆用户id  员工ID
            $validations     = [
                "type" => "IntIn:1,2|>>>:type".$this->getTranslation()->_('error_message'), //1:展示 2：更新
            ];

            //验证
            $this->validateCheck($paramIn, $validations);

            //当前登陆用户信息
            $param['user_info'] = $this->userinfo;
            $staff_id           = $this->userinfo['staff_id'];

            //展示
            $work_pass = [];
            if ($paramIn['type'] == 1) {
                $work_pass = $this->server['workpass']->getWorkPass($staff_id); //读取员工workpass记录

                if (!$work_pass) {
                    $work_pass = $this->server['workpass']->addWorkPass($staff_id); //不存在则新增一条记录
                }
            }


            //续期
            if ($paramIn['type'] == 2) {
                if ($this->getLimitNum($staff_id)) {
                    $this->getDI()->get('logger')->write_log('一天只能更新一次 staff_id'.$staff_id, 'info');
                    $this->jsonReturn(['code' => 0, 'message' => 'Limit updates once a day', 'data' => '']);
                };

                //记录不存在或者无效，则需要生成一条新纪录
                $work_pass = $this->server['workpass']->addWorkPass($staff_id);
                if ($work_pass) {
                    $res = $this->setLimitNum($staff_id);
                    if ($res) {
                        $this->getDI()->get('logger')->write_log('workpass 缓存更新成功 '.$staff_id, 'info');
                    } else {
                        $this->getDI()->get('logger')->write_log('workpass 缓存更新失败 '.$staff_id, 'notice');
                        $this->jsonReturn(['code' => 0, 'message' => 'Operation is error', 'data' => '']);
                    }
                }
            }


            unset($work_pass['id']);
            unset($work_pass['deleted']);

            //读取个人信息
            $work_pass['name'] = $param['user_info']['name'];
            $staff_model       = new StaffRepository($this->lang);
            //职位
            $info = $staff_model->getStaffPosition($staff_id);

            $work_pass['position']             = $info ? $info['job_name'] : 'none';
            $work_pass['name_en']              = $info ? $info['name_en'] : 'none';
            $work_pass['updated_at']           = $this->conversionDate(date('Y-m-d H:i:s',
                strtotime($work_pass['updated_at']) + 28800));
            $work_pass['expiration_date']      = $this->conversionDate($work_pass['expiration_date']);
            $work_pass['created_at']           = $this->conversionDate(date('Y-m-d H:i:s',
                strtotime($work_pass['created_at']) + 28800));

            //[1]入参
            $ac = new ApiClient('hcm_rpc', '', 'get_staff_certificate_info' ,$this->lang);
            $ac->setParams([
                "staff_info_id" => $staff_id,
            ]);
            $ac_result = $ac->execute();
            if($ac_result["result"]['code'] == 1) {
                //整理数据
                $data = $ac_result["result"]['data'];

            } else {
                //记日志 获取薪资信息失败u
                $this->logger->write_log("get_staff_certificate_info_failed : {$staff_id}", 'notice');
            }

            //代表人信息
            $work_pass['proveDownloadSetting']['url'] = $data['labor_sign_url'] ?? '';
            $work_pass['proveDownloadSetting']['name'] = $data['labor_name'] ?? '';
            $work_pass['proveDownloadSetting']['job_title'] = $data['labor_job_title'] ?? '';

            // 公司信息
            $work_pass['header_company_name']       = $data['header_company_name'] ?? '';
            $work_pass['header_logo_url']           = $data['header_logo_url'] ?? '';
            $work_pass['footer_content']            = $data['footer_content'] ?? '';
            $work_pass['content_company_name']      = $data['content_company_name'] ?? '';

            $this->jsonReturn(['code' => 1, 'message' => 'Operation is successful', 'data' => $work_pass]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage());
            $this->jsonReturn($this->checkReturn(-1));
        }
    }

    /**
     * 转义指定语言格式
     * @param date $date
     * @return string
     */
    private function conversionDate($date)
    {
        if ($this->lang == 'zh') {
            $y = date('Y', strtotime($date));
            $m = date('m', strtotime($date));
            $d = date('d', strtotime($date));
            return $y.' 年 '.$m.' 月 '.$d.' 日 ';
        } else {
            $y = date('Y', strtotime($date));
            $m = date('M', strtotime($date));
            $d = date('d', strtotime($date));
            return $y.'-'.$m.'-'.$d;
        }
    }


    /**
     * 更新次数是否超过限制数量
     * @param $staff_id
     * @return bool
     */
    private function getLimitNum($staff_id)
    {
        $key = self::redis_prefix.$staff_id;

        $redis = $this->getDI()->get('redisLib');
        return $redis->get($key);
    }

    /**
     * 更新缓存数据
     * @param $staff_id
     * @return bool
     */
    private function setLimitNum($staff_id)
    {
        $key = self::redis_prefix.$staff_id;

        $redis = $this->getDI()->get('redisLib');
        //有限期截止当天
        $current_time = strtotime(date("Y-m-d H:i:s"), time());
        $limit_time   = strtotime(date("Y-m-d 00:00:00", strtotime('+1 day')));

        $effective_time = $limit_time - $current_time;
        $res            = $redis->set($key, 1, $effective_time);
        if ($res) {
            $this->getDI()->get('logger')->write_log("workpass 缓存更新成功 {$key}", 'info');
            return true;
        } else {
            $this->getDI()->get('logger')->write_log("workpass 缓存更新失败 {$key}", 'notice');
            return false;
        }
    }
}
