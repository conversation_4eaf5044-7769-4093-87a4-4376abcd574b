<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\Controllers\JobtransferController AS BaseJobtransferController;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Modules\Ph\Server\JobtransferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class JobtransferController extends BaseJobtransferController
{

    /**
     * @var mixed|string
     */
    private $bhubCategory;
    private $bulkyCategory;
    public function initialize()
    {
        parent::initialize();
        //这里是hub 部门的可选网点类型
        $bhubCategory = (new SettingEnvServer())->getSetVal('jobtransfer_bhub_category');
        $this->bhubCategory     = empty($bhubCategory) ? (UC("jobtransfer")["bhub_category"] ?? "") : $bhubCategory;
        //这里是Network Bulky 的可选网点类型
        $bulkyCategory = (new SettingEnvServer())->getSetVal('jobtransfer_bulky_category');
        $this->bulkyCategory     =  empty($bulkyCategory) ? (UC("jobtransfer")["bulky_category"] ?? "") : $bulkyCategory;

    }

    /**
     * 更新转岗
     * @param int       audit_id        审批ID
     * @param int       status          审批状态
     * @param string    reject_reason   驳回原因
     * @return string
     */
    public function updateJobtransferAction()
    {
        try {
            //[1]入参校验
            $paramIn                               = $this->paramIn;
            $paramIn['staff_id']                   = $this->userinfo['staff_id'];
            $paramIn['name']                       = $this->userinfo['name'];
            $paramIn['positions']                  = $this->userinfo['positions'];
            $paramIn['organization_id']            = $this->userinfo['organization_id'];
            $paramIn['organization_type']          = $this->userinfo['organization_type'];
            $paramIn['param']['position_category'] = $this->userinfo['positions'];
            $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
            $paramIn['userinfo']                   = $this->userinfo;
            $logger                                = $this->getDI()->get('logger');

            $validations = [
                "audit_id"      => "Required|Int",
                "status"        => "Required|Int",
                "reject_reason" => "Required|StrLenGeLe:0,500",
            ];

            //获取转岗后职位
            if (!isset($paramIn['audit_id']) || empty($paramIn['audit_id'])) {
                $this->validateCheck($paramIn, $validations);
            }

            if ($paramIn['status'] == enums::APPROVAL_STATUS_CANCEL) {
                $returnArr = (new JobtransferServer($this->lang, $this->timezone))->cancelJobtransfer($paramIn['audit_id'], $paramIn['reject_reason'], $paramIn['staff_id']);
                $this->jsonReturn($returnArr);
            }

            //校验是否HRBP已经审批薪资
            $detailInfo = JobTransferModel::findFirst($paramIn['audit_id']);
            if(empty($detailInfo )){
                throw new \Exception('data error');
            }
            $detailInfo = $detailInfo->toArray();
            //校验是否HRBP已经审批薪资
            //是HRBP需要审批角色、薪资
            //非HRBP提交了薪资并且有待审批
            if (isset($paramIn['positions']) && in_array(68, $paramIn['positions']) ||
                isset($detailInfo['after_base_salary']) && !empty($detailInfo['after_base_salary'])) { //当前登录人为hrbp或已经提交了薪资


                $codeList = [
                    8 => 'base_salary',
                    9 => 'exp_allowance',
                    10=> 'position_allowance',
                    11=> 'car_rental',
                    12=> 'trip_payment',
                    13=> 'notebook_rental',
                    15=> 'food_allowance',
                    16=> 'dangerous_area',
                    17=> 'house_rental',
                    24=> 'performance_allowance',
                    25=> 'island_allowance',
                    26=> 'deminimis_benefits',
                    27=> 'other_non_taxable_allowance',
                    28=> 'other_taxable_allowance',
                    29=> 'phone_subsidy',

                ];

                $codeJobTransfer = [
                    //19=> 'role_ids',
                    //20=> 'upload_files',
                    21=> 'hc_expiration_date',
                    22=> 'car_owner',
                    23=> 'rental_car_cteated_at',
                ];
                if (isset($paramIn['extend']) && is_array($paramIn['extend']) && $paramIn['extend']) {
                    foreach ($codeList as $key => $value) {
                        if (isset($paramIn['extend']['code' . $key]) && $paramIn['extend']['code' . $key]) {
                            $paramIn[$value] = $paramIn['extend']['code' . $key]['value'];
                        }
                    }

                    //上传图片
                    //角色
                    //hc过期日期
                    if (isset($paramIn['status']) && $paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL) {
                        foreach ($codeJobTransfer as $key => $value) {
                            if (isset($paramIn['extend']['code' . $key]) && $paramIn['extend']['code' . $key]) {
                                $paramIn[$value] = $paramIn['extend']['code' . $key];
                            }
                        }
                    }
                }

                //如果HRBP已经审批薪资
                //则检验薪资信息
                switch ($detailInfo['after_position_id']) {
                    case enums::$job_title['van_courier']:
                    case enums::$job_title['bike_courier']:
                        $validations = array_merge($validations, [
                            'car_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area' => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    case enums::$job_title['dc_officer']:
                    case enums::$job_title['assistant_branch_supervisor']:
                    case enums::$job_title['shop_officer']:
                    case enums::$job_title['shop_cashier']:
                    case enums::$job_title['hub_staff']:
                    case enums::$job_title['warehouse_staff']:
                    case enums::$job_title['warehouse_staff_sorter']:
                    case enums::$job_title['mini_cs_officer']:
                    case enums::$job_title['store_officer']:
                        $validations = array_merge($validations, [
                            'exp_allowance'   => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental' => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    case enums::$job_title['branch_supervisor']:
                    case enums::$job_title['shop_supervisor']:
                    case enums::$job_title['store_supervisor']:
                    case enums::$job_title['hub_supervisor']:
                        $validations = array_merge($validations, [
                            'position_allowance'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'dangerous_area'      => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                    default:
                        $validations = array_merge($validations, [
                            'position_allowance'  => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'notebook_rental'     => 'IfIntEq:status,2|IntGeLe:0,99999',
                            'house_rental'        => 'IfIntEq:status,2|IntGeLe:0,99999',
                        ]);
                        break;
                }
                $validations = array_merge($validations, [
                    'base_salary'    => 'IfIntEq:status,2|IntGeLe:1,99999|>>>:' . $this->getTranslation()->_('job_transfer_err'),
                    'food_allowance' => 'IfIntEq:status,2|IntGeLe:0,99999',
                ]);
                //如果是hrbp校验角色、车辆所属
                if (isset($paramIn['positions']) && in_array(68, $paramIn['positions']) &&
                    isset($paramIn['status']) && $paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL
                ) {
                    $vehicleJobTitle =   explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ?: JobTransferEnums::TRANSFER_DEFAULT_JOB_TITLE;
                    if (in_array($detailInfo['after_position_id'],$vehicleJobTitle)) {
                        $validations = array_merge($validations, [
                            'car_owner'  => 'Required|IntIn:1,2,3',//1：个人车辆、2：公司车辆、3：借用车辆
                            'rental_car_cteated_at'  => 'IfIntEq:car_owner,2|Required|Date|>>>:'.$this->getTranslation()->_('vehicle_info_0009'),//用车时间
                        ]);
                    }

                    $validations = array_merge($validations, [
                        'role_ids'    => 'Required|ArrLenGe:1|>>>:' . $this->getTranslation()->_('jobtransfer_0034'),
                        'upload_files' => 'Required|ArrLenGe:1|>>>:' . $this->getTranslation()->_('2106'),
                    ]);

                    $validations = array_merge($validations, [
                        "after_working_day_rest_type" => "Required|Int",
                    ]);
                }

            }
            //电话补贴 根据职级
            if (isset($paramIn['extend']) && is_array($paramIn['extend']) && $paramIn['extend'] && isset($paramIn['extend']['code29'])) {

                $paramIn['phone_subsidy'] = $paramIn['extend']['code29']['value'];
                $staffInfo =  HrStaffInfoModel::findFirst([
                                                              'conditions' => 'staff_info_id = :staff_info_id:',
                                                              'bind' => ['staff_info_id' => $detailInfo['staff_id']]
                                                          ]);
                if(empty($staffInfo)){
                    throw new \Exception('The staff does not exist');
                }
                $limit = $staffInfo->job_title_grade_v2 >= 16 ? 1000:500;
                $validations = array_merge($validations,['phone_subsidy'  => "Required|IntIn:0,{$limit}|>>>:".$this->getTranslation()->_('phone_subsidy_0_'.$limit)]);

            }

            $this->validateCheck($paramIn, $validations);

            //[2]业务处理
            $returnArr = (new JobtransferServer($this->lang, $this->timezone))->updateJobtransfer($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $logger->write_log("updateJobtransferAction:异常信息:" . $e->getMessage() . $e->getTraceAsString() ,'info');
            }else{
                $logger->write_log("updateJobtransferAction:异常信息:" . $e->getMessage() . $e->getTraceAsString(), 'notice');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

	/**
	 * 添加转岗信息
	 * @Access  public
	 * @Param   request
	 * @Return  json
	 */
	public function addJobtransferAction()
	{
		try {
			$paramIn             = $this->paramIn;
			$paramIn['userinfo'] = $this->userinfo ?? [];
			if (!$paramIn['userinfo']) {
				throw new \Exception($this->getTranslation()->_('jobtransfer_0004'),enums::$ERROR_CODE['1005']);
			}
			$objJobtransfer = new   JobtransferServer($this->lang, $this->timezone);
			//可选转岗日期为当日后1天-90天内的任意日期
			$startDate = date('Y-m-d', time() + 1 * 86400);
			$endDate   = date('Y-m-d', time() + 90 * 86400);
			//校验参数
			$validations = [
				"staff_id"              => "Required|Int",
				"type"                  => "Required|Int",
				"after_department_id"   => "Required|Int",
				"after_store_id"        => "Required|Str",
				"after_position_id"     => "Required|Int",
				"after_role_ids"        => "Required",
				"hc_id"                 => "Required|Int",
				"after_date"            => "Required|DateFromTo:{$startDate},{$endDate}",
				"job_handover_staff_id" => "Required|Int",
				"reason"                => "Required|StrLenGeLe:10,500|>>>:" . $this->getTranslation()->_('jobtransfer_0001'),
                "after_working_day_rest_type" => "Required|Int",
			];
			$this->validateCheck($paramIn, $validations);

            $reids_key  = 'lock_addJobtransfer_' . $this->userinfo['staff_id'].'_'.$paramIn['staff_id'];
            //[2]业务处理
            $returnArr = $this->atomicLock(function () use ($paramIn,$objJobtransfer) {
                return $objJobtransfer->addJobtransfer($paramIn);
            }, $reids_key, 10, false);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('5202')));
            }

			$this->jsonReturn($returnArr);
		} catch (\Exception $e) {
			if ($e->getCode() == '1000') {
				$this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage(),'info');
			}elseif($e->getCode() == enums::$ERROR_CODE['1005']) {
				$this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage(),'notice');
			}else{
				$this->getDI()->get('logger')->write_log("AddJobtransferAction:异常信息:" . $e->getMessage());
			}
			$this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
		}
	}


	/**
	 * 转岗获取网点下拉列表
	 * @Access  public
	 * @Param   request
	 * @Return  json
	 */
	public function getStoreListAction()
	{
		try {
			//[1]传入参数
			$paramIn        = $this->paramIn;
			$objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
			$param          = [];
			//[2]校验参数
			$validations = [
				"department_id" => "Required|Int",
			];
			$this->validateCheck($paramIn, $validations);
			$departmentId   = $paramIn["department_id"] ?? "";
            $store_data = [];
			//获取部门列表
			$model = new FleSysDepartmentModel();
			$networkDeptIds = $model->getNetworkDepartmentIds();
			$shopDeptIds = $model->getShopDepartmentIds();
			$hubDeptIds = $model->getHubDepartmentIds();
			$bhubDeptIds = $model->getBHubDepartmentIds();
			$networkBulkyDepIds =  $model->getNetworkBulkyDepartmentIds();
			if (in_array($departmentId, $networkDeptIds)) {
				//Network Management
				$param = [
					"category" => $this->networkCategory,
				];
			} elseif (in_array($departmentId, $shopDeptIds)) {
				//Shop Project
				$param = [
					"category" => $this->shopCategory,
				];
			} elseif (in_array($departmentId, $hubDeptIds)) {
				//Hub
				$param = [
					"category" => $this->hubCategory,
				];
                // hub 要增加-1
                $store_data[] = ["id"=> '-1', "name"=> 'Head Office'];
			} elseif (in_array($departmentId, $bhubDeptIds)){
				//B Hub
				$param = [
					"category" => $this->bhubCategory,
				];
			} elseif (in_array($departmentId, $networkBulkyDepIds)){
				//Network Bulky
				$param = [
					"category" => $this->bulkyCategory,
				];
			}

			//获取网点下拉列表
			$data = $objJobtransfer->getStoreList($param);
            if(!empty($data) || !empty($store_data)){
                $data['data'] = array_merge($data['data'] ?? [],$store_data);
            }
			$this->jsonReturn(self::checkReturn(["data" => $data]));
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log("getStoreListAction:异常信息" . $e->getMessage());
			$this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
		}
	}

    /**
     * 转岗验证审批人是否满足条件
     * @throws \Exception
     */
    public function checkTransferOptimizationAction()
    {
        //[1]获取参数
        $paramIn = $this->paramIn;
        $curStaffId = $this->userinfo['id'] ?? '';
        $storeId = $this->userinfo['organization_type'] == 1 ? $this->userinfo['organization_id'] : -1;
        $objJobtransfer = new JobtransferServer($this->lang, $this->timezone);
        $jobTsfStatus = new JobtransferRepository($this->timezone);
        $sysList = new SysStoreServer($this->timezone);
        $staffId = $paramIn["staff_id"] ?? "";
        //是否与申请人属于同一网点或属于管辖范围
        //网点负责人：限制可为负责的网点内员工转岗
        //DM：限制可为负责的片区内员工转岗
        //AM：限制可为负责的大区内员工转岗
        //网点负责人 & hub supervisor & 片区经理
        //校验员工信息
        //不能给自己申请转岗
        //申请调岗员工需要在职
        try {
            //[2]校验
            $validations = [
                "staff_id" => "Required|Int|>>>:" . $this->getTranslation()->_('please_entry_staff_no'),
            ];
            $this->validateCheck($this->paramIn, $validations);
            //获取转岗员工信息
            $transferInfo = $objJobtransfer->getTransferInfo([
                                                                 "staff_id" => $staffId,
                                                             ]);
            //网点获取片区和大区的id
            $storeRegionPieceId = $sysList->getStoreRegionPiece($transferInfo['sys_store_id']);
            //转岗人和审核人不能是相同的人，并且在职
            if ($staffId == $curStaffId || !$transferInfo || $transferInfo["state"] != enums::$service_status['incumbency']) {
                $this->logger->write_log("不在职", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
            }
            //job_transfer表中拿到数据相关员工的转岗状态
            $jobTsfStatus = $jobTsfStatus->getJobTransferAuditOptimization([
                                                                               'jt_staff_id' => $staffId
                                                                           ]);
            //验证是否是为转岗
            if (isset($jobTsfStatus['approval_state']) && isset($jobTsfStatus['state']) && (
                    $jobTsfStatus['approval_state'] == enums::$audit_status['panding'] ||
                    $jobTsfStatus['approval_state'] == enums::$audit_status['approved'] && $jobTsfStatus['state'] == enums::$job_transfer_state['to_be_transfered']
                )
            ) {
                $this->logger->write_log("验证是否是为转岗", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0027'), enums::$ERROR_CODE['1000']);
            }
            //获取审核人一级部门
            $reviewerStaffInfo = $objJobtransfer->getTransferInfo([
                                                                      'staff_id' => $curStaffId
                                                                  ]);
            //如果是 Hub management 部门
            //获取 - Hub Management
            $settingEnvServer = new SettingEnvServer();
            $dept_hub_management_id = $settingEnvServer->getSetVal('dept_hub_management_id');

            //判断是否与申请人属于同一一级部门
            if ($transferInfo['sys_department_id'] !== $reviewerStaffInfo['sys_department_id']) {
                $this->logger->write_log("  一级部门不一样的审核人和转岗人被pass", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
            }

            //校验转岗前网点 & 部门是否存在hrbp如果不存在，则不能申请
            $hrbp = (new WorkflowServer($this->lang, $this->timezone))->findHRBP($transferInfo['node_department_id'], ["store_id" => $transferInfo['sys_store_id']]);
            if (empty($hrbp)) {
                $this->logger->write_log("不存在hrbp", 'info');
                throw new \Exception($this->getTranslation()->_('jobtransfer_0039'), enums::$ERROR_CODE['1000']);
            }

            $result['data'] = false; // true 是有权限

            //[网点负责人]被转岗人所在网点是申请人的下辖网点的
            $manageStoreIds = $sysList->checkOutletManager($curStaffId);

            //2. hub management及下级各部门部门负责人：限制可为“负责的部门”内员工申请转岗
            $hub_management_ancestry_v3 = '999/222/1/25';
            $hub_management = SysDepartmentModel::findFirst([
                                                                "conditions" => " id = :id:",
                                                                'bind'       => ['id' => $dept_hub_management_id],
                                                            ]);
            if($hub_management){
                $hub_management_ancestry_v3 = $hub_management->ancestry_v3;
            }
            $sysDeptInfo=SysDepartmentModel::find([
                                                      "conditions" => " ancestry_v3 like :ancestry_v3: or ancestry_v3 = :ancestry_v3_id: ",
                                                      'bind' =>[
                                                          'ancestry_v3' => $hub_management_ancestry_v3."/%",
                                                          'ancestry_v3_id' => $hub_management_ancestry_v3
                                                      ],
                                                  ])->toArray();
            $sysDeptInfo = array_column($sysDeptInfo,'manager_id');
            if (in_array($curStaffId,$sysDeptInfo)){
                //获取申请人所有管辖的部门
                $curStaffDepf=SysDepartmentModel::find(
                    [
                        "conditions" => " manager_id= :manager_id:",
                        "bind"=>[
                            "manager_id" => $curStaffId
                        ],
                        "columns"=>"id,ancestry_v3"
                    ]
                )->toArray();
                foreach ($curStaffDepf as $key=>$item){
                    if(strpos($item['ancestry_v3'],$hub_management_ancestry_v3 )===false){
                        unset($curStaffDepf[$key]);
                    }
                }
                $staffIdDepf=SysDepartmentModel::findFirst(
                    [
                        "conditions" => " id= :node_department_id:",
                        "bind"=>[
                            "node_department_id" => $transferInfo['node_department_id']
                        ],
                        "columns"=>"id,ancestry_v3"
                    ]
                );
                if($staffIdDepf){
                    $staffIdDepf=$staffIdDepf->toArray();
                    foreach ($curStaffDepf as $item){
                        if(is_numeric(strpos($staffIdDepf['ancestry_v3'],$item['ancestry_v3']))){
                            $result['data'] = true;
                        }
                    }
                    $this->logger->write_log("hub不是负责的员工", 'info');

                    if (!empty($manageStoreIds)){
                        if (! in_array($transferInfo["sys_store_id"], $manageStoreIds)) {
                            $this->logger->write_log("hub不是负责的员工 被转岗人所在网点是申请人的下辖网点的", 'info');
//                            throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
                        } else {
                            $result['data'] = true;
                        }
                    }

//                    throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
                }
            }
            //获取片区经理的管理片区
            //获取大区经理的管理片区
            $pieces = $objJobtransfer->getManagerPieces($curStaffId);
            $regions = $objJobtransfer->getManagerRegions($curStaffId);
            if (isset($regions) && is_array($regions)) {
                if (!in_array($storeRegionPieceId['manage_region'], $regions)) {
                    //被转岗人与大区经理不在同一个大区
                    $this->logger->write_log("被转岗人与大区经理不在同一个大区", 'info');
                } else {
                    $result['data'] = true;
                }
            }
            if (isset($pieces) && is_array($pieces)) {
                if (!in_array($storeRegionPieceId['manage_piece'], $pieces)) {
                    //被转岗人与片区经理不在同一个片区、
                    $this->logger->write_log("被转岗人与片区经理不在同一个片区", 'info');
                } else {
                    $result['data'] = true;
                }
            }
            //[网点负责人]被转岗人所在网点是申请人的下辖网点的
            if (!empty($manageStoreIds)){
                if (! in_array($transferInfo["sys_store_id"], $manageStoreIds)) {
                    $this->logger->write_log("被转岗人所在网点是申请人的下辖网点的", 'info');
                } else {
                    $result['data'] = true;
                }
            }
            //Hub Supervisor职位：限制仅可为所属网点内员工转岗
            if ($reviewerStaffInfo['job_title'] == enums::$job_title['hub_supervisor']){
                if ($transferInfo["sys_store_id"] != $storeId) {
                    $this->logger->write_log("被转岗人与Hub Supervisor职位不在一个网点", 'info');
                }else{
                    $result['data'] = true;
                }
            }
            if($result['data']){
                $this->jsonReturn($this->checkReturn(['data' => true]));
            }
            throw new \Exception($this->getTranslation()->_('jobtransfer_0018'), enums::$ERROR_CODE['1000']);
        } catch (\Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("checkStaffInfoAction:异常信息:" . $e->getMessage(), 'info');
            } else {
                $this->getDI()->get('logger')->write_log("checkStaffInfoAction:异常信息:" . $e->getMessage());
            }
            $this->jsonReturn($this->checkReturn(-3, $e->getMessage()));
        }
    }

}