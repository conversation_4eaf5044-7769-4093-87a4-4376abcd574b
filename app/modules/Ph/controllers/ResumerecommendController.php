<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Server\ResumeRecommendServer;

class ResumerecommendController extends BaseController
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 简历数据源配置初始化
     */
    public function getResumeInitAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        //[2]业务处理
        try {
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getResumeRecommendInit($paramIn);
            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (BusinessException $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        } catch (Exception $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:getResumeInit" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        }
    }

    /**
     * 简历提交接口
     */
    public function resumeRecommendSubmitAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        try {
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->resumeRecommendSubmit($paramIn, $userinfo);
            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (Exception $e) {
            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:resumeRecommendSubmit" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }

    /**
     *保存简历暂存接口
     */
    public function createResumeScratchAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        try {
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->createResumeScratch($paramIn, $userinfo);
            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (BusinessException $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        } catch (Exception $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        }
    }

    /**
     * 推荐简历列表
     */
    public function getResumeRecommendListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        try {
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getResumeRecommendList($paramIn, $userinfo);
            //[3]数据返回
            $this->jsonReturn(['code' => 1, 'data' => $returnArr, 'msg' => '']);
        } catch (Exception $e) {
            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:getResumeRecommendListAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString());
            $this->jsonReturn(self::checkReturn(-3, 'server error'));
        }
    }


    /**
     * 待提交推荐简历删除
     */
    public function deleteAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        //[2]业务处理
        try {

            if (empty($paramIn['recommend_id'])) {
                $this->jsonReturn(['code' => 1, 'data' => [], 'msg' => 'params err not empty']);
            }
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->delete($paramIn);
            //[3]数据返回
            $this->jsonReturn($returnArr);

        } catch (BusinessException $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        } catch (Exception $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        }
    }

    /**
     * 获取简历详情
     */
    public function getResumeInfoAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        try {

            if (empty($paramIn['recommend_id'])) {
                $this->jsonReturn(['code' => 1, 'data' => [], 'msg' => 'params err not empty']);
            }
            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getInfo($paramIn, $userinfo);
            //[3]数据返回
            $this->jsonReturn(['code' => 1, 'data' => $returnArr, 'msg' => '']);

        } catch (BusinessException $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        } catch (Exception $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        }
    }

    /**
     * 获取hc关联网点
     */
    public function getStoreListAction(){
        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        //[2]业务处理
        try {

            $returnArr = (new ResumeRecommendServer($this->lang, $this->timezone))->getStoreHc($paramIn, $userinfo);
            //[3]数据返回
            $this->jsonReturn(['code' => 1, 'data' => $returnArr, 'msg' => '']);
        } catch (BusinessException $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'info');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        } catch (Exception $e) {

            $this->getDI()->get("logger")->write_log("ERROR_INFO  E_File:" . $e->getFile() . " E_Line:" . $e->getLine() . "E_function:createResumeScratchAction" . " E_Msg: " . $e->getMessage() . " E_Trace: " . $e->getTraceAsString(), 'error');
            $this->jsonReturn(self::checkReturn(-3, 'server error'));

        }
    }
}