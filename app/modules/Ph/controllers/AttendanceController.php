<?php
namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\Ph\Server\AttendanceServer;
use FlashExpress\bi\App\Modules\Ph\Server\CheckPunchOutServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Exception;
use FlashExpress\bi\App\Controllers\AttendanceController as BaseAttendanceController;
use FlashExpress\bi\App\Server\AttendanceImageVerifyServer;

class AttendanceController extends BaseAttendanceController
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 人脸照片对比接口
     */
    public function image_verifyAction()
    {
        $parameters                  = $this->paramIn;
        $headerData                  = $this->request->getHeaders();
        $parameters['device']        = $this->processingDefault($headerData, 'User-Agent');
        $parameters['user_info']     = $this->userinfo;
        $parameters['staff_info_id'] = $this->userinfo['id'];
        (new AttendanceImageVerifyServer($this->lang))->verifyImageUseLock($parameters);
        $this->jsonReturn(['code' => ErrCode::SUCCESS, 'message' => "success", 'data' => null]);
    }
}
