<?php
/**
 * Created by PhpStor<PERSON>.
 * User: nick
 * Date: 11/3/21
 * Time: 8:01 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\WorkflowEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\SupportOvertimeBackupModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Modules\Ph\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\OvertimeServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use FlashExpress\bi\App\Modules\Ph\enums\OvertimeEnums;

class OvertimeServer extends GlobalBaseServer
{


    //超时加班 只能是 上班之后 申请晚上下班之后的 对应时长 需要动态获取
    public $overOt = [
        HrOvertimeModel::OVERTIME_1 => [],
        HrOvertimeModel::OVERTIME_3 => [],
        HrOvertimeModel::OVERTIME_5 => [],
        HrOvertimeModel::OVERTIME_7 => [],
        HrOvertimeModel::OVERTIME_9 => [],
        HrOvertimeModel::OVERTIME_11 => [],
    ];

    //全天加班 上班时间开始就可以申请的 节假日 或者 休息日加班 对应时长 是固定的
    public $holidayOt = [
        HrOvertimeModel::OVERTIME_2 => [],
        HrOvertimeModel::OVERTIME_4 => [],
        HrOvertimeModel::OVERTIME_6 => [],
        HrOvertimeModel::OVERTIME_8 => [],
        HrOvertimeModel::OVERTIME_10 => [],
    ];

    public $staffInfo = [];//staff re 方法 getStaffPosition 返回值

    /**
     * 获取加班类型
     * @Access  public
     * @Param   request $paramIn
     * @Param   request $userinfo
     * @Return  jsonData
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        $data = $this->getAllOtType();
        $returnData['data']['dataList'] = $data;
        if (!empty($paramIn['is_svc'])) {//bi工具 或者其他 不需要限制的 直接调用
            return $this->checkReturn($returnData);
        }

        //重新查一下 缓存的不是最新的
        $staff_re = new StaffRepository($this->lang);
        if(empty($this->staffInfo)){
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        //获取有权限的类型和时长
        $data = $this->getTypeDuration($data);

        if ($userinfo && isset($userinfo['store_category']) && in_array($userinfo['store_category'], [
                enums::$stores_category['hub'],
                enums::$stores_category['bhub'],
                enums::$stores_category['os'],
            ])
        ) {
            $returnData['data']['template_type'] = 1;
        } else {

            $returnData['data']['template_type'] = 2;
        }
        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    /**
     * 获取加班类型，用于hcm-api系统展示
     */
    public function getOvertimeTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }
        if(empty($this->staffInfo)){
            $this->staffInfo = (new StaffRepository($this->lang))->getStaffPosition($paramIn['staff_id']);
        }
        if ($this->staffInfo['formal'] == 0){
            $os_data = (new OsOvertimeServer($this->lang, $this->timezone))->getTypeOsOvertime($paramIn);
            $data['data']['dataList'] = $os_data['data']['dataList'];
        }else{
            $data = $this->getTypeOvertime($paramIn);
        }
        return $data['data']['dataList'] ?? [];
    }

    //所有加班类型展示 这里面不做权限判断
    public function getAllOtType($locale = '')
    {

        $new_duration = [
            ['time_hour' => 4, 'time_text' => $this->getTranslation($locale)->_('ot_4_new_text')],
            ['time_hour' => 8, 'time_text' => $this->getTranslation($locale)->_('ot_8_new_text')],
        ];
        $new_customer = [
            ['time_hour' => 2, 'time_text' => '2h'],
            ['time_hour' => 3, 'time_text' => '3h'],
            ['time_hour' => 4, 'time_text' => '4h'],
            ['time_hour' => 5, 'time_text' => '5h'],
            ['time_hour' => 6, 'time_text' => '6h'],
            ['time_hour' => 7, 'time_text' => '7h'],
            ['time_hour' => 8, 'time_text' => '8h'],
        ];
        return [
            [
                //工作日加班1.25倍日薪
                'code' => '1',
                'msg' => $this->getTranslation($locale)->_('5123'),
                'sub_msg' => $this->getTranslation($locale)->_('1.25_times_salary'),
                'duration' => $new_customer,
            ],
            [
                //休息日加班1.3倍日薪
                'code' => '2',
                'msg' => $this->getTranslation($locale)->_('5113'),
                'sub_msg' => $this->getTranslation($locale)->_('1.3_times_salary'),
                'duration' => $new_duration,
            ],
            [
                //休息日超时加班1.69倍日薪
                'code' => '3',
                'msg' => $this->getTranslation($locale)->_('5114'),
                'sub_msg' => $this->getTranslation($locale)->_('1.69_times_salary'),
                'duration' => $new_customer,
            ],
            [
                //RH加班1倍日薪
                'code' => '4',
                'msg' => $this->getTranslation($locale)->_('5115'),
                'sub_msg' => $this->getTranslation($locale)->_('1_time_salary'),
                'duration' => $new_duration,
            ],
            [
                //RH超时加班2.6倍日薪
                'code' => '5',
                'msg' => $this->getTranslation($locale)->_('5116'),
                'sub_msg' => $this->getTranslation($locale)->_('2.6_times_salary'),
                'duration' => $new_customer,
            ],
            [
                //SH加班1.3倍日薪
                'code' => '6',
                'msg' => $this->getTranslation($locale)->_('5117'),
                'sub_msg' => $this->getTranslation($locale)->_('1.3_times_salary'),
                'duration' => $new_duration,
            ],
            [
                //SH超时加班1.69倍日薪
                'code' => '7',
                'msg' => $this->getTranslation($locale)->_('5118'),
                'sub_msg' => $this->getTranslation($locale)->_('1.69_times_salary'),
                'duration' => $new_customer,
            ],
            [
                //RH+休息日加班1.6倍日薪
                'code' => '8',
                'msg' => $this->getTranslation($locale)->_('5119'),
                'sub_msg' => $this->getTranslation($locale)->_('1.6_times_salary'),
                'duration' => $new_duration,
            ],
            [
                //RH+休息日超时加班3.38倍日薪
                'code' => '9',
                'msg' => $this->getTranslation($locale)->_('5120'),
                'sub_msg' => $this->getTranslation($locale)->_('3.38_times_salary'),
                'duration' => $new_customer,
            ],
            [
                //SH+休息日加班1.5倍日薪
                'code' => '10',
                'msg' => $this->getTranslation($locale)->_('5121'),
                'sub_msg' => $this->getTranslation($locale)->_('1.5_time_salary'),
                'duration' => $new_duration,
            ],
            [
                //SH+休息日超时加班1.95倍日薪
                'code' => '11',
                'msg' => $this->getTranslation($locale)->_('5122'),
                'sub_msg' => $this->getTranslation($locale)->_('1.95_times_salary'),
                'duration' => $new_customer,
            ],
        ];

    }

    //给审批流历史用的 类型数据
    public function getApprovalOtType($locale = '')
    {
        return $this->getAllOtType($locale);
    }

    /**
     * 创建延时审批的审批流
     * @param $auditType
     * @param $auditValue
     * @return bool
     */
    public function createDelay($auditType, $auditValue)
    {
        $approvalServer  = new ApprovalServer($this->lang, $this->timezone);
        $extServer       = new OvertimeExtendServer($this->lang, $this->timezone);
        $staffRepository = new StaffRepository();

        $this->getDI()->get('db')->begin();
        $overtimeInfo = HrOvertimeModel::findFirst("overtime_id = " . $auditValue);
        if (empty($overtimeInfo)) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log(sprintf('createDelay not valid id:%s', $auditValue), 'notice');
            return true;
        }
        $staffInfo = $staffRepository->getStaffPosition($overtimeInfo->staff_id);
        if (empty($staffInfo)) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log('createDelay staff id not exists:' . $overtimeInfo->staff_id, 'notice');
            return true;
        }
        $storeInfo             = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $staffInfo['sys_store_id']],
        ]);
        $storeInfo             = empty($storeInfo) ? [] : $storeInfo->toArray();
        $extParam['storeInfo'] = $storeInfo;

        //nw 部门审批详情页特有的字段
        $nwNewDetail = $extServer->nwNewDetail($staffInfo, $overtimeInfo, $extParam);
        $references = json_decode($overtimeInfo->references, true);
        $references = array_merge($references, $nwNewDetail);
        $overtimeInfo->references = json_encode($references, JSON_UNESCAPED_UNICODE);
        $overtimeInfo->save();
        $this->logger->write_log(sprintf('createDelay (id = %d) update references: %s', $auditValue, json_encode($references)), 'info');

        $applyData = AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
            'bind'       => [
                'biz_type'  => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'biz_value' => $auditValue,
            ],
            'for_update' => true,
        ]);
        if(empty($applyData)){
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("createDelay audit_apply表没数据 {$auditValue}" , 'notice');
            return true;
        }
        if ($applyData->getState() != enums::APPROVAL_STATUS_PENDING) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("createDelay audit_apply表审批状态非待审批 {$auditValue}" , 'notice');
            return true;
        }
        if ($applyData->getDelayState() != WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log("createDelay audit_apply表延迟状态非待创建 {$auditValue}" , 'notice');
            return true;
        }
        $params = [
            'model'      => WorkflowEnums::WORKFLOW_CREATE_MODEL_DELAY_STAGE_2,
            'audit_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME,
            'audit_id'   => $auditValue,
            'user'       => $applyData->getSubmitterId(),
        ];
        try {
            $res = $approvalServer->createEx($params);
            if ($res === false) {
                $this->getDI()->get('db')->rollback();
                $this->logger->write_log(sprintf("createDelay 延时审批数据 %s failure,rollback", json_encode($params)), 'notice');
                return false;
            }
        } catch (\Exception $e) {
            $this->getDI()->get('db')->rollback();
            $this->logger->write_log(sprintf("createDelay 延时审批数据 %s err: ". $e->getMessage() . ",rollback", json_encode($params)), 'notice');
            return false;
        }
        $this->getDI()->get('db')->commit();
        return true;
    }

    //部分定制的逻辑 针对 类型时长不同 定制逻辑
    protected function formatSpecialTypeBook($data){
        //如果 符合 Network Management[125]部门及子部门下的DC Officer[37]和DC Supervisor[16] 时长走配置 https://flashexpress.feishu.cn/docx/IDXcdNP0Eoe6wUxYixwcV9kSnBg
        $departmentIds = (new DepartmentRepository($this->lang))->getDepartIds(enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH);
        $jobTitles = [OvertimeEnums::$job_title['dc_officer'],OvertimeEnums::$job_title['dc_supervisor']];
        if (in_array($this->staffInfo['node_department_id'], $departmentIds)
            && in_array($this->staffInfo['job_title'], $jobTitles)
        ) {
            $hours          = (new SettingEnvServer())->getSetVal('nw_ot_hour');
            $arr['hours']   = explode(',', $hours);
            $arr['default'] = [2, 3, 4];
            $hours          = $this->formatHour($arr, HrOvertimeModel::$limitOverHour);
            foreach ($data as &$da) {
                if (in_array($da['code'], array_keys($this->overOt))) {
                    $da['duration'] = $hours;
                }
            }
        }

        //NW部门Van Courier、Bike Courier、Tricycle Courier、DC Officer、 DC Supervisor职位可选择的休息日1.3倍OT时长 https://flashexpress.feishu.cn/docx/NtSrdErvgo9mqyx8nFycxbo6nBb
        $jobTitles = [
            OvertimeEnums::$job_title['van_courier'],
            OvertimeEnums::$job_title['bike_courier'],
            OvertimeEnums::$job_title['tricycle_courier'],
            OvertimeEnums::$job_title['dc_officer'],
            OvertimeEnums::$job_title['dc_supervisor'],
        ];
        if (in_array($this->staffInfo['node_department_id'], $departmentIds)
            && in_array($this->staffInfo['job_title'], $jobTitles)
        ) {
            $hours          = (new SettingEnvServer())->getSetVal('off_day_ot_hours');
            $arr['hours']   = explode(',', $hours);
            $arr['default'] = [8];
            $hours          = $this->formatHour($arr, HrOvertimeModel::$limitNormalHour, true);
            foreach ($data as &$da) {
                if ($da['code'] == HrOvertimeModel::OVERTIME_2) {
                    $da['duration'] = $hours;
                }
            }
        }

        return $data;
    }

    //针对定制的小时枚举 处理逻辑
    protected function formatHour($arr, $limitHour, $isNormal = false)
    {
        $hours   = $arr['hours'];
        $default = $arr['default'];
        if (empty($hours)) {
            $hours = $default;
        }
        $hourData = [];
        $unique   = [];
        $normalDurationArr = OvertimeEnums::$normalDurationText;
        //['time_hour' => 2, 'time_text' => '2h'],
        foreach ($hours as $h) {
            if (empty((int)$h)) {
                continue;
            }
            if (!in_array((int)$h, $limitHour)) {
                continue;
            }
            if (in_array((int)$h, $unique)) {
                continue;
            }
            $unique[]   = (int)$h;
            $text = "{$h}h";
            if($isNormal){
                $text = $this->getTranslation()->_($normalDurationArr[$h]);
            }
            $hourData[] = ['time_hour' => (int)$h, 'time_text' => $text];
        }
        //整理之后没有符合条件的时长 用默认的
        if (empty($hourData)) {
            foreach ($default as $h) {
                $text = "{$h}h";
                if ($isNormal) {
                    $text = $this->getTranslation()->_($normalDurationArr[$h]);
                }
                $hourData[] = ['time_hour' => (int)$h, 'time_text' => $text];
            }
        }
        return $hourData;
    }


    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $this->param = $paramIn;
        $staffId     = $this->processingDefault($paramIn, 'staff_id', 2);
        $type        = $this->processingDefault($paramIn, 'type', 2);
        $start_time  = $this->processingDefault($paramIn, 'start_time');
        $reason      = $this->processingDefault($paramIn, 'reason');
        $duration    = floatval($paramIn['duration']);
        $date        = $this->processingDefault($paramIn, 'date_at');
        $reason      = addcslashes(stripslashes($reason), "'");

        if (empty($date) || empty($start_time) || empty($type)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        //拼接endtime
        $startTime = strtotime($start_time);
        $start_time = date('Y-m-d H:i:s', $startTime);
        $endTime = empty($end_time) ? 0 : strtotime($end_time);
        if (!empty($endTime)) {
            $end_time = date('Y-m-d H:i:s', $endTime);
        } else {
            $endTime = $startTime + floatval($duration) * 3600;
            $end_time = date('Y-m-d H:i:s', $endTime);
        }

        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        $staff_re = new StaffRepository($this->lang);
        $staffInfo = $this->staffInfo = $staff_re->getStaffPosition($staffId);
        $shiftServer = new HrShiftServer();
        $shift_info = $shiftServer->getShiftInfos($staffId, [$date]);
        $shift_info = $shift_info[$date] ?? [];

        $this->checkTypeDuration();

        if (empty($paramIn['is_bi'])) {
            // 新需求 nw 部门 网点员工 指定类型 逻辑 定制 https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
            if ($staffInfo['sys_department_id'] == enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH
                && $staffInfo['organization_type'] == enums::$organization_type['ORGANIZATION_TYPE_1']
                && in_array($type, array_keys($this->holidayOt))
            ) {
                $this->checkNwPeriod($type, $date);
            } else {
                $this->checkOTTimePeriod($type, $shift_info, $date);

            }
        }

        $ext_server = new OvertimeExtendServer($this->lang, $this->timezone);
        [$this->reference, $extend, $this->detailData] = $ext_server->getReference($staffId, $type, $date);


        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $shift_info;
        $check_data = $this->checkOvertime($paramIn);

        if ($check_data['code'] != 1) {
            return $check_data;
        }


        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = '';//bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state = 1;
        if (!empty($paramIn['is_bi'])) {
            $higher = $paramIn['operator'];
            $state = 2;
        }
        $serialNo = $this->getID();
        $insertParam = [
            'staff_id' => $staffId,
            'type' => $type,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'reason' => $reason,
            'reject_reason' => '',
            'state' => $state,
            'duration' => $check_data['data']['duration'],
            'higher_staff_id' => $higher,
            'is_anticipate' => $check_data['data']['is_anticipate'],
            'date_at' => $date,
            'references' => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
            'detail_data' => '',//详情页用的展示数据
            'serial_no' => (!empty($serialNo) ? 'OT' . $serialNo : null),
            'wf_role' => 'ot_new',
        ];

        //新增字段 是否延时审批
        $insertParam['is_delay'] = (int)$this->checkNwApproval($insertParam);

        $overtime_re = new OvertimeRepository($this->timezone);
        $db = $this->getDI()->get('db');
        $db->begin();
        $overtimeId = $overtime_re->addOvertime($insertParam);

        if (!empty($paramIn['is_bi'])) { //bi添加加班
            $db->commit();
            return $this->checkReturn(['data' => ['overtime_id' => $overtimeId]]);
        }

        //没id  数据库异常 一般不走到这
        if (empty($overtimeId)) {
            $db->rollback();
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }

        try {

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);

            $createParams = [
                'audit_id'   => $overtimeId,
                'audit_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                'user'       => $staffId,
                'model'      => $insertParam['is_delay']
                    ? WorkflowEnums::WORKFLOW_CREATE_MODEL_DELAY_STAGE_1
                    : WorkflowEnums::WORKFLOW_CREATE_MODEL_NORMAL,
            ];
            $requestId = $server->createEx($createParams);
            if (!$requestId) {
                throw new \Exception('创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn(['data' => ['overtime_id' => $overtimeId]]);
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("add_overtime 审批流创建异常 {$staffId}" . $e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }


    /**
     * 校验除 时间范围内的 其他逻辑 添加ot 和bi 的 修改ot
     * 加班类型
     * 1-工作日加班1.25倍日薪,
     * 2-休息日加班1.3倍日薪,
     * 3休息日超时加班1.69倍日薪,
     * 4-RH加班1倍日薪,
     * 5-RH超时加班2.6倍日薪,
     * 6-SH加班1.3倍日薪,
     * 7-SH超时加班1.69倍日薪,
     * 8-RH+休息日加班1.6倍日薪,
     * 9-RH+休息日超时加班3.38倍日薪,
     * 10-SH+休息日加班1.5倍日薪,
     * 11-SH+休息日超时加班1.95倍日薪
     * @param $paramIn
     * @throws \Exception
     */
    public function checkOvertime($paramIn)
    {
        $staffId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $type       = $this->processingDefault($paramIn, 'type', 2);
        $start_time = $this->processingDefault($paramIn, 'start_time');
        $duration   = floatval($paramIn['duration']);
        $date       = $this->processingDefault($paramIn, 'date_at');

        $startTime = strtotime($start_time);
        $endTime = $startTime + $duration * 3600;
        //新增逻辑 加班申请 验证是否存在 支援 并且 支援班次开始时间和申请时间不一样要提示
        $limitTypes = [HrOvertimeModel::OVERTIME_2];//只针对 1。3倍
        $this->checkOtSupport($paramIn, $limitTypes);

        $current_date = date('Y-m-d', time());//今天日期
        $start_date = date('Y-m-d', $startTime);//申请开始时间日期

        //新需求 OT申请开始时间不得晚于申请日期次日中午12点。
        $_date = date('Y-m-d 12:00:00', strtotime("+1 day", strtotime($date)));
        $_diffdate = strtotime($_date);

        if ($startTime > $_diffdate) {
            throw new ValidationException($this->getTranslation()->_('ot_time_limit'));
        }

        $staff_model = new StaffRepository();
        $u_info = $this->staffInfo;//add 方法 已经查过 其他方法调用再查 比如 工具 编辑
        if(empty($u_info)){
            $u_info = $staff_model->getStaffPosition($staffId);
        }

        if (empty($u_info)) {
            throw new ValidationException('can not find staff');
        }
        //新需求 网点总部逻辑 按照 该员工工作日期判断 5天为总部 6天为网点
        $organization_type = $u_info['organization_type'];
        $work_days = empty($u_info['week_working_day']) ? 5 : $u_info['week_working_day'];

        //开始时间的日期不能早于申请日期 只试用于凌晨加班 的跨天 且不能超过两天
        $sub = (strtotime($start_date) - strtotime($date)) / 3600;
        if ($start_date < $date) {
            $this->logger->write_log("checkOvertime  {$staffId} wrong_date [-1]" ,'info');
            throw new ValidationException($this->getTranslation()->_('wrong_date'));
        }
        if ($sub >= 48) {
            $this->logger->write_log("checkOvertime  {$staffId} wrong_date [-2]" ,'info');
            throw new ValidationException($this->getTranslation()->_('wrong_date'));
        }

        //如选择“周末和假期加班”，系统验证该申请日期是否为公休日和周末，如申请日期非周末和假期
        $holidays = ThailandHolidayModel::find()->toArray();
        $rh = $sh = [];
        foreach ($holidays as $v) {
            if ($v['holiday_type'] == 1) {
                $rh[] = $v['day'];
            } else {
                $sh[] = $v['day'];
            }
        }

        $overtimeRepo = new OvertimeRepository($this->timeZone);
        $is_workdays = $overtimeRepo->get_workdays($staffId , $date);//当天是否是轮休日
        $is_rest = !empty($is_workdays);

        //校验休息日
        $this->checkRestDay($type, $is_rest, $rh, $sh, $date);
        //判断是预申请还是 补申请
        $is_anticipate = 0;
        if ($start_date >= $current_date) {
            $is_anticipate = 1;//是预申请
        }

        //看是否是补申请 并且 获取该日期打卡时间 校验加班时间 是否在打卡时间之内
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        $add_hour = $this->config->application->add_hour;

        //转换零时区
        $start = date('Y-m-d H:i:s', $startTime - $add_hour * 3600);
        $end = date('Y-m-d H:i:s', $endTime - $add_hour * 3600 - 300);
        $att_info = $att_model->getAttendanceInfo($staffId, $start, $end);

        //打卡间隔时长 新需求 增加考勤容错率 5分钟 由小时 改为分钟
        $attendance_last = 0;
        $allowed_min = 5;
        if (!empty($att_info)) {
            $attendance_last = floor((strtotime($att_info['end_at']) - strtotime($att_info['started_at'])) / 60);
        }//上下班打卡间隔时长

        //如果是当天申请 且有打卡记录 视为 补申请
        if ($is_anticipate == 1 && !empty($att_info)) {
            $is_anticipate = 0;
        }

        //所选日期没有上班打卡记录和下班打卡记录 并且是补申请；
        if (empty($att_info) && $is_anticipate == 0) {
            throw new ValidationException($this->getTranslation()->_('1101'));
        }

        // 校验实际 加班 时长
        $act_last = $duration;
        if ($act_last >= 24) {
            throw new ValidationException($this->getTranslation()->_('overtime_24'));
        }

        if ($act_last <= 0) {
            throw new ValidationException($this->getTranslation()->_('5101'));
        }


        $ext_server = new OvertimeExtendServer($this->lang, $this->timeZone);
        $audit_re = new AuditRepository($this->lang);

        if (in_array($type, array_keys($this->overOt))) {
            //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
            $new_check = $ext_server->extend_check($paramIn, $u_info);
            if ($new_check['code'] != 1) {
                return $new_check;
            }

//            if ($act_last < 2) {
//                throw new ValidationException($this->getTranslation()->_('overtime_act_last'));
//            }

            if ($organization_type == 2) {
                if ($duration > 8) {
                    throw new ValidationException($this->getTranslation()->_('overtime_department_allowed'));
                }
            }

            //新增逻辑
            /**
             *  所选的开始时间+所选OT时长不得晚于实际的下班打卡时间 ---根据开始结束时间区间获取打卡记录 可以解决
             * 如果当天无请假，需要当天出勤超过11小时（9+2）,提示：当日出勤时长不足11小时，不能申请OT！
             * 如果当天请假半天，需要当天出勤超过6小时（4+2），提示：当日出勤时长不足6小时，不能申请OT！
             * 如果当天请假1天（且无打卡），不可以申请OT  提示：当天请假，不能申请OT！
             */
            //补申请 且有打卡记录 或者当天已打卡申请 都算补申请
            if ($is_anticipate == 0) {//补申请 判断请假 验证时长 关联请假
                //获取当天是否有请假记录
                $leave_param['staff_id'] = $staffId;
                $leave_param['day'] = $att_info['attendance_date'];
                $leave_info = $audit_re->getLevelData($leave_param);
                if (!empty($leave_info)) {
                    //0 未请假 1 上午请假 2 下午请假 3 全天请假
                    if ($leave_info['level_state'] == 1 || $leave_info['level_state'] == 2) {
                        if ($attendance_last < (5 * 60 - $allowed_min)) {
                            throw new ValidationException($this->getTranslation()->_('overtime_attendance_limit_5'));
                        }
                    }
                } else {//没有请假 判断是否大于11小时
                    if ($attendance_last < (10 * 60 - $allowed_min)) {
                        throw new ValidationException($this->getTranslation()->_('overtime_attendance_limit_10'));
                    }
                }

                //节假日加班 校验打卡时长 所选时长必须小于或等于实际出勤计算的时长，否则不能提交。
                if (($act_last * 60 - $allowed_min) > $attendance_last) {
                    throw new ValidationException($this->getTranslation()->_('overtime_limit'));
                }
            }

        }

        //节假日上班类型
        if (in_array($type, array_keys($this->holidayOt))) {
            //申请时，选择开始时间和结束时间，开始日期和结束日期必须为同一天或相邻；
            if ($act_last >= 24) {
                throw new ValidationException($this->getTranslation()->_('overtime_24'));
            }

            //补申请
            if ($is_anticipate == 0) {
                //时长与打卡记录不符 不能申请
                if (($act_last * 60 - $allowed_min) > $attendance_last) {
                    throw new ValidationException($this->getTranslation()->_('overtime_limit'));
                }
            }

            //新增需求 Flash Philippines Network 部门的 网点员工 如果加班类型是 4，6 并且没配置轮休 提示信息 不用申请加班 系统自动  https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
            if ($u_info['sys_department_id'] == enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH && $u_info['organization_type'] == 1) {
                $leave_server = new LeaveServer($this->lang, $this->timezone);
                $phDays = $leave_server->ph_days($u_info);
                $phDays = empty($phDays) ? [] : array_column($phDays, 'day');
                $workDays = HrStaffWorkDayModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and date_at = :date:',
                    'bind' => ['staff_id' => $staffId, 'date' => $date],
                ]);
                if (in_array($date, $phDays) && empty($workDays)) {
                    throw new ValidationException($this->getTranslation()->_('ot_toast_46'));
                }
            }

            //新增需求 nw 1.3倍 验证每月可申请时长限制 工具除外
            if(empty($paramIn['is_edit']) && empty($paramIn['is_bi']) && $u_info['sys_department_id'] == enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH){
                $this->checkNwLimitHour($paramIn);
            }
        }


        //校验 同一天是否存在 有交集的ot
        if (empty($paramIn['is_edit'])) {
            $is_contain = $ext_server->check_ot_record($staffId, $date, $startTime, $endTime, $type);
            if ($is_contain['code'] != 1) {
                return $is_contain;
            }
        }

        $res['data']['duration'] = $act_last;
        $res['data']['is_anticipate'] = $is_anticipate;
        return $this->checkReturn($res);

    }


    /**
     * 校验加班起止时间
     * @param int $overtime_type
     * @param array $shift_info
     * @param string $date
     * @return array
     * @throws \Exception
     */
    public function checkOTTimePeriod($overtime_type, $shift_info, $date)
    {
        $date_tmp = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'), time());

        //休息日加班、regular holidays加班、special holidays加班、regular holidays+休息日加班、special holidays+休息日加班
        //ot 可以多两天时间区间限制
        if (in_array($overtime_type, array_keys($this->holidayOt))) {
            $behindTime = strtotime(date("Y-m-d", strtotime("+2 day")));
        }

        // 根据生效日期取新或旧班次
        $start = $shift_info['start'] ?? '';
        $end   = $shift_info['end'] ?? '';

        //如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$start}");
        $shift_end = strtotime("{$date} {$end}");
        if ($shift_start > $shift_end && in_array($overtime_type, array_keys($this->overOt))) {
            $beforeTime = strtotime(date("Y-m-d", strtotime("-1 day")));
        }

        if ($date_tmp < $beforeTime || $date_tmp > $behindTime) {
            $typeList = $this->getAllOtType();
            $typeArr = array_column($typeList, 'msg', 'code');

            //在许可时间外申请，提示错误
            if (in_array($overtime_type, array_keys($this->holidayOt))) {
                $notice_str = str_replace('{type}', $typeArr[$overtime_type],
                    $this->getTranslation()->_('err_msg_ot_over_2_days'));
            } else {
                if ($overtime_type == 1) {
                    $notice_str = $this->getTranslation()->_('err_msg_ot_only_cur_day');
                } else {
                    $notice_str = str_replace('{type}', $typeArr[$overtime_type],
                        $this->getTranslation()->_('err_msg_ot_only_rest_day'));
                }
            }

            throw new ValidationException($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }
    }

    //定制化 菲律宾 nw 部门 限制不通用
    public function checkNwPeriod($overtime_type, $date)
    {
        $date_tmp = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'), time());

        $behindTime = strtotime(date("Y-m-d", strtotime("+7 day")));

        if ($date_tmp <= $beforeTime || $date_tmp > $behindTime) {
            if ($overtime_type == 2 && ($date_tmp <= $beforeTime || $date_tmp > $behindTime)) {
                $notice_str = 'wrong_date_toast_2';
            } else {
                $notice_str = 'wrong_date_toast_gt2';
            }

            throw new ValidationException($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }

    }

    //验证 nw部门的员工 对应职位 可申请的加班时长是否超了 https://flashexpress.feishu.cn/docx/NtSrdErvgo9mqyx8nFycxbo6nBb
    public function checkNwLimitHour($param)
    {
        //不是 1。3倍的 不验证
        if(!$param['type'] == HrOvertimeModel::OVERTIME_2){
            return true;
        }
        //NW部门Van Courier、Bike Courier、Tricycle Courier
        if (in_array($this->staffInfo['job_title'], [
            OvertimeEnums::$job_title['van_courier'],
            OvertimeEnums::$job_title['bike_courier'],
            OvertimeEnums::$job_title['tricycle_courier'],
        ])) {
            $code = 'courier_ot_limitation';
        }

        //NW部门DC Officer、 DC Supervisor
        if (in_array($this->staffInfo['job_title'], [
            OvertimeEnums::$job_title['dc_officer'],
            OvertimeEnums::$job_title['dc_supervisor'],
        ])) {
            $code = 'dco_ot_limitation';
        }
        //不是指定职位 不用限制
        if(empty($code)){
            return true;
        }

        //over_month_time_limit
        $hourLimit = (new SettingEnvServer())->getSetVal($code);
        //看取没取过时长
        if(!isset($this->reference['staff_duration'])){
            $start = getCurMonthFirstDay($param['date_at']);
            $end = getCurMonthLastDay($param['date_at']);
            $applyHour = (new OvertimeRepository($this->timezone))->get_duration($this->staffInfo['staff_info_id'],$start,$end,$param['type']);
        }else{
            $applyHour = $this->reference['staff_duration'];
        }
        if($hourLimit <= 0){
            $hourLimit = 0;
        }
        //加上本次申请
        $applyHour = $applyHour + $param['duration'];

        if($applyHour > $hourLimit){
            throw new ValidationException($this->getTranslation()->_('over_month_time_limit'));
        }

    }


    /**
     * 校验休息日
     * @param int $type 加班类型
     * @param bool $is_rest 是否为休息日
     * @param array $rh RH
     * @param array $sh SH
     * @param string $date
     * @return void
     * @throws \Exception
     */
    public function checkRestDay($type, $is_rest, $rh, $sh, $date)
    {
        //休息日只能申请休息日加班、休息日超时加班
        //regular holidays只能申请regular holidays加班、regular holidays超时加班
        //special holidays只能申请special holidays加班、special holidays超时加班
        if (in_array($date, $rh) && !$is_rest && !in_array($type, [4, 5])) {
            throw new ValidationException($this->getTranslation()->_('ot_reselect'), enums::$ERROR_CODE['1000']);
        }
        if (in_array($date, $sh) && !$is_rest && !in_array($type, [6, 7])) {
            throw new ValidationException($this->getTranslation()->_('ot_reselect'), enums::$ERROR_CODE['1000']);
        }
        if (in_array($date, $rh) && $is_rest && !in_array($type, [8, 9])) {
            throw new ValidationException($this->getTranslation()->_('ot_reselect'), enums::$ERROR_CODE['1000']);
        }
        if (in_array($date, $sh) && $is_rest && !in_array($type, [10, 11])) {
            throw new ValidationException($this->getTranslation()->_('ot_reselect'), enums::$ERROR_CODE['1000']);
        }
        if (!in_array($date, $rh) && !in_array($date, $sh) && $is_rest && !in_array($type, [2, 3])) {
            throw new ValidationException($this->getTranslation()->_('ot_reselect'), enums::$ERROR_CODE['1000']);
        }
        if (!in_array($date, $rh) && !in_array($date, $sh) && $type != 1 && !$is_rest) {
            throw new ValidationException($this->getTranslation()->_('overtime_weekend'),
                enums::$ERROR_CODE['1000']);
        }
    }


    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $this->user = $user;
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if (empty($staff_info['data'])) {
            throw new ValidationException('wrong hrs staff info');
        }

        $this->staffInfo = $staff_info = $staff_info['data'];

        //[2]组织详情数据
        $overtimeType = $this->getDetailOtType();

        $detailLists = [
            'apply_parson'     => sprintf('%s ( %s )', $staff_info['name'], $staff_info['id']),
            'apply_department' => sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? ''),
            'OT_date'          => $result['date_at'],
            'OT_type'          => ($overtimeType[$result['type']] ?? ''),
            'start_time'       => $result['start_time'],
            'end_time'         => $result['end_time'],
            'duration'         => $result['duration'],
            'OT_reason'        => $result['reason'],
            'ot_detail_6'      => $staff_info['store_name'],
        ];

        $references = json_decode($result['references'], true) ?? '';

        $envModel = new SettingEnvServer();
        $nw_id    = $envModel->getSetVal('dept_network_management_id');

        $applyRequest = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_OVERTIME, $auditId);

        //nw 部分职位 有额外显示字段
        if (!empty($references) && $staff_info['department_id'] == $nw_id && !empty($references['store_name'])) {
            //所属网点 显示大区片区
            $detailLists['ot_detail_6'] = $references['store_name'];

            /**
             * $references['staff_duration'] 员工申请时长
             * $references['store_duration'] 网点 申请时长
             * $references['should_delivery_today'] 当天应派件
             * $references['store_job_staff_num'] = 0;//对应日期 所属网点 指定职位的 在职人数
             * $references['attendance_rate'] = "0/0";//出勤率
             * $references['store_rate'] 三天网点人效率
             * $references['piece_rate'] 三天 片区 人效率
             */

            //延时审批-延时显示
            //非延时｜延时-已创建审批需显示
            if ($applyRequest && $applyRequest->getDelayState() != WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING) {
                //分流 翻译key
                if ($staff_info['position_id'] == enums::$job_title['dc_officer']) {//仓管
                    $detailLists['store_officer_staff_num'] = $references['store_job_staff_num'];
                    $detailLists['should_delivery_today']   = $references['should_delivery_today'];
                    $detailLists['attendance_officer_rate'] = $references['attendance_rate'];
                    $detailLists['store_officer_rate']      = $references['store_rate'].' '.$this->getTranslation()->_('rate_unit');
                    $detailLists['piece_officer_rate']      = $references['piece_rate'].' '.$this->getTranslation()->_('rate_unit');
                    $detailLists['staff_duration']          = $references['staff_duration'].'h';
                    $detailLists['store_duration']          = $references['store_duration'].'h';
                } else {//快递员
                    $detailLists['store_courier_staff_num'] = $references['store_job_staff_num'];
                    $detailLists['should_delivery_today']   = $references['should_delivery_today'];
                    $detailLists['attendance_courier_rate'] = $references['attendance_rate'];
                    $detailLists['store_courier_rate']      = $references['store_rate'].' '.$this->getTranslation()->_('rate_unit');
                    $detailLists['piece_courier_rate']      = $references['piece_rate'].' '.$this->getTranslation()->_('rate_unit');
                    $detailLists['staff_duration']          = $references['staff_duration'].'h';
                    $detailLists['store_duration']          = $references['store_duration'].'h';
                }
            }
        }

        //nw 部门定制详情
        if ($staff_info['department_id'] == $nw_id) {
            //是否填写原因
            $this->setAutoReject();

            //延时审批-延时显示
            if (!empty($applyRequest) && $applyRequest->getDelayState() != WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING) {
                $nwNewDetail = $this->nwEffectDetail($result);
                //详情内容合并
                $detailLists = array_merge($detailLists, $nwNewDetail);
            }
        }
        //详情字段展示
        $otherDetail = $this->getInfoDetail($result);
        $otherDetail['unpaid_leave_days'] = floatval($otherDetail['unpaid_leave_days']) . ' ' . $this->getTranslation()->_('by_day');
        $detailLists = array_merge($detailLists, $otherDetail);

        //根据配置项类型 展示结束时间 是否加1小时 只有泰国和菲律宾 公用 其他国家都有
        if (in_array($result['type'], array_keys($this->holidayOt)) && $result['duration'] == 8) {
            $detailLists['end_time'] = date('Y-m-d H:i:s', strtotime($result['end_time'].' + 1 hours'));
        }

        //支援申请和取消 会变更 ot 记录 详情页要展示
        $supportRemark = $this->getSupportChangeLog($auditId);
        if(!empty($supportRemark)){
            $detailLists['ot_support_remark'] = $supportRemark;
        }

        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'              => $this->auditlist->getAudityType(enums::$audit_type['OT']),
            'id'                 => $result['overtime_id'],
            'staff_id'           => $result['staff_id'],
            'type'               => enums::$audit_type['OT'],
            'created_at'         => $result['created_at'],
            'updated_at'         => $result['updated_at'],
            'status'             => $result['state'],
            'status_text'        => $this->auditlist->getAuditStatus('10'.$result['state']),
            'notice'             => $result['notice'] ?? '',
            'serial_no'          => $result['serial_no'] ?? '',
            'is_alert'           => $references['is_alert'] ?? 0,//针对 dc 休息日申请ot 要弹审批通过的原因 如果是 ph 或者爆仓 可以不填写
            'is_approval_reason' => (int)$this->isNeedApprovalReason,//审批同意的时候 是否需要填写原因
            'is_auto_reject'     => (int)$this->isAutoRejectReason,//审批驳回的时候 是否自动驳回不弹窗
            'is_show_workflow_notice' => $applyRequest && $applyRequest->getDelayState() == WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_PENDING, //延时审批待创建审批流时，不显示审批流
        ];

        $returnData['data']['head'] = $data;
        return $returnData;
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //非撤销状态 指定部门指定职位 的审批 请在加班日期第二天8:00后审批 https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
        //针对Flash Philippines Network部门SP和PDC网点类型的DC Officer+Branch Supervisor职位的网点员工：
        $overtimeInfo = HrOvertimeModel::findFirst($auditId);
        if (empty($overtimeInfo)) {
            throw new ValidationException('ot info error');
        }

        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $staffRe = new StaffRepository($this->lang);
            $this->staffInfo = $staffInfo = $staffRe->getStaffPosition($overtimeInfo->staff_id);
            //新增 字段 针对 nw 部门的 SP和PDC网点类型的DC Officer+Branch Supervisor职位的网点员工 https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
            $storeInfo = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind' => ['store_id' => $staffInfo['sys_store_id']],
            ]);
            $storeInfo = empty($storeInfo) ? [] : $storeInfo->toArray();
            $limit_job_titles = HrJobTitleEnums::$limit_ot_job_title;
            $info = $overtimeInfo->toArray();

            //固化detail 字段
            $detailData = $this->getInfoDetail($info);

            if (in_array($info['type'], array_keys($this->overOt))
                && $staffInfo['sys_store_id'] != '-1'
                && !empty($storeInfo)
                && in_array($storeInfo['category'], [enums::$stores_category['sp'], enums::$stores_category['bdc'],enums::$stores_category['pdc']])
                && in_array($staffInfo['job_title'], $limit_job_titles)

            ) {

                //固化人效数据，已经提前到创建延迟审批流时固化
                //这里是兼容老版本延时审批数据
                $auditInfo = AuditApplyModel::findFirst([
                    'conditions' => 'biz_type = :biz_type: and biz_value = :biz_value:',
                    'bind' => [
                        'biz_type' => AuditListEnums::APPROVAL_TYPE_OVERTIME,
                        'biz_value' => $overtimeInfo['overtime_id'],
                    ],
                ]);
                $auditInfo = empty($auditInfo) ? [] : $auditInfo->toArray();
                if ($auditInfo['delay_state'] != WorkflowEnums::WORKFLOW_DELAY_CREATE_STATE_HAS_CREATED) {
                    $extServer = new OvertimeExtendServer($this->lang, $this->timezone);
                    $extParam['storeInfo'] = $storeInfo;
                    $json_arr = $extServer->nwNewDetail($staffInfo, $info, $extParam);

                    $references = json_decode($info['references'], true);
                    $references = array_merge($references, $json_arr);
                }
            }

            //更新最终状态
            $overtimeInfo->state = $state;

            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']],
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $overtimeInfo->approver_id   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $overtimeInfo->approver_name = isset($staff) && $staff ? $staff['name'] : '';
                $overtimeInfo->reject_reason = isset($extend['remark']) ? $extend['remark'] : '';
            }
            if (!empty($references)) {
                $overtimeInfo->references = json_encode($references, JSON_UNESCAPED_UNICODE);
            }
            if(!empty($detailData)){
                $overtimeInfo->detail_data = json_encode($detailData, JSON_UNESCAPED_UNICODE);
            }
            $overtimeInfo->in_approval = HrOvertimeModel::NOT_IN_APPROVAL;
            $overtimeInfo->update();
            $this->sendMessage($overtimeInfo->toArray(),$state);
        }
    }


    /**
     * 修改状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws \Exception
     */
    public function updateOvertimeV3($paramIn = [])
    {
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        $overtimeId = $this->processingDefault($paramIn, 'audit_id', 2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');
        $state = $this->processingDefault($paramIn, 'status', 2);

        //获取审批详情
        $overtimeList = $this->overtime->infoOvertime(['overtime_id' => $overtimeId]);
        if (empty($overtimeList)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
        //oa 审批处理中 不能操作
        if($overtimeList['in_approval'] == HrOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])){
            return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
        }
//        //nw 部门定制 审批 要在第二天8点之后
//        if ($state != enums::APPROVAL_STATUS_CANCEL) {
//            $this->checkNwApproval($overtimeList);
//        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        //校验当前审批人审批状态
        if ($state == enums::$audit_status['approved']) {
            //同意
            $reason = null;
            if (!empty($reject_reason) && $reject_reason != '1')//前端默认写死了个1  因为后端 需要必填1个字符
            {
                $reason = filter_param($reject_reason);
            }

            $flag = $server->approval($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $staffId, $reason);
        } else {
            //前端没有弹出填写原因的弹窗 后端补原因
            if($state == enums::$audit_status['dismissed'] && !empty($paramIn['is_auto_reject'])){
                $reject_reason = $this->getTranslation()->_('ot_auto_reject_reason');
            }
            //驳回
            $flag = $server->reject($overtimeId, AuditListEnums::APPROVAL_TYPE_OVERTIME, $reject_reason, $staffId);
        }

        return $this->checkReturn(['data' => ['audit_id' => $overtimeId]]);
    }

    /**
     * 校验是否存在延时审批
     * @param $applyObject
     * @param int $nodeLevel 只有创建节点 才会 延时
     * @return bool
     */
    public function checkDelay($applyObject,$nodeLevel = 8)
    {
        if (empty($applyObject)) {
            return false;
        }

        if (!($applyObject instanceof AuditApplyModel)) {
            return false;
        }
        $otInfo = HrOvertimeModel::findFirst($applyObject->getBizValue());
        if (empty($otInfo)) {
            return false;
        }
        $otInfo = $otInfo->toArray();
        //判断 审批流调用的 节点是否符合延时审批条件
        if($nodeLevel != enums::WF_ACTION_CREATE){
            return false;
        }

        return $this->checkNwApproval($otInfo,$nodeLevel);
    }


    /**
     * nw 定制员工的申请 是否需要 延时审批 bool
     *
     * @param $otInfo
     * @return bool
     */
    public function checkNwApproval($otInfo)
    {
        //定制类型
        if (!in_array($otInfo['type'], array_keys($this->overOt))) {
            return false;
        }

        //调用
        $staffInfo = $this->staffInfo;
        if(empty($staffInfo)){
            $staffRe = new StaffRepository($this->lang);
            $staffInfo = $staffRe->getStaffPosition($otInfo['staff_id']);
        }

        $phNwId = enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH;
        //nw部门
        if ($staffInfo['sys_department_id'] != $phNwId) {
            return false;
        }
        //非总部
        if ($staffInfo['sys_store_id'] == '-1') {
            return false;
        }

        //需求写的是 DC Officer+DC supervisor
        $limitJobTittle = HrJobTitleEnums::$limit_ot_job_title;
        if (!in_array($staffInfo['job_title'], $limitJobTittle)) {
            return false;
        }

        //判断网点类型
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind' => ['store_id' => $staffInfo['sys_store_id']],
        ]);
        //对应网点分类
        $storeCategories = [enums::$stores_category['sp'], enums::$stores_category['pdc'], enums::$stores_category['bdc']];
        if (!in_array($storeInfo->category, $storeCategories)) {
            return false;
        }

        return true;
    }

    //nw 指定条件申请的所有ot 都不弹驳回原因弹窗
    protected function setAutoReject(){
        //Network Management[125]部门及其子部门下 Van Courier[110] 、Bike Courier[13] 、Tricycle Courier[1000]、Truck Driver[1194]、DC Officer[37]、 DC Supervisor[16]的所有OT类型申请的审批：审批流任一节点点击驳回时，无二次确认弹窗，默认显示驳回原因为：审批人已拒绝该OT申请
        $autoRejectJobs = [
            OvertimeEnums::$job_title['bike_courier'],
            OvertimeEnums::$job_title['van_courier'],
            OvertimeEnums::$job_title['tricycle_courier'],
            OvertimeEnums::$job_title['truck_courier'],
            OvertimeEnums::$job_title['dc_officer'],
            OvertimeEnums::$job_title['dc_supervisor'],
            HrJobTitleEnums::ASSISTANT_DC_SUPERVISOR,
        ];
        if(in_array($this->staffInfo['job_title'], $autoRejectJobs)){
            $this->isAutoRejectReason = true;
        }
    }

    /**
     * 详情页展示nw部门计算人效
     *
     * @history
     * 1.0 https://flashexpress.feishu.cn/docx/GN2vd4SXPoT3VBxlmdWcjAyNnqc
     * 2.0 https://flashexpress.feishu.cn/docx/C6Rkd1saSoYuAQxuBU4cerGwn3c
     *
     * @param $overtimeInfo
     * @return array
     * @throws ValidationException
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    protected function nwEffectDetail($overtimeInfo, $isNeedApprovalReason = true)
    {
        //新增字段针对 nw 部门的
        //SP、BDC和PDC网点类型的
        //DC Officer+Branch Supervisor、Assistant DC Supervisor职位的网点员工
        //https://flashexpress.feishu.cn/docx/doxcnfR0AGGp7SPFD79cJVxsbse
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind' => ['store_id' => $this->staffInfo['store_id']],
        ]);
        if(empty($storeInfo)){
            return [];
        }
        $storeInfo = $storeInfo->toArray();
        $nwNewDetail = [];

        // 2.0 追加职位[Assistant DC Supervisor(1553)]
        if (in_array($overtimeInfo['type'], array_keys($this->overOt))
            && $this->staffInfo['store_id'] != enums::HEAD_OFFICE_ID
            && in_array($storeInfo['category'], [enums::$stores_category['sp'],
                enums::$stores_category['bdc'],
                enums::$stores_category['pdc'],
            ])
            && in_array($this->staffInfo['job_title'], HrJobTitleEnums::$limit_ot_job_title)

        ) {
            $extServer = new OvertimeExtendServer($this->lang, $this->timezone);
            $extParam['storeInfo'] = $storeInfo;
            //nw 部门审批详情页特有的字段
            $nwNewDetail = $extServer->nwNewDetail($this->staffInfo, $overtimeInfo, $extParam);

            $flag = false;//是否 审批统一填写同意原因 true 是 false 保持不变 直接同意不弹窗
            //条件1 SP网点：SP员工所在网点当天人效 < 所在片区平均人效 更改为 SP员工所在网点当天人效 ≥ 系统中配置的SP网点人效
            if (in_array($storeInfo['category'], [enums::$stores_category['sp'], enums::$stores_category['bdc']])) {
                //获取配置的人效
                if ($storeInfo['category'] == enums::$stores_category['sp']) {
                    $settingEnvCode = 'efficiency_of_sp_branches';
                } else {
                    $settingEnvCode = 'efficiency_of_bdc_branches';
                }
                $efficiency = (new SettingEnvServer())->getSetVal($settingEnvCode);
                $efficiency = (trim($efficiency) === '') ? 500 : $efficiency;//默认500 如果保存0 取0
                $flag = $nwNewDetail['store_today_effect'] < $efficiency;

                //拼接单位
                $nwNewDetail['store_today_effect'] = $nwNewDetail['store_today_effect'] . ' ' . $this->getTranslation()->_('rate_unit');
                $nwNewDetail['piece_today_effect'] = $nwNewDetail['piece_today_effect'] . ' ' . $this->getTranslation()->_('rate_unit');
            }

            //条件2 PDC网点：PDC员工所在网点当天人效 < 所有PDC网点当天平均人效 或者 所在网点PDC集包率 < 90%
            if ($storeInfo['category'] == enums::$stores_category['pdc']) {
                $pdcEfficiency = (new SettingEnvServer())->getSetVal('efficiency_of_pdc_branches');
                $pdcEfficiency = (trim($pdcEfficiency) === '') ? 1000 : $pdcEfficiency;//默认500 如果保存0 取0
                $flag = $nwNewDetail['store_today_effect'] < $pdcEfficiency;

                //拼接单位
                $nwNewDetail['store_today_effect'] = $nwNewDetail['store_today_effect'] . ' ' . $this->getTranslation()->_('rate_unit');
                $nwNewDetail['pdc_effect'] = $nwNewDetail['pdc_effect'] . ' ' . $this->getTranslation()->_('rate_unit');
                $nwNewDetail['pdc_seal_rate'] = $nwNewDetail['pdc_seal_rate'] . '%';
            }

            //判断是否填写 审批同意 意见 is_approval_reason = 1
            if ($flag && $overtimeInfo['state'] == enums::APPROVAL_STATUS_PENDING) {
                $applyInfo = AuditApplyModel::findFirst([
                    'conditions' => "biz_type = :biz_type: and biz_value = :ot_id:",
                    'bind' => ['biz_type' => enums::$audit_type['OT'], 'ot_id' => $overtimeInfo['overtime_id']],
                ]);

                //新增需求 要获取 当前审批节点是不是 最终审批节点 控制 审批人 是否填写审批同意的意见
                $flowServer = new WorkflowServer($this->lang, $this->timezone);
                $isNeedApprovalReason && $this->isNeedApprovalReason = !$flowServer->isFinalNode($applyInfo, $this->user);
            }
        }

        return $nwNewDetail;
    }

    //新增的其他非重要详情显示字段
    public function getInfoDetail($detailInfo){
        $infoDetails = [];
        //部分字段审批通过后固化到 detail_data
        if($detailInfo['state'] != enums::$audit_status['panding']){
            $infoDetails = json_decode($detailInfo['detail_data'], true);
            return $infoDetails ?? [];
        }

        //获取对应月份紧急假申请天数   NW部门Van Courier、Bike Courier、Tricycle Courier、Truck Driver、DC Officer、 DC Supervisor职位：BY-OT审批详情页所有OT类型新增字段：
        $jobTitles = [
            OvertimeEnums::$job_title['van_courier'],
            OvertimeEnums::$job_title['bike_courier'],
            OvertimeEnums::$job_title['tricycle_courier'],
            OvertimeEnums::$job_title['truck_courier'],
            OvertimeEnums::$job_title['dc_officer'],
            OvertimeEnums::$job_title['dc_supervisor'],
        ];
        if($this->staffInfo['sys_department_id'] == enums::SALES_CRM_ACCESS_NETWORK_DEPARTMENT_ID_PH
            && in_array($this->staffInfo['job_title'],$jobTitles)
        ){
            $start_month = date("Y-m-01", strtotime($detailInfo['date_at']));
            $end_month = date('Y-m-d', strtotime("{$start_month} last day of "));
            $unpaidMonthDays = (new OvertimeExtendServer($this->lang,$this->timezone))->getUnpaidLeaveDays($this->staffInfo,$start_month,$end_month);
            $infoDetails['unpaid_leave_days'] = (float)$unpaidMonthDays;
        }

        return $infoDetails;
    }

    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        //OT Type
        $detail     = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        $references = json_decode($detail['references'], true);

        //这里有个循环调用，追加参数
        $nwNewDetail = $this->getNwEffectDetail($detail, $detail['staff_id'], false);

        $data = [
            'type'               => $detail['type'],
            'duration'           => $detail['duration'] ?? 0,
            'ph_num'             => $references['ph_num'] ?? 0, //申请当月 有多少个 ph
            'dc_in_all_hours'    => $references['dc_in_all_hours'] ?? 0,//当前网点 dc 已经申请的小数 一周内已经申请的对应ot类型的时长
            'dc_should_hours'    => $references['dc_should_hours'] ?? 0, //当前网点 申请时候 应有的小时预算
            'store_today_effect' => $nwNewDetail['store_today_effect'] ?? 0, //所在网点当天人效
        ];
        $this->logger->write_log('ph overtime getWorkflowParams ' . json_encode($data), 'info');

        return $data;
    }

    public function getNwEffectDetail($detail_info, $staff_info_id, $is_need_approval_reason = true)
    {
        $staff_info = (new StaffServer())->get_staff($staff_info_id);
        $this->staffInfo = $staff_info['data'];

        //这里有个循环调用，追加参数
        return $this->nwEffectDetail($detail_info, $is_need_approval_reason);
    }

    public function getIsNeedApprovalReason()
    {
        return (int)$this->isNeedApprovalReason;
    }
    //加班详情 展示 支援影响班次变更的记录
    public function getSupportChangeLog($otId){
        $data = SupportOvertimeBackupModel::find([
            'columns' => 'overtime_id, start_time, origin_start_time,change_type',
            'conditions' => 'overtime_id = :id:',
            'bind' => ['id' => $otId],
            'order'      => 'id desc',
        ])->toArray();

        if(empty($data)){
            return '';
        }
        $str = '';
        //拼接成 字符串 中间用 br
        foreach ($data as $da){
            if($da['change_type'] == SupportOvertimeBackupModel::LOG_TYPE_APPLY){
                $str .= $this->getTranslation()->_('ot_support_log_1', ['origin_time' => $da['origin_start_time'],'start_time' => $da['start_time']]) . '</br>';
            }
            if($da['change_type'] == SupportOvertimeBackupModel::LOG_TYPE_CANCEL){
                $str .= $this->getTranslation()->_('ot_support_log_2', ['origin_time' => $da['origin_start_time'],'start_time' => $da['start_time']]) . '</br>';
            }
        }
        $str = rtrim($str, '</br>');
        return $str;
    }

    //队列消费（hcm 申请支援和撤销） 和 撤销支援调用
    public function addSupportChangeLog($param)
    {
        $id          = (int)$param['id'];//hr_staff_apply_support_store 主键
        $supportInfo = HrStaffApplySupportStoreModel::findFirst($id);
        if (empty($supportInfo)) {
            $this->logger->write_log("addSupportChangeLog id error " . $id,'info');
            return true;
        }

        //语言环境
        $accountModel = new StaffAccountModel();
        $staffLang = $accountModel->getAcceptLanguage($supportInfo->staff_info_id);

        $paramDeal['change_type'] = $param['change_type'];
        $paramDeal['lang']        = $staffLang;
        $paramDeal['shift_start'] = $supportInfo->shift_start;
        //by 新增 和 hcm 导入
        if (in_array($param['change_type'],
            [SupportOvertimeBackupModel::LOG_TYPE_APPLY, SupportOvertimeBackupModel::LOG_TYPE_CANCEL])) {
            $paramDeal['start'] = $supportInfo->employment_begin_date;
            $paramDeal['end']   = $supportInfo->employment_end_date;
            $this->dealSupportOt($supportInfo->staff_info_id, $paramDeal);
        }

        //修改支援信息 有可能修改 班次 和 日期 !!!!要优先处理 取消的数据 然后再处理新增的数据 取消数据会取找新增类型 还原信息 容易取错
        if ($param['change_type'] == SupportOvertimeBackupModel::LOG_TYPE_EDIT) {
            $this->logger->write_log("addSupportChangeLog edit " . json_encode($param),'info');
            $originBegin   = $param['origin_begin_date'] ?? '';
            $originEnd     = $param['origin_end_date'] ?? '';
            $originShiftId = $param['origin_shift_id'];//原班次id  看是否需要修改交集日期里的加班
            $newBegin      = $supportInfo->employment_begin_date;
            $newEnd        = $supportInfo->employment_end_date;
            //没变化 不用管
            if($newBegin == $originBegin && $newEnd == $originEnd && $originShiftId == $supportInfo->shift_id){
                $this->logger->write_log("addSupportChangeLog no change ", 'info');
                return true;
            }

            //如果只修改班次 日期不变
            if($newBegin == $originBegin && $newEnd == $originEnd && $originShiftId != $supportInfo->shift_id){
                $paramDeal['start']       = $newBegin;
                $paramDeal['end']         = $newEnd;
                $paramDeal['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_APPLY;
                $this->dealSupportOt($supportInfo->staff_info_id, $paramDeal);
                return true;
            }

            $originDates   = DateHelper::DateRange(strtotime($originBegin), strtotime($originEnd));
            $newDates      = DateHelper::DateRange(strtotime($newBegin), strtotime($newEnd));
            //取消掉的日期
            $cancelDates = array_diff($originDates, $newDates);
            if (!empty($cancelDates)) {
                $this->logger->write_log("addSupportChangeLog cancel " . json_encode($cancelDates), 'info');
                sort($cancelDates);
                $paramDeal['start']       = $cancelDates[0];
                $paramDeal['end']         = end($cancelDates);
                $paramDeal['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_CANCEL;
                $this->dealSupportOt($supportInfo->staff_info_id, $paramDeal);
            }

            //新增的日期
            $applyDates = array_diff($newDates, $originDates);
            if (!empty($applyDates)) {
                $this->logger->write_log("addSupportChangeLog add " . json_encode($applyDates), 'info');
                sort($applyDates);
                $paramDeal['start']       = $applyDates[0];
                $paramDeal['end']         = end($applyDates);
                $paramDeal['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_APPLY;
                $this->dealSupportOt($supportInfo->staff_info_id, $paramDeal);
            }

            //交集 只看班次有没有变动 没有就不动
            $interDates = array_intersect($newDates, $originDates);
            if (!empty($interDates) && $supportInfo->shift_id != $originShiftId) {
                $this->logger->write_log("addSupportChangeLog shift change " . json_encode($interDates), 'info');
                sort($interDates);
                $paramDeal['start']       = $interDates[0];
                $paramDeal['end']         = end($interDates);
                $paramDeal['change_type'] = SupportOvertimeBackupModel::LOG_TYPE_APPLY;
                $this->dealSupportOt($supportInfo->staff_info_id, $paramDeal);
            }
        }
        return true;
    }


    /**
     * @param $staffId
     * @param $param //start/end 取ot的日期区间 shift_start 新班次  change_type 日志类型  lang
     * @return bool
     */
    protected function dealSupportOt($staffId, $param)
    {
        $lang          = empty($param['lang']) ? $this->lang : $param['lang'];
        $startDate     = $param['start'];
        $endDate       = $param['end'];
        $changeType    = $param['change_type'];
        $newShiftStart = $param['shift_start'];

        //查询期间内加班
        $otData = HrOvertimeModel::find([
            'conditions' => 'staff_id = :staff_id: and date_at between :start_date: and :end_date: and state in (1,2) and type = :type:',
            'bind'       => [
                'staff_id'   => $staffId,
                'start_date' => $startDate,
                'end_date'   => $endDate,
                'type'       => HrOvertimeModel::OVERTIME_2,
            ],
        ]);
        //顶多1，2条 休息日加班 不会太多
        if (empty($otData->toArray())) {
            return true;
        }
        $logModel = new SupportOvertimeBackupModel();
        foreach ($otData as $da) {
            //原来的 时间
            $originStart = $da->start_time;
            $originEnd   = $da->end_time;

            if ($changeType == SupportOvertimeBackupModel::LOG_TYPE_APPLY) {
                $duration = $da->duration;
                //如果 时间相同 不操作
                $newStartTime = $da->date_at . ' ' . $newShiftStart;
                $newStartTime = date('Y-m-d H:i:s', strtotime($newStartTime));//整理格式
                $newEndTime   = date('Y-m-d H:i:s', strtotime($newStartTime) + ($duration * 3600));
                if ($da->start_time == $newStartTime) {
                    continue;
                }
                //新增日志记录
                $row['start_time'] = $newStartTime;//本次支援开始班次
                $row['end_time']   = $newEndTime;

                //修改ot时间为 支援班次时间
                $da->start_time = $newStartTime;
                $da->end_time   = $newEndTime;
                //消息内容
                $content = $this->getTranslation($lang)->_('ot_support_content_1',
                    ['date_at' => $da->date_at, 'origin_time' => $originStart, 'start_time' => $newStartTime]);
            } else {//取消操作 要取ot 的最早一条 change_type = 1 的log记录 还原ot 并且新增一条日志
                $logInfo = SupportOvertimeBackupModel::findFirst([
                    'conditions' => 'overtime_id = :ot_id: and change_type = :change_type:',
                    'bind'       => [
                        'ot_id'       => $da->overtime_id,
                        'change_type' => SupportOvertimeBackupModel::LOG_TYPE_APPLY,
                    ],
                ]);
                //班次时间 相同 没有记录 或者 有可能是 上线之前历史数据 不处理 也不发消息
                if (empty($logInfo)) {
                    continue;
                }

                //增加 撤销日志
                $row['start_time'] = $logInfo->origin_start_time;//还原之前的时间
                $row['end_time']   = $logInfo->origin_end_time;

                //还原ot时间
                $da->start_time = $logInfo->origin_start_time;
                $da->end_time   = $logInfo->origin_end_time;
                //消息内容
                $content        = $this->getTranslation($lang)->_('ot_support_content_2', [
                    'date_at'     => $da->date_at,
                    'origin_time' => $originStart,
                    'start_time'  => $logInfo->origin_start_time,
                ]);
            }
            $da->updated_at = new \Phalcon\Db\RawValue('updated_at');
            $da->update();
            $row['origin_start_time'] = $originStart;
            $row['origin_end_time']   = $originEnd;
            $row['overtime_id']       = $da->overtime_id;
            $row['staff_id']          = $da->staff_id;
            $row['change_type']       = $changeType;
            $row['duration']          = $da->duration;
            $row['date_at']           = $da->date_at;
            $clone                    = clone $logModel;
            $clone->create($row);

            //消息 推送
            try{
                $title    = $this->getTranslation($lang)->_('ot_support_title', ['date_at' => $da->date_at]);
                $msgId                          = time() . $staffId . rand(1000000, 9999999);
                $paramMsg['staff_users']        = [$staffId];//数组 多个员工id
                $paramMsg['message_title']      = $title;
                $paramMsg['message_content']    = $content;
                $paramMsg['staff_info_ids_str'] = $staffId;
                $paramMsg['id']                 = $msgId;
                $paramMsg['category']           = MessageEnums::CATEGORY_GENERAL;

                $msgClient   = new ApiClient('hcm_rpc', '', 'add_kit_message', $lang);
                $msgClient->setParams($paramMsg);
                $msgClient->execute();

                $pushParam['staff_info_id']   = $staffId;
                $pushParam['src']             = 'backyard';
                $pushParam['message_title']   = $title;
                $pushParam['message_content'] = $content;
                $pushParam['message_scheme']  = "flashbackyard://fe/page?path=message&messageid=" . $msgId;

                $pushClient = new ApiClient('bi_rpc', '', 'push_to_staff');
                $pushClient->setParams($pushParam);
                $r1 = $pushClient->execute();
                $pushParam['src'] = "kit";
                $pushClient = new ApiClient('bi_rpc', '', 'push_to_staff');
                $pushClient->setParams($pushParam);
                $r2 = $pushClient->execute();
            }catch (\Exception $e){
                $this->logger->write_log("addSupportChangeLog msg push " . $e->getTraceAsString(), 'info');
            }
        }
        return true;
    }


}
