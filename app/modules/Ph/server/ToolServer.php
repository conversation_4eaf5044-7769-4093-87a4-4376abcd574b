<?php

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\MenuEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\ResumeRecommendServer;
use FlashExpress\bi\App\Server\ToolServer as GlobalBaseServer;

class ToolServer extends GlobalBaseServer
{

    public function checkSeller($staff_info_id)
    {
        // bi svc 接口判断当前登录人是否为 network子部门及本部门
        $data['staff_info_id'] = $staff_info_id;
        $ret         = new ApiClient('bi_rpcv2', '', 'seller.check_nw_staff', $this->lang);
        $ret->setParams($data);
        $res = $ret->execute();
        $this->getDI()->get('logger')->write_log('tool-about 4', "info");
        return $res;
    }

    /**
     * 获取个人信息菜单列表
     * @param $params
     * @return array
     */
    public function getPersonalInformationMenuList($params): array
    {
        $show  = $this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID;
        $platform = $params['platform'] ?? enums::FB_BACKYARD;

        //姓名工号
        $menu_list[] = $this->getMenuStaffName($params);
        //基本信息
        $menu_list[] = $this->getMenuBaseInformation($params);
        if ($show) {
            //工资
            $menu_list[] = $this->getMenuSalary($params);
        }
        if ($this->incentiveShow) {
            //提成
            $menu_list[] = $this->getMenuCommission($params);
        }
        if ($show) {
            //前公司纳税收入
            $menu_list[] = $this->getMenuFormerCompanyTaxableIncome($params);
            //承诺书
            $menu_list[] = $this->getMenuCommitment($params);
        }
        //电子合同
       // $menu_list[] = $this->getMenuElectronicContract($params);

        // 车辆信息(v21270 增加BY入口)
        $menu_list[] = $this->getMenuVehicleInfo($params);

        return array_values(array_filter($menu_list));
    }

    /**
     * 个人信息菜单 - 承诺书 ph 有条件显示 h5链接 有红点 无右侧文字 无 右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuCommitment($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $menu = [];
        if (!empty( $this->staffInfo['job_title'] ) && in_array( $this->staffInfo['job_title'], [enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['tricycle_courier'] ] ) ) {
            $commitment_num = (new ResumeRecommendServer($this->lang, $this->timezone))->getCommitmentNum(['staff_id' => $staff_info_id]);
            if ($commitment_num) {
                $read_count = '1';
            }
            $menu = $this->combinationMenuParams([
                'id'    => 'personal_information_menu_commitment',
                'title' => $this->t->_('personal_information_menu_commitment'), //菜单标题
                'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE, //跳转类型
                'dst'   => env('sign_url') . '/#/pdf-signature-loapoa', //跳转链接
                'read_count' => $read_count ?? '', //红点数
            ]);
        }
        return $menu;
    }

    /**
     * 个人信息菜单 - 前公司纳税收入 ph 默认显示 h5链接 无红点 无右侧文字 无右侧文本状态
     * @param $params
     * @return array
     */
    public function getMenuFormerCompanyTaxableIncome($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $compare_date =  '2022-01-01'; // 对比时间
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and hire_date >= :hire_date: ",
            'bind' => [
                'staff_info_id'  => $staff_info_id,
                'hire_date' =>$compare_date,
            ],
        ]);

        $menu = [];
        if(!empty($staff_info)){
            $menu = $this->combinationMenuParams([
                'id'    => 'personal_information_menu_former_company_taxable',
                'title' => $this->t->_('personal_information_menu_former_company_taxable'), //菜单标题
                'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE,//跳转类型
                'dst'   => env('sign_url') . '/#/taxFilling', //跳转链接
            ]);
        }
        return $menu;
    }

    /**
     * 个人信息菜单 - 基本信息 默认显示 h5链接 有红点 无右侧文字 无右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuBaseInformation($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $read_count    = $this->getMenuBaseInformationRedCount($staff_info_id);
        return $this->combinationMenuParams([
            'id'         => 'personal_information_menu_base_information',
            'title'      => $this->t->_('personal_information_menu_base_information'), //菜单标题
            'type'       => MenuEnums::MENU_DST_TYPE_H5_PAGE,                          //跳转类型
            'dst'        => env('sign_url') . '/#/BasicInfo',                   //跳转链接
            'read_count' => !empty($read_count) ? (string)$read_count : '',            //红点数
        ]);
    }

    /**
     * 基本信息红点菜单
     * @param $staff_info_id
     * @return int
     */
    public function getMenuBaseInformationRedCount($staff_info_id): int
    {
        $read_count = 0;

        $annexRet  = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staff_info_id],
        ])->toArray();
        $annexList = array_column($annexRet, null, 'type');

        $allType = [
            HrStaffAnnexInfoModel::TYPE_SOCIAL_SECURITY,
            HrStaffAnnexInfoModel::TYPE_FUND,
            HrStaffAnnexInfoModel::TYPE_MEDICAL_INSURANCE,
            HrStaffAnnexInfoModel::TYPE_TAX_CARD,
        ];
        //个人代理，只保留 税号
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoOne($staff_info_id, 'hire_type');
        if(!empty($staffInfo) && $staffInfo['hire_type'] == HrHcModel::HIRE_TYPE_CONTRACT_LABOUR) {
            $allType = [
                HrStaffAnnexInfoModel::TYPE_TAX_CARD,
            ];
        }

        // ph 只有审核拒绝 才会出现 +1的小红点 备用银行卡
        // 备用银行卡换类型了 如果以后要打开 得换成 HrStaffAnnexInfoModel::TYPE_BACKUP_BANK_CARD
//        if (isset($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT) {
//            $read_count++;
//        }
        
        // ph银行卡 个人代理才显示
        if (in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) && (empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) || is_null($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state']) || $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
            $read_count++;
        }

        // 身份证未上传 或者 拒绝时 红点需要 +1
        if (empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) || ($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
            $read_count++;
        }

        foreach ($allType as $key => $type) {
            // 医保、公积金、社保号、税号 // 清空卡号时 audit_state 字段会置空
            if (empty($annexList[$type]) || is_null($annexList[$type]['audit_state']) || ($annexList[$type]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
                // 未上传 或者 审核拒绝需要+1
                $read_count++;
            }
        }

        return $read_count;
    }

    /**
     * 获取个人信息红点总数 基本信息红点数量+承诺书
     * @param $staff_info_id
     * @return int
     */
    public function getPersonalInformationRedCount($staff_info_id): int
    {
        $read_count = $this->getMenuBaseInformationRedCount($staff_info_id);
        $staffInfo  = HrStaffInfoServer::getUserInfoByStaffInfoId($staff_info_id, 'job_title');
        if (!empty($staffInfo->job_title) && in_array($staffInfo->job_title, [
                enums::$job_title['van_courier'],
                enums::$job_title['bike_courier'],
                enums::$job_title['tricycle_courier'],
            ])) {
            //承诺书
            $commitment_num = (new ResumeRecommendServer($this->lang,
                $this->timezone))->getCommitmentNum(['staff_id' => $staff_info_id]);
            if ($commitment_num) {
                $read_count++;
            }
        }

        return $read_count;
    }
}