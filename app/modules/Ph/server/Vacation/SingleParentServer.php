<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 4/18/23
 * Time: 3:51 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\VacationServer as GlobalServer;


class SingleParentServer extends GlobalServer implements LeaveInterface
{
    private static $instance = null;

    public function getLimitDays()
    {
        // TODO: Implement getLimitDays() method.
    }

    public function businessCheck()
    {
        // TODO: Implement businessCheck() method.
    }

    public function dataSave()
    {
        // TODO: Implement dataSave() method.
    }

    //任务 每年初始化调用
    public static function getInstance($lang,$timezone){
        if(!self::$instance instanceof self){
            self::$instance = new self($lang,$timezone);
        }
        return self::$instance;
    }

    //撤销驳回 返还额度
    public function returnRemainDays($auditId, $staffInfo, $extend = [])
    {
        $this->staffInfo = $staffInfo;
        //split 对应的 占用的 对应周期的 额度
        $splitInfo = $this->getUsedDays($auditId);

        //没有拆分表额度信息
        if (empty($splitInfo)) {
            $this->logger->write_log("拆分表信息异常 {$this->staffInfo['staff_info_id']} {$auditId}");
            return true;
        }

        $remainData = StaffLeaveRemainDaysModel::find([
            'column'     => 'staff_info_id,days,leave_days,year,freeze_days',
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year in ({cycle:array})',
            'bind'       => [
                'staff_id'   => $this->staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_20,
                'cycle'      => array_keys($splitInfo),
            ],
        ]);


        if (empty($remainData->toArray())) {
            $this->logger->write_log("额度表信息异常 {$this->staffInfo['staff_info_id']} {$auditId} ".json_encode($splitInfo));
            return true;
        }

        foreach ($remainData as $remain) {
            $needBackDays = $splitInfo[$remain->year] ?? 0;//需要返还的 额度
            //使用额度 减少
            $remain->days       += $needBackDays;
            $remain->leave_days -= $needBackDays;
            $remain->update();
        }
    }


    //申请权限
    public function applyPermission($staffInfo)
    {
        //正式员工
        if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_1
            && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_1
            && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF_0
        ) {
            return true;
        }

        return false;
    }


    //任务 年1月1号 初始化一下在职人员额度数据 只保存 不计算 实习生0
    public function taskInitialize($staffInfo)
    {
        if (!$this->applyPermission($staffInfo)) {
            $this->logger->write_log("single_leave_send false ".json_encode($staffInfo), 'info');
            return;
        }
        //如果 存在记录 不操作
        $remainInfo = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_20,
                'year_at'    => date('Y'),
            ],
        ]);
        if (!empty($remainInfo)) {
            return true;
        }

        //默认 天数
        $days = enums::SINGLE_DAYS_UN;

        $model                   = new StaffLeaveRemainDaysModel();
        $insert['staff_info_id'] = $staffInfo['staff_info_id'];
        $insert['leave_type']    = enums::LEAVE_TYPE_20;
        $insert['year']          = date('Y');
        $insert['task_date']     = date('Y-m-d');
        $insert['freeze_days']   = $insert['days'] = $days;

        return $model->create($insert);
    }

    //获取 对应年的 额度
    public function getDays($staffInfo, $year)
    {
        //5月1号 之前 逻辑保持原来的 一次性属性
        $envServer = new SettingEnvServer();
        $pointDate = $envServer->getSetVal('ph_sick_point_date');
        if (date('Y-m-d') < $pointDate) {
            return $this->oldRulesDays($staffInfo);
        }

        //没有申请权限
        if (!$this->applyPermission($staffInfo)) {
            return 0;
        }

        $remainInfo = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_20,
                'year_at'    => $year,
            ],
        ]);
        //如果没有 发放一条
        if (empty($remainInfo)) {
            $remainInfo = new StaffLeaveRemainDaysModel();
            //获取 对应年 已经申请的假期
            $audit_re = new AuditRepository($this->lang);
            $used_day = 0;
            $used     = $audit_re->get_used_leave_days($staffInfo['staff_info_id'], $year, '20');
            if (!empty($used) && !empty($used[0]['num'])) {
                $used_day = $used[0]['num'];
            }

            $row['staff_info_id'] = $staffInfo['staff_info_id'];
            $row['leave_type']    = enums::LEAVE_TYPE_20;
            $row['year']          = $year;
            $row['freeze_days']   = enums::SINGLE_DAYS_UN;
            $row['days']          = max(enums::SINGLE_DAYS_UN - $used_day, 0);//剩余
            $row['leave_days']    = $used_day;//已使用
            $remainInfo->create($row);
        }

        return $remainInfo->days;
    }

    //育儿假 上线时间和 5月1号期间 用旧规则
    protected function oldRulesDays($staffInfo){
        $days = StaffLeaveRemainDaysModel::sum([
            'column'     => 'leave_days',
            'conditions' => "staff_info_id = :staff_id: and leave_type = :leave_type:",
            'bind'       => [
                'staff_id'   => $staffInfo['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_20,
            ],
        ]);
        if (!empty($days) && $days > 0) {
            return 0;
        }
        //如果是空 按旧逻辑 申请过旧不能申请
        $audit_re = new AuditRepository($this->lang);
        $used     = $audit_re->get_used_leave_days($staffInfo['staff_info_id'], 2022, '20');
        if (!empty($used) && !empty($used[0]['num'])) {
            return 0;
        }
        $used     = $audit_re->get_used_leave_days($staffInfo['staff_info_id'], 2023, '20');
        if (!empty($used) && !empty($used[0]['num'])) {
            return 0;
        }
        return enums::SINGLE_DAYS_UN;
    }


}