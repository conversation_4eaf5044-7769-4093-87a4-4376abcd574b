<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/29/23
 * Time: 8:15 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;
use FlashExpress\bi\App\Server\PayrollServer as GlobalServer;


class PayrollServer extends GlobalServer{
    const UP_MONTH = 1;//上半月
    const LOW_MONTH = 2;//下半月


    //菲律宾特殊
    public function formatSalaryDate($month){
        $month_type = substr($month, -1);
        $real_month = substr($month, 0, -1);

        $start = $month_type == self::UP_MONTH ? $real_month . '-01' : $real_month . '-16';
        $end = $month_type == self::UP_MONTH ? $real_month . '-15':date('Y-m-d', strtotime("{$real_month} last day of"));//每月最后一天
        return [$start,$end];
    }


}