<?php
/**
 * Author: Bruce
 * Date  : 2022-04-23 21:55
 * Description:
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Modules\Ph\library\Enums\CeoMailEnums;
use FlashExpress\bi\App\Server\CeoMailServer as GlobalBaseServer;

class CeoMailServer extends GlobalBaseServer
{
    /**
     * 获取需要阅读承诺书的信息
     * @return array
     */
    public function getCommitmentCategoryInfo()
    {
        return CeoMailEnums::MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS;
    }

    /**
     * 获取 关于薪酬分类
     * @param array $params
     * @return int
     */
    public function getCompensationCategory($params = [])
    {
        return CeoMailEnums::ABOUT_COMPENSATION_CATEGORY_ID;
    }

    /**
     * 获取电子合同
     * @param $isIndependent
     * @return int
     */
    public function getContractCategory($isIndependent = false)
    {
        return $isIndependent ? CeoMailEnums::ABOUT_OTHER_SYS_CATEGORY_ID : CeoMailEnums::ABOUT_BANK_CATEGORY_ID;
    }

    /**
     * 获取 flash box 提交表单页地址。
     * @return mixed
     */
    public function getFlashBoxUrlIncrease()
    {
        $result['flash_box_url'] = '';

        $info = $this->getCategory(CeoMailEnums::ABOUT_EXPRESS_SYS_OTHER_CATEGORY_ID);
        if(empty($info)) {
            return $result;
        }
        $name = urlencode($info['category_name_en']);
        $result['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_SUBMIT_PAGE . '?source=fbi&is_read_commitment=0&problem_category=' . CeoMailEnums::ABOUT_EXPRESS_SYS_OTHER_CATEGORY_ID . '&seconProblemName=' . $name;
        return $result;
    }

    /**
     *获取关于提成分类
     *
     */
    public function getIncentiveCategory(): int
    {
        return CeoMailEnums::ABOUT_INCENTIVE_ID;
    }
}