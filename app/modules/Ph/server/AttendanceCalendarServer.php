<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:25
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\AttendanceCalendarServer as BaseServer;
use FlashExpress\bi\App\Server\HrShiftServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class AttendanceCalendarServer extends BaseServer
{
    public function dealWithHoliday($holiday, $date)
    {
        if (isset($holiday['holiday_type'])) {
            if ($holiday['holiday_type'] == 1) {
                return 'RH';
            }
            if ($holiday['holiday_type'] == 2) {
                return 'SH';
            }
        }
        return 'PH';
    }

    public function getHolidayData($startDate='',$endDate=''): array
    {
        $holiday_data = [];
        $staffInfo = $this->staffInfo;
        if(!empty($staffInfo['hire_type']) && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            return $holiday_data;
        }
        $holiday_data = (new LeaveServer())->ph_days($this->staffInfo);
        return empty($holiday_data) ? [] : array_column($holiday_data, NULL,'day');
    }

    /**
     * 标记迟到早退
     * @param $date
     * @param $seconds
     * @param int|null $leaveType 存在半天请假的情况下,开始&结束班次需要加减相应时长在判断迟到早退
     * @return bool
     */
    public function dealWithLateAndLeaveEarly($date, $seconds = 0, $leaveType = null)
    {
        //主播 因 实际工作时长 小于 应工作时长 标记 早退
        if (in_array($this->current_job_title, $this->liveJobId)) {
            $this->setLiveLeaveEarly($date, $leaveType);
            return true;
        }
        //弹性打卡偏移量  秒
        $current_flexible_time = 0;
        //获取弹性打卡偏移量
        //上班打卡存在,  班次存在 , 并且没请上午假
        if (!empty($this->staffAData[$date]['started_at']) && !empty($this->staffAData[$date]['shift_id']) && $leaveType != AttendanceCalendarEnums::LEAVE_UP_TYPE) {
            $HrShiftServer = new HrShiftServer();
            //弹性打卡偏移量  秒
            $current_flexible_time = $HrShiftServer->getCurrentFlexibleShiftTime([
                'started_at_time' => strtotime($this->staffAData[$date]['started_at']),
                'shift_id'        => $this->staffAData[$date]['shift_id'],
                'date'            => $date,
            ]);
        }

        if (!empty($this->staffAData[$date]['started_at']) && !empty($this->staffAData[$date]['shift_start'])) {
            //不考虑宽限分钟数，精确到分钟
            $start_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['started_at']));

            if(strtotime($start_at) > strtotime($date.' '.$this->staffAData[$date]['shift_start']) + $current_flexible_time + ($leaveType == AttendanceCalendarEnums::LEAVE_UP_TYPE ? $seconds : 0)){
                $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GO_WORK, AttendanceCalendarEnums::BE_LATE);
            }

        }
        if (!empty($this->staffAData[$date]['end_at']) && !empty($this->staffAData[$date]['shift_end']) && !empty($this->staffAData[$date]['shift_start'])) {
            $shift_end = strtotime($date.' '.$this->staffAData[$date]['shift_end']) + $current_flexible_time;
            //处理跨天班次
            if (strtotime($date.' '.$this->staffAData[$date]['shift_start']) > strtotime($date.' '.$this->staffAData[$date]['shift_end'])) {
                $shift_end = strtotime($date.' '.$this->staffAData[$date]['shift_end']) + 24 * 3600 + $current_flexible_time;
            }

            //不考虑宽限分钟数，精确到分钟
            $end_at = date('Y-m-d H:i', strtotime($this->staffAData[$date]['end_at']));

            if (strtotime($end_at) < ($shift_end - ($leaveType == AttendanceCalendarEnums::LEAVE_DOWN_TYPE ? $seconds : 0))) {
                $this->markMissingCardAndLateAndLeaveEarly($date, AttendanceCalendarEnums::GET_OFF_WORK, AttendanceCalendarEnums::LEAVE_EARLY);
            }
        }
    }

    /**
     * @description 获取处罚列表显示权限
     * @return bool
     */
    protected function getPenaltyPermission(): bool
    {
        if($this->staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
            return false;
        }
        $server = new AttendancePenaltyServer($this->lang, $this->timeZone);
        //这里不传网点ID，始终显示入口
        return $server->checkIsNeedPenalty($this->staffInfo['job_title'], $this->staffInfo['sys_department_id']);
    }

}