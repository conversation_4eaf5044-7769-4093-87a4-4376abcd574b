<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 11/3/21
 * Time: 8:01 PM
 */

namespace FlashExpress\bi\App\Modules\Ph\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\OutsourcingOvertimeApplyModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingOvertimeModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\OsOvertimeServer as GlobalBaseServer;

class OsOvertimeServer extends GlobalBaseServer
{

    public $staffInfo = [];//staff re 方法 getStaffPosition 返回值
    public $hour = 9;//根据上班打卡时间 推算可加班时间

    const OT_CV = 60;//有效ot 容差
    /**
     * 获取加班类型
     * @Access  public
     * @Param   request $paramIn
     * @Param   request $userinfo
     * @Return  jsonData
     */
    public function getTypeOsOvertime($paramIn = [])
    {
        $data = $this->getAllType();
        $returnData['data']['dataList'] = $data;
        if (!empty($paramIn['is_svc'])) {//bi工具 或者其他 不需要限制的 直接调用
            return $this->checkReturn($returnData);
        }
        //重新查一下 缓存的不是最新的
        $staff_re = new StaffRepository($this->lang);
        if(empty($this->staffInfo)){
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        //获取有权限的类型和时长
        $data = $this->getOsTypeDuration($data);
        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    /**
     * 获取加班类型，用于hcm-api系统展示
     */
    public function getOsOtTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }
        $data = $this->getTypeOsOvertime($paramIn);
        return $data['data']['dataList'] ?? [];
    }

    //所有加班类型展示 这里面不做权限判断
    public function getAllType($locale = '')
    {
        if(empty($locale)){
            $locale = $this->lang;
        }
        return [
            [
                //外协延长工作时间加班
                'code' => OutsourcingOvertimeModel::TYPE_ADD_WORK_TIME,
                'msg' => $this->getTranslation($locale)->_('os_add_work_time'),
            ],
        ];
    }


    /**
     * 校验加班起止时间
     * @throws \Exception
     */
    public function checkPeriod()
    {
        $date       = $this->param['date_at'];
        $date_tmp   = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'), time());

        // 根据生效日期取新或旧班次
        $start = $this->shiftInfo['start'] ?? '';
        $end   = $this->shiftInfo['end'] ?? '';

        //如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$start}");
        $shift_end   = strtotime("{$date} {$end}");
        if ($shift_start > $shift_end && $this->param['type'] == OutsourcingOvertimeModel::TYPE_ADD_WORK_TIME) {
            $beforeTime = strtotime(date("Y-m-d", strtotime("-1 day")));
        }
        if ($date_tmp < $beforeTime || $date_tmp > $behindTime) {
            $notice_str = $this->getTranslation()->_('os_err_msg_ot_only_cur_day');
            throw new ValidationException($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }
    }
    //验证
    public function checkOsOt()
    {
        $staffId    = $this->param['os_id'];
        $start_time = $this->param['start_time'];
        $startTime = strtotime($start_time);
        $start_date = date('Y-m-d', $startTime);//申请开始时间日期

        //新需求 OT申请开始时间不得晚于申请日期次日中午12点。
        $nextDay = date('Y-m-d 12:00:00', strtotime("+1 day", strtotime($this->param['date_at'])));
        $nextDayStamp = strtotime($nextDay);
        if ($startTime > $nextDayStamp) {
            throw new ValidationException($this->getTranslation()->_('os_ot_time_limit'));
        }

        //开始时间的日期不能早于申请日期
        if ($start_date < $this->param['date_at']) {
            $this->logger->write_log("checkOvertime  {$staffId} wrong_date [-1]" ,'info');
            throw new ValidationException($this->getTranslation()->_('wrong_date'));
        }

        $this->extend_check();
        //校验 同一天是否存在 有交集的ot
        $exist = OutsourcingOvertimeModel::findFirst([
            'conditions' => 'os_staff_id = :staff_id: and date_at = :date_at: and state in (1,2)',
            'bind' => ['staff_id' => $staffId, 'date_at' => $this->param['date_at']]
        ]);

        if(!empty($exist)){
            throw new ValidationException($this->getTranslation()->_('os_ot_exist'));//当日已申请过延长班次，请勿重复申请'
        }
        return true;
    }

    //https://flashexpress.feishu.cn/wiki/CTekwMRqFioCIRkNOpAcEQOQnQT
    public function extend_check()
    {
        $config_hour = $this->config->application->add_hour;
        $staff_id    = $this->param['os_id'];
        $date        = date('Y-m-d', strtotime($this->param['date_at']));
        $add_hour    = $this->hour;
        //获取请假记录 如果加班日当天 存在下午请假
        $leave_re   = new AuditRepository($this->lang);
        $leave_info = $leave_re->get_leave_date($staff_id, $date, $date);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if (!empty($leave_info) && $leave_info[0]['leave_type'] != 15) {
            if ($leave_info[0]['type'] != 1) {
                throw new ValidationException($this->getTranslation()->_('os_overtime_leave_limit'));
            }
            $add_hour = $add_hour / 2;//减去半天
        }

        //没有班次信息 不让申请
        if (empty($this->shiftInfo)) {
            throw new ValidationException($this->getTranslation()->_('no_shift_notice'));
        }

        //如果 没打上班卡 不让申请
        $att_info = StaffWorkAttendance::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
        if (empty($att_info)) {
            //没上班卡 判断是否出差 取出差打卡 上班卡信息
            $att_info = StaffAuditReissueForBusinessModel::findFirst("staff_info_id = {$staff_id} and attendance_date = '{$date}'");
            if (empty($att_info)) {
                throw new ValidationException($this->getTranslation()->_('os_overtime_att_start'));
            }
            $att_info               = $att_info->toArray();
            $att_info['started_at'] = $att_info['start_time'];
        } else {
            $att_info = $att_info->toArray();
        }

        if (empty($att_info['started_at'])) {
            throw new ValidationException($this->getTranslation()->_('os_overtime_att_start'));
        }

        //去秒数
        $att_info['started_at'] = date('Y-m-d H:i:00', strtotime("{$att_info['started_at']}"));

        //通过日期判断新旧班次
        $start = $this->shiftInfo['start'];

        //跟班次比对 如果是迟到 加班开始时间 应该在 迟到小时+1小时 时间整点
        $shift_start_time = strtotime("{$date} {$start}");
        $card_time        = strtotime($att_info['started_at']) + ($config_hour * 3600);
        //没迟到  取班次时间整点 加对应的小时数
        $limit_start = date('Y-m-d H:i:s', $shift_start_time + ($add_hour * 3600));
        if ($card_time > $shift_start_time) {//如果迟到 取打卡时间 加1小时 再加上对应的小时数
            $shift_i     = date("i", $shift_start_time);//取班次的分钟
            $limit_start = date("Y-m-d H:{$shift_i}:00", $card_time + 3600);
            $limit_start = date('Y-m-d H:i:s', strtotime($limit_start) + ($add_hour * 3600));
        }

        $ot_start_time = date('Y-m-d H:i:s', strtotime($this->param['start_time']));
        $l1            = date('Y-m-d H:i:s', $shift_start_time);
        $l2            = date('Y-m-d H:i:s', $card_time);
        $this->logger->write_log("{$staff_id} add_hour {$add_hour},shift_start_time {$l1},card_time {$l2},limit_start {$limit_start},ot_start_time {$ot_start_time} ", 'info');
        if ($ot_start_time < $limit_start) {
            throw new ValidationException($this->getTranslation()->_('os_overtime_forbidden', ['shift_time' => $start,'card_time' => $l2,'limit_time' => $limit_start]));
        }
        return true;
    }




    /**
     * 获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        return [];
    }

    //处理加班是否有效
    public function dealOvertime($otInfo)
    {
        $attendanceInfo = StaffWorkAttendance::findFirst([
            'columns'    => 'started_at, end_at',
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date_at:',
            'bind'       => ['staff_id' => $otInfo->os_staff_id, 'date_at' => $otInfo->date_at],
        ]);

        $otInfo->updated_at = new \Phalcon\Db\RawValue('updated_at');
        //缺卡
        if (empty($attendanceInfo) || empty($attendanceInfo->started_at) || empty($attendanceInfo->end_at)) {
            $otInfo->salary_state = OutsourcingOvertimeModel::SALARY_STATE_MISS_CARD;
            $flag                 = $otInfo->update();
            $this->logger->write_log("dealOvertime  {$otInfo->os_staff_id} {$otInfo->date_at} " . OutsourcingOvertimeModel::SALARY_STATE_MISS_CARD,
                'info');
            return $flag;
        }

        $attStart = show_time_zone($attendanceInfo->started_at);
        $attEnd   = show_time_zone($attendanceInfo->end_at);

        $current_ot_cv = self::OT_CV;
        //有效OT
        if (strtotime($attStart) <= (strtotime($otInfo->start_time) + $current_ot_cv) && strtotime($attEnd) >= (strtotime($otInfo->end_time) - $current_ot_cv)) {
            $otInfo->salary_state = OutsourcingOvertimeModel::SALARY_STATE_EFFECTIVE;
            $flag                 = $otInfo->update();
            $this->logger->write_log("dealOvertime  {$otInfo->os_staff_id} {$otInfo->date_at} " . OutsourcingOvertimeModel::SALARY_STATE_EFFECTIVE,
                'info');
            return $flag;
        }

        //没包住
        $otInfo->salary_state = OutsourcingOvertimeModel::SALARY_STATE_COVER;//打卡时间没覆盖
        $flag                 = $otInfo->update();
        $this->logger->write_log("dealOvertime  {$otInfo->os_staff_id} {$otInfo->date_at} " . OutsourcingOvertimeModel::SALARY_STATE_COVER,
            'info');
        return $flag;
    }


    //规则不同 规则文案放后端了
    public function ruleConfirm($staffInfo){
        return $this->getTranslation()->_('os_ot_rule_ph');
    }
    
    public function addOsOvertime($param)
    {
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            $duration = floatval($param['duration']);
            $end_time = date('Y-m-d H:i:s', strtotime($param['start_time']) + ($duration * 3600));
            $insert['staff_info_id'] = $param['operator'] ?? 10000;//操作申请人
            $insert['os_staff_ids']  = $param['staff_id'];
            $insert['type']          = OutsourcingOvertimeModel::TYPE_ADD_WORK_TIME;
            $insert['start_time']    = $param['start_time'];
            $insert['end_time']      = $end_time;
            $insert['duration']      = $duration;
            $insert['reason']        = $param['reason'];
            $insert['date_at']       = date('Y-m-d');
            $insert['lang']          = $this->lang;
            $insert['is_finished']   = OutsourcingOvertimeApplyModel::FINISHED;
            $res = $db->insertAsDict('outsourcing_overtime_apply', $insert);
            if (!$res) {
                throw new \Exception('插入数据失败');
            }
            $apply_id = $db->lastInsertId();
            
            $_param['id'] = $apply_id;//主表主键
            $_param['staff_id'] = $insert['staff_info_id'];
            $_param['os_id'] = $param['staff_id'];//被操作人
            $_param['type'] = $insert['type'];
            $_param['start_time'] = $insert['start_time'];
            $_param['end_time'] = $insert['end_time'];
            $_param['reason'] = $insert['reason'];
            $_param['duration'] = $insert['duration'];
            $_param['date_at'] = $insert['date_at'];
            $_param['is_svc'] = 1;
            $this->param = $_param;
            $this->initOsOt();
            $this->checkPeriod();
            $this->checkOsOt();
            $typeList = (new OsOvertimeServer($this->lang, $this->timezone))->getTypeOsOvertime(['staff_id'=> $_param['os_id']]);
            if ($typeList['code'] != 1 || empty($typeList['data']['dataList'])) {
                throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
            }
            $typeList = array_column($typeList['data']['dataList'], null, 'code');
            $typeArr = array_keys($typeList);
            if (!in_array($this->param['type'], $typeArr)) {
                throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
            }
            $durationArr = array_column($typeList[$this->param['type']]['duration'], 'time_hour');
            $this->logger->write_log("checkTypeDuration {$this->staffInfo['id']} " . json_encode($typeArr) . json_encode($durationArr),
                'info');
            if (!in_array($this->param['duration'], $durationArr)) {
                throw new ValidationException($this->getTranslation()->_('os_ot_duration_permission'));
            }
            $serialNo    = $this->getID();
            
            $insertParam = [
                'serial_no'     => (!empty($serialNo) ? 'OSOT' . $serialNo : null),
                'oid'           => $this->param['id'],
                'staff_info_id' => $this->param['staff_id'],
                'os_staff_id'   => $this->param['os_id'],
                'type'          => $this->param['type'],
                'start_time'    => $this->param['start_time'],
                'end_time'      => $this->param['end_time'],
                'reason'        => $this->param['reason'],
                'reject_reason' => '',
                'state'         => enums::$audit_status['approved'],
                'approval_id'   => $_param['staff_id'],
                'approval_name' => $param['operator_name'] ?? '',
                'duration'      => $this->param['duration'],
                'date_at'       => $this->param['date_at'],
                'detail_data'   => '',
                'in_approval'   => OutsourcingOvertimeModel::NOT_IN_APPROVAL,
            ];
            $res = $db->insertAsDict('outsourcing_overtime', $insertParam);
            if (!$res) {
                throw new \Exception('插入数据失败');
            }
            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw new ValidationException($e->getMessage());
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log([
                'func' => 'addOsOvertime',
                'param' => $param,
                'error' => $e->getMessage()
                ]
            );
            throw new ValidationException($this->getTranslation()->_('4008'));
        }
        return true;
    }


}
