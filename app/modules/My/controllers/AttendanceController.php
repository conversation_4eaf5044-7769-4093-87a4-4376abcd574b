<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\ClockEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Modules\My\Server\AttendanceServer;
use FlashExpress\bi\App\Modules\My\Server\CheckPunchOutServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class AttendanceController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }



    /**
     * Notes: 缺卡提醒、迟到早退惩罚提醒
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function malformedAction()
    {
        $param                        = [];
        $param['attendance_date']     = $this->request->get('attendance_date');
        $param['attendance_category'] = $this->request->get('attendance_category');
        $param['shift_type']          = $this->request->get('shift_type') ?? 0;
        $param['shift_index']         = $this->request->get('shift_index') ?? 0;
        $server                       = new AttendanceServer($this->lang, $this->timezone);
        $param['user_info']           = $this->userinfo;
        $result                       = $server->malformed($param);
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }


    //新版 info 接口
    public function newAttendanceInfoAction()
    {
        $param              = $this->paramIn;
        $param['user_info'] = $this->userinfo;
        $param['platform']  = strtoupper($this->platform);
        //获取该员工 该天考勤信息
        $server = new AttendanceServer($this->lang, $this->timezone);
        $info   = $server->newAttendanceInfo($param);
        $this->logger->write_log('new_attendance_info '.$this->userinfo['id'].json_encode($info).json_encode($param),
            'info');
        return $this->jsonReturn(['code' => 1, 'message' => 'success', 'data' => $info]);
    }

    //新版 保存接口
    public function newCreateAttendanceAction()
    {
        $param              = $this->paramIn;
        $setVal = (new SettingEnvServer())->getSetVal('punch_out_switch');
        //增加拦截验证 只针对下班
        if(!empty($setVal) && in_array($param['shift_index'], [enums::ATTENDANCE_SHIFT_INDEX_2,enums::ATTENDANCE_SHIFT_INDEX_4])){
            $paramsIn['is_skip']     = 1;
            $paramsIn['platform']    = $this->platform;
            $paramsIn['shift_type']  = $param['shift_type'] ?? 0;
            $paramsIn['shift_index'] = $param['shift_index'] ?? 0;
            $userInfo                = $this->userinfo;
            $checkPunchOutServer     = new CheckPunchOutServer($this->lang, $this->timezone);
            $check                    = $checkPunchOutServer->check_staff_punch_out($userInfo, $paramsIn);
            $this->logger->write_log("attendance_check_punch_out {$this->userinfo['id']} " . json_encode($check, JSON_UNESCAPED_UNICODE), 'info');
            if(!empty($check) && $check['code'] == '-3'){
                return $this->jsonReturn($check);
            }
        }
        $param['user_info'] = $this->userinfo;
        $param['platform']  = strtoupper($this->platform);
        $server = new AttendanceServer($this->lang, $this->timezone);
        //保存打卡
        /**
         * @see AttendanceServer::saveAttendance()
         */
        $flag = $server->saveAttendanceUseLock($param);
        $this->logger->write_log("new_create_attendance {$this->userinfo['id']} ".json_encode($flag), 'info');
        if (is_array($flag)) {
            http_response_code(422);
            return $this->jsonReturn($flag);
        }
        if ($flag) {
            return $this->jsonReturn(['code' => 1, 'message' => 'success', 'data' => null]);
        }
        return $this->jsonReturn(['code' => -1, 'message' => 'failed', 'data' => null]);
    }

    /**
     * 上班打卡提醒
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function check_clock_in_beforeAction()
    {
        //[1]入参
        $paramIn['attendance_date'] = $this->request->get('attendance_date', 'trim');
        $paramIn['shift_type']      = $this->request->get('shift_type', 'int');
        $paramIn['shift_index']     = $this->request->get('shift_index', 'int');
        $userinfo                   = $this->userinfo;
        $paramIn['platform']        = $this->platform;

        //历史接口传参，目前接口无使用，预留多班次，枚举暂不定义
        $validations = [
            "shift_type"      => "IntIn:1,2|>>>:shift_type" . $this->t->_('miss_args'),
            "shift_index"     => "IntIn:1,2,3,4|>>>:shift_index" . $this->t->_('miss_args'),
            "attendance_date" => "Date|>>>:attendance_date" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new CheckPunchOutServer($this->lang, $this->timezone))->checkClockInBefore($paramIn, $userinfo);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 跳过限制打卡业务
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function set_clock_in_skipAction()
    {
        //[1]入参
        $paramIn['business_type'] = $this->request->get('business_type', 'trim');
        $userinfo                 = $this->userinfo;

        $validations = [
            "business_type" => "Required|StrIn:" . implode_array_keys(ClockEnums::$business_types) . "|>>>:business_type" . $this->t->_('miss_args'),
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new CheckPunchOutServer($this->lang, $this->timezone))->setClockSkip($paramIn, $userinfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * @description 获取下次侦测时间、当前时间
     */
    public function getNextDetectTimeAction()
    {
        //获取参数
        $staffId = $this->userinfo['id'];
        $result  = [
            "is_need_verify"     => false,
        ];

        try {
            $attServer             = new AttendanceServer($this->lang, $this->timezone);
            $param['user_info']    = $this->userinfo;
            $attendanceInfo        = $attServer->newAttendanceInfo($param);
            $getBrushFaceStaffInfo = $attServer->getBrushFaceStaffInfoFromCache($staffId, $attendanceInfo['attendance_date']);
            if (!$getBrushFaceStaffInfo['is_check']) { //员工身份不符合
                throw new ValidationException('success', ErrCode::SUCCESS);
            }

            if ($attServer->outsourceStaffPunchField($staffId)) { //如果在白名单中不校验
                throw new ValidationException('success', ErrCode::SUCCESS);
            }

            if ($attServer->checkIsNeedVerify($staffId, $attendanceInfo['attendance_date'])) {
                $result['is_need_verify'] = true;
            } else {
                $result['is_need_verify'] = false;
            }
        } catch (ValidationException $ve) {
            $returnCode = $ve->getCode();
            $message    = $ve->getMessage();
        }

        $this->jsonReturn(['code' => $returnCode ?? ErrCode::SUCCESS, 'message' => $message ?? 'success', 'data' => $result]);
    }
}
