<?php
/**
 * Author: Bruce
 * Date  : 2024-11-12 23:04
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\Controllers\RenewContractBusinessController as BaseRenewContractBusinessController;
use FlashExpress\bi\App\Modules\My\Server\RenewContractBusinessServer;

class RenewContractBusinessController extends BaseRenewContractBusinessController
{

    /**
     * 个人代理合同续约 审批
     */
    public function updateAction()
    {
        $paramIn = $this->paramIn;

        $validations         = [
            "audit_id"           => "Required|Int",
            "status"              => "Required|IntIn:2,3,4|>>>:status " . $this->getTranslation()->_('miss_args'),
            "reject_reason"      => "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1020'),
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new RenewContractBusinessServer($this->lang, $this->timezone))->updateUseLock($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}