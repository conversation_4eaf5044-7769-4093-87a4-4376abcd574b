<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\ToolController as GlobalBaseToolController;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Modules\My\library\Enums\AbortList;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Server\RoyaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\ShareCenterServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\ToolServer;

class ToolController extends GlobalBaseToolController
{    /**
 * 我的页面输出地址菜单和配置
 */
    public function aboutAction()
    {
        $higherStaff = AbortList::DEFAULT_CONFIG;

        $paramIn['userinfo'] = $this->userinfo;

        $StaffServer    = new StaffServer();
        $RoyaltyServer  = new RoyaltyServer($this->lang, $this->timezone, $this->userinfo);
        $headerData     = $this->request->getHeaders();
        $equipment_type = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');
        $equipment_type = strtoupper($equipment_type);
        $enum_type      = \FlashExpress\bi\App\library\enums::EQM_TYPE;
        $is_by_ask      = isset($enum_type[$equipment_type]) && $enum_type[$equipment_type] == 3 ? true : false; //是否为 by 请求
        //判断是否为可显示账号设置
        $is_account_settings = $is_by_ask ? $StaffServer->isShowAccountSettings($paramIn['userinfo']['id']) : false;

        $staffInfo = $StaffServer->get_staff($paramIn['userinfo']['id'])['data'];
        if ($staffInfo) {
            $returenData = $StaffServer->getStoreManager($staffInfo['store_id']);
        }
        $t           = $this->getTranslation();
        $server      = new BackyardServer($this->lang, $this->timezone);
        $shareServer = new ShareCenterServer($this->lang, $this->timezone);
        $isLnt = (new StaffServer())->isLntStaff($paramIn['userinfo']['id']);

        $config = (new SettingEnvServer())->listByCode(['No_Show_Regulation_CompanyID','No_Show_Information_Center_CompanyID']);
        $config = array_column($config,'set_val','code');

        $no_show_regulation = !empty($config['No_Show_Regulation_CompanyID']) && in_array($staffInfo['contract_company_id'],explode(',',$config['No_Show_Regulation_CompanyID'])) ? true : false;
        $no_show_information_center = !empty($config['No_Show_Information_Center_CompanyID']) && in_array($staffInfo['contract_company_id'],explode(',',$config['No_Show_Information_Center_CompanyID'])) ? true : false;

        foreach ($higherStaff as $key => &$val) {
            $higherStaff[$key]['title'] = $t->_('abort_list_'.$val['id']);

            //如果是LNT公司  Flash box 改名为：Inquiry
            if($val['id'] == 'mailbox' && $isLnt){
                $higherStaff[$key]['title'] = $t->_('abort_list_inquiry');
            }
            //销售代理
            if (in_array(RolesModel::SALES_AGENT, $this->userinfo['positions']) && $val['id'] != 'about') {
                unset($higherStaff[$key]);
            }

            //联合判断有没有权限看没有权限是空数据
            if ($val['id'] == 'royalty') {
                $returnArr = $RoyaltyServer->getRoyaltyInfo($paramIn);
                //拼接提成
                if ($returnArr['code'] == 1) {
                    $higherStaff[$key]['title'] = $t->_('abort_list_'.$val['id'])
                        .$returnArr['data']['dataList']['royaltyInfo']['monthRoyaltyTitle'];
                } else {
                    unset($higherStaff[$key]);
                }
            }

            //判断权限
            if ($val['id'] == 'asset') {
                if (empty($returenData) || $returenData != $paramIn['userinfo']['id']) { //该网点不存在负责人或当前登录的人不是负责人
                    unset($higherStaff[$key]);
                }
            }

            //判断权限
            if ($val['id'] == 'sop') {
                //个人代理 规章制度&SOP
                if (in_array($staffInfo['hire_type'] , HrStaffInfoModel::$agentTypeTogether) || $no_show_regulation) {
                    unset($higherStaff[$key]);
                }
            }


            //紧急事故 权限
            if ($val['id'] == 'emergency') {
                $flag = $server->emergency_permission($this->userinfo['id']);
                if (!$flag) {
                    unset($higherStaff[$key]);
                }
            }

            //网点信息权限 当前登陆人 是网点员工 并且是 网点负责人 才显示
            if ($val['id'] == 'storeinfo') {
                //网点负责人 为空 可能登陆员工是总部 或者 所属网点没负责人  或者 不是当前登陆人
                if (empty($staffInfo['store_manager_id']) || $staffInfo['store_manager_id'] != $this->userinfo['id']) {
                    unset($higherStaff[$key]);
                }
            }

            //个人信息-是否有待签署合同
            if ($val['id'] == 'myinfo') {
                $unhandle_contract = [];
                $contractObj       = HrStaffContractModel::find([
                    'conditions' => 'staff_id = :staff_id: and contract_is_deleted = 0 and contract_is_need = 1 and contract_status=30',
                    'bind'       => [
                        'staff_id' => $this->userinfo['id'],
                    ],
                ]);
                if (!empty($contractObj)) {
                    $unhandle_contract = $contractObj->toArray();
                }
                $val['is_have_unhandle_contract'] = !empty($unhandle_contract) ? true : false;

                $val['badge_field_name'] = 'un_myinfo_count';
            }

            // 账号设置
            if ($val['id'] == 'account_cancellation') {
                if (!$is_account_settings) { //如果不显示 就销毁
                    unset($higherStaff[$key]);
                }
            }

            // 文件共享中心
            if ($val['id'] == 'shareCenter') {
                //个人代理 信息共享中心
                if (in_array($staffInfo['hire_type'] , HrStaffInfoModel::$agentTypeTogether) || $no_show_information_center) {
                    unset($higherStaff[$key]);
                } else {
                    $val['is_new'] = $shareServer->checkShareCenterHaveNewFile($paramIn['userinfo']['id']);
                }
            }

            //Flash box入口 仅 编制 实习生可见
            if($val['id'] == 'mailbox' && !in_array($staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])){
                unset($higherStaff[$key]);
            }

            //设置 子账号，合作商加盟商工号
            if ($val['id'] == 'setting' && ToolServer::isHiddenSettingConfig($staffInfo['formal'],$staffInfo['is_sub_staff'],$paramIn['userinfo']['id'])) {
                unset($higherStaff[$key]);
            }


            //切换国家地址
            //urlencode('https://backyard-ui.flashexpress.com')
            $env_url    = $val['id'] == 'mailbox' ? urlencode(env('h5_endpoint')) : urlencode(env('sign_url'));
            $val['dst'] = str_replace('{url}', $env_url, $val['dst']);
        }
        $higherStaff = array_values($higherStaff);
        $returnData  = [
            'code' => 1,
            'msg'  => 'success',
            'data' => $higherStaff,
        ];
        $this->jsonReturn($returnData);
    }

}