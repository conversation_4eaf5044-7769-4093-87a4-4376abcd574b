<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\Controllers\JobTransferConfirmController as BaseJobTransferConfirmController;
use FlashExpress\bi\App\Modules\My\Server\JobTransferConfirmServer;

class JobTransferConfirmController extends BaseJobTransferConfirmController
{
    /**
     * @description 获取转岗确认详情
     * @throws \Exception
     */
    public function confirmDetailAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //校验参数
        $this->validateCheck($param, JobTransferConfirmServer::$validateDetail);

        //[2]查询相同请求
        $server                 = new JobTransferConfirmServer($this->lang, $this->timezone);
        $param['staff_info_id'] = $server->changeStaffIdToMaster($param['staff_info_id']);
        $returnArr              = $server->getConfirmDetail($param);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * @description 转岗确认
     */
    public function doConfirmAction()
    {
        //[1]参数定义
        $param                  = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];

        //校验参数
        $this->validateCheck($param, JobTransferConfirmServer::$validateConfirm);

        //[2]查询相同请求
        $server                 = new JobTransferConfirmServer($this->lang, $this->timezone);
        $param['staff_info_id'] = $server->changeStaffIdToMaster($param['staff_info_id']);
        $returnArr              = $server->doConfirmUseLock($param);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}