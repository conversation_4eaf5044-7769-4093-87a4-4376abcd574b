<?php
/**
 * Author: Bruce
 * Date  : 2022-12-08 18:03
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\Modules\My\Server\CeoMailServer;

class CeoMailController extends BaseController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }

    // 问题分类列表
    public function problemCategoryListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        try{
            // 获取问题分类列表
            $data = (new CeoMailServer($this->lang, $this->timezone))->getCategoryList($paramIn);

            $result = [
                'code' => 1,
                'msg' => 'success',
                'data' => $data
            ];
            return $this->jsonReturn(self::checkReturn($result));
        } catch (\Exception $e){
            $logger = $this->getDI()->get('logger');
            $logger->write_log("CEO MAIL: problemCategoryList-" .$e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_server')));
        }
    }

}