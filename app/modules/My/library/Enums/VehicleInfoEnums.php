<?php
// 车辆信息管理 2.0版本 2021.03

namespace FlashExpress\bi\App\Modules\My\library\Enums;

class VehicleInfoEnums
{
    // 翻译系统前缀
    const TRANSLATION_PREFIX = 'vehicle_info_';

    // 车辆来源
    const TRANSLATION_PREFIX_VEHICLE_SOURCE = self::TRANSLATION_PREFIX.'vehicle_source_';
    const VEHICLE_SOURCE_PERSONAL_CODE = 1;// 使用个人车辆
    const VEHICLE_SOURCE_RENTAL_CODE = 2;  // 租用公司车辆
    const VEHICLE_SOURCE_BORROW_CODE = 3;  // 借用车辆
    const VEHICLE_SOURCE_ITEM = [
        self::VEHICLE_SOURCE_PERSONAL_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_PERSONAL_CODE,
        self::VEHICLE_SOURCE_RENTAL_CODE   => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_RENTAL_CODE,
        self::VEHICLE_SOURCE_BORROW_CODE   => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_BORROW_CODE,
    ];

    // 驾照类型
    const DRIVER_LICENSE_TYPE_001 = 1;
    const DRIVER_LICENSE_TYPE_002 = 2;
    const DRIVER_LICENSE_TYPE_003 = 3;
    const DRIVER_LICENSE_TYPE_004 = 4;
    const DRIVER_LICENSE_TYPE_005 = 5;

    const DRIVER_LICENSE_TYPE_ITEM = [
        self::DRIVER_LICENSE_TYPE_001 => "Learner's Driving Licence (LDL)",
        self::DRIVER_LICENSE_TYPE_002 => 'Probationary Driving Licence (PDL)',
        self::DRIVER_LICENSE_TYPE_003 => 'Competent Driving Licence (CDL)',
        self::DRIVER_LICENSE_TYPE_004 => 'Goods Driving Licence (GDL)',
        self::DRIVER_LICENSE_TYPE_005 => 'Public Service Vehicle (PSV)',
    ];

    //驾照车辆限制
    const DRIVING_LICENSE_VEHICLE_RESTRICTIONS = [
        '1'  => ['value' => '1', 'label' => 'A'],
        '2'  => ['value' => '2', 'label' => 'A1'],
        '3'  => ['value' => '3', 'label' => 'B'],
        '4'  => ['value' => '4', 'label' => 'B1'],
        '5'  => ['value' => '5', 'label' => 'B2'],
        '6'  => ['value' => '6', 'label' => 'C'],
        '7'  => ['value' => '7', 'label' => 'D'],
        '8'  => ['value' => '8', 'label' => 'DA'],
        '9'  => ['value' => '9', 'label' => 'E'],
        '10' => ['value' => '10', 'label' => 'E1'],
        '11' => ['value' => '11', 'label' => 'E2'],
        '12' => ['value' => '12', 'label' => 'F'],
        '13' => ['value' => '13', 'label' => 'G'],
        '14' => ['value' => '14', 'label' => 'H'],
        '15' => ['value' => '15', 'label' => 'I'],
        '16' => ['value' => '16', 'label' => 'M'],
    ];

    // 审核状态
    const TRANSLATION_PREFIX_APPROVAL_STATUS = self::TRANSLATION_PREFIX.'approval_status_';
    const APPROVAL_UN_SUBMITTED_CODE = 0;                   // 未提交
    const APPROVAL_PENDING_CODE = 1;                        //待审核
    const APPROVAL_PASSED_CODE = 2;                         //通过
    const APPROVAL_REJECT_CODE = 3;                         //驳回
    const APPROVAL_STATUS_ITEM = [
        self::APPROVAL_UN_SUBMITTED_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_UN_SUBMITTED_CODE,
        self::APPROVAL_PENDING_CODE      => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PENDING_CODE,
        self::APPROVAL_PASSED_CODE       => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PASSED_CODE,
        self::APPROVAL_REJECT_CODE       => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_REJECT_CODE,
    ];

    // 车辆添加渠道
    const VEHICLE_ADD_CHANNEL_KIT = 'kit';
    const VEHICLE_ADD_CHANNEL_FBI = 'fbi';

    // 车辆模块有权限的职位
    const JOB_VAN_TITLE_ID = 110;
    const BDC_DRIVER_JOB_TITLE_ID = 1759;
    const JOB_VAN_PROJECT_TITLE_ID = 1015;
    const JOB_BIKE_TITLE_ID = 13;
    const JOB_TRICYCLE_TITLE_ID = 1000;
    const JOB_CAR_TITLE_ID = 1199;

    const JOB_VAN_TITLE_NAME = 'Van Courier';
    const JOB_VAN_PROJECT_TITLE_NAME = 'Van Courier (Project)';
    const JOB_BIKE_TITLE_NAME = 'Bike Courier';
    const JOB_TRICYCLE_TITLE_NAME = 'Tricycle Courier';
    const JOB_CAR_TITLE_NAME = 'Car Courier';

    const JOB_TITLE_ITEM = [
        self::JOB_VAN_TITLE_ID         => self::JOB_VAN_TITLE_NAME,
        self::JOB_VAN_PROJECT_TITLE_ID => self::JOB_VAN_PROJECT_TITLE_NAME,
        self::JOB_BIKE_TITLE_ID        => self::JOB_BIKE_TITLE_NAME,
        self::JOB_TRICYCLE_TITLE_ID    => self::JOB_TRICYCLE_TITLE_NAME,
        self::JOB_CAR_TITLE_ID         => self::JOB_CAR_TITLE_NAME,

    ];

    // 车辆类型
    const VEHICLE_TYPE_BIKE_CODE = 0;
    const VEHICLE_TYPE_VAN_CODE = 1;
    const VEHICLE_TYPE_TRICYCLE_CODE = 2;
    const VEHICLE_TYPE_CAR_CODE = 3;

    const VEHICLE_TYPE_BIKE_TEXT = 'Bike';
    const VEHICLE_TYPE_VAN_TEXT = 'Van';
    const VEHICLE_TYPE_CAR_TEXT = 'Car';
    const VEHICLE_TYPE_TRICYCLE_TEXT = 'Tricycle';

    const VEHICLE_TYPE_ITEM = [
        self::VEHICLE_TYPE_BIKE_CODE     => self::VEHICLE_TYPE_BIKE_TEXT,
        self::VEHICLE_TYPE_VAN_CODE      => self::VEHICLE_TYPE_VAN_TEXT,
        self::VEHICLE_TYPE_TRICYCLE_CODE => self::VEHICLE_TYPE_TRICYCLE_TEXT,
        self::VEHICLE_TYPE_CAR_CODE      => self::VEHICLE_TYPE_CAR_TEXT,

    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_CODE = [
        self::JOB_VAN_TITLE_ID         => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_BIKE_TITLE_ID        => self::VEHICLE_TYPE_BIKE_CODE,
        self::JOB_TRICYCLE_TITLE_ID    => self::VEHICLE_TYPE_TRICYCLE_CODE,
        self::JOB_CAR_TITLE_ID         => self::VEHICLE_TYPE_CAR_CODE,
    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_TEXT = [
        self::JOB_VAN_TITLE_ID         => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_BIKE_TITLE_ID        => self::VEHICLE_TYPE_BIKE_TEXT,
        self::JOB_TRICYCLE_TITLE_ID    => self::VEHICLE_TYPE_TRICYCLE_TEXT,
        self::JOB_CAR_TITLE_ID         => self::VEHICLE_TYPE_CAR_TEXT,
    ];

    // van car 相关职位分组
    const VAN_JOB_GROUP_ITEM = [
        self::JOB_VAN_TITLE_ID,
        self::JOB_VAN_PROJECT_TITLE_ID,
        self::JOB_CAR_TITLE_ID,
    ];


    // 可从 HR-IS 系统 同步过来的枚举字段列表
    const HR_IS_STAFF_CAR_NO_KEY = 'CAR_NO';                // 车牌号
    const HR_IS_STAFF_CAR_TYPE_KEY = 'CAR_TYPE';            // 车牌类型
    const HR_IS_STAFF_MANGER_KEY = 'MANGER';                // 直线主管
    const HR_IS_STAFF_DRIVER_LICENSE_KEY = 'DRIVER_LICENSE';//驾驶证号
    const HR_IS_STAFF_ITEMS = [
        self::HR_IS_STAFF_CAR_NO_KEY,
        self::HR_IS_STAFF_CAR_TYPE_KEY,
        self::HR_IS_STAFF_MANGER_KEY,
        self::HR_IS_STAFF_DRIVER_LICENSE_KEY,
    ];

    // 油类型
    const OIL_TYPE_001 = 1;
    const OIL_TYPE_002 = 2;
    const OIL_TYPE_003 = 3;
    const OIL_TYPE_ITEM = [
        self::OIL_TYPE_001 => 'Ron95',
        self::OIL_TYPE_002 => 'Ron97',
        self::OIL_TYPE_003 => 'Diesel',
    ];

    //油卡补贴方式2:油卡
    const OIL_SUBSIDY_TYPE_1 = 1;
    const OIL_SUBSIDY_TYPE_2 = 2;
    const OIL_SUBSIDY_TYPE_ITEMS = [
        self::OIL_SUBSIDY_TYPE_2 => 'oil_subsidy_type_2',
    ];

    // 油卡开通状态
    const OIL_CARD_STATUS_NOT_OPENED = 0;
    const OIL_CARD_STATUS_OPENED = 1;
    const OIL_CARD_STATUS_ITEM = [
        self::OIL_CARD_STATUS_NOT_OPENED => 'not_open',
        self::OIL_CARD_STATUS_OPENED     => 'already_opened',
    ];

    // 油卡公司
    const OIL_COMPANY_SHELL_CODE = 1;
    const OIL_COMPANY_PETRON_CODE = 2;

    const OIL_COMPANY_ITEM = [
        self::OIL_COMPANY_SHELL_CODE  => 'Shell',
        self::OIL_COMPANY_PETRON_CODE => 'Petron',
    ];

    // 车型
    const VEHICLE_SIZE_ITEM = [
        ['value' => '1', 'label' => '4W1T-10ft'],
        ['value' => '2', 'label' => '6W5T-14ft'],
        ['value' => '3', 'label' => '6W5T-17ft'],
        ['value' => '4', 'label' => '6W7.5T-17ft'],
        ['value' => '5', 'label' => '6W7.5T-20ft'],
        ['value' => '6', 'label' => '6W10T-24ft'],
        ['value' => '7', 'label' => '10W'],
        ['value' => '8', 'label' => 'Other'],
    ];

    // 车类型
    const VEHICLE_TYPE_CATEGORY_LIST = [
        1 => 'Pick-up (sedan)',
        2 => 'Van',
        3 => 'Lorry',
        4 => 'Van Project - Van',
        5 => 'Panel Van',
        6 => 'Window Van',
        7 => 'Car',
        8 => 'MPV',
        9 => 'Van Project - Lorry',
    ];

    /**
     * 职位对应车型号关系
     */
    const JOB_TITLE_VEHICLE_CATEGORY_ITEM = [
        self::JOB_VAN_TITLE_ID => [
            ['value' => 3, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[3]],
            ['value' => 4, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[4]],
            ['value' => 5, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[5]],
            ['value' => 6, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[6]],
            ['value' => 9, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[9]],
        ],
        self::JOB_CAR_TITLE_ID => [
            ['value' => 7, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[7]],
            ['value' => 8, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[8]],
        ],
    ];

    /**
     * LNT职位对应车型号关系
     */
    const LNT_JOB_TITLE_VEHICLE_CATEGORY_ITEM = [
        self::JOB_VAN_TITLE_ID => [
            ['value' => 4, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[4]],
            ['value' => 9, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[9]],
        ],
        self::JOB_CAR_TITLE_ID => [
            ['value' => 7, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[7]],
            ['value' => 8, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[8]],
        ],
    ];


    //从uconfig迁移过来的
    const CONFIG_VEHICLE_INFO = [
        'vehicle_brand'  => [
            [
                'value' => '1',
                'label' => 'CAM',
                'data'  => [
                    ['value' => '1', 'label' => 'JMC Potente'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '2',
                'label' => 'CHANA',
                'data'  => [
                    ['value' => '1', 'label' => 'Era Star II Single Cab'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '3',
                'label' => 'Chevrolet',
                'data'  => [
                    ['value' => '1', 'label' => 'Colorado 2.8 LTZ'],
                    ['value' => '2', 'label' => 'Colorado 2.8 High Country'],
                    ['value' => '3', 'label' => 'Colorado 2.5 LTZ'],
                    ['value' => '4', 'label' => 'Colorado 2.5 LT '],
                    ['value' => '5', 'label' => 'Colorado 2.5 LT (MT)'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '4',
                'label' => 'Daihatsu',
                'data'  => [
                    ['value' => '1', 'label' => 'Gran Max'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '5',
                'label' => 'Ford',
                'data'  => [
                    ['value' => '1', 'label' => 'Ranger 2.2 XL Single Cab (M)'],
                    ['value' => '2', 'label' => 'Ranger 2.2 XL (M)'],
                    ['value' => '3', 'label' => 'Ranger 2.2 XL (A)'],
                    ['value' => '4', 'label' => 'Ranger 2.2 XLT (M)'],
                    ['value' => '5', 'label' => 'Ranger 2.2 XLT (A)'],
                    ['value' => '6', 'label' => 'Ranger 2.0Si-Turbo XLT+(A)'],
                    ['value' => '7', 'label' => 'Ranger 2.0Si-Turbo WildTrak4x2(A)'],
                    ['value' => '8', 'label' => 'Ranger 2.0Bi-TurboWildTrak4x4(A)'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '6',
                'label' => 'FOTON',
                'data'  => [
                    ['value' => '1', 'label' => 'View C2 High Roof 2.0 Petrol'],
                    ['value' => '2', 'label' => 'View C2 High Roof 2.8 Diesel'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '7',
                'label' => 'Golden Dragon',
                'data'  => [
                    ['value' => '1', 'label' => 'X5 3.0L Turbo Diesel'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '8',
                'label' => 'Hino',
                'data'  => [
                    ['value' => '1', 'label' => 'WU300R'],
                    ['value' => '100', 'label' => 'Other'],

                ],
            ],
            [
                'value' => '9',
                'label' => 'ISUZU',
                'data'  => [
                    ['value' => '1', 'label' => 'D-Max 1.9L 4x4 Single Cab'],
                    ['value' => '2', 'label' => 'D-Max 3.0L 4x4 Single Cab'],
                    ['value' => '3', 'label' => 'D-MAX 1.9L 4x4 MT Standard'],
                    ['value' => '4', 'label' => 'D-MAX 1.9L 4x4 AT Standard'],
                    ['value' => '5', 'label' => 'D-MAX 1.9L 4x4 AT Premium'],
                    ['value' => '6', 'label' => 'D-MAX 3.0L 4x4 AT Premium'],
                    ['value' => '7', 'label' => 'D-MAX 3.0L 4x4 AT X-Terrain'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '10',
                'label' => 'Maxus',
                'data'  => [
                    ['value' => '1', 'label' => 'V80 2.5L High Roof Panel Van LWB'],
                    ['value' => '2', 'label' => 'V80 2.5L Window Van 12 Seater'],
                    ['value' => '3', 'label' => 'V80 2.5L Window Van 15 Seater'],
                    ['value' => '4', 'label' => 'V80 2.5L Bas Pekerja Window Van 12 Seater'],
                    ['value' => '5', 'label' => 'V80 2.5L Bas Pesiaran Window Van 15 Seater'],
                    ['value' => '6', 'label' => 'V80 2.5L Bas Pesiaran Window Van 12 Seater'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '11',
                'label' => 'Mazda',
                'data'  => [
                    ['value' => '1', 'label' => 'BT-50 2.2MT'],
                    ['value' => '2', 'label' => 'BT-50 2.2AT'],
                    ['value' => '3', 'label' => 'BT-50 3.2AT'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '12',
                'label' => 'Mitsubishi',
                'data'  => [
                    ['value' => '1', 'label' => 'Triton Quest'],
                    ['value' => '2', 'label' => 'Triton VGT MT'],
                    ['value' => '3', 'label' => 'Triton VGT AT'],
                    ['value' => '4', 'label' => 'Triton VGT AT Premium'],
                    ['value' => '5', 'label' => 'Triton VGT MT Premium'],
                    ['value' => '6', 'label' => 'Fuso'],
                    ['value' => '7', 'label' => 'Center Luton Box'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '13',
                'label' => 'Nissan',
                'data'  => [
                    ['value' => '1', 'label' => 'NP300 Navara Pro-4X 2.5 (A) '],
                    ['value' => '2', 'label' => 'NP300 Navara VL 2.5 （A)'],
                    ['value' => '3', 'label' => 'NP300 Navara SE 2.5 (A)'],
                    ['value' => '4', 'label' => 'NP300 Navara SE 2.5 （M)'],
                    ['value' => '5', 'label' => 'NP300 Navara V 2.5 (A)'],
                    ['value' => '6', 'label' => 'NP300 Navara Single Cab 2.5 (M)'],
                    ['value' => '7', 'label' => 'NV350 Urvan'],
                    ['value' => '8', 'label' => 'NV200 Semi-Panel Van 1.6L '],
                    ['value' => '9', 'label' => 'NV200 Panel Van 1.6L'],
                    ['value' => '10', 'label' => 'NLR'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '14',
                'label' => 'TOYOTA',
                'data'  => [
                    ['value' => '1', 'label' => 'Hilux Double Cab 2.4 E AT 4x4 '],
                    ['value' => '2', 'label' => 'Hilux Double Cab 2.4 G MT 4x4'],
                    ['value' => '3', 'label' => 'Hilux Double Cab 2.4 V AT 4x4'],
                    ['value' => '4', 'label' => 'Hilux Double Cab 2.8 Rogue AT 4x4'],
                    ['value' => '5', 'label' => 'Hiace Panel Van 2.5 Turbo Diesel'],
                    ['value' => '100', 'label' => 'Other'],
                ],
            ],
            [
                'value' => '100',
                'label' => 'Other',
                'data'  => [
                    ['value' => '100', 'car_long' => '0', 'car_width' => '0', 'car_high' => '0', 'label' => 'Other'],
                ],
            ],
        ],
        'vehicle_size'   => self::VEHICLE_SIZE_ITEM,
        'oil_type'       => self::OIL_TYPE_ITEM,
        'oil_company'    => [
            ['value' => self::OIL_COMPANY_SHELL_CODE, 'label' => self::OIL_COMPANY_ITEM[self::OIL_COMPANY_SHELL_CODE]],
            [
                'value' => self::OIL_COMPANY_PETRON_CODE,
                'label' => self::OIL_COMPANY_ITEM[self::OIL_COMPANY_PETRON_CODE],
            ],
        ],
        'branch_mileage' => [
        ],
    ];


    public static function getCodeTxtMap($lang = '', $code = '')
    {
    }

}
