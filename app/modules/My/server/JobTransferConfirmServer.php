<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\JobTransferConfirmEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\PdfEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\WorkflowModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\ApplyRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\JobtransferRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\JobTransferConfirmServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;


//转岗确认
class JobTransferConfirmServer extends GlobalBaseServer
{
    public static $validateConfirm = [
        'id'       => 'Required|Int',
        'state'    => 'Required|Int',
        'sign_url' => 'IfIntEq:state,2|Required|StrLenGe:1',
        'version'  => 'IfIntEq:state,2|Required|StrLenGe:1',
    ];

    /**
     * @description 获取转岗详情
     * 1. 部门、职位、职级、工作网点
     * 2. 薪资
     * 3. Short Notice时间
     * 4. 年假天数
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getConfirmDetail($params)
    {
        $id               = $params['id'];
        $staffInfoId      = $params['staff_info_id'];
        $templateCnf      = $params['template_cnf'] ?? PdfEnums::RESPONSE_TYPE_IMAGE;
        $isSalaryWithUnit = $params['is_salary_with_unit'] ?? false;

            //获取转岗信息
        $jobTransferRepo = new JobtransferRepository($this->timeZone);
        $transferInfo = $jobTransferRepo->getJobTransferDetailById($id);
        if (empty($transferInfo)) {
            return [];
        }

        //获取页眉页脚
        //获取页眉页脚
        $queryParams = [
            'staff_info_id' => $staffInfoId,
            'department_id' => $transferInfo['after_department_id'],
        ];

        //获取页眉页脚
        $templateCnf = (new PdfHelperServer())->getHeaderAndFooter($queryParams);

        //查询职位名称 ids
        $sysObj       = new SysListRepository();
        $positionIds  = [$transferInfo['current_position_id'], $transferInfo['after_position_id']];
        $positionData = $sysObj->getPositionList(['ids' => $positionIds]);
        $positionData = array_column($positionData, 'name', 'id');

        //查询部门名称 ids
        $departmentIds  = [$transferInfo['current_department_id'], $transferInfo['after_department_id']];
        $departmentData = $sysObj->getDepartmentList(['ids' => $departmentIds]);
        $departmentData = array_column($departmentData, 'name', 'id');

        //查询网点名称 ids
        $storeIds     = [$transferInfo['current_store_id'], $transferInfo['after_store_id']];
        $storeData    = $sysObj->getStoreList(['ids' => $storeIds]);
        $storeData    = array_column($storeData, 'name', 'id');
        $transferReverseParams = [
            'department_data'     => $departmentData,
            'store_data'          => $storeData,
            'position_data'       => $positionData,
            'is_salary_with_unit' => $isSalaryWithUnit,
        ];
        //获取确认状态
        $confirmState = JobTransferConfirmEnums::getCodeTxtMap();

        //如果已经转岗确认了，获取固化的数据
        if ($transferInfo['confirm_state'] == JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
            //获取被转岗人信息
            $staffService = new StaffServer();
            $staffInfo    = $staffService->getStaffInfoSpecColumns($staffInfoId,
                "h.staff_info_id, 
                h.name as staff_name,
                h.identity,
                h.formal,
                date_format(h.hire_date,'%Y-%m-%d') hire_date");

            //没转岗确认实时取薪资、年假天数、short Notice
            $transferDetail = $this->getTransferPendingConfirmInfo($transferInfo, $staffInfo);
            $afterDate      = $transferInfo['after_date'] ?? '';
        } else {
            $transferData   = json_decode($transferInfo['confirm_content'], true);
            $staffInfo      = $transferData['staff_info'];
            $transferDetail = $transferData['transfer_info'];
            $afterDate      = $transferData['persist_info']['after_date'] ?? ''; //确认完成需要取固化的日期
        }

        //将转岗确认的数据进行排序
        $sortTransferDetail = $transferDetail['salary'];
        $this->sortArrayRecursively($sortTransferDetail);
        //获取确认单类型
        $confirmType = $this->calcConfirmType($transferDetail);
        //获取转岗后公司名
        $afterCompanyName = (new DepartmentRepository())->getCompanyInfoByDepartmentId($transferInfo['after_department_id']);

        $result = [
            //转岗确认单类型 1=只要职级升高 2=只有职位变更 3=其他
            'confirm_type'           => $confirmType,
            //信函生成日
            'confirmation_gen_date'  => $this->formatDate($transferInfo['confirmation_gen_date']),
            //转岗日期
            'after_date'             => $this->formatDate($afterDate),
            //工号
            'staff_info_id'          => $staffInfoId,
            //姓名
            'staff_name'             => $staffInfo['staff_name'],
            'staff_identity'         => $staffInfo['identity'],
            'staff_sign_url'         => $transferInfo['sign_url'] ?? '',
            'staff_company_name'     => $afterCompanyName['name'] ?? '',
            'before_department_name' => $departmentData[$transferInfo['current_department_id']] ?? '',
            'before_job_title_name'  => $positionData[$transferInfo['current_position_id']] ?? '',
            'after_job_title_name'   => $positionData[$transferInfo['after_position_id']] ?? '',
            'confirm_state'          => $transferInfo['confirm_state'],
            'confirm_state_label'    => $this->getTranslation()->_($confirmState[$transferInfo['confirm_state']]),
            'confirm_list'           => $this->formatterDate($transferDetail, $transferReverseParams),
            'confirm_date'           => !empty($transferInfo['confirm_date'])
                ? $this->formatDate($transferInfo['confirm_date'])
                : '',
            //用于在转岗确认时，对比确认的数据版本与获取的转岗确认的数据的版本是否一致
            'version'                => md5(json_encode($sortTransferDetail, JSON_UNESCAPED_UNICODE)),
            'salary_data'            => RUNTIME == 'dev' ? $transferDetail['salary'] : [],
            'type'                   => $transferInfo['type'],
            'state'                  => $transferInfo['state'],
        ];
        $result = array_merge($result, $templateCnf);

        return ['data' => $result];
    }

    /**
     * 获取转岗确认相关信息
     * @param $transfer_info
     * @param $staff_info
     * @return array
     * @throws \Exception
     */
    public function getTransferPendingConfirmInfo($transfer_info, $staff_info): array
    {
        //转岗前
        $params              = [
            'staff_info_id'        => $transfer_info['staff_id'],
            'hire_type'            => $transfer_info['current_hire_type'],
            'hire_date'            => $staff_info['hire_date'],
            'job_title_grade_v2'   => $transfer_info['current_job_title_grade'],
            'before_transfer_date' => date('Y-m-d', strtotime($transfer_info['after_date'] . '-1 day')),
            'formal'               => $staff_info['formal'],
            'job_title'            => $transfer_info['current_position_id'],
        ];
        $currentTransferInfo = $this->getStaffBaseInfo($params);

        //转岗后
        $params            = [
            'staff_info_id'        => $transfer_info['staff_id'],
            'hire_type'            => $transfer_info['after_hire_type'],
            'hire_date'            => $staff_info['hire_date'],
            'job_title_grade_v2'   => $transfer_info['after_job_title_grade'],
            'before_transfer_date' => $transfer_info['after_date'],
            'formal'               => $staff_info['formal'],
            'job_title'            => $transfer_info['after_position_id'],
        ];
        $afterTransferInfo = $this->getStaffBaseInfo($params);

        //薪资
        $salaryInfo = $this->getTransferSalaryById(['job_transfer_id' => $transfer_info['id']]);

        return [
            'basic' => [
                [
                    'before' => $transfer_info['current_position_id'],
                    'after'  => $transfer_info['after_position_id'],
                    'name'   => 'Job Title',
                ],
                [
                    'before' => $transfer_info['current_department_id'],
                    'after'  => $transfer_info['after_department_id'],
                    'name'   => 'Department',
                ],
                [
                    'before' => $transfer_info['current_store_id'],
                    'after'  => $transfer_info['after_store_id'],
                    'name'   => 'Branch',
                ],
                [
                    'before' => $transfer_info['current_job_title_grade'],
                    'after'  => $transfer_info['after_job_title_grade'],
                    'name'   => 'Job Grade',
                ],
                [
                    'before' => $currentTransferInfo['annual_leave_days'],
                    'after'  => $afterTransferInfo['annual_leave_days'],
                    'name'   => 'Annual Leave',
                ],
                [
                    'before' => $currentTransferInfo['notice_period'],
                    'after'  => $afterTransferInfo['notice_period'],
                    'name'   => 'Notice Period',
                ],
            ],
            'salary' => $salaryInfo,
        ];
    }

    /**
     * @description 获取转岗基础薪资
     * @param array $params
     */
    public function getTransferSalaryById($params = [])
    {
        $ac = new ApiClient('hcm_rpc', '', 'get_job_transfer_base_salary_after');
        $ac->setParams($params);
        $ac_result = $ac->execute();

        if (!isset($ac_result['result']['code']) || $ac_result['result']['code'] != ErrCode::SUCCESS) {
            $this->logger->write_log(sprintf('get getTransferSalary err, params: %s', json_encode($params)));
        }
        if (!isset($ac_result['result']['data']) || empty($ac_result['result']['data'])) {
            return [];
        }
        foreach ($ac_result['result']['data'] as &$item) {
            $item['after'] = number_format($item['after'], 2, '.', ',');
            $item['before'] = number_format($item['before'], 2, '.', ',');
            $item['unit_txt'] = $this->formatSalaryUnit($item['unit']);
        }
        return $ac_result['result']['data'] ?? [];
    }


    /**
     * @param array $params
     * @return array
     */
    public function getTransferSalary($params = []): array
    {
        $ac_salary   = new ApiClient('hcm_rpc', '', 'get_special_batch_transfer_salary');
        //校验薪资是否变化
        $ac_salary->setParams([
            'staff_id'            => $params['staff_id'],
            'current_position_id' => $params['current_position_id'],
            'after_position_id'   => $params['after_position_id'],
            'current_store_id'    => $params['current_store_id'],
            'after_store_id'      => $params['after_store_id'],
            'after_date'          => $params['after_date'],
            'after_car_type'      => $params['after_car_type'],

        ]);
        $ac_result = $ac_salary->execute();
        if (!isset($ac_result['result']['code']) || $ac_result['result']['code'] != ErrCode::SUCCESS) {
            $this->logger->write_log(sprintf('get getTransferSalary err, params: %s', json_encode($params)));
        }
        if (empty($ac_result['result']['data'])) {
            return [];
        }
        return $ac_result['result']['data'];
    }

    /**
     * 获取转岗确认基础信息
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getStaffBaseInfo($params): array
    {
        //年假额度
        //staff_info_id,hire_type,hire_date,job_title_grade_v2,after_date
        $leaveServer = new LeaveServer();
        $annualLeaveDays = $leaveServer->getAnnualLeaveShouldDays($params, $params['before_transfer_date']);

        //short notice period
        //formal,hire_type,job_title_grade_v2,job_title
        $personServer = (new PersoninfoServer($this->lang, $this->timeZone));
        $resignationNotice = $personServer->resignationNotice($params, $params['staff_info_id']);

        return [
            'annual_leave_days' => $annualLeaveDays,
            'notice_period'     => $resignationNotice['resignation'] ?? '',
        ];
    }

    /**
     * 计算转岗确认单类型
     * 1. 只要职级提高
     * 2. 只有职位变更
     * 3. 其他
     *
     * @param array $transferDetail
     * @return int
     * @throws \Exception
     */
    private function calcConfirmType(array $transferDetail): int
    {
        //转岗确认单类型
        $confirmType        = JobTransferConfirmEnums::CONFIRM_TYPE_OTHER;
        $changeColumnsCount = 0;
        $jobTitleChangeFlag = false;

        if (!isset($transferDetail['basic'])) {
            throw new \Exception($this->getTranslation()->_('4008'));
        }
        foreach ($transferDetail['basic'] as $value) {
            if (!isset($value['name'])) {
                continue;
            }
            if ($value['name'] == 'Job Grade' && $value['after'] > $value['before']) {
                $confirmType = JobTransferConfirmEnums::CONFIRM_TYPE_GRADE_PROMOTION;
            }
            if ($value['name'] == 'Job Title' && $value['after'] != $value['before']) {
                $jobTitleChangeFlag = true;
            }
            if ($value['after'] != $value['before']) {
                $changeColumnsCount++;
            }
        }
        foreach ($transferDetail['salary'] as $value) {
            if ($value['after'] != $value['before']) {
                $changeColumnsCount++;
            }
        }

        //有且只有一个变更项为职位，且
        if ($changeColumnsCount == 1 && $jobTitleChangeFlag) {
            $confirmType = JobTransferConfirmEnums::CONFIRM_TYPE_ONLY_JOB_TITLE_CHANGED;
        }
        return $confirmType;
    }

    private function formatDate($date)
    {
        return date('d F Y', strtotime($date));
    }

    /**
     * 转义数据
     * @param array $transferDetail
     * @param array $params
     * @return array
     */
    private function formatterDate(array $transferDetail, array $params): array
    {
        foreach ($transferDetail['basic'] as &$value) {
            if ($value['after'] == $value['before']) {
                $value = [];
                continue;
            }
            if ($value['name'] == 'Job Title') {
                $value['after'] = $params['position_data'][$value['after']] ?? '';
                $value['before'] = $params['position_data'][$value['before']] ?? '';
            }

            if ($value['name'] == 'Department') {
                $value['after'] = $params['department_data'][$value['after']] ?? '';
                $value['before'] = $params['department_data'][$value['before']] ?? '';
            }

            if ($value['name'] == 'Branch') {
                $value['after'] = $params['store_data'][$value['after']] ?? '';
                $value['before'] = $params['store_data'][$value['before']] ?? '';
            }
            if ($value['name'] == 'Job Grade') {
                $value['after'] = sprintf('F%d', $value['after']);
                $value['before'] = sprintf('F%d', $value['before']);
            }

            if (in_array($value['name'], ['Annual Leave'])) {
                $value['after'] = sprintf('%d days', $value['after']);
                $value['before'] = sprintf('%d days', $value['before']);
            }
        }
        foreach ($transferDetail['salary'] as &$value) {
            if ($value['after'] == $value['before']) {
                $value = [];
                continue;
            }
            if (!empty($value['unit_txt']) && $params['is_salary_with_unit']) {
                $value['after'] = sprintf('RM %s %s', $value['after'], $value['unit_txt']);
                $value['before'] = sprintf('RM %s %s', $value['before'], $value['unit_txt']);
            } else {
                $value['after'] = sprintf('RM %s', $value['after']);
                $value['before'] = sprintf('RM %s', $value['before']);
            }
        }
        $transferDetailList = array_merge(...array_values($transferDetail));
        return array_values(array_filter($transferDetailList));
    }

    /**
     * 获取官方签名--废弃
     * @param $company_id
     * @return array|string[]
     */
    private function getCompanySignInfo($company_id): array
    {
        switch ($company_id) {
            case CommonEnums::FLASH_COMMERCE_COMPANY_ID:
                //TODO: 临时写死
                $setting = [
                    'official_staff_name'      => 'Yong Shi Xian',
                    'official_staff_job_title' => 'Head of PMO',
                    'official_sign_url'        => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1724912034-e3aa1b06a9bb4eba9729904bb43f2713.jpg',
                ];
                break;
            default:
                $settingEnvServer = new SettingEnvServer();
                $settingCnf = $settingEnvServer->getSetVal('prove-download-setting');
                $settingCnf = json_decode($settingCnf, true);
                $setting = [
                    'official_staff_name'      => $settingCnf['name'] ?? '',
                    'official_staff_job_title' => $settingCnf['job_title'] ?? '',
                    'official_sign_url'        => $settingCnf['url'] ?? '',
                ];
                break;
        }
       return $setting;
    }

    /**
     * @description 转岗确认
     * @param $params
     * @return array
     * @throws ValidationException
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function doConfirm($params)
    {
        $id = $this->processingDefault($params, 'id');

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $jobTransferInfo = JobTransferModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $id
                ],
                'for_update' => true
            ]);
            if (empty($jobTransferInfo)) {
                throw new ValidationException('data not exist');
            }

            //只有待确认状态才可以确认
            if ($jobTransferInfo->confirm_state != JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_transfer_confirm_version_not_latest'), ErrCode::JOB_TRANSFER_CONFIRM_DATE_VERSION_NOT_LATEST);
            }

            switch ($jobTransferInfo->type) {
                case JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL:
                    $this->handleJobTransferSpecialConfirm($jobTransferInfo, $params);
                    break;
                default:
                    $this->handleJobTransferNormalConfirm($jobTransferInfo, $params);
                    break;
            }

            $db->commit();
        } catch (ValidationException $ve) {
            if ($db->isUnderTransaction()) {
                $db->rollback();
            }
            $this->logger->write_log('doConfirm err' . $ve->getMessage() . $ve->getTraceAsString(), 'info');
            throw $ve;
        }

        return $this->checkReturn(['data' => true]);
    }

    /**
     * 处理特殊批量转岗
     * @param $jobTransferModel
     * @param $params
     * @return void
     * @throws ValidationException
     * @throws \Exception
     */
    private function handleJobTransferSpecialConfirm($jobTransferModel, $params)
    {
        $signUrl     = $this->processingDefault($params, 'sign_url');
        $state       = $this->processingDefault($params, 'state');
        $confirmDate = date('Y-m-d');

        //获取固化详情
        $insertConfirmContent = $this->getPersistConfirmBaseInfo($params, $jobTransferModel);

        //生成PDF
        if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
            $acknowledgeServer                  = new JobTransferAcknowledgementServer($this->lang, $this->timeZone);
            $confirmationSign                   = $acknowledgeServer->generateJobTransferPdf([
                'id'            => $jobTransferModel->id,
                'staff_info_id' => $jobTransferModel->staff_id,
                'sign_url'      => $signUrl,
                'confirm_date'  => $confirmDate,
            ]);
            $jobTransferModel->confirmation_url = $confirmationSign;
        }

        $jobTransferModel->confirm_state   = $state;
        $jobTransferModel->sign_url        = $signUrl ?? '';
        $jobTransferModel->confirm_date    = $confirmDate;
        $jobTransferModel->confirm_content = json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
        $jobTransferModel->save();
    }

    /**
     * 提交转岗确认
     * @throws InnerException
     * @throws ValidationException
     */
    public function handleJobTransferNormalConfirm($jobTransferModel, $params)
    {
        $request = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $jobTransferModel->id);
        $isSecondStageAudit = $this->isSecondStageAudit($request);
        if (!$isSecondStageAudit) { //一阶段+待确认
            $this->handleJobTransferConfirmWhenConfirmation($jobTransferModel, $params);
        } else { //已经进入二阶段
            $this->handleJobTransferConfirmWhenSecondStage($jobTransferModel, $params);
        }
    }

    /**
     * 处理转岗确认（未创建二阶段审批时）
     * @throws InnerException
     * @throws ValidationException
     * @throws \Exception
     */
    private function handleJobTransferConfirmWhenConfirmation($jobTransferInfo, $params)
    {
        $id          = $this->processingDefault($params, 'id');
        $staffInfoId = $this->processingDefault($params, 'staff_info_id');
        $signUrl     = $this->processingDefault($params, 'sign_url');
        $state       = $this->processingDefault($params, 'state');

        //获取要固化的数据
        $insertConfirmContent = $this->getPersistConfirmBaseInfo($params, $jobTransferInfo);

        //转岗拒绝
        //审批状态变更为已驳回
        //转岗状态变更为未转岗
        $requestObj = (new ApplyRepository())->getApplyObject(AuditListEnums::APPROVAL_TYPE_JT, $id);

        //更新日志
        //Notice: 一阶段审批完成插入一个待确认的log，在员工确认同意/拒绝后，要同步更改这条日志的状态
        $confirmState = $state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS ? enums::WF_ACTION_CONFIRM_PASS: enums::WF_ACTION_CONFIRM_REJECT;
        $workflowService = new WorkflowServer($this->lang, $this->timeZone);
        $workflowService->updateAuditLog($requestObj, $staffInfoId, $confirmState, null);

        if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT) {

            //确认驳回也要固化数据
            $jobTransferInfo->sign_url       = '';
            $jobTransferInfo->confirm_date   = date('Y-m-d');
            $jobTransferInfo->confirm_content= json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
            $jobTransferInfo->approval_state = enums::APPROVAL_STATUS_REJECTED;
            $jobTransferInfo->state          = JobTransferModel::JOBTRANSFER_STATE_NOT_TRANSFERED;
            $jobTransferInfo->confirm_state  = $state;
            $jobTransferInfo->save();

            //确认拒绝
            $requestObj->setState(Enums::APPROVAL_STATUS_REJECTED);
            $requestObj->setFinalApprovalTime(date('Y-m-d H:i:s'));
            $requestObj->setFinalApprover($staffInfoId);
            $requestObj->save();

            //返还HC
            if (!empty($jobTransferInfo->hc_id)) {
                (new HcServer($this->lang, $this->timeZone))->remittanceHc($jobTransferInfo->hc_id);
            }

            //发送确认驳回提醒
            $this->sendRejectNotice($jobTransferInfo);
        }

        if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {

            //保存确认结果
            $jobTransferInfo->confirm_state  = $state;
            $jobTransferInfo->sign_url       = $signUrl;
            $jobTransferInfo->confirm_date   = date('Y-m-d');
            $jobTransferInfo->confirm_content= json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
            $jobTransferInfo->save();

            //获取下一个阶段的审批流ID
            $workflowInfo = WorkflowModel::findFirst(JobTransferEnums::TRANSFER_OUT_WORKFLOW_ID);

            //创建二阶段的审批流
            if ($requestObj->workflow_id == $workflowInfo->next_stage_flow_id) {
                return;
            }

            //创建二阶段审批
            $extend['from_submit'] = [
                'department_id' => [$jobTransferInfo->current_department_id, $jobTransferInfo->after_department_id],
                'sys_store_id'  => [$jobTransferInfo->current_store_id, $jobTransferInfo->after_store_id],
            ];
            $creatParams           = [
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_JT,
                'submitter_id' => $jobTransferInfo->submitter_id,
                'workflow_id'  => $workflowInfo->next_stage_flow_id,
                'audit_id'     => $jobTransferInfo->id,
                'extend'       => $extend,
            ];
            (new ApprovalServer($this->lang, $this->timeZone))->createMultiStage($creatParams);

            $this->logger->write_log(sprintf('[doConfirm] create workflow ,audit id is %d, params is %s', $id, json_encode($creatParams)), 'info');
        }
    }

    /**
     * 处理转岗确认（未创建二阶段审批时）
     * @throws ValidationException
     */
    public function handleJobTransferConfirmWhenSecondStage($jobTransferInfo, $params)
    {
        $signUrl     = $this->processingDefault($params, 'sign_url');
        $state       = $this->processingDefault($params, 'state');

        //获取要固化的数据
        $insertConfirmContent = $this->getPersistConfirmBaseInfo($params, $jobTransferInfo);

        //转岗拒绝
        //审批状态变更为已驳回
        //转岗状态变更为未转岗
        if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_REJECT) {
            //确认驳回也要固化数据
            $jobTransferInfo->sign_url       = '';
            $jobTransferInfo->confirm_date   = date('Y-m-d');
            $jobTransferInfo->confirm_content= json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
            $jobTransferInfo->confirm_state  = $state;
            $jobTransferInfo->save();

            //发送确认驳回提醒
            $this->sendRejectNotice($jobTransferInfo);
        }

        if ($state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) { //保存确认结果
            $jobTransferInfo->sign_url       = $signUrl;
            $jobTransferInfo->confirm_date   = date('Y-m-d');
            $jobTransferInfo->confirm_content= json_encode($insertConfirmContent, JSON_UNESCAPED_UNICODE);
            $jobTransferInfo->confirm_state  = $state;
            $jobTransferInfo->save();
        }
    }

    /**
     * 获取要固化的转岗确认详情
     * @param $params
     * @param $jobTransferInfo
     * @return array
     * @throws ValidationException
     * @throws \Exception
     */
    public function getPersistConfirmBaseInfo($params, $jobTransferInfo): array
    {
        $state   = $params['state'];
        $version = $params['version'] ?? '';

        $staffService = new StaffServer();
        $staffInfo    = $staffService->getStaffInfoSpecColumns($jobTransferInfo->staff_id,
            "h.staff_info_id, 
                h.name as staff_name,
                h.identity,
                h.formal,
                date_format(h.hire_date,'%Y-%m-%d') hire_date");

        //获取确认详情
        //将转岗确认的数据进行排序
        $transferDetail     = $this->getTransferPendingConfirmInfo($jobTransferInfo->toArray(), $staffInfo);

        //确认同意需要验证版本
        if (!empty($version) && $state == JobTransferConfirmEnums::CONFIRM_STATE_CONFIRM_PASS) {
            $sortTransferDetail = $transferDetail['salary'];
            $this->sortArrayRecursively($sortTransferDetail);
            if ($version != md5(json_encode($sortTransferDetail, JSON_UNESCAPED_UNICODE))) {
                throw new ValidationException($this->getTranslation()->_('err_msg_job_transfer_confirm_version_not_latest'), ErrCode::JOB_TRANSFER_CONFIRM_DATE_VERSION_NOT_LATEST);
            }
        }
        return [
            'transfer_info' => $transferDetail,
            'staff_info'    => $staffInfo,
            'persist_info'  => [
                'after_date' => $jobTransferInfo->after_date
            ],
        ];
    }

    public function sortArrayRecursively(&$array)
    {
        foreach ($array as &$value) {
            if (is_array($value)) {
                $this->sortArrayRecursively($value);
            }
        }
        sort($array);
    }

    public function arraysHaveChanged($array1, $array2)
    {
        // 深度克隆数组
        $sortedArray1 = $array1;
        $sortedArray2 = $array2;

        // 对数组及子数组排序
        $this->sortArrayRecursively($sortedArray1);
        $this->sortArrayRecursively($sortedArray2);

        // 计算哈希值
        $hash1 = md5(json_encode($sortedArray1));
        $hash2 = md5(json_encode($sortedArray2));

        // 比较哈希值
        return $hash1 !== $hash2;
    }

    /**
     * @description 获取待确认转岗信息
     * @param $staff_info_id
     * @return mixed
     */
    public function getPendingConfirmInfo($staff_info_id)
    {
        $checkStaffIds = [$staff_info_id];
        $staffInfo     = (new StaffServer())->getStaffById($staff_info_id);

        //如当前账号是子账号
        if (!empty($staffInfo) && $staffInfo['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            //获取主账号信息
            $supportInfo = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = $staff_info_id");
            if (!empty($supportInfo)) {
                $checkStaffIds[] = $supportInfo['staff_info_id'];
            }
        }
        //一线、非一线待转岗、待确认
        //特殊批量转岗，转岗成功、待确认 需拦截下班打卡
        return JobTransferModel::findFirst([
            'conditions' => 'staff_id in({staff_id:array}) and (type in({normal_type:array}) and state = :normal_state: and 
                confirm_state = :confirm_state: or type = :special_type: and state = :special_state: and confirm_state = :confirm_state:)',
            'bind'       => [
                'staff_id'      => $checkStaffIds,
                'normal_type'   => [JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE, JobTransferEnums::JOB_TRANSFER_TYPE_NOT_FRONT_LINE],
                'normal_state'  => JobTransferModel::JOBTRANSFER_STATE_TO_BE_TRANSFERED,
                'confirm_state' => JobTransferConfirmEnums::CONFIRM_STATE_PENDING_CONFIRM,
                'special_type'  => JobTransferEnums::JOB_TRANSFER_TYPE_SPECIAL,
                'special_state' => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
            ],
        ]);
    }
}