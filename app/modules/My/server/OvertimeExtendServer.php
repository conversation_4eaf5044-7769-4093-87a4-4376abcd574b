<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditDetailEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOvertimeForLeaveModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StatisticForOvertimeRepository;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\OvertimeExtendServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class OvertimeExtendServer extends GlobalBaseServer
{
    //快递人效
    protected const COURIER_EFFECT = 40;
    //仓管人效
    protected const DC_OFFICER_EFFECT = 400;

    public $ignoreOvertimeId;//马来新增逻辑 可以直接撤销加班 验证需要跳过

    public $effectNum;//规则配置的 effect 值
    /**
     * @param $staffId
     * @param $date
     * @param $start_time
     * @param $end_time
     * @param $type
     * @return array
     */
    public function check_ot_record($staffId, $date, $start_time, $end_time, $type)
    {
        $start_time = date('Y-m-d H:i:s', $start_time);
        $end_time   = date('Y-m-d H:i:s', $end_time);
        //检查是否有重复记录
        $type_arr = [1, 2, 3, 4, 5, 6];

        if(in_array($type,[HrOvertimeModel::OVERTIME_1,HrOvertimeModel::OVERTIME_2])){

        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_3,HrOvertimeModel::OVERTIME_4])){
            //3，4 为一组 和其他所有 互斥
            $type_arr = array_diff($type_arr,[3,4]);
        }
        if(in_array($type,[HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])){
            $type_arr = array_diff($type_arr,[5,6]);
        }
        //把本次申请类型加上
        $type_arr = array_merge($type_arr,[$type]);

        $conditions = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' and type in({type_arr:array})";
        $bind['type_arr'] = $type_arr;
        if(!empty($this->ignoreOvertimeId)){
            $conditions .= " and overtime_id != :ot_id:";
            $bind['ot_id'] = $this->ignoreOvertimeId;
        }
        $exist = HrOvertimeModel::find(
            [
                'conditions' => $conditions,
                'bind'       => $bind,
                'columns'    => 'overtime_id',
            ]
        )->toArray();
        if (!empty($exist)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('5102'));
        }

        if (!in_array($type, [2, 3, 4, 5, 6])) {//1。5倍ot 下面不需要校验
            return $this->checkReturn([]);
        }
        $where = "state in (1,2) and staff_id = {$staffId}  and date_at = '{$date}' 
                          and (
                            (start_time < '{$start_time}' and end_time > '{$start_time}') or 
                            (start_time < '{$end_time}' and end_time > '{$end_time}') or 
                            (start_time < '{$start_time}' and end_time > '{$end_time}') or 
                            (start_time >= '{$start_time}' and end_time <= '{$end_time}') 
                          )";

        switch ($type) {
            case 2:
                $where .= " and type IN(5,6)";
                break;
            case 3:
                $where .= " and type = 4";
                break;
            case 4:
                $where .= " and type = 3";
                break;
            case 5:
                $where .= " and type = 6";
                break;
            case 6:
                $where .= " and type = 5";
                break;
            default:
                break;
        }

        if (!empty($this->ignoreOvertimeId)) {
            $where         .= " and overtime_id != {$this->ignoreOvertimeId}";
        }
        $check = HrOvertimeModel::find([
            'conditions' => $where,
            'bind'       => $bind,
            'columns'    => 'overtime_id',
        ])->toArray();
        $this->logger->write_log("ot_check_exist {$staffId} start_time {$start_time},end_time {$end_time} ", 'info');

        if (!empty($check)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('5102'));
        }

        return $this->checkReturn([]);
    }


    //马来新班次 调整 加班验证逻辑
    public function newExtendCheck($param, $staffInfo)
    {
        $staff_id  = $staffInfo['staff_info_id'];
        $date      = date('Y-m-d', strtotime($param['date_at']));//加班日期
        $shiftInfo = $param['shift_info'];//新版 班次信息

        //请半天假和全天假：不允许申请加班 并且 从只有审核通过的 改为待审批+审核通过
        $leave_re                       = new AuditRepository($this->lang);
        $leaveParam['staff_id']         = $staff_id;
        $leaveParam['leave_start_time'] = $date;
        $leaveParam['leave_end_time']   = $date;
        $leave_info                     = $leave_re->getLeaveData($leaveParam);
        //如果是请上午假 加5小时 其他情况不让申请ot 休息日类型假期 剔除
        if (!empty($leave_info)) {
            //判断是不是 只有 15类型
            $leave_info = array_column($leave_info, null, 'date_at');
            $types      = array_unique(explode(',', $leave_info[$date]['type_str']));
            $types      = array_diff($types, [enums::LEAVE_TYPE_15]);
            //出了 休息日类型 还有其他类型的请假 要提示不让申请
            if (!empty($types)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_leave_limit'));
            }
        }

        //没有班次信息 不让申请
        if (empty($shiftInfo)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }

        //获取 上班卡信息 包括出差打卡
        $attendanceInfo = StaffWorkAttendanceModel::find([
            'columns'    => "concat(staff_info_id,'_',attendance_date,'_',shift_type) as u_key, started_at",
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
            'bind'       => [
                'staff_id' => $staff_id,
                'date'     => $date,
            ],
        ])->toArray();
        $attendanceInfo = empty($attendanceInfo) ? [] : array_column($attendanceInfo, 'started_at', 'u_key');
        //如果正式表没有 要查出差打卡表找上班打卡
        if (empty($attendanceInfo)) {
            $attendanceInfo = StaffAuditReissueForBusinessModel::find([
                'columns'    => "concat(staff_info_id,'_',attendance_date,'_',shift_type) as u_key, start_time as started_at",
                'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
                'bind'       => [
                    'staff_id' => $staff_id,
                    'date'     => $date,
                ],
            ])->toArray();
            $attendanceInfo = empty($attendanceInfo) ? [] : array_column($attendanceInfo, 'started_at', 'u_key');
        }

        //判断 是否迟到 计算迟到分钟数
        $otAddTime = $shiftInfo['ot'] * 60;//下班班次后 x分钟 可申请加班 转化为秒

        //一个班的情况
        if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            $key = "{$staff_id}_{$date}_".StaffWorkAttendanceModel::SHIFT_TYPE_ONLY;
            //如果 没打上班卡 不让申请
            if (empty($attendanceInfo[$key])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
            }
            $shiftDate = "{$date} {$shiftInfo['first_start']}:00";

            //迟到时间 单位 秒
            $lateTime = $this->getLateTime($attendanceInfo[$key], $shiftDate, $shiftInfo['first_late']);

            //下班 班次时间
            $endShiftDate = "{$date} {$shiftInfo['first_end']}:00";
            //如果跨天
            if ($endShiftDate < $shiftDate) {
                $endShiftDate = date('Y-m-d H:i:s', strtotime("{$endShiftDate} +1 day"));
            }
        } else {//两个班的情况 分两个 上班
            $keyFirst  = "{$staff_id}_{$date}_".StaffWorkAttendanceModel::SHIFT_TYPE_FIRST;
            $keySecond = "{$staff_id}_{$date}_".StaffWorkAttendanceModel::SHIFT_TYPE_SECOND;
            //两个班次 有一个没打上班卡 就不能申请 加班
            if (empty($attendanceInfo[$keyFirst]) || empty($attendanceInfo[$keySecond])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_att_start'));
            }

            $shiftDateFirst = "{$date} {$shiftInfo['first_start']}:00";
            $lateTime1      = $this->getLateTime($attendanceInfo[$keyFirst], $shiftDateFirst, $shiftInfo['first_late']);

            $shiftDateSecond = "{$date} {$shiftInfo['second_start']}:00";
            //如果跨天 要+1天
            if ($shiftDateSecond < $shiftDateFirst) {
                $shiftDateSecond = date('Y-m-d H:i:s', strtotime('+1 day'));
            }
            //迟到 秒数
            $lateTime2 = $this->getLateTime($attendanceInfo[$keySecond], $shiftDateSecond, $shiftInfo['second_late']);
            $lateTime  = bcadd($lateTime1, $lateTime2);

            //下班 班次时间
            $endShiftDate = "{$date} {$shiftInfo['second_end']}:00";
            //如果跨天
            if ($endShiftDate < $shiftDateFirst) {
                $endShiftDate = date('Y-m-d H:i:s', strtotime("{$endShiftDate} +1 day"));
            }
        }

        $shift_i       = date("i", strtotime($endShiftDate));//取班次的分钟
        $endShiftStamp = strtotime($endShiftDate);

        //限制开始申请 时间点
        $limitStamp = $endShiftStamp;
        if ($lateTime > 0) {
            $limitStamp += ($lateTime + 3600);
        }
        $limitStart = date("Y-m-d H:{$shift_i}:00", $limitStamp);
        $limitStart = date('Y-m-d H:i:s', strtotime($limitStart) + $otAddTime);

        $ot_start_time = date('Y-m-d H:i:s', strtotime($param['start_time']));
        $this->logger->write_log("ot_shift_check_{$staff_id} add_hour {$shiftInfo['ot']},lateTime {$lateTime} limitStart {$limitStart} shiftType {$shiftInfo['shift_type']}",
            'info');
        //my 针对工具 要吧这个限制也加上 所以去掉了hcm 入口判断
        if ($ot_start_time < $limitStart) {
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_forbidden'));
        }
        return $this->checkReturn([]);
    }

    /**
     * //判断是否迟到 并获取 迟到时长（秒）
     * @param $cardTime
     * @param $shiftTime
     * @param $lateMinute 超过n 分钟 记为迟到 没超过 不算分钟数
     * @return false|float|int
     */
    protected function getLateTime($cardTime, $shiftTime, $lateMinute)
    {
        $config_hour = $this->config->application->add_hour;//时区
        //去秒数  需求说 不去秒数了 过一秒 就算迟到
        $cardTime = date('Y-m-d H:i:s', strtotime("{$cardTime}"));
        //判断迟到 时间
        $shiftStartStamp = strtotime($shiftTime) + ($lateMinute * 60);//班次上班时间 + 可迟到分钟数
        $cardTime        = strtotime($cardTime) + ($config_hour * 3600);
        $logCardTime     = date('Y-m-d H:i:s', $cardTime);
        $logShiftTime    = date('Y-m-d H:i:s', $shiftStartStamp);
        $this->logger->write_log("getLateTime cardTime {$logCardTime},shiftTime {$logShiftTime} ", 'info');

        if ($cardTime > $shiftStartStamp) {//迟到了 算出来迟到多少秒
            return $cardTime - strtotime($shiftTime);
        }
        return 0;
    }

    /**
     * 校验权限
     * @description 申请加班 针对每种类型的  权限展示 https://l8bx01gcjr.feishu.cn/docs/doccnM3g2ev63nhQDTFk15I13Vg
     * @param $staffId
     * @param array $data
     * @return array
     */
    public function check_permission($staffId, $data = [])
    {
        return $data;
    }


    /**
     * 获取依赖数据
     *   1. 职位为Hub Supervisor 以及 Branch Supervisor的员工开放所有加班类型的申请入口
     *   2. 基本工资大于等于2000，没有加班申请入口; 基本工资小于2000，开放所有加班类型的申请入口
     *
     * @param $staffId
     * @param $type
     * @param $date
     * @return array
     */
    public function getReference($staffId, $type, $date)
    {
        //马来固化申请人的职位
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);
        $references = [];

        if (empty($staff_info)) {
            return [$references, []];
        }

        $staff_info                      = $staff_info->toArray();
        if(in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            return [$references, []];
        }

        $references['job_title']         = $staff_info['job_title'];
        $references['sys_department_id'] = $staff_info['node_department_id'];

        //这里是 network management 部门 和 network bulky 的  DC Officer 职位
        $envModel     = new SettingEnvServer();
        $setting_code = ['dept_network_management_id', 'nw_operation_id'];
        $setting_val  = $envModel->listByCode($setting_code);
        if (!empty($setting_val)) {
            $setting_val = array_column($setting_val, 'set_val', 'code');
        }

        $networkManagementId = $setting_val['dept_network_management_id'] ?? '';
        $nwOperationId       = $setting_val['nw_operation_id'] ?? '';

        if (in_array($staff_info['sys_department_id'],
                [$networkManagementId]) && $staff_info['job_title'] == enums::$job_title['dc_officer']) {
            //拼接 网点 大区 片区
            $references['store_name'] = $this->getStoreDetailName($staff_info['sys_store_id']);

            //泰国 迁移过来的历史逻辑 固化的数据  对应日期 上一周的计算数据
            $statistic_mode = new StatisticForOvertimeRepository();
            $data           = $statistic_mode->find_by_store($staff_info['sys_store_id'], $date);
            if (empty($data))//默认值
            {
                $data['all_effective_num'] = $data['store_effective_num'] = $data['attendance_num'] = $data['parcel_num'] = 0;
            }

            //网点在职仓管数
            //$references['job_num'] = count($rate_data['in_staff']) ?? 0;
            $references = array_merge($references, $data);

            //本月累计时长
            //获取该员工一个月的加班时长
            $start_date             = date("Y-m-01", strtotime($date));
            $end_date               = date('Y-m-d', strtotime("{$start_date} last day of "));
            $model                  = new OvertimeRepository($this->timeZone);
            $duration               = $model->get_duration($staffId, $start_date, $end_date, $type);
            $references['duration'] = empty($duration) ? 0 : round($duration, 1);
        }

        return [$references, []];
    }

    /**
     * @description OT详情页追加详情字段
     * @param $staff_info
     * @param $apply_info
     * @param $param
     * @return array
     */
    public function ot_dc_detail($staff_info, $apply_info, $param = [])
    {
        $references = json_decode($apply_info['references'], true);
        //对应类型 的翻译
        $type = $apply_info['type'];

        //'ot_detail_3' => '本月累计1.5倍OT时长',
        //'ot_detail_9' => '本月累计3倍OT时长',
        //ot_2_times_month 马来新增的 2倍
        //详情页 排版同一 所以不能 变量替换
        $ot_key       = HrOvertimeModel::$my_ot_key;
        $language_key = $ot_key[$type] ?? 'invalid_ot_type ';

        if ($apply_info['state'] != enums::APPROVAL_STATUS_PENDING && empty($param['is_approval'])) {//最终状态 直接取数据 没有就没有了
            //[1]所在网点当天快递员出勤人数
            $detailLists['courier_attendance_rate'] = $references['courier_attendance_rate'];
            //[2]所在网点当天仓管出勤人数
            $detailLists['dc_attendance_rate'] = $references['dc_attendance_rate'];
            //[3]所在网点当天正副主管出勤人数
            $detailLists['branch_supervisor_attendance_rate'] = $references['branch_supervisor_attendance_rate'];
            //[4]所在网点当天应派包裹量
            $detailLists['should_delivery_today'] = $references['should_delivery_today'] ?? 0;
            //[5]所在网点当天揽件包裹量
            $detailLists['should_pickup_today'] = $references['should_pickup_today'] ?? 0;
            //[6]所在网点当天快递员派件人效
            $detailLists['courier_delivery_today_effect'] = $references['courier_delivery_today_effect'];
            //[7]所在网点当天快递员揽收人效
            $detailLists['courier_pickup_today_effect'] = $references['courier_pickup_today_effect'];
            //[8]所在网点当天仓管人效
            $detailLists['dc_today_effect'] = $references['dc_today_effect'] ?? "";
            //[9]最后一票妥投员工和时间
            $detailLists['last_delivery_staff_info'] = $references['last_delivery_staff_info'] ?? '';
            //[10]最后一票揽收员工和时间
            $detailLists['last_pickup_staff_info'] = $references['last_pickup_staff_info'] ?? '';
            //[11]当天车辆计划/实际到港时间
            $detailLists['ot_fleet_info'] = $references['ot_fleet_info'] ?? '';
            //[12]当天线路发车时间
            $detailLists['ot_fleet_leave_info'] = $references['ot_fleet_leave_info'] ?? '';
            //[13]申请人本月累计x倍OT时长
            $detailLists[$language_key] = ($references['duration'] ?? 0).' h';

            return $detailLists;
        }

        //获取审批辅助数据
        $param['store_id'] = $staff_info['sys_store_id'];
        $param['date']     = $apply_info['date_at'];
        $param['begin_date'] = date('Y-m-d', strtotime("{$apply_info['date_at']} -1 day"));
        $param['end_date'] = $apply_info['date_at'];
        $assistData        = $this->getAuditAssistData($param);

        //[1]所在网点当天快递员出勤人数
        //获取【Bike Courier +Van Courier +Car Courier】出勤数据
        $courierJobTitleList               = [
            enums::$job_title['bike_courier'],
            enums::$job_title['van_courier'],
            enums::$job_title['car_courier'],
        ];
        $courierAttendanceData             = $this->getAttendanceNum($staff_info['sys_store_id'],
            $apply_info['date_at'], $courierJobTitleList);
        $result['courier_attendance_rate'] = sprintf("%d/%d", $courierAttendanceData['all_num'] ?? 0,
            $courierAttendanceData['staff_num'] ?? 0);

        //[2]所在网点当天仓管出勤人数
        $dcOfficerJobTitleList        = [
            enums::$job_title['dc_officer'],
        ];
        $dcOfficerAttendanceData      = $this->getAttendanceNum($staff_info['sys_store_id'],
            $apply_info['date_at'], $dcOfficerJobTitleList);
        $result['dc_attendance_rate'] = sprintf("%d/%d", $dcOfficerAttendanceData['all_num'] ?? 0,
            $dcOfficerAttendanceData['staff_num'] ?? 0);

        //[3]所在网点当天正副主管出勤人数
        $supervisorJobTitleList                      = [
            enums::$job_title['branch_supervisor'],
            enums::$job_title['assistant_branch_supervisor'],
        ];
        $supervisorAttendanceData                    = $this->getAttendanceNum($staff_info['sys_store_id'],
            $apply_info['date_at'], $supervisorJobTitleList);
        $result['branch_supervisor_attendance_rate'] = sprintf("%d/%d", $supervisorAttendanceData['all_num'] ?? 0,
            $supervisorAttendanceData['staff_num'] ?? 0);

        //[4]所在网点当天应派包裹量
        $result['should_delivery_today'] = $assistData['should_delivery_today'] ?? 0;

        //[5]所在网点当天揽件包裹量
        $result['should_pickup_today'] = $assistData['should_pickup_today'] ?? 0;

        //[6]所在网点当天快递员派件人效
        $courierDeliveryTodayEffect = $this->calcAttendanceRate($result['should_delivery_today'],
            $courierAttendanceData['all_num']);
        if ($courierDeliveryTodayEffect < self::COURIER_EFFECT) {
            $color = AuditDetailEnums::FONT_COLOR_RED;
        } else {
            $color = AuditDetailEnums::FONT_COLOR_DEFAULT;
        }
        $result['courier_delivery_today_effect'] = ['value' => $courierDeliveryTodayEffect, 'color' => $color];

        //[7]所在网点当天快递员揽收人效
        $courierPickupTodayEffect = $this->calcAttendanceRate($result['should_pickup_today'],
            $courierAttendanceData['all_num']);
        if ($courierPickupTodayEffect < self::COURIER_EFFECT) {
            $color = AuditDetailEnums::FONT_COLOR_RED;
        } else {
            $color = AuditDetailEnums::FONT_COLOR_DEFAULT;
        }
        $result['courier_pickup_today_effect'] = ['value' => $courierPickupTodayEffect, 'color' => $color];

        //[8]所在网点当天仓管人效
        $dcTodayEffect = $this->calcAttendanceRate(($result['should_delivery_today'] + $result['should_pickup_today']),
            $dcOfficerAttendanceData['all_num']);
        if ($dcTodayEffect < self::DC_OFFICER_EFFECT) {
            $color = AuditDetailEnums::FONT_COLOR_RED;
        } else {
            $color = AuditDetailEnums::FONT_COLOR_DEFAULT;
        }
        $result['dc_today_effect'] = ['value' => $dcTodayEffect, 'color' => $color];

        //[9]最后一票妥投员工和时间
        //[10]最后一票揽收员工和时间
        $lastStaffIds    = array_merge(array_column($assistData['last_delivery_staff_info'], 'staff_info_id'),
            array_column($assistData['last_pickup_staff_info'], 'staff_info_id'));
        $lastStaffIdsArr = array_values(array_filter($lastStaffIds));
        if (!empty($lastStaffIdsArr)) {
            $staffInfo    = HrStaffInfoModel::find([
                'conditions' => "staff_info_id in ({staff_info_id:array})",
                'bind'       => [
                    'staff_info_id' => $lastStaffIdsArr,
                ],
                'columns'    => 'staff_info_id,name',
            ])->toArray();
            $staffInfoMap = array_column($staffInfo, 'name', 'staff_info_id');

            //妥投员工和时间
            $staffInfo                          = isset($assistData['last_delivery_staff_info']) && $assistData['last_delivery_staff_info'] ? current($assistData['last_delivery_staff_info']) : [];
            $staffId                            = $staffInfo['staff_info_id'] ?? "";
            $time                               = $staffInfo['stat_end'] ?? "";
            $result['last_delivery_staff_info'] = $this->getLastStaffInfo($staffId, $time, $staffInfoMap);

            //揽收员工和时间
            $staffInfo                        = isset($assistData['last_pickup_staff_info']) && $assistData['last_pickup_staff_info'] ? current($assistData['last_pickup_staff_info']) : [];
            $staffId                          = $staffInfo['staff_info_id'] ?? "";
            $time                             = $staffInfo['last_pickup_time'] ?? "";
            $result['last_pickup_staff_info'] = $this->getLastStaffInfo($staffId, $time, $staffInfoMap);
        } else {
            $result['last_delivery_staff_info'] = "";
            $result['last_pickup_staff_info']   = "";
        }
        //$result[$language_key] = ($references['duration'] ?? 0).' h';

        //[11]当天车辆计划/实际到港时间
        //格式[车牌号: HH:mm/HH:mm; 车牌号: HH:mm/HH:mm]
        $fleetArriveData = $assistData['fleet_arrive_data'] ?? [];
        foreach ($fleetArriveData as $v) {
            $arrive_time     = empty($v['plan_arrive_time']) ? '' : date('H:i', strtotime($v['plan_arrive_time']));
            $act_arrive_time = empty($v['real_arrive_time']) ? '' : date('H:i', strtotime($v['real_arrive_time']));
            $arriveGroup[]   = sprintf("%s: %s/%s", $v['proof_plate_number'], $arrive_time, $act_arrive_time);
        }
        $result['ot_fleet_info'] = join('; ', $arriveGroup ?? []);

        //[12]当天线路发车时间
        //格式[车牌号: HH:mm; 车牌号: HH:mm]
        $fleetLeaveData = $assistData['fleet_leave_data'] ?? [];
        foreach ($fleetLeaveData as $v) {
            $leave_time   = empty($v['plan_leave_time']) ? '' : date('H:i', strtotime($v['plan_leave_time']));
            $leaveGroup[] = sprintf("%s: %s", $v['proof_plate_number'], $leave_time);
        }
        $result['ot_fleet_leave_info'] = join('; ', $leaveGroup ?? []);

        //[13]申请人本月累计x倍OT时长
        $result[$language_key] = ($references['duration'] ?? 0).' h';

        return $result;
    }

    /**
     * @description 获取辅助审批数据
     * @param array $params
     * @return array
     */
    public function getAuditAssistData($params)
    {
//        $res                     = $this->sendRequest($params, 'deliverycount.get_latest_delivery_by_store_date', 'ard_api');
//        $result['dc_all_parcel'] = array_column($res, null, 'stat_date');
        //获取网点应派包裹量
        $result['should_delivery_today'] = $this->sendRequest($params, 'dc.get_dc_should_delivery_count');

        //获取网点揽件包裹量
        $fleParam                      = ['storeId' => $params['store_id'], 'date' => $params['date']];
        $uri                           = '/svc/ticket/pickup/store/count';
        $result['should_pickup_today'] = $this->sendFleRequest($uri, $fleParam);

//        $result['should_pickup_today'] = $this->sendRequest($params, 'dc.get_dc_operation_count');
        //当天车辆计划/实际到港时间
        $result['fleet_arrive_data'] = $this->sendRequest($params, 'dc.get_store_arrive_fleet_time');
        //当天网点线路发车时间
        $result['fleet_leave_data'] = $this->sendRequest($params, 'dc.get_store_leave_fleet_time');
        //最后一票妥投员工和时间
        $result['last_delivery_staff_info'] = $this->sendRequest($params, 'dc.get_last_delivery_staff_info');
        //最后一票揽收员工和时间
        $result['last_pickup_staff_info'] = $this->sendRequest($params, 'dc.get_last_pickup_staff_info');

        return $result;
    }



    /**
     * @description 计算指定网点、指定日期、指定职位的人的出勤率
     * @param $store_id
     * @param $date_at
     * @param $job_title_ids
     * @return array
     */
    public function getAttendanceNum($store_id, $date_at, $job_title_ids)
    {
        //获取出勤人数+在职人数
        $rate_data = $this->attendance_rate($date_at, $job_title_ids, $store_id);
        if (!empty($rate_data)) {
            //对应网点职位出勤人数
            $all_attendance_num = count($rate_data['all_num']);

            //对应网点职位在职人数+非网点在职&但是在该网点打卡人数
            $staff_num = count($rate_data['in_staff']) + count($rate_data['out_staff']);
            return ['all_num' => $all_attendance_num, 'staff_num' => $staff_num];
        } else {
            return ['all_num' => 0, 'staff_num' => 0];
        }
    }

    /**
     * @description 计算出勤率
     * @param int $num 件数
     * @param int $attendance_num 出勤人数
     * @param int $precision 有效值位数
     * @return string 返回样例 20 件/人
     */
    public function calcAttendanceRate(int $num, int $attendance_num, int $precision = 2): string
    {
        if ($attendance_num <= 0) {
            $rate      = 0;
            $formatter = sprintf("%d %%s", $rate); //获取返回有效位数，例如: %.1f %s
        } else {
            $rate      = round($num / $attendance_num, $precision);
            $formatter = sprintf("%.1f %%s", $rate); //获取返回有效位数，例如: %.1f %s
        }
        return sprintf($formatter, $this->getTranslation()->_('rate_unit'));
    }

    /**
     * @description 获取最后一票妥投/揽收员工和时间
     * 返回数据格式： 姓名(工号), HH:mm
     *
     * @param int $staff_id 最后一票妥投/揽收员工工号
     * @param string $time 最后一票妥投/揽收时间
     * @param array $staff_info_map 员工姓名与工号映射
     * @return string
     */
    public function getLastStaffInfo($staff_id, $time, $staff_info_map)
    {
        if (empty($staff_id) || empty($time) || empty($staff_info_map)) {
            return "";
        }
        $timeFormatter = date("H:i", strtotime($time));
        return sprintf("%s(%s), %s", $staff_info_map[$staff_id] ?? '', $staff_id, $timeFormatter);
    }

    //增加补休假
    public function addLieu($param)
    {
        $settingEnv        = new SettingEnvServer();
        $jobTitleInfo      = $settingEnv->getSetVal('underlying_staff_job_title');
        $jobTitleList      = explode(',', $jobTitleInfo);
        $submitterJobTitle = $param['job_title'] ?? '';

        if (in_array($submitterJobTitle, $jobTitleList)) {
            return;
        }

        //添加到给调休假的表
        $otModel = new HrOvertimeForLeaveModel();
        $otModel->setOvertimeId($param['overtime_id']);
        $otModel->setStaffInfoId($param['staff_id']);
        $otModel->setDuration($param['duration']);
        $otModel->setDateAt($param['date_at']);
        $otModel->save();

        $this->logger->write_log(sprintf("%s 已经添加调休假(ot id %s)", $param['staff_id'], $param['overtime_id']), 'info');
    }


    public function checkNwEffectDuration($staffInfo, $date){
        $isDco       = $staffInfo['job_title'] == enums::$job_title['dc_officer'];//是否dco
        $shiftInfo   = $this->shiftInfo;
        $startShift  = $shiftInfo['first_start'];
        $endShift    = $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE ? $shiftInfo['first_end'] : $shiftInfo['second_end'];
        $overTwoDays = $startShift > $endShift;//是否跨天
        //当天还是非当天
        $isCurrentDay = $this->getIsCurrent($overTwoDays, $date);
        //bs 和 abs 的职位 用快递员的 出勤人数
        $notDcoJobs = [
            enums::$job_title['bike_courier'],
            enums::$job_title['van_courier'],
            enums::$job_title['car_courier'],
        ];
        $dcoJobs = [enums::$job_title['dc_officer']];
        $limitJobs = $isDco ? $dcoJobs : $notDcoJobs;

        $return['limit_duration'] = 0;//能申请的时长
        $return['is_current']        = $isCurrentDay;//是否申请的非当天  非当天：特指跨天班次的前天或者非跨天班次的昨天 （需求群定的）。
        if ($isCurrentDay) {
            $parcelNum                = $this->getParcelNum($staffInfo, $date, $staffInfo['job_title']);
            $limitDuration            = $this->getNwEffect($staffInfo, $date, $limitJobs, $parcelNum);
            $return['limit_duration'] = min(enums::OVERTIME_LIMIT_DURATION, $limitDuration);
            return $return;
        }

        //非当天 看开关
        //开关是否打开
        $res    = ConditionsRulesServer::getInstance()
                    ->setRuleKey('OT_Time_verification')
                    ->setParameters(['staff_info_id' => $staffInfo['staff_info_id']])
                    ->getConfig();
        $switch = boolval($res['response_data']);
        $this->logger->write_log("OT_Time_verification 开关".json_encode($res), 'info');
        if ($switch === false) {
            $return['limit_duration'] = -1;//代表开关关闭不限制申请
            return $return;
        }

        //获取 迟到班车最晚实际到达时间
        $lastFleetTime = $this->getLastFleetTime($staffInfo, $date);
        //应下班时间（班次下班时间）
        $workEndTime = "{$date} {$endShift}:00";
        if($overTwoDays){
            $workEndTime = date('Y-m-d H:i:s', strtotime("{$workEndTime} +1 day"));
        }
        //  - 申请OT日班车实际到达网点时间晚于DCO OT日期应下班时间，若无则该不可以申请加班
        if(empty($lastFleetTime) || $workEndTime > $lastFleetTime){
            $this->logger->write_log("ot_fleet_last_time {$workEndTime} {$lastFleetTime}", 'info');
            throw new ValidationException($this->getTranslation()->_('no_late_bus'));
        }

        //------- 非当天 是不是 dco  都用dco 的数据 文档写的
        //新接口的件数         - 新规则：当日应揽派件总量=当日揽件总操作量+当日到港件量+昨日应派未妥投（实时数据，对应之前FBI-DC每日运营统计报表截图数据）
//        $param['store_id']   = $staffInfo['sys_store_id'];
//        $param['begin_date'] = date('Y-m-d', strtotime("{$date} -1 day"));
//        $param['end_date']   = $date;
//        $dcData                = $this->sendRequest($param, 'deliverycount.get_latest_delivery_by_store_date', 'ard_api');
//        if(empty($dcData)){
//            return $return;
//        }
//        $dcData = array_column($dcData, null, 'stat_date');
//        //当日应揽派件总量=当日揽件总操作量+当日到港件量+昨日应派未妥投(接口返回的是当天的 文档要前一天的)
//        $allParcelNum  = (float)$dcData[$param['begin_date']]['count_delivery_not_signed'] + (float)$dcData[$date]['pickup_operation_count'] + (float)$dcData[$date]['inbound_count'];
//        $limitJobs     = [enums::$job_title['dc_officer']];//要用 dco 的效率 不管是不是 dco
//        $limitDuration = $this->getNwEffect($staffInfo, $date, $limitJobs, $allParcelNum);
//
//        //$limitDuration 与 当日揽件总操作量/OT_efficiency）做比较，四舍五入，封顶4小时
//        $pickDuration             = empty($this->effectNum) ? 0 : round((float)$dcData[$date]['pickup_operation_count'] / $this->effectNum);
//        $limitDuration            = max($limitDuration, $pickDuration);
//        $return['limit_duration'] = min(enums::OVERTIME_LIMIT_DURATION, $limitDuration);


        //临时 修改逻辑 算法不要了 只要 返回4小时
        $return['limit_duration'] = enums::OVERTIME_LIMIT_DURATION;
        return $return;
    }

    //判断申请的 是当天 还是非当天的加班
    public function getIsCurrent($isOverDays, $date){
        //当天 或者 跨天班次 前一天
        $today     = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $beforeYesterday = date('Y-m-d', strtotime('-2 day'));
        //申请当天加班 直接返回小时
        if ($date == $today || ($isOverDays && $date == $yesterday)) {
            return true;
        }

        //非当天
        if ((!$isOverDays && $date == $yesterday) || ($isOverDays && $date == $beforeYesterday)) {
            return false;
        }
        //其他情况 有问题
        $this->logger->write_log("is_current_day {$date} ", 'info');
        return false;

    }


    //dc 和非dc 件量的公式不一样 需求群定的
    public function getParcelNum($staffInfo, $date, $jobTitle)
    {
        //dco 新规则
        if ($jobTitle == enums::$job_title['dc_officer']) {
            //新接口的件数         - 新规则：当日应揽派件总量=当日揽件总操作量+当日到港件量+昨日应派未妥投（实时数据，对应之前FBI-DC每日运营统计报表截图数据）
            $storeId = $staffInfo['sys_store_id'];
            //获取 接口数据 包裹量  java 接口
            $param['store_id']   = $storeId;
            $param['begin_date'] = date('Y-m-d', strtotime("{$date} -1 day"));
            $param['end_date']   = $date;
            $data                = $this->sendRequest($param, 'deliverycount.get_latest_delivery_by_store_date', 'ard_api');
            if(empty($data)){
                return 0;
            }
            $data                = array_column($data, null, 'stat_date');
            //当日应揽派件总量=当日揽件总操作量+当日到港件量+昨日应派未妥投(接口返回的是当天的 文档要前一天的)
            $parcelNum = (float)$data[$param['begin_date']]['count_delivery_not_signed'] + (float)$data[$date]['pickup_operation_count'] + (float)$data[$date]['inbound_count'];
            return $parcelNum;
        }

        //bs 和ab是的公式 没变         - 当日揽派件总量= 所在网点OT日期当天的应派包裹量+所在网点OT日期当天的揽件量（均是实时数据）
        $param['store_id'] = $staffInfo['sys_store_id'];
        $param['date']     = $date;
        //网点揽件包裹量
        $uri = '/svc/ticket/pickup/store/count';
        $p1  = $this->sendFleRequest($uri, ['storeId' => $staffInfo['sys_store_id'], 'date' => $date]);
        //网点应派件
        $p2        = $this->sendRequest($param, 'dc.get_dc_should_delivery_count');
        $parcelNum = $p1 + $p2;
        return $parcelNum;
    }

    //根据人效 获取 可加班时长 https://flashexpress.feishu.cn/docx/NqGhdWyBIoQbyPxBGwFcyQK2nXc
    //改版 https://flashexpress.feishu.cn/docx/TutQdpNVeouc9kxI1cfcJpR0n4e
    public function getNwEffect($staffInfo, $date, $jobTitles, $parcelNum)
    {
        //获取出勤人数
        $rateData      = $this->attendance_rate($date, $jobTitles, $staffInfo['sys_store_id']);
        $attendanceNum = empty($rateData) ? 0 : count($rateData['all_num']);
        if (empty($attendanceNum)) {
            return 0;
        }
        //人效
        $effect = round($parcelNum / $attendanceNum, 2);
        //根据申请职位 计算可申请时长 分 dco  和另外两个职位 返回时长
        if (in_array(enums::$job_title['dc_officer'], $jobTitles)) {
            //获取可配置 人效变量
            $res = ConditionsRulesServer::getInstance()
                ->setRuleKey('OT_efficiency')
                ->setParameters(['staff_info_id' => $staffInfo['staff_info_id']])
                ->getConfig();
            $effectNum = floatval($res['response_data']);
            if(empty($effectNum)){
                $this->logger->write_log("OT_efficiency " . json_encode($res), 'info');
            }
            $this->effectNum = $effectNum;
            $duration = empty($effectNum) ? 0 : round($effect / $effectNum);//向下取整 改成 四舍五入
        } else {
            //人效 < 50 或者件数 < 1000
            if ($parcelNum <= enums::OT_PARCEL_BASE || $effect <= 50) {
                $duration = 0;
            } else {
                $num      = $parcelNum - enums::OT_PARCEL_BASE;
                $duration = ceil($num / enums::OT_PARCEL_STEP);
                $duration = min(enums::OVERTIME_LIMIT_DURATION, $duration);
            }
        }
        return $duration;
    }

    //根据 车辆到港 获取 增加申请时长 同 checkOtEffect 方法
    public function getLastFleetTime($staffInfo, $date)
    {
        $storeId = $staffInfo['sys_store_id'];
        $param['store_id']   = $storeId;
        $param['date']       = $date;
        //https://flashexpress.feishu.cn/docx/YjOGdxokcolp66x88iZcTOf8nCf
        /**
         * 原规则：发车网点≠分拨，下一站=员工所在网点，计划到达时间=OT日期
         * 新规则：下一站=员工所在网点，计划到达时间=OT日期
         */
        //$param['hub_ignore'] = 1;//这个地方 需要跳过 hub网点
        //根据 （计划到达时间 和当前时间 的迟到时长） 和（计划到达时间和实际到达时间 的迟到时长） 取最大 要最晚的一班
        $fleetArriveData = $this->sendRequest($param, 'dc.get_store_arrive_fleet_time');
        if (empty($fleetArriveData)) {
            return 0;
        }
        $lastArrived    = [];//最晚一班 已经到达的
        foreach ($fleetArriveData as $v) {
            //有到达时间 并且 比较晚 或者 没有值的时候 存一个
            if (!empty($v['real_arrive_time']) && (empty($lastArrived) || $v['real_arrive_time'] > $lastArrived['real_arrive_time'])) {
                $lastArrived = $v;
            }
        }

        //取迟到时间最长的
        if (!empty($lastArrived)) {
            return $lastArrived['real_arrive_time'];
        }
        return '';

    }

}