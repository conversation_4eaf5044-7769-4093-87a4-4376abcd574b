<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use Exception;
use FlashExpress\bi\App\Enums\MilesEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\OssHelper;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ResignFileModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\formPdfServer;
use FlashExpress\bi\App\Server\CancelContractServer as BaseCancelContractServer;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class CancelContractServer extends BaseCancelContractServer
{

    /**
     * @throws BusinessException
     */
    protected function validation($staffInfo, $paramIn): bool
    {
        $leaveDate = $this->processingDefault($paramIn, 'leave_date');
        //校验离职日期小于签约日期
        if ($leaveDate < $staffInfo['signing_date']) {
            throw new BusinessException($this->getTranslation()->_('18535_err_msg_001'));
        }
        return true;
    }


    // 马来 离职通知期 和 解约须知

    /**
     * @param $data
     * @return mixed
     * @throws Exception
     */
    protected function fullInsertData(&$data, $paramIn)
    {
//        $pdf_url = $this->processingDefault($paramIn, 'pdf_url');
//        if (empty($pdf_url)) {
//            throw new ValidationException('pdf url empty!');
//        }
//        $data['leave_acceptance_book'] = $pdf_url;

        $staffPositionInfo = (new StaffRepository())->getStaffPosition($data['submitter_id']);
        $resignationNotice = (new PersoninfoServer($this->lang, $this->timezone))->resignationNotice($staffPositionInfo,
            $data['submitter_id']);
        if ($resignationNotice) {
            $data = array_merge($data, [
                'resignation_notice'    => $resignationNotice['resignation_notice'],
                'resignation_work_day'  => $resignationNotice['work_day'],
                'resignation_leave_day' => $resignationNotice['leave_day'],

            ]);
        }
        return $data;
    }


    /**
     * 预览
     * @throws Exception
     */
    public function previewPdf($paramsIn)
    {
        $staffInfo = (new StaffRepository())->checkoutStaffById($paramsIn['staff_id']);

        $form_data = [
            'staff_info_id' => $staffInfo['staff_info_id'],
            'send_time'     => date('Y-m-d'),
            'sign_date'     => date('Y-m-d'),
            'staff_name'    => $staffInfo['name'],
            'start_date'    => $staffInfo['signing_date'],
            'last_work_day' => $paramsIn['last_work_date'],
            'end_date'      => $paramsIn['leave_date'],
            'identity'      => $staffInfo['identity'],
        ];
        //合同编号
        if (empty($paramsIn['number'])) {
            $paramsIn['number'] = $this->getNumber($staffInfo['staff_info_id']);
        }

        $form_data['number'] = $paramsIn['number'];

        $template_list = (new SettingEnvServer())->listByCode([
            'cancel_contract_template_header',
            'cancel_contract_template_footer',
            'cancel_contract_template_content',
        ]);
        if (!empty($template_list)) {
            $template_list = array_column($template_list, 'set_val', 'code');
        }

        $html_oss_url   = $template_list['cancel_contract_template_content'];
        $headerTemplate = $template_list['cancel_contract_template_header'];
        $footerTemplate = $template_list['cancel_contract_template_footer'];

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $headerTemplate,
            'footerTemplate'      => $footerTemplate,
        ];
        $img_data                  = [];
        if (!empty($paramsIn['sign_img'])) {
            $img_data = [['name' => 'sign_img', 'url' => $paramsIn['sign_img']]];
        }

        $pdf_file_data = (new formPdfServer())->generatePdf($html_oss_url, $form_data, $img_data,
            $staffInfo['staff_info_id'], $pdf_header_footer_setting, 'attchment');
        if (empty($pdf_file_data['object_url'])) {
            throw new \Exception('CancelContract make pdf error ');
        }
        $paramsIn['pdf_url'] = $pdf_file_data['object_url'];

        return $this->checkReturn(['code' => 1, 'data' => $paramsIn]);
    }

    /**
     * 发送邮件1
     * @param $resignInfo
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendEmail($resignInfo): bool
    {
        // 查询个人邮箱
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $resignInfo['submitter_id'],
            ],
        ]);
        if (empty($staff_info) || empty($resignInfo)){
            throw new \Exception('data error ');
        }
        $staff_info = $staff_info->toArray();
        // 生成pdf
        $pdf_url = $this->createPdf($resignInfo, $staff_info);
        if (empty($pdf_url)){
            throw new \Exception('pdf error ');
        }

        
        //获取语言
        $t    = $this->getTranslation('en');
        $email_content = $t->_('cancelcontract_email_content');
        $email_title = $t->_('cancelcontract_email_title',
            ['staff_id'   => $resignInfo['submitter_id'],
             'staff_name' => $staff_info['name'],
             'leave_date' => date('d-m-Y', strtotime($resignInfo['leave_date'])),
            ]);
        
        $this->getDI()->get('logger')->write_log("CancelContract sendMail 发送邮件开始 , staff_info_id = ".($staff_info['staff_info_id'] ?? '')." personal_email = ".($staff_info['personal_email'] ?? '') . "pdf_url = ".$pdf_url, 'info');
        if (!empty($staff_info['personal_email'])) {
            $toUsers       = $staff_info['personal_email'];
            if ($toUsers && is_string($toUsers)) {
                $toUsers = explode(',', $toUsers);
            }
            //获取邮件抄送列表
            $envModel = new SettingEnvServer();
            $cc_toUsers = $envModel->getSetVal('email_agent_suspension_resign',',');
            $fileName    = $email_title . '.pdf';
            $tmpFilePath = sys_get_temp_dir() . '/' . basename($pdf_url) . '.pdf';
            if (!file_put_contents($tmpFilePath, file_get_contents($pdf_url))) {
                $this->getDI()->get("logger")->write_log('CancelContract sendMail 附件下载失败 ', 'error');
            }else{
                $ret = (new MailServer())->send_mail($toUsers, $email_title, $email_content, $tmpFilePath, $fileName,$cc_toUsers);
                $this->getDI()->get('logger')->write_log("CancelContract sendMail 发送邮件结束 toUsers:" . implode(',',
                        $toUsers) . ', email_content:' . $email_content . ',result:' . $ret, 'info');
            }
        }
        $this->getDI()->get('logger')->write_log("CancelContract sendMail 发送消息开始 , staff_info_id = ".($staff_info['staff_info_id'] ?? '')." personal_email = ".($staff_info['personal_email'] ?? '') . "pdf_url = ".$pdf_url, 'info');
        $message_param['staff_users']        = [$staff_info['staff_info_id']];
        $message_param['message_title']      = $email_title;
        $_pdf_url = 'https://vpdf.flashexpress.my/web/viewer.html?file='.$pdf_url;
        $message_param['message_content']    = addslashes("<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='{$_pdf_url}' width='100%' height='98%'></iframe>");
        $message_param['staff_info_ids_str'] = $staff_info['staff_info_id'];
        $message_param['id']                 = time() . $staff_info['staff_info_id'] . rand(1000000, 9999999);
        $message_param['category']           = -1;
        $bi_rpc                              = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams($message_param);
        $bi_rpc->execute();
        
        $pdfFileName    = str_replace('/', '', $email_title);
        $resign_file = new ResignFileModel();
        $resign_file->staff_info_id = $staff_info['staff_info_id'];
        $resign_file->type = ResignFileModel::TYPE_AGENT_CANCEL_CONTRACT;
        $resign_file->file_url = $pdf_url;
        $resign_file->file_name = $pdfFileName;
        $resign_file->email_title = $email_title;
        $resign_file->email_content = $email_content;
        $resign_file->operate_id = MilesEnums::SYSTEM_STAFF_ID;
        $resign_file->save();
        
        return true;
    }

    /**
     * @param $path
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function getTerminationTplPath($path)
    {
        $upload_result = OssHelper::uploadFile($path, true);
        return $upload_result['object_url'];
    }

    /**
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createPdf($resignInfo, $staff_info)
    {
        $filePath = APP_PATH . "/views/termination_contract/cancel_contract_pdf_template.ftl";
        try {
            if (in_array(RUNTIME, ['dev', 'test', 'tra'])) {
                $pdf_template_url = $this->getTerminationTplPath($filePath);
            } else {
                $pdf_template_url = $this->getTerminationTplPathFromCache($filePath);
            }
        } catch (\Throwable $e) {
            $this->logger->write_log('cancel_contract_pdf_template  error' . $e->getMessage());
            return '';
        }
//        $pdf_template_url = (new SettingEnvServer())->getSetVal('cancel_contract_pdf_template_url');
        if (empty($pdf_template_url)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $notice_explanation = '';
        if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $short_notice_days = round((strtotime($resignInfo['resignation_leave_day']) - strtotime($resignInfo['leave_date'])) / (86400));
            if ($short_notice_days > 0) {
                $notice_explanation = '<p>Memandangkan demikian, terdapat short notice sebanyak '.$short_notice_days.' hari. Berdasarkan Klausa 2.5 Garis Panduan Perkhidmatan, anda
        hendaklah membayar ganti rugi short notice sebanyak RM100.00 sehari.</p>';
            }
        }
        $form_data = [
            'staff_name'         => $staff_info['name'] ?? '',
            'staff_info_id'      => $staff_info['staff_info_id'] ?? '',
            'leave_date'         => date('d-m-Y',strtotime($resignInfo['created_at'])),
            'last_leave_date'    => !empty($resignInfo['last_work_date']) ? date('d-m-Y',strtotime($resignInfo['last_work_date'])) : '',
            'notice_explanation' => $notice_explanation,
            'generate_date'      => date('d-m-Y'),
        ];
        //获取页眉页脚
        $queryParams = [
            'staff_info_id' => $resignInfo['submitter_id'],
            'department_id' => $staff_info['node_department_id'],
        ];
        $templateCnf = (new PdfHelperServer())->getHeaderAndFooter($queryParams);
        $form_data['company_name'] = $templateCnf['company_full_name'];
        [$header, $footer] = (new PdfHelperServer())->getHeaderFooter($templateCnf);

        $pdf_header_footer_setting = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => $header,
            'footerTemplate'      => $footer,
        ];
        $leave_date = date('d-m-Y', strtotime($resignInfo['leave_date']));
        $file_name = "{$staff_info['staff_info_id']}-{$staff_info['name']}-NOTIS PENAMATAN PERJANJIAN PERKHIDMATAN KONTRAKTOR BEBAS (“PERJANJIAN”) PADA {$leave_date}";
        $pdf_file_data             = (new formPdfServer())->generatePdf($pdf_template_url, $form_data, [],
            $file_name, $pdf_header_footer_setting, 'attchment');
        if (empty($pdf_file_data['object_url'])) {
            throw new \Exception('pdf error ');
        }
        return $pdf_file_data['object_url'];
    }


}