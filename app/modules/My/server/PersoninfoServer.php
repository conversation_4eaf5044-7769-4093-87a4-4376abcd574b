<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Models\backyard\StaffPayrollModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryEpfSocsoModel;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AiServer;
use FlashExpress\bi\App\Server\PersoninfoServer as GlobalBaseServer;

class PersoninfoServer extends GlobalBaseServer
{

    /**
     * 当天班次名称
     * @param $staff_id
     * @return string
     */
    protected function getStaffCurrentShiftInfo($staff_id, $hire_type): string
    {
        //新规则 整合 获取班次信息 拼接
        $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
        $shiftInfo        = $attendanceServer->getStaffShiftInfoByDate($staff_id, date('Y-m-d'));
        if(in_array($hire_type,HrStaffInfoModel::$agentTypeTogether)){
            $str = "{$shiftInfo['first_start']}-{$shiftInfo['first_end']}";
            if($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
                $str .= ",{$shiftInfo['second_start']}-{$shiftInfo['second_end']}";
            }
            return $str;
        }
        return $shiftInfo['shift_name'] ?? '';
    }

    /**
     * @param $paramIn
     * @return array
     * @throws \ReflectionException
     */
    public function getSalaryInfoFromHCM($paramIn): array
    {
        $month                 = $paramIn['month'];
        $staffId               = $paramIn['staff_id'];
        $ac                    = new ApiClient('hcm_rpc', '', 'get_salary_data', $this->lang);
        $setParams['staff_id'] = $staffId;
        $setParams['month']    = $month;
        if (isset($paramIn['run_type'])) {
            $setParams['run_type'] = $paramIn['run_type'];
        }
        $ac->setParams($setParams);
        $ac_result = $ac->execute();
        if ($ac_result["result"]['code'] == 1 || !empty($ac_result["result"]['data'])) {
            $salaryData                 = $ac_result["result"]['data'];
            $salaryData['staff_id']     = $staffId;
            $salaryData['salary_cycle'] = $month;
            $salaryData['run_type']     = $setParams['run_type'] ?? null;
            if (empty($salaryData['salary_date'])) {
                $salaryData['salary_date'] = $month;
            }
            //跳转 flash_box 地址
            $salaryData['flash_box_url'] = (
                !empty($salaryData['run_type']) && $salaryData['run_type'] == StaffPayrollModel::RUN_TYPE_1
                || $salaryData['salary_date'] == '2024-01')
                ? env('h5_endpoint') . CeoMailEnums::FLASH_BOX_CATEGORY
                : env('h5_endpoint') . CeoMailEnums::FLASH_BOX_URL;
            //工资条考勤数据
            if (!empty($salaryData['run_type']) && $salaryData['run_type'] == StaffPayrollModel::RUN_TYPE_1
                || $salaryData['salary_date'] == '2024-01') {
                $attendanceData = $this->salaryAttendanceStat($salaryData['staff_id'], $salaryData['salary_cycle'],
                    'only_total');
                $salaryData     = array_merge($salaryData, $attendanceData);
                [$salaryData['effect_num'], $salaryData['invalid_num']] = $this->formatOtData($salaryData['staff_id'],
                    $salaryData['salary_cycle']);
            }
            return $salaryData;
        }
        return ['flash_box_url' => env('h5_endpoint') . CeoMailEnums::FLASH_BOX_URL];
    }
    /**
     * ai 审核身份证
     * 情况说明：1.当前系统是马来且用户是马来国籍，走ai审核系统；
     *         2.当前系统是马来且用户不是马来国籍，走人工审核系统；
     * @param $paramIn
     * @return array
     */
    public function ai_id_card_audit($paramIn) {
        try {
            $this->getDI()->get('logger')->write_log('ai_id_card_audit params:'. json_encode($paramIn), 'info');
            $staff_id = $paramIn['staff_id'];
            $id_card_url = $paramIn['file_url'] ?? '';

            $hr_staff_item = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
                'bind' => [
                    'staff_info_id' => $staff_id,
                ]
            ]);
            $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
            $nationality = $hr_staff_item['value'] ?? 0;

            //判断是否是马来国籍，如果是，走AI 身份证审批
            if($nationality == enums::IS_MY_NATIONALITY) {
                $redis = $this->getDI()->get('redisLib');
                $redis_key = 'STAFF_AI_ID_CARD_UPLOAD_COUNT_'.$staff_id;
                $redis->expire($redis_key, 30 * 60);
                $upload_count = $redis->incr($redis_key);

                $post_result = $this->ai_id_card_ocr_post($id_card_url);

                $ai_result = $post_result['data'] ?? [];

                // 识别记录结果
                $auditCheckInfo = StaffInfoAuditCheckModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: and type = :type: ',
                    'bind'       => [
                        'staff_info_id' => $staff_id,
                        'type'          => StaffInfoAuditCheckModel::TYPE_IDENTITY,
                    ],
                ]);
                if (empty($auditCheckInfo)) {
                    $auditCheckInfo = new StaffInfoAuditCheckModel();
                }
                $auditCheckInfo->staff_info_id   = $staff_id;
                $auditCheckInfo->type            = StaffInfoAuditCheckModel::TYPE_IDENTITY;
                $staff_model = new StaffRepository($this->lang);
                $info = $staff_model->getStaffPosition($staff_id);
                //获取生日
                $staffItems = (new StaffRepository())->getSpecStaffItemsInfo($staff_id, ['BIRTHDAY']);
                $info['birthday'] = !empty($staffItems['BIRTHDAY']) ? $staffItems['BIRTHDAY'] : '';
                $compare_id_card = (new AiServer())->ai_compare([
                    'ai_data'    => $ai_result['result'] ?? [],
                    'staff_info' => $info,
                    'method' => 'aiCompareIdCard',
                ]);
                $auditCheckInfo->ai_recognition_data = $compare_id_card ? json_encode($compare_id_card, JSON_UNESCAPED_UNICODE) : '';
                $auditCheckInfo->save();
                
                // 这里之前顺序错了，调整下顺序
                if($upload_count >= 4) {
                    //未通过审核
                    $msg = $this->getTranslation()->_('id_card_ai_audit_error_5');

                    $returnData['data'] = [
                        'code' => 1,
                        'msg' => '',
                        'result' => [
                            'status' => 2,
                            'upload_count' => $upload_count,
                            'file_url' => $id_card_url,
                            'msg' => $msg,
                        ]
                    ];

                    return $this->checkReturn($returnData);
                }
                
                if(empty($ai_result)) {
                    return $this->checkReturn(-3, 'Review failed, please try again later');
                }
                if(strtoupper($ai_result['status']) == 'ERROR') {
                    $error_msg = '';
                    switch ($ai_result['error']['code']) {
                        case 'IMAGE_SIZE_EXCEED':
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_1');
                            break;
                        case 'NO_MALAYSIA_ID_CARD_IN_IMAGE_OR_LOW_IMAGE_QUALITY':
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_2');//'未检测到身份证，请保证证件无遮挡，并重新上传';
                            break;
                        default:
                            $error_msg = $this->getTranslation()->_('id_card_ai_audit_error_3');//'未检测到身份证，请重新上传';
                    }
                    $returnData['data'] = [
                        'code' => 0,
                        'msg' => $error_msg,
                        'result' => []
                    ];
                    return $this->checkReturn($returnData);
                }
                if(!empty($compare_id_card['is_pass'])) {
                    $redis->delete($redis_key);
                    //通过审核
                    $returnData['data'] = [
                        'code' => 1,
                        'msg' => '',
                        'result' => [
                            'status' => 1,
                            'upload_count' => 1,
                            'file_url' => $id_card_url,
                            'msg' => $this->getTranslation()->_('id_card_ai_audit_success'),
                        ]
                    ];
                } else {
                    //未通过审核
                    $msg = $this->getTranslation()->_('id_card_ai_audit_error_5');
                    if($upload_count < 4) {
                        $msg = $this->getTranslation()->_('id_card_ai_audit_error_4');
                    }
                    $returnData['data'] = [
                        'code' => 1,
                        'msg' => '',
                        'result' => [
                            'status' => 2,
                            'upload_count' => $upload_count,
                            'file_url' => $id_card_url,
                            'msg' => $msg,
                        ]
                    ];
                }
                $redis_id_card_key = 'STAFF_AI_ID_CARD_AUDIT_'.$staff_id;
                $redis->set($redis_id_card_key, json_encode($returnData['data']), 600);
                $this->getDI()->get('logger')->write_log('ai_id_card_audit 结果:'. json_encode($returnData), 'info');
            } else {
                $returnData['data'] = [
                    'code' => 1,
                    'msg' => '',
                    'result' => [
                        'status' => 2,
                        'upload_count' => 4,
                        'file_url' => $id_card_url,
                        'msg' => $this->getTranslation()->_('id_card_ai_audit_error_6')
                    ]
                ];
                $this->getDI()->get('logger')->write_log('非马来国籍 ai_id_card_audit 结果:'. json_encode($returnData), 'info');
            }
            return $this->checkReturn($returnData);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('ai_id_card_audit---'.$e->getMessage().'-----'.$e->getLine().$e->getFile());
            return $this->checkReturn(-3, 'error');
        }
    }

    /**
     * 记录审核日志--my有ai审核日志
     * @param $staff_id
     * @param $audit_before_state
     * @param $status
     */
    public function insertAiAuditLog($staff_id, $audit_before_state, $status)
    {
        $audit_log = [
            'staff_info_id' => $staff_id,
            'audit_id' => 10000,
            'audit_name' => $this->getTranslation()->_('ai_audit'),
            'audit_before_state' => $audit_before_state,
            'audit_after_state' => $status,
        ];
        $audit_log_result = $this->getDI()->get('db')->insertAsDict('staff_identity_annex_audit_log', $audit_log);
        if (!$audit_log_result) {
            $msg = "identity annex audit log insert fail:" . var_export($audit_log, true) . PHP_EOL;
            $this->getDI()->get("logger")->write_log($msg, 'info');
        }
    }

    /**
     * 保存公积金号码
     * @param $staffId
     * @param $epfNo
     * @return bool
     */
    public function save_epf_no($staffId, $epfNo): bool
    {
        $findEpf = StaffSalaryEpfSocsoModel::findFirst([
            'conditions' => 'staff_info_id =:staff_info_id:',
            'bind'       => ['staff_info_id' => $staffId],
        ]);
        if ($findEpf && empty($findEpf->epf_no)) {
            $findEpf->epf_no = $epfNo;
            $findEpf->save();
        }
        if (!$findEpf) {
            $model                = new StaffSalaryEpfSocsoModel();
            $model->epf_er        = 13; //雇主公积金缴纳比例
            $model->epf_ee        = 11;//雇员公积金缴纳比例
            $model->staff_info_id = $staffId;
            $model->epf_no        = $epfNo;
            $model->create();
        }
        return true;
    }
}
