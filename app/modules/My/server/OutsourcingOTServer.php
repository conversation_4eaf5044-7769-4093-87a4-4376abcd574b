<?php
/**
 * Author: Bruce
 * Date  : 2024-02-24 12:10
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Server\OutsourcingOTServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class OutsourcingOTServer extends GlobalBaseServer
{
    /**
     * 根据当前登入者信息判断是否为分拨经理
     * @param array $userInfo
     * @return bool
     */
    public function isHubFenBoManger(array $userInfo): bool
    {
        // 拿到分拨经理的角色id
        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');
        $loginPositions = $userInfo['positions'];

        if (array_intersect($hubOsRoles, $loginPositions)) {
            return true;
        }
        return false;
    }

}