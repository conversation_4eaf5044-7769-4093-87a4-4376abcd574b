<?php
namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Models\backyard\AdvanceFuelModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffVehicleStatusRecordRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;


use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\BaseServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\VehicleServer as BaseVehicleServer;

class VehicleServer extends BaseVehicleServer
{

    public $isVan = false;

    const OTHER = 100;
    /**
     * @var PublicRepository
     */
    private $public;
    /**
     * @var AuditListServer
     */
    private $auditlist;
    /**
     * @var OvertimeRepository
     */
    private $ov;
    /**
     * @var VehicleRepository
     */
    private $vehicle;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang);
        $this->vehicle = new VehicleRepository($lang, $timezone);
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
    }

    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleS($paramIn = [])
    {
        $returnData = VehicleInfoEnums::CONFIG_VEHICLE_INFO;

        $oilType = [];
        $ot = VehicleInfoEnums::OIL_TYPE_ITEM;
        $oilType[] = ['value' => 1, 'label' => $ot[1]];
        $oilType[] = ['value' => 2, 'label' => $ot[2]];
        $oilType[] = ['value' => 3, 'label' => $ot[3]];
        $returnData['oil_type'] = $oilType;


        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }

        // 驾照类型
        $driver_license_item = [];
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            $driver_license_item[] = [
                'value' => $l_k,
                'label' => $l_v,
            ];
        }


        // 车型
        $returnData['vehicle_size'] = [];
        foreach (self::getVehicleSize(false)  as $k => $v) {
            $returnData['vehicle_size'][] = [
                'value' => strval($k),
                'label' => $v
            ];
        }

        $returnData['driver_license_item'] = $driver_license_item;
        //是否展示车类型
        $returnData['is_show_vehicle_type_category'] = in_array($paramIn['job_title'],VehicleInfoEnums::VAN_JOB_GROUP_ITEM)? 1 : 0;
        //车类型
        $returnData['vehicle_type_category_list'] = VehicleInfoEnums::JOB_TITLE_VEHICLE_CATEGORY_ITEM[$paramIn['job_title']]??[];
        $returnData['driving_license_vehicle_restrictions'] = array_values(VehicleInfoEnums::DRIVING_LICENSE_VEHICLE_RESTRICTIONS);

        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }

    /**
     * 车辆信息
     * @Access  public
     * @param $paramIn
     * @return array
     */
    public function getVehicleInfoS($paramIn = [])
    {
        $res = $this->vehicle->getVehicleInfoR($paramIn['id']);
        $returnData['vehicle_info'] = array();

        //如果存在车辆信息显示车辆型号品牌名称
        if ($res) {
            //TODO 判断本月是否能换车
            $res['change_car']  = 1;
            $returnData['vehicle_info'] = $res;
        }

        $returnData['vehicle_info'] = $this->handleVehicleInfo($returnData['vehicle_info'], $paramIn);

        return $returnData;
    }

    /**
     * 里程信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageS($paramIn = [], $userinfo = [])
    {
        $returnData = $resImgArr = [];
        //获取里程信息
        $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $userinfo['id']);
        if ($res) {
            //获取里程对应的图片信息SMI-上班 // EMI-下班
            $resImg = $this->vehicle->getMileageImgR($res['id']);
            if ($resImg) {
                foreach ($resImg as $k => $v) {
                    if (strstr($v['object_key'], 'SMI')) {
                        $resImgArr['started_path'] = $v['object_key'];
                        $resImgArr['started_bucket'] = $v['bucket_name'];
                        $resImgArr['started_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    } else if (strstr($v['object_key'], 'EMI')) {
                        $resImgArr['end_path'] = $v['object_key'];
                        $resImgArr['end_bucket'] = $v['bucket_name'];
                        $resImgArr['end_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    }
                }
            }
            $res = $res + $resImgArr;
        } else {
            $res = (object)[];
        }
        //获取本月补里程次数
        $count = $this->vehicle->getMileageCountR($userinfo['id'], $paramIn['mileage_date']);
        //月次数计算和剩余提示
        $count_str = str_replace("{}", (3 - $count['record_count']), $this->getTranslation()->_('7137'));
        //提示已经用完
        $alert = $count['record_count'] >= 3 ? $this->getTranslation()->_('7138') : 3 - $count['record_count'];
        //网点补里程大小控制
        $branchMileageArr = VehicleInfoEnums::CONFIG_VEHICLE_INFO['branch_mileage'];
        $lang = $this->getTranslation()->_('7140');
        $branch_mileage_msg = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? str_replace( '50',bcdiv($branchMileageArr[$userinfo['organization_id']],1000,0) ,$lang) : $lang;
        $branch_mileage = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? $branchMileageArr[$userinfo['organization_id']] : 150000;
        //当日里程信息和历史申请次数
        $returnData['data'] = $count;
        $returnData['data']['list'] = $res;
        $returnData['data']['count'] = $count_str;
        $returnData['data']['alert'] = $alert;
        $returnData['data']['branch_mileage'] = bcdiv($branch_mileage,1000,0);
        $returnData['data']['branch_mileage_msg'] = $branch_mileage_msg;
        return $this->checkReturn($returnData);
    }


    /**
     * 创建车辆信息 userinfo 里只有 id 和 job_title 如果要用别的 记得在外层新增参数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoS($paramIn = [], $userinfo,$operate_staff_id = '')
    {
        $returnData['data'] = [];

        // 整合入表字段
        $vehicleData = $this->filterVehicleData($paramIn, $userinfo);
        $this->getDI()->get('logger')->write_log('kit_add_vehicle_info vehicleData: '.json_encode($vehicleData, JSON_UNESCAPED_UNICODE), 'info');

        //查询验证是否有数据
        $vehicleInfo = $this->vehicle->getVehicleInfoR($vehicleData['uid']);
        if (!empty($vehicleInfo) && ($vehicleInfo['approval_status'] == VehicleInfoEnums::APPROVAL_PENDING_CODE)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0006'));

        }

        // 验证车牌号 是否 与 其他在职人的车牌号重复
        if ($this->checkPlateNumberIsExist($vehicleData['plate_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0001'));
        }

        // 验证发动机号 是否 与 其他在职人的发动机号重复
        if ($this->checkEngineNoIsExist($vehicleData['engine_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0003'));
        }

        // 验证开始用车日期, 不得早于入职日期
        $hire_date = $this->getStaffHireDate($userinfo['id']);
        if ($vehicleData['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE && !empty($vehicleData['vehicle_start_date']) && $vehicleData['vehicle_start_date'] < $hire_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0007'));
        }

        if(!empty($vehicleData['oil_type'])){
            $vehicleData['unit_price'] = 0;//$this->getUnitPriceByCBM($vehicleData['oil_type'],$vehicleData['car_long'],$vehicleData['car_width'],$vehicleData['car_high']);
        }

        if($this->isVan){
            if(empty($vehicleInfo)){
                //校验油卡卡号是否已经存在
                if(!empty($vehicleData['oil_number'])){
                    if($exist_staff_id = $this->checkOilNoIsExist($vehicleData['oil_number'],$userinfo['id'])){
                        return $this->checkReturn(-3,str_replace("XXX",$exist_staff_id, $this->getTranslation()['oil_number_008']));
                    }
                }
                $vehicleData['oil_subsidy_type'] = VehicleInfoEnums::OIL_SUBSIDY_TYPE_2;

            }else{
                if(!empty($vehicleData['oil_number'])){
                    // 油卡号是否变更，若变更，则需校验旧油卡是否未充值
                    $oil_checkout = $this->getCheckOilNumberByIsIntoMoney($vehicleInfo['oil_number'],$userinfo['id']);
                    if(!empty($oil_checkout) && ($oil_checkout['oil_number'] != $vehicleData['oil_number'])){
                        return $this->checkReturn(-3, $this->getTranslation()['fuel_manage_oil_number_no_checkout']);
                    }
                    $this->getDI()->get('logger')->write_log('kit_add_vehicle_info oil_subsidy_type form 1 to 2 : '.json_encode($vehicleData, JSON_UNESCAPED_UNICODE), 'info');

                }
            }

            if(isset($vehicleData['oil_number'])&&$vehicleData['oil_number']){
                if(!($vehicleData['oil_img']&&$vehicleData['oil_company']&&$vehicleData['oil_effective_date'])){
                    return $this->checkReturn(-3, 'oil_info_error');
                }
            }else{
                if(!(empty($vehicleData['oil_img'])&&empty($vehicleData['oil_company'])&&empty($vehicleData['oil_effective_date']))){
                    return $this->checkReturn(-3, 'oil_info_error');
                }
            }
        }

        if (empty($vehicleInfo)) {
            $vehicleData['formal_data'] = '';
            $vehicleData['creator_id'] = $userinfo['id'];
            $vehicleData['create_channel'] = VehicleInfoEnums::VEHICLE_ADD_CHANNEL_KIT;
            $res = $this->vehicle->addVehicleInfoR($vehicleData);
        } else {
            $res = $this->vehicle->updateVehicleInfo($vehicleData);
        }

        if ($res) {
            if (!empty($vehicleData['oil_number'])) {
                // 检测一下用不用修改 预计油费申请记录 如果预支状态等于待提交 则需更新为待充值 目前仅my有此逻辑
                $advance_fuel = AdvanceFuelModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id:",
                    'bind'       => [
                        'staff_info_id' => $vehicleData['uid'],
                    ],
                ]);
                if (!empty($advance_fuel) && $advance_fuel->advance_status == AdvanceFuelModel::ADVANCE_STATUS_TO_BE_SUBMITTED) {
                    $advance_fuel->advance_status = AdvanceFuelModel::ADVANCE_STATUS_TO_BE_RECHARGED;
                    $advance_fuel->save();
                }
            }
            
            
            // 车辆信息日志
            $vehicleLogData = [];
            $vehicleLogData['staff_id'] = $vehicleData['uid'];
            $vehicleLogData['operate_staff_id'] = $operate_staff_id ?? $vehicleData['uid'];
            $vehicleLogData['text'] = json_encode($vehicleData, JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);

            $returnData['data'] = $vehicleData['uid'];
            return $this->checkReturn($returnData);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     * 根据长宽高获取 单价
     * @param $oil_type
     * @param $long
     * @param $width
     * @param $high
     * @return float|int|string
     */
    private function getUnitPriceByCBM($oil_type,$long,$width,$high)
    {
        if($oil_type == VehicleInfoEnums::OIL_TYPE_001){
            return 4*100;
        }
        $re = 0;
        $cbm= round(bcmul(bcmul($long,$width,4),$high,4),2);
        if($cbm >= 3.75 && $cbm < 7.5){
            $re = 3;
        }elseif($cbm >=7.5 && $cbm<12 ){
            $re = 3.5;
        }elseif($cbm>=12){
            $re = 4;
        }
        return  bcmul($re , 100);
    }

    /**
     * 调用push
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function pushVehicleMsg($paramIn, $userinfo)
    {
        try {
            //获取上级ID
            $newUserInfo['staff_info_id'] = $userinfo['id'];
            $higherUId = $this->ov->getHigherStaffId($newUserInfo);
            $lang = (new StaffServer())->getLanguage($higherUId['value']);
            //调用push 发消息
            $t = $this->getTranslation($lang);
            $message_title = $t->_('6007');
            $audit_type = $t->_('6010');
            $pushParam = [
                'staff_info_id' => $higherUId['value'],    //接收push信息人id
                'message_title' => $message_title,    //push标题
                'userinfo' => $userinfo,    //当前登陆用户信息
                'lastInsert_id' => $userinfo['id'],    //操作id
                'audit_type' => 'vehicle',    //模块名称
                'is_audit' => 1,    //xx状态
                'lang' => $lang
            ];
            $success = $this->public->pushMessage($pushParam);
            if ($success) {
                /* 执行修改操作 */
                $sql = "update vehicle_mileage set is_push = 1 where id = {$paramIn['id']} ";
                $this->getDI()->get('db')->query($sql);
            }
        } catch (\Exception $e) {
            $this->wLog('pushError', $e->getMessage(), 'vehicle',  'push');
        }
        return true;
    }

    /**
     * 车辆里程补卡申请记录次数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getVehicleMileageS($paramIn, $userinfo)
    {
        $sql = " SELECT count(*) as record_count FROM vehicle_mileage where status = 1 AND apply_user = {$userinfo['id']}  AND mileage_date =  '{$paramIn['mileage_date']}' ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 获取油卡充值记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffRecord($mouth, $staff_id)
    {
        $beign = date('Y-m-d', mktime(0, 0, 0, $mouth, 1, date('Y')));
        $end = date('Y-m-d', mktime(23, 59, 59, $mouth + 1, 0, date('Y')));
        $sql = "SELECT	s_mr.staff_info_id,
                mileage_date,
                start_kilometres,
                end_kilometres,
                prepaid_slip_no,
                s_mr.money,
                LEFT ( s_mr.updated_at, 10 ) AS updated_at,
                s_mr_info.recharge_at ,
                s_mr_info.state ,
                v_info.unit_price
            FROM
                staff_mileage_record as s_mr
            left join staff_mileage_record_prepaid_info as s_mr_info on s_mr.prepaid_slip_no = s_mr_info.prepaid_no and s_mr_info.`staff_id` = s_mr.`staff_info_id`
            left join vehicle_info as v_info on s_mr.staff_info_id = v_info.uid
            WHERE
                s_mr.staff_info_id = {$staff_id} 
                AND mileage_date >= '{$beign}' 
                AND mileage_date <= '{$end}';
        ";
        
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($info_data)) {
            return [];
        }
        $list = [];
        foreach ($info_data as $key => $value) {
            $list[$key]['created_at'] = $value['mileage_date'];
            $list[$key]['kilometres'] =
                ($value['end_kilometres'] > 0 && $value['start_kilometres'] > 0) && ($value['end_kilometres'] > $value['start_kilometres']) ?
                bcdiv(bcsub($value['end_kilometres'], $value['start_kilometres']), 1000) : "NA";
            $list[$key]['fuel_prepaid'] = 0;
            $list[$key]['fuel_subsidies'] = 0;
            $list[$key]['buy_data'] = "NA";
            $list[$key]['fuel_prepaid'] = bcdiv($value['money'],100,2); //预计补贴金额
            if($info_data[$key]['state']){
                $list[$key]['fuel_subsidies'] = bcdiv($value['money'],100,2); //补贴金额
                $list[$key]['buy_data'] = $value['updated_at'];
            }
                
            $list[$key]['recharge_at'] = $value['recharge_at'] ?? "NA";
        }
        return $list;
    }
    /**
     * 获取人员车辆信息
     */
    public function getVehicleInfoByStaffID($staff_id)
    {
        $sql = "SELECT
                `id`,
                `vehicle_brand`,
                `vehicle_model`,
                `vehicle_size`,
                `plate_number`,
                `buy_date`,
                `oil_number`,
                `oil_type`,
                `oil_company`,
                `vehicle_img`,
                `driving_licence_img`,
                `uid` as staff_id,
                `deleted`,
                `money`,
                `is_open`,
                `open_date`,
                `updated_at`,
                `created_at`,
                `is_cut_money`,
                balance
            FROM
                `vehicle_info` 
            WHERE
                uid = $staff_id
        ";
        $info_data = $this->getDI()->get('db')->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC);
        return $info_data ?? [];
    }

    /**
     * 获取车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffVehicle($staff_id)
    {
        $info_data = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind' => ['uid' => $staff_id],
            'columns' => ['id', 'approval_status']
        ]);

        if (!empty($info_data) && in_array($info_data->approval_status, [1, 2])) {
            return 1;
        }

        return 0;
    }




    /**
     * 获取里程信息[bi使用]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffMileageS($paramIn = [])
    {
        return $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $paramIn['staff_info_id']);
    }

    /**
     * 修改里程信息[bi使用]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateStaffMileageS($paramIn = [])
    {
            $res = $this->vehicle->updateStaffMileageR($paramIn);
            return $res;
    }

    /**
     * 获取人员车辆信息
     */
    public function getVehicleInfoByWhere($where = [])
    {
        if(empty($where)){
            return [];
        }

        $sql = "SELECT
                `id`,
                `vehicle_brand`,
                `vehicle_model`,
                `vehicle_size`,
                `plate_number`,
                `buy_date`,
                `oil_number`,
                `oil_type`,
                `oil_company`,
                `vehicle_img`,
                `driving_licence_img`,
                `uid` as staff_id,
                `deleted`,
                `money`,
                `is_open`,
                `open_date`,
                `updated_at`,
                `created_at`,
                `is_cut_money`,
                balance
            FROM
                `vehicle_info` 
            WHERE
                1 = 1

        ";
        $str_where = '';
        if (isset($where['oil_company'])){
            $str_where .= ' and oil_company = 3 ';//PT
        }
        $info_data = $this->getDI()->get('db')->fetchAll($sql.$str_where,\Phalcon\Db::FETCH_ASSOC);
        return $info_data ?? [];
    }

    /**
     * 判断本月能修改几次
     */
    public function getStaffRecordMileage($mouth, $staff_id)
    {
        try{
            $beign = date('Y-m-d', mktime(0, 0, 0, $mouth, 1, date('Y')));
            $end = date('Y-m-d', mktime(23, 59, 59, $mouth + 1, 0, date('Y')));
            $sql = "SELECT	s_mr.staff_info_id,
                    mileage_date,
                    prepaid_slip_no
                FROM
                    staff_mileage_record as s_mr
                WHERE
                    s_mr.staff_info_id = {$staff_id} 
                    AND mileage_date >= '{$beign}' 
                    AND mileage_date <= '{$end}'
                    AND change_car = 1;
            ";
            
            $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            return count($info_data ?? []);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage(),'error');
        }
        return 0;
    }

    /**
     * 里程信息【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageModifyS($paramIn = [], $userinfo = [])
    {
        $returnData = $resImgArr = [];
        //获取里程信息
        $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $userinfo['id']);
        if ($res) {
            //获取里程对应的图片信息SMI-上班 // EMI-下班
            $resImg = $this->vehicle->getMileageImgR($res['id']);
            if ($resImg) {
                foreach ($resImg as $k => $v) {
                    if (strstr($v['object_key'], 'SMI')) {
                        $resImgArr['started_path'] = $v['object_key'];
                        $resImgArr['started_bucket'] = $v['bucket_name'];
                        $resImgArr['started_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    } else if (strstr($v['object_key'], 'EMI')) {
                        $resImgArr['end_path'] = $v['object_key'];
                        $resImgArr['end_bucket'] = $v['bucket_name'];
                        $resImgArr['end_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    }
                }
            }
            $res = $res + $resImgArr;
            //当日已经修改的里程信息
            $NewInfo = $this->vehicle->getMileageModifyDetailR($userinfo['id'], $paramIn['mileage_date']);
            $res['new_start_kilometres'] = $NewInfo['start_kilometres'];
            $res['new_end_kilometres'] = $NewInfo['end_kilometres'];
            //by不补里程记录 不允许再次修改
            $res['permission'] = (isset($res['create_channel']) && $res['create_channel'] == 1 ) ? 0 : 1 ;
            $res['msg'] = (isset($res['create_channel']) && $res['create_channel'] == 1 ) ?$this->getTranslation()->_('mileage_modify_msg1') : '' ;
        } else {
            $res = (object)[];
        }
        //获取本月补里程次数
        $count = $this->vehicle->getMileageModifyCountR($userinfo['id'], $paramIn['mileage_date']);
        //月次数计算和剩余提示
        $count_str = str_replace("{}", (2 - $count['record_count']), $this->getTranslation()->_('7137'));
        //提示已经用完
        $alert = $count['record_count'] >= 2 ? $this->getTranslation()->_('7138') : 2 - $count['record_count'];



        $returnData['data'] = $count;
        $returnData['data']['list'] = $res;
        //$returnData['data']['count'] = $count_str;
        //$returnData['data']['alert'] = $alert;
        return $this->checkReturn($returnData);
    }
    /**
     * 车辆里程补卡申请记录次数【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function dayMileageModifyS($paramIn, $userinfo)
    {
        $sql = " SELECT count(*) as record_count FROM approve_modify_mileage where staff_info_id = {$userinfo['id']}  AND mileage_date =  '{$paramIn['mileage_date']}' ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * 创建【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addMileageModifyS($paramIn = [], $userinfo)
    {
        //格式化订单数据
        $serialNo = $this->getID();
        //当日已经修改的里程信息
        $returnData['data'] = [];
        $updateData['mileage_date'] = isset($paramIn['mileage_date']) ? $paramIn['mileage_date'] : '';//里程日期
        $updateData['start_kilometres'] = isset($paramIn['start_kilometres']) ? $paramIn['start_kilometres'] : '';//上班公里数(m)

        $updateData['end_kilometres'] = isset($paramIn['end_kilometres']) ? $paramIn['end_kilometres'] : '';//下班公里数(m)

        //$updateData['status'] = isset($paramIn['status']) ? $paramIn['status'] : 1;
        $updateData['staff_info_id'] = isset($userinfo['id']) ? $userinfo['id'] : '';//申请人
        $updateData['serial_no']   = !empty($serialNo) ?'MI'.$serialNo : NULL;

        $res = $this->vehicle->dayMileageCountR($updateData['mileage_date'], $userinfo['id']);
        $updateData['origin_id'] = isset($res['id']) ? $res['id'] : 0;//origin_id
        $updateData['origin_start_kilometres'] = isset($res['start_kilometres']) ? $res['start_kilometres'] : 0;//start_kilometres
        $updateData['origin_end_kilometres'] = isset($res['end_kilometres']) ? $res['end_kilometres'] : 0;//end_kilometres

        //插入数据
        $returnData['data'] = $this->vehicle->addMileageModifyR($updateData);
        if ($returnData['data']){
            return $this->checkReturn($returnData);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('2109'));
        }

    }

    /**
     * 获取检查清单项目
     * @param $userinfo
     * @return array[]
     */
    public function getCheckListItems($userinfo)
    {
        $t = $this->getTranslation();
        $checkList =
        [
            'staff'=>['name'=>$userinfo['name']],
            'checklist'=>[
            [
                'key'=>'car_lights_list',
                'name'=>$t['car_lights_list'],
                'items' => [
                    [
                        'name'=>$t['front_lights'],
                        'key' => 'front_lights',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['rear_lights'],
                        'key' => 'rear_lights',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['double_flash'],
                        'key' => 'double_flash',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['roof_lamp'],
                        'key' => 'roof_lamp',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['left_turn_light'],
                        'key' => 'left_turn_light',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['right_turn_light'],
                        'key' => 'right_turn_light',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ]
                ]
            ],
            [
                'key'=>'container_list',
                'name'=>$t['container_list'],
                'items' => [
                    [
                        'name'=>$t['left_container_wall'],
                        'key' => 'left_container_wall',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['right_container_wall'],
                        'key' => 'right_container_wall',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['wall_container_top'],
                        'key' => 'wall_container_top',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['container_door'],
                        'key' => 'container_door',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['door_lock'],
                        'key' => 'door_lock',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ]
                ]
            ],
            [
                'key'=>'safety_equipment_list',
                'name'=>$t['safety_equipment_list'],
                'items' => [
                    [
                        'name'=>$t['fire_extinguisher'],
                        'key' => 'fire_extinguisher',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['flashlight'],
                        'key' => 'flashlight',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['cone_or_triangle_reflective_sign'],
                        'key' => 'cone_or_triangle_reflective_sign',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['wheel_baffle_plate'],
                        'key' => 'wheel_baffle_plate',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ],
                    [
                        'name'=>$t['reflective_coat'],
                        'key' => 'reflective_coat',
                        'options'=>[
                            ['label'=>$t['yes'],'value'=>'yes'],
                            ['label'=>$t['no'],'value'=>'no']
                        ],
                        'selectValue' => 'yes'
                    ]
                ]
            ],
            [
                'key'=>'other_list',
                'name'=>$t['other_list'],
                'items' => [
                    [
                        'name'=>$t['tire_condition'],
                        'key' => 'tire_condition',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['wiper'],
                        'key' => 'wiper',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                    [
                        'name'=>$t['brake'],
                        'key' => 'brake',
                        'options'=>[
                            ['label'=>$t['intact'],'value'=>'intact'],
                            ['label'=>$t['damage'],'value'=>'damage']
                        ],
                        'selectValue' => 'intact'
                    ],
                ]
            ]
        ]
        ];

        return $checkList;
    }

    /**
     * @param $reportDate
     * @param $staffId
     * @param $duty
     * @param $checkList
     * @return bool
     */
    public function addStatusRecord($reportDate,$staffId,$duty,$checkList)
    {
        $column1 = $duty .'_duty_report_time';
        $column2 = $duty .'_duty_check_list';
        $reportTime = date('H:i:s',time());
        $data = [
            'report_date' => $reportDate,
            'report_time' => $reportTime,
            'staff_info_id' => $staffId,
            $column1 => $reportTime,
            $column2 => json_encode($checkList),
        ];

        $repo = new StaffVehicleStatusRecordRepository();
        $records = $repo->findAllByCondition(['staff_info_id'=>$staffId,'report_date'=>$reportDate]);
        $record = $records->getFirst();
        if ($record){
            return $repo->update($record,$data);
        }else{
            return $repo->insert($data);
        }
    }

    /**
     * 格式化车辆信息详情
     * @param array $vehicle_info
     * @param array $paramIn
     * @return mixed
     */
    protected function handleVehicleInfo(array $vehicle_info, array $paramIn)
    {
        if (empty($paramIn)) {
            return [];
        }

        // 为空, 补充默认字段 及 默认值
        if (empty($vehicle_info)) {
            // 车辆品牌及车辆型号、购买日期、油卡公司 van
            $vehicle_info['vehicle_brand'] = '';
            $vehicle_info['vehicle_brand_text'] = '';
            $vehicle_info['vehicle_model'] = '';
            $vehicle_info['vehicle_model_text'] = '';
            $vehicle_info['buy_date'] = null;
            $vehicle_info['oil_number'] = '';
            $vehicle_info['oil_company'] = 0;

            // 车辆照片/机动车登记证 &&
            $vehicle_info['vehicle_img'] = [];
            $vehicle_info['registration_certificate_img'] = '';

            $vehicle_info['vehicle_registration_number'] = '';//机动车登记编号 new
            $vehicle_info['vehicle_registration_date'] = null;//机动车登记日期 new
            $vehicle_info['vehicle_proof_number'] = '';//车辆凭证号码 new
            $vehicle_info['vehicle_proof_img'] = '';//车辆凭证图片 new
            $vehicle_info['car_long'] = '';//车辆 new
            $vehicle_info['car_width'] = '';//车辆 new
            $vehicle_info['car_high'] = '';//车辆 new


            // 车辆保险 &&
            $vehicle_info['insurance_policy_number'] = '';
            $vehicle_info['insurance_start_date'] = null;
            $vehicle_info['insurance_end_date'] = null;

            // 车辆税 &&
            $vehicle_info['vehicle_tax_expiration_date'] = null;
            $vehicle_info['vehicle_tax_certificate_img'] = '';

            // 驾照信息 &&
            $vehicle_info['driver_license_type'] = '';
            $vehicle_info['driver_license_type_other_text'] = '';
            $vehicle_info['driver_license_start_date'] = null;
            $vehicle_info['driver_license_end_date'] = null;
            $vehicle_info['driving_licence_img'] = '';
            $vehicle_info['driving_license_vehicle_restrictions'] = [];//驾照车辆限制 new
            $vehicle_info['driving_license_vehicle_restrictions_str'] = [];//驾照车辆限制 new

        } else {
            // 删除无需字段
            unset($vehicle_info['id']);
            unset($vehicle_info['deleted']);
            unset($vehicle_info['money']);
            unset($vehicle_info['is_open']);
            unset($vehicle_info['open_date']);
            unset($vehicle_info['updated_at']);
            unset($vehicle_info['created_at']);
            unset($vehicle_info['is_cut_money']);
            unset($vehicle_info['balance']);
            unset($vehicle_info['unit_price']);
            unset($vehicle_info['approval_staff_id']);
            unset($vehicle_info['approval_time']);
            unset($vehicle_info['creator_id']);
            unset($vehicle_info['editor_id']);
            unset($vehicle_info['create_channel']);
        }

        // 车辆类型, 职位优先
        $vehicle_info['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$paramIn['job_title']];
        $vehicle_info['vehicle_type_label'] = VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicle_info['vehicle_type']];
        $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($paramIn['id'], 'vehicle_source, vehicle_use_date, job_title,hire_type');
        $hr_staff_info = $hr_staff_info ? $hr_staff_info->toArray() : [];
        $vehicle_info['job_title'] = $hr_staff_info['job_title'] ?? 0 ;
        $vehicle_info['hire_type'] = intval($hr_staff_info['hire_type']);
        // 车辆来源: 同步fbi-hr_is数据
        if (empty($vehicle_info['vehicle_source'])) {

            $vehicle_info['vehicle_source'] = $hr_staff_info['vehicle_source'] ?? VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
            $vehicle_info['vehicle_start_date'] = $hr_staff_info['vehicle_use_date'] ?? null;
        }

        $vehicle_info['vehicle_source_label'] = $vehicle_info['vehicle_source'] ? $this->getTranslation()->_(VehicleInfoEnums::VEHICLE_SOURCE_ITEM[$vehicle_info['vehicle_source']]) : '';

        // 车牌号 hr-is取默认值, 如没有，则再从whr_is取默认值
        if (empty($vehicle_info['plate_number'])) {
            $vehicle_info['plate_number'] = (new StaffRepository())->getAvatar($paramIn['id'], 'CAR_NO');
        }

        // 上牌地点/发动机号码/驾照号码 whr-is取默认值
        if (
            empty($vehicle_info['plate_number'])
        ||
            empty($vehicle_info['license_location'])
        ||
            empty($vehicle_info['engine_number'])
        ||
            empty($vehicle_info['driver_license_number'])
        ) {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['entry' => HrEntryModel::class]);
            $builder->innerJoin(HrEconomyAbilityModel::class,'entry.resume_id = hr.resume_id', 'hr');
            $builder->where('entry.staff_id = :staff_id:', ['staff_id' => $paramIn['id']]);
            $builder->columns([
                'hr.car_number',// 车牌号
                'hr.driver_number',//驾照号
                'hr.place_cards',//上牌地点
                'hr.car_engine_number',//发动机号
            ]);
            $win_staff_info = $builder->getQuery()->getSingleResult();
            if (!empty($win_staff_info)) {
                $vehicle_info['plate_number'] = !empty($vehicle_info['plate_number']) ? $vehicle_info['plate_number'] : $win_staff_info->car_number ?? '';
                $vehicle_info['license_location'] = !empty($vehicle_info['license_location']) ? $vehicle_info['license_location'] : $win_staff_info->place_cards ?? '';
                $vehicle_info['engine_number'] = !empty($vehicle_info['engine_number']) ? $vehicle_info['engine_number'] : $win_staff_info->car_engine_number ?? '';
                $vehicle_info['driver_license_number'] = !empty($vehicle_info['driver_license_number']) ? $vehicle_info['driver_license_number'] : $win_staff_info->driver_number ?? '';
            }
        }


        $vehicle_setting = VehicleInfoEnums::CONFIG_VEHICLE_INFO;
        $vehicle_info['oil_company']=intval($vehicle_info['oil_company']);
        // 油卡企业
        $vehicle_info['oil_company_label'] = '';
        if (!empty($vehicle_info['oil_company'])) {
            $oil_company_conf = array_column($vehicle_setting['oil_company'], 'label', 'value');
            $vehicle_info['oil_company_label'] = $oil_company_conf[$vehicle_info['oil_company']] ?? '';
        }

        // 车辆品牌/车辆型号
        $vehicle_info['vehicle_brand_label'] = '';
        $vehicle_info['vehicle_model_label'] = '';
        if ($vehicle_info['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE && !empty($vehicle_info['vehicle_brand'])) {
            $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
            $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], 'label', 'value');

            $vehicle_info['vehicle_brand_label'] = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
            $vehicle_info['vehicle_model_label'] = !empty($vehicle_info['vehicle_model']) ? $vehicle_model_conf[$vehicle_info['vehicle_model']] : '';
        }

        // 车型
        $vehicle_info['vehicle_size'] = $vehicle_info['vehicle_size'] ?? '';
        $vehicle_info['vehicle_size_label'] = self::getVehicleSize(true)[$vehicle_info['vehicle_size']]?? '';
        //
        $vehicle_info['vehicle_img'] = !empty( $vehicle_info['vehicle_img'])? explode("\n", $vehicle_info['vehicle_img']):[];


        // 油类型
        $vehicle_info['oil_type'] = $vehicle_info['oil_type'] ?? '';
        $vehicle_info['oil_type_label'] = $vehicle_info['oil_type'] ? $vehicle_setting['oil_type'][$vehicle_info['oil_type']] : '';

        // 驾照图片(两张) &&
        $vehicle_info['driving_licence_img_item'] = [];
        if (!empty($vehicle_info['driving_licence_img'])) {
            $vehicle_info['driving_licence_img_item'] = explode("\n", $vehicle_info['driving_licence_img']);
        }

        // 驾照类型 &&
        $vehicle_info['driver_license_type_label'] = '';
        if (!empty($vehicle_info['driver_license_type'])) {
            foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $license_k => $license_v) {
                $vehicle_info['driver_license_type_label'] = $license_v;
            }
        }

        //驾照车辆限制
        $vehicle_info['driving_license_vehicle_restrictions_str'] = empty($vehicle_info['driving_license_vehicle_restrictions']) ? '': $this->getDrivingLicenseVehicleRestrictionsFromIds( $vehicle_info['driving_license_vehicle_restrictions']) ;
        $vehicle_info['driving_license_vehicle_restrictions'] = empty($vehicle_info['driving_license_vehicle_restrictions'])?[]:explode(',', $vehicle_info['driving_license_vehicle_restrictions']) ;

        // 审核状态
        $vehicle_info['approval_status'] = $vehicle_info['approval_status'] ?? VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;

        // 员工入职日期
        $vehicle_info['staff_hire_date'] = $this->getStaffHireDate($paramIn['id']);
        return $vehicle_info;
    }


    /**
     * 根据驾照车辆限制id 获取名称
     * @param string $ids
     * @return string
     */
    private function getDrivingLicenseVehicleRestrictionsFromIds(string $ids)
    {
        $names = [];
        $ids_arr  = explode(',',$ids);
        $drivingLicenseVehicleRestrictions = VehicleInfoEnums::DRIVING_LICENSE_VEHICLE_RESTRICTIONS;
        foreach ($drivingLicenseVehicleRestrictions as $item)
        {
            if(in_array($item['value'],$ids_arr)){
                $names[] = $item['label'];
            }
        }
        return implode(',',$names);
    }

    /**
     * 提取不同职位需入库的字段
     * @param array $vehicle_data
     * @param array $user_info
     * @return array $data
     */
    protected function filterVehicleData(array $vehicle_data, array $user_info)
    {
        // 公共字段
        $data = [
            'uid'                                  => $user_info['id'],
            'vehicle_source'                       => $vehicle_data['vehicle_source'],
            'plate_number'                         => $vehicle_data['plate_number'],
            'license_location'                     => $vehicle_data['license_location'],
            'registration_certificate_img'         => $vehicle_data['registration_certificate_img'],
            'vehicle_img'                          => implode("\n", $vehicle_data['vehicle_img']),
            'insurance_policy_number'              => $vehicle_data['insurance_policy_number'],
            'insurance_start_date'                 => $vehicle_data['insurance_start_date'],
            'insurance_end_date'                   => $vehicle_data['insurance_end_date'],
            'vehicle_tax_expiration_date'          => $vehicle_data['vehicle_tax_expiration_date'],
            'vehicle_tax_certificate_img'          => $vehicle_data['vehicle_tax_certificate_img'],
            'driver_license_type'                  => $vehicle_data['driver_license_type'],
            'driver_license_type_other_text'       => $vehicle_data['driver_license_type'] != 100 ? '' : $vehicle_data['driver_license_type_other_text'] ?? '',
            'driver_license_number'                => $vehicle_data['driver_license_number'],
            'driver_license_start_date'            => $vehicle_data['driver_license_start_date'],
            'driver_license_end_date'              => $vehicle_data['driver_license_end_date'],
            'driving_licence_img'                  => implode("\n", $vehicle_data['driving_licence_img_item']),
            'vehicle_type'                         => VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE,
            'engine_number'                        => $vehicle_data['engine_number'],

            // 重置审核信息
            'approval_status'                      => VehicleInfoEnums::APPROVAL_PENDING_CODE,
            'approval_staff_id'                    => '',
            'approval_time'                        => null,
            'approval_remark'                      => '',
            'editor_id'                            => $user_info['id'],
            //new add
            'vehicle_registration_date'            => $vehicle_data['vehicle_registration_date'],
            'vehicle_registration_number'          => $vehicle_data['vehicle_registration_number'],
            'driving_license_vehicle_restrictions' => implode(',',
                $vehicle_data['driving_license_vehicle_restrictions']),
        ];

        // 用车开始日期
        if ($vehicle_data['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
            $data['vehicle_start_date'] = $vehicle_data['vehicle_start_date'];
        }

        // van 特有字段
        if (in_array($user_info['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
            $this->isVan = true;

            $data['buy_date']              = $vehicle_data['buy_date'];
            $data['vehicle_brand']         = $vehicle_data['vehicle_brand'];
            $data['vehicle_brand_text']    = $vehicle_data['vehicle_brand'] != 100 ? '' : $vehicle_data['vehicle_brand_text'] ?? '';
            $data['vehicle_model']         = $vehicle_data['vehicle_model'];
            $data['vehicle_model_text']    = $vehicle_data['vehicle_model'] != 100 ? '' : $vehicle_data['vehicle_model_text'] ?? '';
            $data['vehicle_size']          = $vehicle_data['vehicle_size'];
            $data['oil_type']              = $vehicle_data['oil_type'];
            $data['vehicle_type_category'] = $vehicle_data['vehicle_type_category'];
            $data['oil_number']            = $vehicle_data['oil_number'] ?? '';
            $data['oil_company']           = $vehicle_data['oil_company'] ?? 0;
            $data['oil_img']               = $vehicle_data['oil_img'] ?? '';
            $data['oil_effective_date']    = $vehicle_data['oil_effective_date'] ?? null;
            if (empty($vehicle_data['oil_effective_date'])) {
                $data['oil_effective_date'] = null;
            }
            $data['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$user_info['job_title']];

        } elseif ($user_info['job_title'] == VehicleInfoEnums::JOB_TRICYCLE_TITLE_ID) {
            $data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_TRICYCLE_CODE;
        } elseif ($user_info['job_title'] == VehicleInfoEnums::JOB_BIKE_TITLE_ID && in_array($user_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            $data['oil_type']              = $vehicle_data['oil_type'];
            $data['oil_number']            = $vehicle_data['oil_number'] ?? '';
            $data['oil_company']           = $vehicle_data['oil_company'] ?? 0;
            $data['oil_img']               = $vehicle_data['oil_img'] ?? '';
            $data['oil_effective_date']    = $vehicle_data['oil_effective_date'] ?? null;
            if (empty($vehicle_data['oil_effective_date'])) {
                $data['oil_effective_date'] = null;
            }
        }

        return $data;
    }

    /**
     * 获取车辆的长宽高
     * @param $vehicle_brand
     * @param $vehicle_model
     * @return string[]
     */
    private function getVehicleLWH($vehicle_brand,$vehicle_model)
    {
        $lwh = [
            'car_long'=>'0',
            'car_width'=>'0',
            'car_high'=>'0',
        ];
        $info  = VehicleInfoEnums::CONFIG_VEHICLE_INFO['vehicle_brand'];
        foreach ($info as $item) {
            if($item['value'] == $vehicle_brand){
                foreach ($item['data'] as $vm) {
                    if($vm['value'] == $vehicle_model){
                        $lwh['car_long']  = $vm['car_long'];
                        $lwh['car_width']  = $vm['car_width'];
                        $lwh['car_high']  = $vm['car_high'];
                        return $lwh;
                    }
                }
            }
        }
        return  $lwh;
    }

    /**
     * 验证发动机号是否与在职人重复
     * @param string $engine_number
     * @param int $user_id
     * @return mixed
     */
    protected function checkEngineNoIsExist(string $engine_number, int $user_id)
    {
        if (empty($engine_number) || empty($user_id)) {
            return true;
        }

        $engine_number_staff_item = VehicleInfoModel::find([
            'conditions' => 'engine_number = :engine_number:',
            'bind' => ['engine_number' => $engine_number],
            'columns' => ['uid']
        ])->toArray();
        $engine_number_staff_item = $engine_number_staff_item ? array_column($engine_number_staff_item,  'uid', 'uid') : [];
        if (!empty($engine_number_staff_item[$user_id])) {
            unset($engine_number_staff_item[$user_id]);
        }

        $staff_count = 0;
        if (!empty($engine_number_staff_item)) {
            // 是否在职
            $staff_count = StaffInfoModel::count([
                'conditions' => 'id IN ({ids:array}) AND state = :state:',
                'bind' => ['ids' => array_values($engine_number_staff_item), 'state' => 1]
            ]);
        }

        return $staff_count ? true : false;
    }

    /**
     * 获取员工入职日期
     * @param $staff_info_id
     * @return mixed
     */
    protected function getStaffHireDate($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return '';
        }

        // 员工入职日期
        $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($staff_info_id, 'hire_date');
        return !empty($hr_staff_info->hire_date) ? substr($hr_staff_info->hire_date, 0, 10) : '';
    }
}
