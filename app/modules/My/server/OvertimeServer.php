<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrOvertimeModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\SalaryHistoryModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditApplyRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\ConditionsRulesServer;
use FlashExpress\bi\App\Server\OvertimeServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;

class OvertimeServer extends GlobalBaseServer
{

    public $isUnpaid;

    //超时 时长 类型 1，4，6 用
    public $overDuration = [
        ['time_hour' => 1, 'time_text' => '1h'],
        ['time_hour' => 2, 'time_text' => '2h'],
        ['time_hour' => 3, 'time_text' => '3h'],
        ['time_hour' => 4, 'time_text' => '4h'],
    ];
    //休息日 时长 类型 2，3用
    public $holidayDuration = [
        ['time_hour' => 4, 'time_text' => '4h'],
        ['time_hour' => 8, 'time_text' => '8h'],
    ];

    //节假日时长 类型5 用
    public $phDuration = [
        ['time_hour' => 8, 'time_text' => '8h'],
    ];

    //超时加班 只能是 上班之后 申请晚上下班之后的 对应时长 需要动态获取
    public $overOt = [
        HrOvertimeModel::OVERTIME_1 => [],
        HrOvertimeModel::OVERTIME_4 => [],
        HrOvertimeModel::OVERTIME_6 => [],
    ];

    //全天加班 上班时间开始就可以申请的 节假日 或者 休息日加班 对应时长 是固定的
    public $holidayOt = [
        HrOvertimeModel::OVERTIME_2 => [],
        HrOvertimeModel::OVERTIME_3 => [],
        HrOvertimeModel::OVERTIME_5 => [],
        HrOvertimeModel::OVERTIME_7 => [],
    ];


    //特殊定制部门职位需要撤销对应日期的ot 然后重新申请
    public $cancelId;//需要撤销的ot id
    public $specialNwJob;//需要走特殊逻辑验证的 nw 部门的 dco bs abs
    public $sub_duration = 0;//撤销前一天加班 重新申请操作需要减去撤销的duration


    public function initData($param){
        // 校验时间段 可选日期为近5天(前天、昨天和今天 明天后天) 如果是bi工具 不做时间校验
        //新需求 验证逻辑 修改 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
        //马来新班次 需求 https://flashexpress.feishu.cn/docx/AoQtdqg8Sof2TLxlTivcCuVDnTf
        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $this->shiftInfo = $attendanceServer->getStaffShiftInfoByDate($param['staff_id'],$param['date_at']);

        $staff_model = new StaffRepository();
        $this->staffInfo = $staff_model->getStaffPosition($param['staff_id']);

    }
    //是否是特殊部门职位
    public function setSpecialNwJob(){
        $networkManagementId = (new SettingEnvServer())->getSetVal('dept_network_management_id');
        if($this->staffInfo['sys_department_id'] != $networkManagementId){
            return false;
        }

        if(!in_array($this->staffInfo['job_title'], [
            enums::$job_title['dc_officer'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['assistant_branch_supervisor'],
        ])){
            return false;
        }

        return true;
    }


    /**
     * 获取加班类型
     * @Access  public
     * @Param   array $paramIn
     * @Param   array $userinfo
     * @Return  array
     */
    public function getTypeOvertime($paramIn = [], $userinfo = [])
    {
        $data                           = $this->getAllOtType();
        if (!empty($paramIn['is_svc'])) {
            $returnData['data']['dataList'] = $data;
            return $this->checkReturn($returnData);
        }
        $staff_re = new StaffRepository($this->lang);
        if(empty($this->staffInfo)){
            $this->staffInfo = $staff_re->getStaffPosition($paramIn['staff_id']);
        }

        //获取有权限的类型和时长
        $data = $this->getTypeDuration($data);

        if (empty($userinfo)) {//bi工具 或者其他 不需要限制的 直接调用
            $returnData['data']['dataList'] = $data;
            return $this->checkReturn($returnData);
        }

        //佳雪需求：基层员工用模板1 非基层员工用模板2
        //https://flashexpress.feishu.cn/docx/doxcn97CVUImrddOaiXywWGO1Ve
        //获取员工是否为底层员工 这个还用旧的配置
        $isUnderlyingStaff = $this->getStaffInfoType($paramIn['staff_id']);
        if ($userinfo && $isUnderlyingStaff) { //给钱
            $returnData['data']['template_type'] = 1;
            $returnData['data']['is_underlying_staff'] = 1;
        } else { //给调休
            //非基层员工，如果工资大于4000给调休假，小于4000给工资
            if (!$this->checkIncome($paramIn['staff_id'])) { //给假
                $returnData['data']['template_type'] = 2;
                $returnData['data']['is_underlying_staff'] = 2;
            } else { //给钱
                $returnData['data']['template_type'] = 1;
                $returnData['data']['is_underlying_staff'] = 1;
            }
        }
        $returnData['data']['dataList'] = $data;
        return $this->checkReturn($returnData);
    }

    //这里不做 权限判断 详情页 hcm工具 回显都要用 有权限都写上面去

    /**
     * 新版 需要 选择 日期 和 类型之后 判断 加班时间和加班时长
     * code 加班类型
     * @return array
     */
    public function getAllOtType($locale = ''){
        return [
            [
                //工作日加班1.5倍日薪
                'code' => HrOvertimeModel::OVERTIME_1,
                'msg'  => $this->getTranslation($locale)->_('5141'),
                'sub_msg' => $this->getTranslation($locale)->_('1.5_times_salary'),
                'duration' => [],
            ],[
                //OFF Day加班1.5倍日薪
                'code' => HrOvertimeModel::OVERTIME_2,
                'msg'  => $this->getTranslation($locale)->_('5126'),
                'sub_msg' => str_replace('{n}', 1.5, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //Rest Day加班1倍日薪
                'code' => HrOvertimeModel::OVERTIME_3,
                'msg'  => $this->getTranslation($locale)->_('5127'),
                'sub_msg' => str_replace('{n}', 1, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //Rest Day超时加班2倍日薪
                'code' => HrOvertimeModel::OVERTIME_4,
                'msg'  => $this->getTranslation($locale)->_('5128'),
                'sub_msg' => str_replace('{n}', 2, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //节假日加班2倍日薪
                'code' => HrOvertimeModel::OVERTIME_5,
                'msg'  => $this->getTranslation($locale)->_('5140'),
                'sub_msg' => str_replace('{n}', 2, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //节假日超时加班3倍日薪
                'code' => HrOvertimeModel::OVERTIME_6,
                'msg'  => $this->getTranslation($locale)->_('5125'),
                'sub_msg' => str_replace('{n}', 3, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],
        ];
    }


    /**
     * 新版 需要 选择 日期 和 类型之后 判断 加班时间和加班时长
     * code 加班类型
     * @return array
     */
    public function getApprovalOtType($locale = ''){
        return [
            [
                //工作日加班1.5倍日薪
                'code' => HrOvertimeModel::OVERTIME_1,
                'msg'  => $this->getTranslation($locale)->_('5141'),
                'sub_msg' => $this->getTranslation($locale)->_('1.5_times_salary'),
                'duration' => [],
            ],[
                //OFF Day加班1.5倍日薪
                'code' => HrOvertimeModel::OVERTIME_2,
                'msg'  => $this->getTranslation($locale)->_('5126'),
                'sub_msg' => str_replace('{n}', 1.5, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //Rest Day加班1倍日薪
                'code' => HrOvertimeModel::OVERTIME_3,
                'msg'  => $this->getTranslation($locale)->_('5127'),
                'sub_msg' => str_replace('{n}', 1, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //Rest Day超时加班2倍日薪
                'code' => HrOvertimeModel::OVERTIME_4,
                'msg'  => $this->getTranslation($locale)->_('5128'),
                'sub_msg' => str_replace('{n}', 2, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //节假日加班2倍日薪
                'code' => HrOvertimeModel::OVERTIME_5,
                'msg'  => $this->getTranslation($locale)->_('5140'),
                'sub_msg' => str_replace('{n}', 2, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //节假日超时加班3倍日薪
                'code' => HrOvertimeModel::OVERTIME_6,
                'msg'  => $this->getTranslation($locale)->_('5125'),
                'sub_msg' => str_replace('{n}', 3, $this->getTranslation($locale)->_('n_times_salary')),
                'duration' => [],
            ],[
                //个人代理类型
                'code' => HrOvertimeModel::OVERTIME_7,
                'msg'  => $this->getTranslation($locale)->_('apply_bonus_type'),
                'sub_msg' => '',
                'duration' => [],
            ],
        ];
    }


    //高层 只有这几个选项 以后搬权限配置表 audit_permission
    //用新配置 这个废弃
    public function upLevelType(){

        return [
            [
                //OFF Day加班1.5倍日薪
                'code' => HrOvertimeModel::OVERTIME_2,
                'msg'  => $this->getTranslation()->_('5126'),
                'sub_msg' => '',
                'duration' => []
            ],[
                //Rest Day加班1倍日薪
                'code' => HrOvertimeModel::OVERTIME_3,
                'msg'  => $this->getTranslation()->_('5127'),
                'sub_msg' => '',
                'duration' => []
            ],[
                //节假日加班2倍日薪
                'code' => HrOvertimeModel::OVERTIME_5,
                'msg'  => $this->getTranslation()->_('5140'),
                'sub_msg' => '',
                'duration' => []
            ]
        ];
    }

    //个人代理类型 申请的选项
    public function bonusType(){
        return [
            [
                //个人代理类型
                'code' => HrOvertimeModel::OVERTIME_7,
                'msg'  => $this->getTranslation()->_('apply_bonus_type'),
                'sub_msg' => '',
                'duration' => [],
            ]
        ];
    }

    /**
     * 新建加班
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function addOvertimeV3($paramIn = [])
    {
        $this->param   = $paramIn;
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $type          = $this->processingDefault($paramIn, 'type',2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $reason        = $this->processingDefault($paramIn, 'reason');
        $duration      = floatval($paramIn['duration']);
        $date          = $this->processingDefault($paramIn, 'date_at');
        $reason        = addcslashes(stripslashes($reason),"'");

        $time_type = intval($paramIn['time_type']);

        if(empty($date) || empty($start_time) || empty($type)){
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        //初始化数据 员工信息 班次信息
        $this->initData($paramIn);
        $this->specialNwJob = $this->setSpecialNwJob();

        $shift_info = $this->shiftInfo;
        $staffInfo = $this->staffInfo;

        if(empty($shift_info)) { //没有班次信息 不让申请
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }

        //拼接 end time
        $start_time = date('Y-m-d H:i:s',strtotime($start_time));
        $end_time = $paramIn['end_time'] = date('Y-m-d H:i:s',strtotime($start_time) + $duration * 3600);


        //如果是 前后半天 类型的 要用对应的 班次结束时间
        $holidayKeys = array_keys($this->holidayOt);
        if(in_array($type,$holidayKeys)){
            if(empty($time_type)){
                return $this->checkReturn(-3, $this->getTranslation()->_('time_type_error'));
            }
            $end_time = $paramIn['end_time'] = $this->getEndTime($shift_info,$time_type);
            //工具过来的 duration 需要重新计算
            if(empty($duration) && !empty($paramIn['is_bi'])){
                $durationData = $this->shiftTimeArray($shift_info);
                $durationData = array_column($durationData['time_list'],'duration', 'time_type');
                $paramIn['duration'] = $duration = $durationData[$time_type] ?? 0;
            }
        }


        $this->checkTypeDuration();

        if(empty($paramIn['is_bi'])){
            $this->checkOTTimePeriod($type, $shift_info, $date);
        }
        $ext_server = new OvertimeExtendServer($this->lang,$this->timezone);

        //获取参考数据
        [$references, $extend] = $ext_server->getReference($staffId, $type, $date);
        //累计时长 要包含本次
        if (isset($references['duration'])) {
            $references['duration'] += $duration;
        }
        //里面的数据 下面会有改动
        $this->reference = $references;


        //!!!!!!  加班 校验逻辑
        $paramIn['shift_info'] = $shift_info;
        $paramIn['references'] = $references;
        $check_data            = $this->checkOvertime($paramIn);

        if ($check_data['code'] != 1) {
            return $check_data;
        }


        //马来 nw 定制 自动撤销对应日期的加班 以本次为准
        if(!empty($this->cancelId) && !empty($paramIn['is_cancel_commit'])){
            $this->cancelV3(['staff_id' => $staffId, 'audit_id' => $this->cancelId]);
            if($staffInfo['job_title'] == enums::$job_title['dc_officer']){
                $this->reference['duration'] -= $this->sub_duration;
            }

        }

        //获取基层员工职位
        $settingEnv = new SettingEnvServer();
        $jobTitleInfo = $settingEnv->getSetVal('underlying_staff_job_title');
        $jobTitleList = explode(',', $jobTitleInfo);
        $sub_type = HrOvertimeModel::SUB_TYPE_GET_WAGE;
        //非基层员工 & 工资大于4000给调休假
        if (!in_array($staffInfo['job_title'], $jobTitleList) && !$this->checkIncome($staffId)) { //申请人是底层员工
            //添加到给调休假的表
            $sub_type = HrOvertimeModel::SUB_TYPE_GET_LIEU; //0=薪酬 1=给调休
        }

        //新增了 bi工具 补记录 状态直接为审核通过 不发push 审核人为 操作工具hr
        $higher = '';//bi工具 直接审核通过 需要记录操作人 带申请的 不需要记录上级
        $state  = enums::APPROVAL_STATUS_PENDING;
        if (!empty($paramIn['is_bi'])) {
            $higher = $paramIn['operator'];
            $state  = enums::APPROVAL_STATUS_APPROVAL;
        }
        $serialNo    = $this->getID();
        $insertParam = [
            'staff_id'        => $staffId,
            'type'            => $type,
            'start_time'      => $start_time,
            'end_time'        => $end_time,
            'reason'          => $reason,
            'reject_reason'   => '',
            'state'           => $state,
            'duration'        => $check_data['data']['duration'],
            'higher_staff_id' => $higher,
            'is_anticipate'   => $check_data['data']['is_anticipate'],
            'date_at'         => $date,
            'time_type'       => intval($paramIn['time_type']),//新增 回传字段 1 前半天 2 后半天 3 全天
            'references'      => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
            'serial_no'       => (!empty($serialNo) ? 'OT'.$serialNo : null),
            'wf_role'         => 'ot_new',
            'sub_type'        => $sub_type,
        ];

        $db = $this->getDI()->get('db');
        $db->begin();
        //员工申请 走审批流
        try {
            $overtimeId = $this->overtime->addOvertime($insertParam);
            if(empty($overtimeId)){
                $db->rollback();
                throw new ValidationException('add ot failed');
            }

            //工具
            if (!empty($paramIn['is_bi'])) {
                if ($sub_type == HrOvertimeModel::SUB_TYPE_GET_LIEU) {
                    $leaveParam['job_title']   = $references['job_title'] ?? 0;
                    $leaveParam['overtime_id'] = $overtimeId;
                    $leaveParam['staff_id']    = $staffId;
                    $leaveParam['duration']    = $check_data['data']['duration'];
                    $leaveParam['date_at']     = $date;
                    $otExtServer               = new OvertimeExtendServer($this->lang, $this->timezone);
                    //添加到给调休假的表
                    $otExtServer->addLieu($leaveParam);
                    $this->logger->write_log(sprintf("%s 已经添加调休假(ot id %s)", $staffId, $overtimeId), 'info');
                }
                $db->commit();
                return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
            }
            //创建 加班 或者 奖金申请
            $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME;
            if($this->isUnpaid){
                $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID;
            }
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($overtimeId, $approvalType, $staffId,null,$extend);
            if (!$requestId) {
                $db->rollback();
                $this->logger->write_log("addOvertimeV3 {$staffId} 创建审批流失败");
                throw new ValidationException('add ot failed');
            }
            $db->commit();
            return $this->checkReturn(['data'=>['overtime_id'=>$overtimeId]]);
        } catch (\Exception $e){
            $db->rollback();
            $this->logger->write_log("addOvertimeV3 {$staffId} 创建审批流异常 " . $e->getMessage());
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     * 修改状态
     * @Access  public
     * @param array $paramIn
     * @return array
     * @throws \Exception
     */
    public function updateOvertimeV3($paramIn = [])
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id',2);
        $overtimeId    = $this->processingDefault($paramIn, 'audit_id',2);
        $reject_reason = $this->processingDefault($paramIn, 'reject_reason');
        $state         = $this->processingDefault($paramIn, 'status',2);

        //获取审批详情
        $overtimeList = $this->overtime->infoOvertime(['overtime_id'=>$overtimeId]);
        if (empty($overtimeList)){
            return $this->checkReturn(-3,$this->getTranslation()->_('4102'));
        }
        //oa 审批处理中 不能操作
        if($overtimeList['in_approval'] == HrOvertimeModel::IN_APPROVAL && empty($paramIn['is_mq'])){
            return $this->checkReturn(-3,$this->getTranslation()->_('ot_in_approval_notice'));
        }
        $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME;
        if($overtimeList['type'] == HrOvertimeModel::OVERTIME_7){
            $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID;
        }

        $server = new ApprovalServer($this->lang, $this->timezone);
        if ($state == enums::$audit_status['approved']) {
            //同意
            $flag = $server->approval($overtimeId, $approvalType, $staffId);
        } else {
            $flag = $server->reject($overtimeId, $approvalType, $reject_reason, $staffId);
        }
        return $this->checkReturn(['data'=>['audit_id'=>$overtimeId]]);
    }

    /**
     * 校验申请加班日期、班次
     * @param int $overtime_type 加班类型
     * @param array $shift_info 班次信息
     * @param string $date 加班日期
     * @return void
     * @throws \Exception
     */
    public function checkOTTimePeriod($overtime_type, $shift_info, $date)
    {
        $date_tmp = strtotime($date);
        $beforeTime = $behindTime = strtotime(date('Y-m-d'),time());

        //OFF Day加班、Rest Day加班、法定节假日加班班
        //ot 可以多两天时间区间限制
        if(in_array($overtime_type, array_keys($this->holidayOt))) {
            $behindTime = strtotime(date("Y-m-d",strtotime("+2 day")));
        }

        $start = $shift_info['first_start'];
        $end = $shift_info['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE ? $shift_info['first_end'] : $shift_info['second_end'];
        //如果是班次跨天的员工，比如23：00-8：00班次，跨天的班次，可以申请前一天的加班。
        $shift_start = strtotime("{$date} {$start}");
        $shift_end = strtotime("{$date} {$end}");
        //超时加班 可以放宽开始时间的限制
        if(in_array($overtime_type, array_keys($this->overOt))) {
            if($shift_start > $shift_end) {
                $beforeTime = strtotime(date("Y-m-d",strtotime("-1 day")));
            }

            //新增逻辑 如果是 nw 的 特殊职位 再多给一天 https://flashexpress.feishu.cn/docx/TutQdpNVeouc9kxI1cfcJpR0n4e
            if($this->specialNwJob){
                $beforeTime = strtotime(date("Y-m-d",$beforeTime - (24 * 3600)));
            }
        }


        if($date_tmp < $beforeTime || $date_tmp > $behindTime) {
            //获取员工是否为底层员工
            $typeList = $this->getAllOtType();
            $typeArr = array_column($typeList, 'msg', 'code');

            //在许可时间外申请，提示错误
            if(in_array($overtime_type, [2, 3, 5, 7])) {
                $notice_str = str_replace('{type}',$typeArr[$overtime_type],$this->getTranslation()->_('err_msg_ot_over_2_days'));
            } else if($overtime_type == 1) {
                $notice_str = $this->getTranslation()->_('err_msg_ot_only_cur_day');
            } else {
                $notice_str =  str_replace('{type}',$typeArr[$overtime_type],$this->getTranslation()->_('err_msg_ot_only_rest_day'));
            }
            throw new \Exception($this->getTranslation()->_($notice_str), enums::$ERROR_CODE['1000']);
        }
    }

    /**
     * 校验除 时间范围内的 其他逻辑 添加ot 和bi 的 修改ot
     * 加班类型
     * 1-工作日加班1.25倍日薪,
     * 2-休息日加班1.3倍日薪,
     * 3休息日超时加班1.69倍日薪,
     * 4-RH加班1倍日薪,
     * 5-RH超时加班2.6倍日薪,
     * @param $paramIn
     * @throws \Exception
     */
    public function checkOvertime($paramIn)
    {
        $staffId       = $this->processingDefault($paramIn, 'staff_id', 2);
        $type          = $this->processingDefault($paramIn, 'type', 2);
        $start_time    = $this->processingDefault($paramIn, 'start_time');
        $end_time      = $this->processingDefault($paramIn, 'end_time');
        $duration      = floatval($paramIn['duration']);
        $date          = $this->processingDefault($paramIn, 'date_at');
        $time_type     = empty($paramIn['time_type']) ? 0 : intval($paramIn['time_type']);

        $startTime = strtotime($start_time);
        $endTime = strtotime($end_time);

        $current_date = date('Y-m-d',time());//今天日期
        $start_date = date('Y-m-d',$startTime);//申请开始时间日期

        //新需求 OT申请开始时间不得晚于申请日期次日中午12点。
        $_date = date('Y-m-d 12:00:00', strtotime ("+1 day", strtotime($date)));
        $_diffdate = strtotime($_date);

        if ($startTime > $_diffdate){
            return $this->checkReturn(-3, $this->getTranslation()->_('ot_time_limit'));
        }

        $u_info = $this->staffInfo;
        if (empty($u_info)) {
            return $this->checkReturn(-3, 'can not find staff');
        }

        //支援期间不能申请工作日加班
        $attendanceRe = new AttendanceRepository($this->lang, $this->timezone);
        if (empty($paramIn['is_bi']) && $type == HrOvertimeModel::OVERTIME_1 && !empty($attendanceRe->getSupportOsStaffInfo($staffId, $date))) {//支援期间不能申请工作日加班
            return $this->checkReturn(-3, $this->getTranslation()->_('support_restrict_application_ot'));
        }

        //个人代理 无底薪
        if(in_array($u_info['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $this->isUnpaid = true;
        }
        //新需求 网点总部逻辑 按照 该员工工作日期判断 5天为总部 6天为网点
        $organization_type = $u_info['organization_type'];
        $work_days = empty($u_info['week_working_day']) ? 5 : $u_info['week_working_day'];

        //开始时间的日期不能早于申请日期 只试用于凌晨加班 的跨天 且不能超过两天
        $sub = (strtotime($start_date) - strtotime($date)) / 3600;
        if ($start_date < $date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('wrong_date'));
        }
        if ($sub >= 48) {
            return $this->checkReturn(-3, $this->getTranslation()->_('wrong_date'));
        }

        //如选择“周末和假期加班”，系统验证该申请日期是否为公休日和周末，如申请日期非周末和假期
        $holidays = (new AuditServer($this->lang, $this->timezone))->get_holidays($u_info);

        $is_rest = false;
        $is_off  = false;
        $isUnStaff = false;

        //是否为底层员工
        $settingEnv = new SettingEnvServer();
        $underlyingStaffJobTitle = $settingEnv->getSetVal('underlying_staff_job_title');
        $underlyingStaffJobTitleArr = explode(',', $underlyingStaffJobTitle);
        if (in_array($u_info['job_title'], $underlyingStaffJobTitleArr)) { //是否为底层员工
            $isUnStaff = true;
        }
        //个人代理员工 按基层算
        if($this->isUnpaid){
            $isUnStaff = true;
        }

        $overtimeRepo = new OvertimeRepository($this->timeZone);
        $is_workdays = $overtimeRepo->get_workdays($staffId , $date);//当天是否是轮休日
        if ($work_days == 6){
            if(!empty($is_workdays) && $is_workdays['type'] == HrStaffWorkDayModel::TYPE_REST){
                $is_rest = true;
            }
            //校验休息日
            //6天工作的人没有Off day
            $this->checkRestDay($type, $is_rest, false, $holidays, $date, $isUnStaff);
        }
        //总部员工 新增 周末判断
        if ($work_days == 5){
            if(!empty($is_workdays) && $is_workdays['type'] == HrStaffWorkDayModel::TYPE_REST) {
                $is_rest = true;
            } else if(!empty($is_workdays) && $is_workdays['type'] == HrStaffWorkDayModel::TYPE_OFF) {
                $is_off = true;
            }
            //校验休息日
            $this->checkRestDay($type, $is_rest, $is_off, $holidays, $date, $isUnStaff);
        }

        //判断是预申请还是 补申请
        $is_anticipate = 0;
        if($start_date >= $current_date){
            $is_anticipate = 1;//是预申请
        }

        //看是否是补申请 并且 获取该日期打卡时间 校验加班时间 是否在打卡时间之内
        $att_model = new AttendanceRepository($this->lang, $this->timeZone);
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        //转换零时区
        $start = date('Y-m-d H:i:s',$startTime - $add_hour * 3600);
        $end = date('Y-m-d H:i:s',$endTime - $add_hour * 3600 - 300);
        $att_info = $att_model->getAttendanceInfo($staffId,$start,$end);

        //双班次 特殊情况 判断 两个班的打卡时间 都符合条件才行
        if($paramIn['shift_info']['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE && $paramIn['time_type'] == HrOvertimeModel::TIME_TYPE_WHOLE){
            $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
            $formatShiftInfo = $attendanceServer->formatShiftInfo($paramIn['shift_info']);
            $att_info = $att_model->getNewAttendanceOtInfo($staffId,$paramIn['shift_info']['shift_date']);
            $att_info = $this->checkOtBetween($formatShiftInfo,$att_info);
        }
        //打卡间隔时长 新需求 增加考勤容错率 5分钟 由小时 改为分钟
        $attendance_last = 0;
        $allowed_min = 5;
        if (!empty($att_info)) {
            $attendance_last = floor((strtotime($att_info['end_at']) - strtotime($att_info['started_at'])) / 60);
        }//上下班打卡间隔时长

        //如果是当天申请 且有打卡记录 视为 补申请
        if ($is_anticipate == 1 && !empty($att_info)) {
            $is_anticipate = 0;
        }

        //所选日期没有上班打卡记录和下班打卡记录 并且是补申请；
        if(empty($att_info) && $is_anticipate == 0){
            return $this->checkReturn(-3, $this->getTranslation()->_('1101'));
        }

        // 校验实际 加班 时长
        $act_last = floor(($endTime - $startTime) / 3600);
        if ($act_last >= 24) {
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));
        }

        if ($act_last <= 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('5101'));
        }

        $ext_server = new OvertimeExtendServer($this->lang,$this->timeZone);
        $overKeys = array_keys($this->overOt);//超时加班
        if(in_array($type, $overKeys)){
            //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnznGnDKzb4akgPuhYQq5oHc
            if(empty($paramIn['shift_info']['is_ot'])){//配置项 不能申请ot 0 不能申请 1 可以申请
                return $this->checkReturn(-3, $this->getTranslation()->_('ot_forbidden'));
            }
            //超时加班 time type 要传0
            if($time_type != HrOvertimeModel::TIME_TYPE_DEFAULT){
                return $this->checkReturn(-3, $this->getTranslation()->_('wrong param time type'));
            }

            $new_check = $ext_server->newExtendCheck($paramIn, $u_info);
            if ($new_check['code'] != 1) {
                return $new_check;
            }

            if($organization_type == 2){
                if($duration > 4){
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_department_allowed_4'));
                }
            }
            //补申请 且有打卡记录 或者当天已打卡申请 都算补申请
            if($is_anticipate == 0){//补申请 判断请假 验证时长 关联请假
                //节假日加班 校验打卡时长 所选时长必须小于或等于实际出勤计算的时长，否则不能提交。
                if(($act_last * 60 - $allowed_min) > $attendance_last){
                    return $this->checkReturn(-3, $this->getTranslation()->_('overtime_limit'));
                }
            }

            /**
             * 针对 超时加班 新增 时长限制需求
            1. 限制对象：Malaysia Network Management部门及其子部门下的DC Officer、Branch Supervisor、Assistant Branch Supervisor职位
            2. 限制OT类型：Normal Day OT(1.5)、Rest Day OT(2.0)、PH OT(3.0)
             * 3 hcm 工具 放开限制
             * 改版 https://flashexpress.feishu.cn/docx/TutQdpNVeouc9kxI1cfcJpR0n4e
             */
            if ($this->specialNwJob && empty($paramIn['is_edit']) && empty($paramIn['is_bi'])){
                $ext_server->shiftInfo = $this->shiftInfo;
                $return = $ext_server->checkNwEffectDuration($u_info,$date);
                $limitDuration = $return['limit_duration'];
                //超过 限制时长 提示不能申请 ot
                if($duration > $limitDuration && $limitDuration != -1){
                    return $this->checkReturn(-3, $this->getTranslation()->_('ot_fleet_effect_notice',['hour' => $limitDuration]));
                }

                //非当天查询是否存在需要撤销的加班
                if(!$return['is_current']){
                    $exist = HrOvertimeModel::findFirst([
                        'conditions' => 'staff_id = :staff_id: and date_at = :date_at: and type = :type: and state in ({states:array})',
                        'bind' => ['staff_id' => $staffId, 'date_at' => $date, 'type' => $type, 'states' => [1,2]]
                    ]);
                    if(!empty($exist)){
                        $this->cancelId = $exist->overtime_id;
                        $ext_server->ignoreOvertimeId = $exist->overtime_id;
                        //本次申请需要减去的小时数
                        $this->sub_duration = $exist->duration;
                    }
                    //如果 非 cancel commit 并且存在记录 需要二次提示
                    if(!empty($exist) && empty($paramIn['is_cancel_commit'])){
                        $json['before']['date_at'] = $date;
                        $json['before']['duration'] = $exist->duration . 'h';
                        $json['before']['last'] = date('H:i',strtotime($exist->start_time)) . '-' . date('H:i', strtotime($exist->end_time));
                        $json['after']['date_at'] = $date;
                        $json['after']['duration'] = number_format($duration,2) . 'h';
                        $json['after']['last'] = date('H:i',$startTime) . '-' . date('H:i',$endTime);
                        throw new ValidationException(json_encode($json),10099);
                    }
                }
            }
        }

        //节假日上班类型
        $holidayKeys = array_keys($this->holidayOt);
        if(in_array($type, $holidayKeys)){
            //节假日加班 要传 time type 1 前半天 2 后半天 3 全天
            if($time_type == HrOvertimeModel::TIME_TYPE_DEFAULT){
                return $this->checkReturn(-3, $this->getTranslation()->_('wrong param time type'));
            }

            //申请时，选择开始时间和结束时间，开始日期和结束日期必须为同一天或相邻；
            if ($act_last >= 24) {
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_24'));
            }

            //补申请
            if ($is_anticipate == 0 && ($act_last * 60 - $allowed_min) > $attendance_last) {
                //时长与打卡记录不符 不能申请
                return $this->checkReturn(-3, $this->getTranslation()->_('overtime_limit'));
            }
        }

        //校验 同一天是否存在 有交集的ot
        if (empty($paramIn['is_edit'])) {
            $is_contain = $ext_server->check_ot_record($staffId, $date, $startTime, $endTime, $type);
            if ($is_contain['code'] != 1) {
                return $is_contain;
            }
        }

        $res['data']['duration'] = $duration;
        $res['data']['is_anticipate'] = $is_anticipate;
        return $this->checkReturn($res);
    }

    //看 双班次的 打卡时间 是不是 符合班次配置条件
    protected function checkOtBetween($formatShift,$attendanceData){
        $firstKey = "{$formatShift['shift_date']}_".StaffWorkAttendanceModel::SHIFT_TYPE_FIRST;
        $secondKey = "{$formatShift['shift_date']}_".StaffWorkAttendanceModel::SHIFT_TYPE_SECOND;

        if(empty($attendanceData[$firstKey]) || empty($attendanceData[$secondKey])){
            $this->logger->write_log("checkOtBetween {$formatShift['staff_info_id']} 缺卡" ,'info');
            return [];
        }


        //第一班次 缺卡
        if(empty($attendanceData[$firstKey]['started_at']) || empty($attendanceData[$firstKey]['end_at'])){
            $this->logger->write_log("checkOtBetween {$formatShift['staff_info_id']} 缺卡" ,'info');
            return [];
        }

        //第二班次缺卡
        if(empty($attendanceData[$secondKey]['started_at']) || empty($attendanceData[$secondKey]['end_at'])){
            $this->logger->write_log("checkOtBetween {$formatShift['staff_info_id']} 缺卡" ,'info');
            return [];
        }


        $add_hour = $this->config->application->add_hour;
        $firstStart = date('Y-m-d H:i:s',strtotime($attendanceData[$firstKey]['started_at']) + $add_hour * 3600);
        $firstEnd = date('Y-m-d H:i:s',strtotime($attendanceData[$firstKey]['end_at']) + $add_hour * 3600);
        //格式化
        $shiftFirstStart = $formatShift['format_first_start'].':00';
        $shiftFirstEnd = $formatShift['format_first_end'].':00';

        //有迟到 或者早退
        if($firstStart > $shiftFirstStart || $firstEnd < $shiftFirstEnd){
            $this->logger->write_log("checkOtBetween {$formatShift['staff_info_id']} 迟到早退 {$firstStart} {$shiftFirstStart} {$firstEnd} {$shiftFirstEnd}" ,'info');
            return [];
        }


        $secondStart = date('Y-m-d H:i:s',strtotime($attendanceData[$secondKey]['started_at']) + $add_hour * 3600);
        $secondEnd = date('Y-m-d H:i:s',strtotime($attendanceData[$secondKey]['end_at']) + $add_hour * 3600);

        $shiftSecondStart = $formatShift['format_second_start'].':00';
        $shiftSecondEnd = $formatShift['format_second_end'].':00';

        if($secondStart > $shiftSecondStart || $secondEnd < $shiftSecondEnd){
            $this->logger->write_log("checkOtBetween {$formatShift['staff_info_id']} 迟到早退 {$secondStart} {$shiftSecondStart} {$secondEnd} {$shiftSecondEnd}" ,'info');
            return [];
        }

        //整理成一条数据 兼容之前的代码
        $return['staff_info_id'] = $formatShift['staff_info_id'];
        $return['started_at'] = $attendanceData[$firstKey]['started_at'];
        $return['end_at'] = $attendanceData[$secondKey]['end_at'];
        return $return;

    }

    /**
     * 校验休息日
     * @param int $type 加班类型
     * @param bool $is_rest 是否为休息日
     * @param $is_off
     * @param array $ph PH
     * @param string $date
     * @param $is_underlying_staff
     * @return void
     * @throws \Exception
     */
    public function checkRestDay(int $type, bool $is_rest,bool $is_off,array $ph, string $date, $is_underlying_staff = true)
    {
        //个人代理 类型 只能在ph 申请
        if($type == HrOvertimeModel::OVERTIME_7 ){
            if(in_array($date, $ph)){
                return;
            }
            throw new \Exception($this->getTranslation()->_('apply_must_on_ph'),enums::$ERROR_CODE['1000']);
        }
        //https://flashexpress.feishu.cn/docx/Z8Uad8A7zoHv5Kx27xpcEDp4ndg
        if ($is_underlying_staff) { //基层
            if (in_array($date, $ph) && $is_rest && !$is_off) { //【ph + rest Day】只能申请Rest Day OT(1.0)以及Rest Day OT(2.0) 7
                //固定休 rest ot 3,4  ph ot 5,6
                if($this->staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_2){
                    //看 是不是 固定休息同一天
                    $flag = $this->checkStaffRestWeekday($date);
                    //    即是Rest Day又是PH且Rest Day=周日(6天班固定休) or 周六日(5天班固定休) ，只能申请Rest Day OT(1.0)、Rest Day OT(2.0)，即周末遇到PH维持原判
                    if($flag === true && !in_array($type,[HrOvertimeModel::OVERTIME_3,HrOvertimeModel::OVERTIME_4])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_restday_and_ph'),enums::$ERROR_CODE['1000']);
                    }
                    //  2. 即是Rest Day又是PH且Rest Day≠周日(6天班固定休) or 周六日(5天班固定休) ，只能申请PH OT (2.0)、PH OT (3.0)，即周中遇到PH需按法定假期加班执行
                    if($flag === false && !in_array($type,[HrOvertimeModel::OVERTIME_5,HrOvertimeModel::OVERTIME_6])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_ph'),enums::$ERROR_CODE['1000']);
                    }
                }else{
                    //原逻辑 非固定休的 不变
                    if(!in_array($type, [3,4])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_restday_and_ph'),enums::$ERROR_CODE['1000']);
                    }
                }
            }
//            else if ((in_array($date, $ph) || $is_rest || $is_off) && $type == 1) { //(ph | rest Day | Off Day)不能申请工作日加班 1
//
//                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_normal_day'),enums::$ERROR_CODE['1000']);
//
//            }
            else if (!(in_array($date, $ph) || $is_rest || $is_off) && $type != 1) { //工作日不能申请非工作日加班 1

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_normal_day'),enums::$ERROR_CODE['1000']);

            } else if (in_array($date, $ph) && $type == 1) { //只能申请PH OT (2.0)、PH OT (3.0)

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_ph'),enums::$ERROR_CODE['1000']);

            }else if ( $is_rest && $type == 1 ) { //只能申请Rest Day OT(1.0)、Rest Day OT (2.0)

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_rest_day'),enums::$ERROR_CODE['1000']);

            }else if ( $is_off && $type == 1 ) { //The employee could only apply for OFF Day OT(1.5)

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_off_day'),enums::$ERROR_CODE['1000']);

            } else if (!in_array($date, $ph) && $is_rest && !$is_off && !in_array($type, [3,4])) { //【Rest Day】 4 3

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_rest_day'),enums::$ERROR_CODE['1000']);

            } else if (in_array($date, $ph) && !$is_rest && $is_off && !in_array($type, [5,6,7])) { //【ph + Off Day】只能申请PH OT (2.0)以及PH OT (3.0) 8

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_ph_and_offday'),enums::$ERROR_CODE['1000']);

            } else if (!in_array($date, $ph) && !$is_rest && $is_off && $type != 2) { //【Off Day】 2

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_off_day'),enums::$ERROR_CODE['1000']);

            } else if (in_array($date, $ph) && !$is_rest && !$is_off && !in_array($type, [5,6,7])) { //【ph】只能申请ph 5 6

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_ph'),enums::$ERROR_CODE['1000']);

            }

        } else { //非基层
            if (in_array($date, $ph) && $is_rest && !$is_off) { //【ph + rest Day】只能申请Rest Day OT 4 愿逻辑  in_array($type, [2,5]) 不能申请
                //固定休新规则
                if($this->staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_2){
                    //看 是不是 固定休息同一天
                    $flag = $this->checkStaffRestWeekday($date);
                    //    即是Rest Day又是PH且Rest Day=周日(6天班固定休) or 周六日(5天班固定休) ，只能申请Rest Day OT（1.0），即周末遇到PH维持原判
                    if($flag === true && !in_array($type,[HrOvertimeModel::OVERTIME_3])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_rest_day_ot'),enums::$ERROR_CODE['1000']);
                    }
                    //  2. 即是Rest Day又是PH且Rest Day≠周日(6天班固定休) or 周六日(5天班固定休) ，只能申请PH OT (2.0)，即周中遇到PH需按法定假期加班执行
                    if($flag === false && !in_array($type,[HrOvertimeModel::OVERTIME_5])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_phday'),enums::$ERROR_CODE['1000']);
                    }
                }else{
                    //原逻辑
                    if(in_array($type, [2,5])){
                        throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_rest_day_ot'),enums::$ERROR_CODE['1000']);
                    }
                }
            } else if (!in_array($date, $ph) && $is_rest && !$is_off && in_array($type, [2,5])) { //【Rest Day】只能申请Rest Day OT 2

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_rest_day_ot'),enums::$ERROR_CODE['1000']);

            } else if (in_array($date, $ph) && !$is_rest && $is_off && in_array($type, [2,3])) { //【ph + off】只能申请PH OT 5

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_phday'),enums::$ERROR_CODE['1000']);

            } else if (!in_array($date, $ph) && !$is_rest && $is_off && in_array($type, [3,5])) { //【Off Day】只能申请Off Day 1

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_off_day_ot'),enums::$ERROR_CODE['1000']);

            } else if (in_array($date, $ph) && !$is_rest && !$is_off && $type != 5) { //【ph】只能申请ph 3

                throw new \Exception($this->getTranslation()->_('err_msg_only_apply_for_phday'),enums::$ERROR_CODE['1000']);

            } else if (!(in_array($date, $ph) || $is_rest || $is_off) && $type != 1) {

                throw new \Exception($this->getTranslation()->_('err_msg_work_day_no_overtime'),enums::$ERROR_CODE['1000']);
            }
        }
    }

    //马来特殊逻辑 返回对应的 固有rest day  5天班固定休原有逻辑：前边1天为Off day，后边1天为Rest Day 6天班是后一天
    public function checkStaffRestWeekday($date, $staffInfo = []){
        if(empty($this->staffInfo)){
            $this->staffInfo = $staffInfo;
        }
        $storeServer = new SysStoreServer($this->lang,$this->timezone);
        $staff_code = $storeServer->getProvince($this->staffInfo);

        //找配置的 特殊州
        $special = SysServer::getSpecialOffRestDayProvince($date);
        //特殊州 固定休的 星期不一样
        if(in_array($staff_code, $special)){
            //周六
            $week = 6;
        }else{
            //周日
            $week = 0;
        }
        $flag = date('w',strtotime($date)) == $week;
        $this->logger->write_log("checkStaffRestWeekday {$date} " . json_encode($staffInfo), 'info');
        return $flag;
    }


    /**
     * 获取员工是否为底层员工
     * 底层员工定义
     *  （37）DC Officer、（807）Hub Operator、（812）Onsite Officer、（300）Warehouse staff、（13）Bike Courier、
     *  （110）Van Courier、（272）HUB Supervisor、 （16）Branch Supervisor、(745) Hub Forklift Driver
     *
     * 非底层员工定义
     *   非上面的职位
     *
     * @param string $staff_info_id
     * @return bool
     */
    public function getStaffInfoType($staff_info_id): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }

        //获取员工的职位
        $staffInfo = $this->staffInfo;
        if(empty($this->staffInfo)){
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $staff_info_id
                ],
                'columns' => 'job_title,hire_type'
            ]);
            $staffInfo = empty($staffInfo) ? [] : $staffInfo->toArray();
        }

        if (empty($staffInfo)) { //fbi不存在员工工号
            return false;
        }

        //获取底层员工职位
        $settingEnv = new SettingEnvServer();
        $underlyingStaffJobTitle = $settingEnv->getSetVal('underlying_staff_job_title');
        if (empty($underlyingStaffJobTitle)) {
            return false;
        }
        if (isset($staffInfo['job_title']) && in_array($staffInfo['job_title'], explode(',', $underlyingStaffJobTitle))) { //职位为基层员工
            return true;
        }
        return false;
    }


    /**
     * 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return mixed|void
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //[1]获取加班详情数据
        $result = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        //个人代理类型
        $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME;
        if($result['type'] == HrOvertimeModel::OVERTIME_7){
            $approvalType = AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID;
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_id']);
        if ($staff_info['data']) {
            $staff_info = $staff_info['data'];
        }

        //[2]组织详情数据
        $overtimeTypeList = $this->getAllOtType();
        $overtimeType     = array_column($overtimeTypeList, 'msg', 'code');

        $detailLists['apply_parson'] = sprintf('%s ( %s )', $staff_info['name'], $staff_info['id']);
        $detailLists['apply_department'] = sprintf('%s - %s', $staff_info['depart_name'] ?? '', $staff_info['job_name'] ?? '');
        if($result['type'] == HrOvertimeModel::OVERTIME_7){
            $detailLists['hire_type'] = $this->getTranslation()->_('hire_type_13');
        }
        $detailLists['OT_date'] = $result['date_at'];
        $detailLists['OT_type'] = ($overtimeType[$result['type']] ?? '');
        $detailLists['ot_time_between'] = $this->formatOtTime($result);
        $detailLists['duration'] = $result['duration'];
        $detailLists['OT_reason'] = $result['reason'];
        $detailLists['ot_detail_6'] = $staff_info['store_name'];


        $references = json_decode($result['references'], true) ?? '';


        //这里是 network management 部门 和 network bulky 的  DC Officer 职位
        $envModel     = new SettingEnvServer();
        $setting_code = ['dept_network_management_id'];
        $setting_val  = $envModel->listByCode($setting_code);
        if (!empty($setting_val)) {
            $setting_val = array_column($setting_val, 'set_val', 'code');
        }

        $networkManagementId = $setting_val['dept_network_management_id'] ?? '';

        if (in_array($staff_info['sys_department_id'],
                [$networkManagementId]) && in_array($staff_info['job_title'], [
                enums::$job_title['dc_officer'],
                enums::$job_title['branch_supervisor'],
                enums::$job_title['assistant_branch_supervisor'],
            ])) {
            //覆盖 网点名称  带上 大区片区
            $detailLists['ot_detail_6'] = $references['store_name'] ?? $staff_info['store_name'];

            //获取定制 详情页字段
            $ext_param['networkManagementId'] = $networkManagementId;
            $ext_server                       = new OvertimeExtendServer($this->lang, $this->timezone);
            $other_detail_data                = $ext_server->ot_dc_detail($staff_info, $result, $ext_param);
            $detailLists                      = array_merge($detailLists, $other_detail_data);
        }
//        //新增字段 剩余时长 针对部分员工
//        if (isset($references['dc_left_hours'])) {
//            $detailLists['nw_op_left_hours'] = ($references['dc_left_hours'] ?? 0)."h";
//        }

        $returnData['data']['detail'] = $this->format($detailLists);

        $data = [
            'title'       => $this->auditlist->getAudityType($approvalType),
            'id'          => $result['overtime_id'],
            'staff_id'    => $result['staff_id'],
            'type'        => $approvalType,
            'created_at'  => $result['created_at'],
            'updated_at'  => $result['updated_at'],
            'status'      => $result['state'],
            'status_text' => $this->auditlist->getAuditStatus('10'.$result['state']),
            'notice'      => $result['notice'] ?? '',
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        $returnData['data']['head'] = $data;
        return $returnData;
    }

    public function getOptionsRule($auditId)
    {
        $result = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
        if ($result['sub_type'] == HrOvertimeModel::SUB_TYPE_GET_LIEU) { //给调休假
            $optionRule = new AuditOptionRule(true, false, true, false, false, false);
        } else { //给薪酬
            $optionRule = new AuditOptionRule(true, false, true, true, false, false);
        }
        return $optionRule;
    }

    /**
     * 审批完成回调方法
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        //如果为最终审批状态，则同步更新审批状态
        if ($isFinal) {
            $overtimeInfo = HrOvertimeModel::findFirst($auditId);
            if (empty($overtimeInfo)) {
                throw new \Exception('ot info error');
            }
            $overtimeInfo->state = $state;
            $overtimeInfo->in_approval = HrOvertimeModel::NOT_IN_APPROVAL;
            if ($state == Enums::APPROVAL_STATUS_REJECTED) {
                if (isset($extend['staff_id'])) {
                    $staff = HrStaffInfoModel::findFirst([
                        'conditions' => ' staff_info_id = :staff_id: ',
                        'bind'       => ['staff_id' => $extend['staff_id']]
                    ]);
                    if ($staff) {
                        $staff = $staff->toArray();
                    }
                }
                $overtimeInfo->approver_id   = isset($extend['staff_id']) ? $extend['staff_id'] : 0;
                $overtimeInfo->approver_name = isset($staff) && $staff ? $staff['name'] : '';
                $overtimeInfo->reject_reason = isset($extend['remark']) ? $extend['remark'] : '';
            }
            $overtimeInfo->update();

            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                if ($overtimeInfo->sub_type == HrOvertimeModel::SUB_TYPE_GET_LIEU) {
                    //添加到给调休假的表
                    $leaveParam['job_title']   = $submitterJobTitleArr['job_title'] ?? '';
                    $leaveParam['overtime_id'] = $auditId;
                    $leaveParam['staff_id']    = $overtimeInfo->staff_id;
                    $leaveParam['duration']    = $overtimeInfo->duration;
                    $leaveParam['date_at']     = $overtimeInfo->date_at;
                    $otExtServer               = new OvertimeExtendServer($this->lang, $this->timezone);
                    $otExtServer->addLieu($leaveParam);
                    $this->logger->write_log(sprintf("%s 已经添加调休假(ot id %s)", $overtimeInfo->staff_id, $auditId), 'info');
                }
            }

            $staff_re   = new StaffRepository($this->lang);
            $staff_info = $staff_re->getStaffPosition($overtimeInfo->staff_id);

            //如果 是 nw 或者 bu 的 dc  需要固化数据
            //这里是 network management 部门 和 network bulky 的  DC Officer 职位
            $envModel     = new SettingEnvServer();
            $setting_code = ['dept_network_management_id', 'dept_network_bulky_id'];
            $setting_val  = $envModel->listByCode($setting_code);
            if (!empty($setting_val)) {
                $setting_val = array_column($setting_val, 'set_val', 'code');
            }

            $networkManagementId = $setting_val['dept_network_management_id'] ?? '';
            $networkBulkyId      = $setting_val['dept_network_bulky_id'] ?? '';

            if (in_array($staff_info['sys_department_id'], [
                    $networkManagementId,
                    $networkBulkyId,
                ]) && in_array($staff_info['job_title'], [
                    enums::$job_title['dc_officer'],
                    enums::$job_title['branch_supervisor'],
                    enums::$job_title['assistant_branch_supervisor'],
                ])) {
                //最终审批通过 调用 fbi 接口 获取动态数据 固化到 json里
                $json_arr = json_decode($overtimeInfo->references, true);

                //获取详情数据
                $apply_info                       = $this->overtime->infoOvertime(['overtime_id' => $auditId]);
                $ext_param['networkManagementId'] = $networkManagementId;
                //审批通过操作 特殊字段
                $ext_param['is_approval'] = true;
                $referencesData                   = (new OvertimeExtendServer($this->lang, $this->timezone))->ot_dc_detail($staff_info, $apply_info, $ext_param);
                $json_arr                         = array_merge($json_arr, $referencesData);
                if (!empty($json_arr)) {
                    $overtimeInfo->references = json_encode($json_arr, JSON_UNESCAPED_UNICODE);
                    $overtimeInfo->update();
                }
            }
            //马来个人代理 翻译不同
            $flag = in_array($staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether);
            $this->sendMessage($overtimeInfo->toArray(),$state, $flag);

        }
    }

    //工具 调用 非app
    /**
     * 编辑加班  bi工具
     * @param $paramIn
     */
    public function edit_overtime($paramIn){
        $staffId = $this->processingDefault($paramIn, 'staff_id', 2);
        //工作日校验 加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班
        $type        = $this->processingDefault($paramIn, 'type', 2);
        $start_time  = $this->processingDefault($paramIn, 'start_time');
        $date        = $this->processingDefault($paramIn, 'date_at');
        $duration    = $this->processingDefault($paramIn, 'duration');
        $overtime_id = intval($paramIn['overtime_id']);
        $time_type   = intval($paramIn['time_type']);

        if (empty($date) || empty($start_time) || empty($type) || empty($overtime_id)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        $end_time           = $paramIn['end_time'] = date('Y-m-d H:i:s', strtotime($start_time) + $duration * 3600);
        $paramIn['is_edit'] = 1;

        //初始化数据 员工信息 班次信息
        $this->initData($paramIn);
        $this->specialNwJob = $this->setSpecialNwJob();
        $shift_info = $this->shiftInfo;

        if(empty($shift_info)) { //没有班次信息 不让申请
            return $this->checkReturn(-3, $this->getTranslation()->_('no_shift_notice'));
        }

        //如果是 前后半天 类型的 要用对应的 班次结束时间
        $holidayKeys = array_keys($this->holidayOt);
        if (in_array($type, $holidayKeys)) {
            $end_time = $paramIn['end_time'] = $this->getEndTime($shift_info, $time_type);
        }
        $paramIn['shift_info'] = $shift_info;
        $res = $this->checkOvertime($paramIn);
        if ($res['code'] != 1) {
            return $res;
        }

        $model = new OvertimeRepository($this->timezone);
        $info  = $model->getInfoById($overtime_id);
        if ($info['state'] != enums::APPROVAL_STATUS_APPROVAL && empty($paramIn['due_to_holiday'])) {//非审核通过的 记录 不允许 修改 没走完正常审核逻辑
            return $this->checkReturn(-3, $this->getTranslation()->_('overtime_bi_notice'));
        }

        $update_data = [
            'type'            => $type,
            'start_time'      => $start_time,
            'end_time'        => $end_time,
            'state'           => isset($paramIn['due_to_holiday']) ? $info['state'] :  enums::APPROVAL_STATUS_APPROVAL,//bi 工具 直接审核通过  因holiday变更的除外
            'duration'        => $res['data']['duration'],
            'higher_staff_id' => $paramIn['operator'],
            'is_anticipate'   => $res['data']['is_anticipate'],
            'date_at'         => $date,
            'time_type'       => $time_type,
        ];
        $model = new OvertimeRepository($this->timezone);
        $flag  = $model->updateInfoByTable('hr_overtime', 'overtime_id', $overtime_id, $update_data);
        if ($flag) {
            if ($type != $info['type']) {
                (new AuditApplyRepository)->editOtTypeOfSummary($overtime_id,$staffId,$type);
            }
            return $this->checkReturn([]);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
    }


    /**
     * 获取加班类型，用于hcm-api系统展示
     */
    public function getOvertimeTypeList($paramIn = [])
    {
        if(empty($paramIn['staff_id'])) {
            return [];
        }

        $data = $this->getTypeOvertime($paramIn);
        return $data['data']['dataList'] ?? [];

        $data = $this->getAllOtType();
        $res = ConditionsRulesServer::getInstance()
            ->setRuleKey('OT_apply_rules')
            ->setParameters(['staff_info_id' => $paramIn['staff_id']])
            ->getConfig();
        $setting = [];
        if(!empty($res['response_type']) && $res['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
            $setting = $res['response_data'];
        }
        $data = $this->formatPermissionOvertimeType($data, $setting);

        return $data;

    }

    //根据 选择类型 获取 对应的 时长 和加班时间选项
    public function overtimeDurationByShift($param){
        if(empty($param['date_at']) || empty($param['type'])){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        //获取班次类型
        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $shiftInfo = $attendanceServer->getStaffShiftInfoByDate($param['staff_id'],$param['date_at']);

        $return['is_gray'] = enums::OVERTIME_DURATION_IS_NOT_GRAY;
        $return['time_list'] = [];//小时 分钟 或者 根据班次获取的加班固定选项
        $return['duration'] = [];// 时长

        //超时加班类型
        $overKeys = array_keys($this->overOt);

        //节假日加班类型
        $holidayKeys = array_keys($this->holidayOt);

        //超时加班 的时长
        if(in_array($param['type'],$overKeys)){
            //获取有权限的类型和时长
            $res = ConditionsRulesServer::getInstance()
                ->setRuleKey('OT_Hours')
                ->setParameters(['staff_info_id' => $param['staff_id'], 'type' => $param['type']])
                ->getConfig();
            $durations = $res['response_data'] ?? '';
            $durations =  str_replace('，', ',', $durations);
            $durations = empty($durations) ? [] : explode(',', $durations);
            //2-8小时 产品说 1-8小时
            $i = 1;
            $limitDuration = [];
            while ($i <= 8){
                $limitDuration[] = $i;
                $i += 0.5;
            }
            foreach ($durations as $hour){
                if(!in_array($hour, $limitDuration)){
                    continue;
                }
                $row['time_hour'] = floatval($hour);
                $row['time_text'] = "{$hour}h";
                $return['duration'][] = $row;
            }
            $return['time_list'] = $this->timeArray();
        }
        //休息日加班 转换成 前后半天
        if(in_array($param['type'],$holidayKeys)){
            $return['is_gray'] = enums::OVERTIME_DURATION_IS_GRAY;
            $format = $this->shiftTimeArray($shiftInfo);
            $return['time_list'] = $format['time_list'];
        }
        return $this->checkReturn(['data' => $return]);
    }
    //获取 小时和分钟
    public function timeArray(){
        //获取小时 0-23
        $hour = [];
        for ($i = 0; $i <= 23; $i++) {
            $str    = str_pad($i, 2,'0',STR_PAD_LEFT);
            $hour[] = $str;
        }
        $minute = ['00', '30'];
        //获取 分钟 00 和 30

        return ['hour' => $hour, 'minute' => $minute];
    }

    //根据班次 获取对应的 前半天后半天 加班时间行选项
    public function shiftTimeArray($shiftInfo){
        $t = $this->getTranslation();

        //没有班次 也要有选项
        if(empty($shiftInfo)){
            $res['time_list'] = [
                ['time_type' => HrOvertimeModel::TIME_TYPE_AM,'text' => $t->_('half_am'),'start_time' => '','duration' => 0],
                ['time_type' => HrOvertimeModel::TIME_TYPE_PM,'text' => $t->_('half_pm'),'start_time' => '','duration' => 0],
                ['time_type' => HrOvertimeModel::TIME_TYPE_WHOLE,'text' => $t->_('whole_day'),'start_time' => '','duration' => 0],
            ];
            return $res;
        }

        //一次班
        if($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){
            $amStart = $shiftInfo['first_start'];
            $amEnd = $shiftInfo['middle_rest_start'];

            $pmStart = $shiftInfo['middle_rest_end'];
            $pmEnd = $shiftInfo['first_end'];

            //是否需要加次日
            $amNext = ($amStart > $amEnd) ? $t->_('next_day') : '';
            $pmNext = ($pmStart > $pmEnd) ? $t->_('next_day') : '';
            $wholeNext = ($amStart > $pmEnd) ? $t->_('next_day') : '';

            //拼接 text
            $textAm = $t->_('half_am')." ({$amStart} - {$amEnd}{$amNext})";
            $textPm = $t->_('half_pm')." ({$pmStart} - {$pmEnd}{$pmNext})";
            $textWhole = $t->_('whole_day')." ({$amStart} - {$pmEnd}{$wholeNext})";

            //计算 时长 上半天
            $startAm = "{$shiftInfo['shift_date']} {$amStart}";
            $endAm = "{$shiftInfo['shift_date']} {$amEnd}";
            $durationAm = (strtotime($endAm) - strtotime($startAm)) / 3600;
            //跨天情况
            if($durationAm < 0){
                $durationAm += 24;
            }

            //不进位取整
            $durationAm = floor($durationAm * 100) / 100;

            //计算时长 下半天
            $startPm = "{$shiftInfo['shift_date']} {$pmStart}";
            $endPm = "{$shiftInfo['shift_date']} {$pmEnd}";
            $durationPm = (strtotime($endPm) - strtotime($startPm)) / 3600;
            //跨天情况
            if($durationPm < 0){
                $durationPm += 24;
            }

            $durationPm = floor($durationPm * 100) / 100;

            //计算时长 全天
            $wholeDuration = $durationAm + $durationPm;//上午 + 下午 时长
            $return['time_list'] = [
                ['time_type' => HrOvertimeModel::TIME_TYPE_AM,'text' => $textAm,'start_time' => $amStart,'duration' => $durationAm],
                ['time_type' => HrOvertimeModel::TIME_TYPE_PM,'text' => $textPm,'start_time' => $pmStart,'duration' => $durationPm],
                ['time_type' => HrOvertimeModel::TIME_TYPE_WHOLE,'text' => $textWhole,'start_time' => $amStart,'duration' => $wholeDuration],
            ];
        }else{//两次班
            //是否需要加次日
            $amNext = ($shiftInfo['first_start'] > $shiftInfo['first_end']) ? $t->_('next_day') : '';
            $pmNext = ($shiftInfo['second_start'] > $shiftInfo['second_end']) ? $t->_('next_day') : '';
//            $wholeNext = ($shiftInfo['first_start'] > $shiftInfo['second_end']) ? $t->_('next_day') : '';

            //拼接 text
            $textAm = $t->_('half_am')." ({$shiftInfo['first_start']} - {$shiftInfo['first_end']}{$amNext})";
            $textPm = $t->_('half_pm')." ({$shiftInfo['second_start']} - {$shiftInfo['second_end']}{$pmNext})";
            $textWhole = $t->_('whole_day')." ({$shiftInfo['first_start']} - {$shiftInfo['first_end']}{$amNext},{$shiftInfo['second_start']} - {$shiftInfo['second_end']}{$pmNext})";

            //计算 时长 上半天
            $startAm = "{$shiftInfo['shift_date']} {$shiftInfo['first_start']}";
            $endAm = "{$shiftInfo['shift_date']} {$shiftInfo['first_end']}";
            $durationAm = (strtotime($endAm) - strtotime($startAm)) / 3600;
            //跨天情况
            if($durationAm < 0){
                $durationAm += 24;
            }
            $durationAm = floor($durationAm * 100) / 100;

            //计算时长 下半天
            $startPm = "{$shiftInfo['shift_date']} {$shiftInfo['second_start']}";
            $endPm = "{$shiftInfo['shift_date']} {$shiftInfo['second_end']}";
            $durationPm = (strtotime($endPm) - strtotime($startPm)) / 3600;
            //跨天情况
            if($durationPm < 0){
                $durationPm += 24;
            }
            $durationPm = floor($durationPm * 100) / 100;

            //计算时长 全天
            $wholeDuration = $durationAm + $durationPm;//上午 + 下午 时长
            $return['time_list'] = [
                ['time_type' => HrOvertimeModel::TIME_TYPE_AM,'text' => $textAm,'start_time' => $shiftInfo['first_start'],'duration' => $durationAm],
                ['time_type' => HrOvertimeModel::TIME_TYPE_PM,'text' => $textPm,'start_time' => $shiftInfo['second_start'],'duration' => $durationPm],
                ['time_type' => HrOvertimeModel::TIME_TYPE_WHOLE,'text' => $textWhole,'start_time' => $shiftInfo['first_start'],'duration' => $wholeDuration],
            ];
        }
        return $return;
    }


    //马来 新班次需求 要合并 加班开始结束时间 为一个 加班时间的字段
    public function formatOtTime($otInfo){
        $start = $otInfo['start_time'];
        $end = $otInfo['end_time'];
        //超时加班
        $overKeys = array_keys($this->overOt);
        //旧规则展示逻辑 整合 (老数据 默认0  或者 新数据 但是非节假日加班类型)
        if($otInfo['time_type'] == HrOvertimeModel::TIME_TYPE_DEFAULT || in_array($otInfo['type'],$overKeys)){
            $holidayKeys = array_keys($this->holidayOt);
            //8小时加班 要加一小时 休息时间
            if(in_array($otInfo['type'], $holidayKeys) && $otInfo['duration'] == 8){
                $end = date('Y-m-d H:i:s', strtotime($otInfo['end_time'].' + 1 hours'));
            }
            $text = date('Y-m-d H:i',strtotime($start)) . '~' . date('Y-m-d H:i',strtotime($end));
            return $text;
        }

        //新规则 整合 获取班次信息 拼接
        $attendanceServer = new AttendanceServer($this->lang,$this->timezone);
        $shiftInfo = $attendanceServer->getStaffShiftInfoByDate($otInfo['staff_id'],$otInfo['date_at']);


        //判断 当天班次 是 一次 还是两次 用的显示时间不一样
        $text = '';
        if($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){//一次班
            $text = date('Y-m-d H:i',strtotime($start)) . '-' . date('H:i',strtotime($end));
            //是否跨天
            if($shiftInfo['first_start'] > $shiftInfo['first_end']){
                $text .= $this->getTranslation()->_('next_day');
            }
        }else{//两次班
            //拼接 前半天
            if($otInfo['time_type'] == HrOvertimeModel::TIME_TYPE_AM){
                $text = date('Y-m-d H:i',strtotime($start)) . '-' . date('H:i',strtotime($end));

                //是否跨天
                if($shiftInfo['first_start'] > $shiftInfo['first_end']){
                    $text .= $this->getTranslation()->_('next_day');
                }
            }

            //拼接 后半天
            if($otInfo['time_type'] == HrOvertimeModel::TIME_TYPE_PM){
                $text = date('Y-m-d H:i',strtotime($start)) . '-' . date('H:i',strtotime($end));
                //是否跨天
                if($shiftInfo['second_start'] > $shiftInfo['second_end']){
                    $text .= $this->getTranslation()->_('next_day');
                }

            }

            //拼接 全天
            if($otInfo['time_type'] == HrOvertimeModel::TIME_TYPE_WHOLE){
                $textFirst = date('Y-m-d H:i',strtotime($start)) . '-' . $shiftInfo['first_end'];

                //是否跨天
                if($shiftInfo['first_start'] > $shiftInfo['first_end']){
                    $textFirst .= $this->getTranslation()->_('next_day');
                }

                //第二段开始时间
                $secondStart = "{$otInfo['date_at']} {$shiftInfo['second_start']}";
                //如果 第二点开始时间 跨天
                if($shiftInfo['first_start'] > $shiftInfo['second_start']){
                    $secondStart = date('Y-m-d H:i',strtotime("{$secondStart} +1 day"));
                }

                $textSecond = $secondStart . '-' . date('H:i',strtotime($end));
                //是否跨天
                if($shiftInfo['second_start'] > $shiftInfo['second_end']){
                    $textSecond .= $this->getTranslation()->_('next_day');
                }
                $text = "{$textFirst}~{$textSecond}";
            }
        }
        return $text;
    }

    //加班申请 根据 申请的前半天后半天 获取 结束时间 判断跨天情况
    public function getEndTime($shift_info,$time_type){
        //前半天
        $date = $shift_info['shift_date'];
        $start = $shift_info['first_start'];
        if($time_type == HrOvertimeModel::TIME_TYPE_AM){
            if($shift_info['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){

                $end = $shift_info['middle_rest_start'];//一次班
            }else{
                $end = $shift_info['first_end'];//2次班情况
            }
        }else{//后半天 或者全天 结束时间 取值
            if($shift_info['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE){
                $end = $shift_info['first_end'];//一次班
            }else{
                $end = $shift_info['second_end'];//2次班情况
            }
        }

        if($start > $end){
            $date = date('Y-m-d',strtotime("{$date} +1 day"));
        }

        return "{$date} {$end}";
    }


    /**
     * 生成概要信息(用作列表页展示)
     * @param $auditId    int   审批ID
     * @param $user
     * @return mixed
     */
    public function genSummary(int $auditId, $user)
    {
        //获取加班详情
        $info = $this->overtime->infoOvertime(['overtime_id'=>$auditId]);
        if (empty($info)) {
            return '';
        }
        if($info['type'] == HrOvertimeModel::OVERTIME_7){
            $param = [
                [
                    'key'   => "bonus_date",
                    'value' => $info['date_at']
                ],
                [
                    'key'   => "bonus_type",
                    'value' => $info['type']
                ],
                [
                    'key'   => "duration",
                    'value' => $info['duration'] . 'h'
                ]
                ,[
                    'key'   => "created_at",
                    'value' => $info['real_create']
                ]
            ];
            return $param;
        }


        $param = [
            [
                'key'   => "OT_date",
                'value' => $info['date_at']
            ],
            [
                'key'   => "OT_type",
                'value' => $info['type']
            ],
            [
                'key'   => "duration",
                'value' => $info['duration'] . 'h'
            ]
            ,[
                'key'   => "created_at",
                'value' => $info['real_create']
            ]
        ];
        return $param ;
    }

    /**
     * 校验员工收入
     * @param $staffInfoId
     * @param $overtimeInfo
     * @return bool
     * @throws BusinessException
     */
    public function checkIncome($staffInfoId, $overtimeInfo = null): bool
    {
        $staffInfo = $this->staffInfo;
        if (empty($staffInfo)) {
            //获取员工入职日期
            $staffInfo = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, hire_date',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => ['staff_info_id' => $staffInfoId]
            ]);
        }
        if (empty($staffInfo)) {
            return false;
        }
        //当前申请时间-3个月<入职时间，取offer工资
        $currentTime = isset($overtimeInfo) && $overtimeInfo ? strtotime($overtimeInfo->created_at . '-3 month')
            : strtotime("-3 month");
        if ($currentTime < strtotime($staffInfo['hire_date'])) {
            $entryInfo = HrEntryModel::findFirst([
                'conditions' => 'staff_id = :staff_info_id: and status = 1',
                'bind' => ['staff_info_id' => $staffInfoId],
            ]);
            if (empty($entryInfo)) {
                return false;
            }

            $offerInfo = HrInterviewOfferModel::findFirst([
                'conditions' => 'id = :offer_id:',
                'bind' => ['offer_id' => $entryInfo->interview_offer_id]
            ]);
            if (empty($offerInfo)) {
                return false;
            }
            //天数=申请时间的上个发薪周期天数。如8月申请，取6.24-7.23差值
            //收入 = 基本薪资+租房津贴（房补）+电脑补贴+手机津贴+（油补津贴+私家车车补津贴+货车车补津贴+GDL补贴+区域津贴）*天数
            $startDate = date("Y-m-24", strtotime(date("Y-m-01") . '-2 month'));
            $endDate = date("Y-m-24", strtotime(date("Y-m-01") . '-1 month'));
            $days = intval((strtotime($endDate) - strtotime($startDate)) / 86400);

            $otherSalary = $offerInfo->fuel_allowance + $offerInfo->car_allowance + $offerInfo->vehicle_allowance + $offerInfo->gdl_allowance + $offerInfo->site_allowance;
            $otherSalary = $otherSalary * $days;
            $sum_column = [
                $offerInfo->money,
                $offerInfo->renting,
                $offerInfo->computer,
                $offerInfo->mobile_allowance,
                $otherSalary,
            ];
            $totalSalaryIncome = array_sum($sum_column) / 100;
        } else {
            //获取上一个月工资条
            $month = date("Y-m", strtotime(date("Y-m-01") . '-1 month'));
            $rpc = new ApiClient('hcm_rpc', '', 'get_ot_salary_amount',$this->lang);
            $rpc->setParams(['month' => $month,'staff_id' => $staffInfoId]);
            $return = $rpc->execute();
            $this->logger->write_log('checkIncome '.$staffInfoId .' ' . json_encode([['month' => $month,$return]]),'info');
            if (!empty($return['result']['code']) && $return['result']['code'] == 1) {
                $totalSalaryIncome = $return['result']['data']['amount_total'];
            } else {
                throw new BusinessException($this->getTranslation()->_('please_retry'),ErrCode::RPC_ERROR);
            }
        }
        if (bccomp($totalSalaryIncome, 4000) > 0) { // 4000以上不用给加班费
            return false;
        }
        return true;
    }

    //节假日加班 8小时 需要+1小时的type 马来的结束时间 不加
    public function getHourOverType(){
        return [];
    }



    //马来 加班 和 奖金两种审批类型
    public function getAuditType($info){
        if($info['type'] == HrOvertimeModel::OVERTIME_7){
            return AuditListEnums::APPROVAL_TYPE_OVERTIME_UNPAID;
        }
        return AuditListEnums::APPROVAL_TYPE_OVERTIME;
    }


    //申请加班操作 验证 有没有类型和时长的权限
    public function checkTypeDuration()
    {
        $checkParam['staff_id'] = $this->staffInfo['staff_info_id'];
        $typeList               = $this->getTypeOvertime($checkParam);

        if ($typeList['code'] != 1 || empty($typeList['data']['dataList'])) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }
        //整理数据
        $typeList = array_column($typeList['data']['dataList'], null, 'code');
        //能申请的 类型
        $typeArr = array_keys($typeList);
        if(!in_array($this->param['type'], $typeArr)){
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004'));
        }

        //能申请的时长
        $durationArr = $this->overtimeDurationByShift($this->param);
        $durationArr = $durationArr['data'];
        $this->logger->write_log("checkTypeDuration {$this->staffInfo['id']} ".json_encode($typeArr).json_encode($durationArr), 'info');
        if(in_array($this->param['type'], array_keys($this->holidayOt))){
            //非超时加班没有时长字段
            return true;
        }
        if (!in_array($this->param['duration'], array_column($durationArr['duration'],'time_hour'))) {
            throw new ValidationException($this->getTranslation()->_('ot_duration_permission'));
        }
        return true;
    }


}
