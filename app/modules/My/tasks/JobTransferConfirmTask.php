<?php

namespace FlashExpress\bi\App\Modules\My\Tasks;

use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Modules\My\Server\JobTransferAcknowledgementServer;
use FlashExpress\bi\App\Modules\My\Server\JobTransferV2Server;

class JobTransferConfirmTask extends \BaseTask
{
    /**
     * 重新发转岗确认单
     * php app/cli.php job_transfer_confirm resendTransferConfirmation
     * @throws \Exception
     */
    public function resendTransferConfirmationAction($args = [])
    {
        $staffIds = [];
        if (isset($args[0]) && $args[0]) {
            $staffIds = explode(',', $args[0]);
        }
        $server = new JobTransferV2Server($this->lang, $this->timezone);
        $server->resendTransferConfirmation($staffIds);
    }

    /**
     * 重新生成转岗确认单
     * @param $args
     * @return void
     */
    public function renewPdfAction($args = [])
    {
        if (empty($args[0])) {
            echo "无数据需要处理";
            return;
        }
        $model = JobTransferModel::findFirst($args[0]);
        if (empty($model)) {
            echo "无效的 id :" . $args[0];
            return;
        }

        $acknowledgeServer                  = new JobTransferAcknowledgementServer($this->lang, $this->timezone);
        $confirmationSign = $acknowledgeServer
            ->generateJobTransferPdf(['id' => $model->id, 'staff_info_id' => $model->staff_id]);
        if (empty($confirmationSign)) {
            echo "生成pdf 失败";
            return;
        }
        $model->confirmation_url = $confirmationSign;
        $model->save();
    }
}