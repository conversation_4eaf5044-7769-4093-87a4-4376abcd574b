<?php

namespace FlashExpress\bi\App\Modules\My\Tasks;


use FlashExpress\bi\App\Models\backyard\TicketModel;

class TicketTask extends \BaseTask
{

    public function update_ticket_idAction()
    {
        try {
            $page = 1;
            $size = 1;
            do {
                $ticket = TicketModel::findFirst([
                    'order' => 'id desc',
                    'offset' => ($page - 1) * $size,
                    'limit' => $size
                ]);
                if ($ticket) {
                    $ticketData = $ticket->toArray();

                    $day = date('Y-m-d', strtotime($ticketData['created_at']));
                    $count = TicketModel::count([
                        'conditions' => ' created_at >= :begin_time: and created_at <= :end_time: ',
                        'bind' => [
                            'begin_time' => $day . ' 00:00:00',
                            'end_time' => $ticketData['created_at'],
                        ]
                    ]);
                    $country_code = strtoupper(env('country_code', 'Th'));
                    $country_code = substr($country_code, 0, 2);
                    $ticket_id = $country_code . gmdate("Ymd", strtotime($ticketData['created_at'])) . str_pad($count, 4, 0,STR_PAD_LEFT);

                    $ticket->ticket_id = $ticket_id;
                    $ticket->save();
                    echo 'update ticket_id :' . $ticket_id . " id " . $ticket->id .   PHP_EOL;
                }

                $page++;
            } while ($ticket);
        } catch (\Exception $exception) {
            echo 'error : ' . $exception->getMessage() .  PHP_EOL;
            $this->getDI()->get('logger')->write_log('update_ticket_id ', json_encode([
                'Err_msg' => $exception->getMessage(),
                'Err_line' => $exception->getLine(),
                'Err_file' => $exception->getFile(),
            ], JSON_UNESCAPED_UNICODE));
        }

    }
}