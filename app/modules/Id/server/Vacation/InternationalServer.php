<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 8/21/23
 * Time: 5:56 PM
 */


namespace FlashExpress\bi\App\Modules\Id\Server\Vacation;

use FlashExpress\bi\App\Interfaces\LeaveInterface;
use FlashExpress\bi\App\Server\Vacation\InternationalServer as GlobalServer;


class InternationalServer extends GlobalServer implements LeaveInterface{

    private static $instance = null;

    public $today;

    public $thisYear;


    //任务 每年初始化调用
    public static function getInstance($lang,$timezone){
        if(!self::$instance instanceof self){
            self::$instance = new self($lang,$timezone);
        }
        return self::$instance;
    }


}