<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Server\ProbationServer as GlobalProbationServer;

class ProbationServer extends GlobalProbationServer
{
	
	/**
	 * 根据入职时间获得员工
	 * @param $hire_date_begin
	 * @param $hire_date_end
	 * @param int $is_delay 是否是延长试用期的用户
	 * @return array
	 */
	public function getStaffs($hire_date_begin='', $hire_date_end='',$is_delay=0,$greater_job_title_grade_v2='',$less_job_title_grade_v2='',$date_logic = false)
	{
		$sql = "
            select
                hsi.staff_info_id,
                hsi.job_title_level,
                hsi.job_title_grade_v2,
                hsi.hire_date,
                item.`value` as manager_id,
                hp.cur_level,
                hp.status,
                hp.second_audit_status,
                hp.first_audit_status
            from
                hr_staff_info as hsi
            left join
                hr_staff_items as item on item.staff_info_id = hsi.staff_info_id and item.item='MANGER'
            left join
                hr_probation as hp on hp.staff_info_id = hsi.staff_info_id
            where
                hsi.state =1 and hsi.formal=1  and hsi.is_sub_staff = 0  and hsi.hire_type != 13 and (hp.is_system=0 or hp.is_system is NULL)
        ";
        if ($date_logic){
            $hire_date_end = date("Y-m-d", strtotime("-6 month", strtotime($hire_date_begin)));
            $sql .= 'and hsi.hire_date <= :hire_date_begin and hsi.hire_date>=:hire_date_end';
        }else{
            // 之前老逻辑不动
            $sql .= 'and hsi.hire_date>= :hire_date_begin and hsi.hire_date< :hire_date_end';
        }
		
		if(empty($is_delay)){
			$sql.=" and (hp.is_delay=0 or hp.is_delay is NULL)";
		}else{
			$sql.=" and hp.is_delay=1";
		}
        $bind = ['hire_date_begin' => $hire_date_begin, 'hire_date_end' => $hire_date_end];
        //增加条件
        if (!empty($greater_job_title_grade_v2)) {
            $sql .= ' and hsi.job_title_grade_v2 > :begin_job_title_grade_v2 ';
            $bind['begin_job_title_grade_v2'] = (int)$greater_job_title_grade_v2;
        }

        //增加条件
        if (!empty($less_job_title_grade_v2)) {
            $sql .= ' and ifnull(hsi.job_title_grade_v2,0) <= :end_job_title_grade_v2 ';
            $bind['end_job_title_grade_v2'] = (int)$less_job_title_grade_v2;
        }

		return $this->getDI()->get("db_rby")->query(
			$sql,
            $bind
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}
	
}
