<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\OfferSignApproveServer as GlobalServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class OfferSignApproveServer extends GlobalServer
{
    const PDF_TYPE_1 = 1;//pdf 1 Candy 签字
    const PDF_TYPE_2 = 2;//pdf 2 Diana 签字

    /**
     * 新增申请
     * @param $param
     * @return array
     */
    public function addApprove($param)
    {
        self::$addParams = $param;

        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $this->validateCheck($param, [
                'interview_id'  => 'Required|int',//面试ID
                'resume_id'     => 'Required|int',//简历ID
                'submitter_id'  => 'Required|int',//提交人
                'approve_state' => 'Required|int',//审批状态
                'pdf_type'      => 'Required|int',//pdf模板类型
                'pdf_path'      => 'Required|Str',//pdf路径
            ]);

            $lastResume = $this->getLastApprove($param['resume_id']);
            if ($lastResume && in_array($lastResume['approve_state'], [self::AUDIT_STATUS_1, self::AUDIT_STATUS_2])) {
                // 正在审批 或 一审批通过
                return $this->checkReturn([
                    "code" => -1,
                    "msg"  => "该offer签字正在审批中或已审批通过",
                ]);
            }

            $staff_info = (new StaffServer())->get_staff($param['submitter_id']);
            if (!isset($staff_info['data'])) {
                return $this->checkReturn([
                    "code" => -1,
                    "msg"  => "未找到申请人！",
                ]);
            }


            //生成一条记录 添加审批
            $HrInterviewOfferSignApproveModel = new HrInterviewOfferSignApproveModel();
            $result                           = $HrInterviewOfferSignApproveModel->save(
                [
                    'interview_id'  => $param['interview_id'],
                    'resume_id'     => $param['resume_id'],
                    'submitter_id'  => $param['submitter_id'],
                    'approve_state' => 1,
                    'pdf_type'      => $param['pdf_type'],
                    'pdf_path'      => $param['pdf_path'],
                    "serial_no"     => "ST".$this->getID(),
                    "workflow_role" => '',
                    'higher_id'     => $param['higher_id'],
                ]
            );

            //写入失败记录日志
            if (!$result) {
                $this->getDI()->get("logger")->write_log(
                    "addApprove: insert db: hr_interview_offer_sign_approve fail"
                    .json_encode($param, JSON_UNESCAPED_UNICODE)
                    , 'notice');
                throw new \Exception('OfferSignApproveServer :insert db HrInterviewOfferSignApproveModel fail');
            }
            //业务ID
            $offer_sign_id = $HrInterviewOfferSignApproveModel->id;
            $this->insertLog($param['resume_id'], $param['submitter_id'], 17);

            //固化审批流流程
            $create_flow = (new ApprovalServer($this->lang, $this->timezone))->create(
                $offer_sign_id,
                enums::$audit_type['ST'],
                $param['submitter_id'],
                null
            );

            if (!$create_flow) {
                throw new \Exception("OfferSignApproveServer: 固化审批流流程失败 业务ID: {$offer_sign_id}");
            }

            $db->commit();
            $this->getDI()->get("logger")->write_log("OfferSignApproveServer : addApprove success: hr_interview_offer_sign_approve id:{$offer_sign_id}",
                "info");
            return $this->checkReturn([
                "code" => 1,
                "msg"  => 'success',
            ]);
        } catch (\Exception $e) {
            $db->rollBack();

            $this->getDI()->get("logger")->write_log("OfferSignApproveServer : addApprove fail:".$e->getMessage().' '.$e->getTraceAsString(),
                "notice");

            return $this->checkReturn([
                "code" => -1,
                "msg"  => $e->getMessage(),
            ]);
        }
    }


    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $offerSignApprove = HrInterviewOfferSignApproveModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $auditId],
        ]);

        $resume_data = HrResumeModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $offerSignApprove->resume_id],
        ]);
        $resume_data = $resume_data ? $resume_data->toArray() : [];

        if ($offerSignApprove) {
            $salary_approve_info = SalaryApproveModel::findFirst([
                'conditions' => " resume_id =:resume_id:",
                'bind'       => ['resume_id' => $offerSignApprove->resume_id],
                'order'      => 'id desc',
            ]);
            if ($salary_approve_info) {
                $salary_approve_info = $salary_approve_info->toArray();
            } else {
                $this->getDI()->get("logger")->write_log("getWorkflowParams_offerSignApprove resume_id:{$offerSignApprove->resume_id},offer_info is null",
                    "info");
            }
            // 试用期通过薪资
            $trial_salary = intval($salary_approve_info['trial_salary'] ?? 0);
            // 星火津贴
            $xinghuo_allowance = intval($salary_approve_info['xinghuo_allowance'] ?? 0);
            // 租房津贴
            $renting = intval($salary_approve_info['renting'] ?? 0);
            // 语言津贴
            $language_allowance = intval($salary_approve_info['language_allowance'] ?? 0);

            // 薪酬包
            $money = intval($salary_approve_info['money'] ?? 0);

            // 总金额
            $total = intval($money) + intval($xinghuo_allowance) + intval($renting) + intval($language_allowance);


            // 国籍
            $nationality = $resume_data["nationality"] ?? 0;

            if ($nationality == 2) {
                // 中国国籍
                if ($money >= 25800000) {
                    // 56780审批
                    $k1 = 'type2';
                } else {
                    // 118830审批
                    $k1 = 'type1';
                }
            } else {
                if ($total >= 25800000) {
                    // 56780审批
                    $k1 = 'type2';
                } else {
                    // 118830审批
                    $k1 = 'type1';
                }
            }
            $this->getDI()->get("logger")->write_log("getWorkflowParams_offerSignApprove resume_id:{$offerSignApprove->resume_id},totle:{$total},k1:{$k1},trial_salary:{$trial_salary},xinghuo_allowance:{$xinghuo_allowance},renting:{$renting}",
                "info");
            return [
                'country_code' => env('country_code', 'id'),
                'k1'           => $k1,
            ];
        }
        return [];
    }


    public function updateApprove($uid, $id, $status, $remark)
    {
        //申请业务记录
        $info = $this->getRecord($id);
        if (!$info) {
            $this->getDI()->get("logger")->write_log("offer_sign_approval_error: 非审批状态"
                .json_encode($id, JSON_UNESCAPED_UNICODE)
                ." id:".$id);
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_("1015")));
        }
        $db = $this->getDI()->get("db");
        $db->begin();
        try {

            self::$params   = [
                'id'            => $id,
                'userinfo'      => $uid,
                'status'        => $status,
                'revoke_remark' => $remark,
            ];
            $approvalServer = new ApprovalServer($this->lang, $this->timezone);
            if ($status == enums::APPROVAL_STATUS_APPROVAL) {
                //只有待审批状态才能进行：驳回，同意操作。
                if ($info['approve_state'] != enums::APPROVAL_STATUS_PENDING) {
                    $this->getDI()->get("logger")->write_log("offer_sign_approval_error: 非待审批状态，不能进行驳回、同意"
                        .json_encode($id, JSON_UNESCAPED_UNICODE)
                        ." id:".$id, 'info');
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_("4012")));
                }

                $approvalServer->approval(
                    $id,
                    enums::$audit_type['ST'],
                    $uid['id']
                );
            } else {
                if ($status == enums::APPROVAL_STATUS_REJECTED) {
                    $approvalServer->reject(
                        $id,
                        enums::$audit_type['ST'],
                        $remark,
                        $uid['id']
                    );
                } else {
                    if ($status == enums::APPROVAL_STATUS_CANCEL) {
                        $approvalServer->cancel(
                            $id,
                            enums::$audit_type['ST'],
                            $remark,
                            $uid['id']
                        );
                    } else {
                        throw new \Exception('no exist status '.json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            $db->commit();
            return $this->checkReturn(1);
        } catch (\Exception $e) {
            if ($e->getCode() == ErrCode::WORKFLOW_DATA_ERROR && $status == enums::$audit_status['revoked']) {
                $this->setProperty($id, $status, null, true);
                $db->commit();
                return $this->checkReturn(1);
            }
            $db->rollback();
            $this->getDI()->get("logger")->write_log("offer_sign_approval_error: 事务未提交"
                .json_encode($uid, JSON_UNESCAPED_UNICODE)
                ." id: ".$id
                ." message: ".$e->getMessage()
                ." line: ".$e->getLine()
                ." file: ".$e->getFile());
            return $this->checkReturn(["code" => -1, "msg" => $this->getTranslation()->_("4008")]);
        }
    }


    public function getStreams($postData, $comFrom)
    {
        $request = AuditApplyModel::findFirst([
            "conditions" => " biz_type = :biz_type: and biz_value = :biz_value:",
            "bind"       => [
                "biz_type"  => enums::$audit_type['ST'],
                "biz_value" => $postData['st_id'],
            ],
        ]);

        $user = $postData['userinfo']['id'];

        return
            (new WorkflowServer($this->lang, $this->timezone))->getAuditLogs($request, $user);
    }


    public function getOptions($comFrom, $idUnion, $userinfo)
    {
        $auditId = explode("_", $idUnion)[1];
        $user    = $userinfo['id'];
        if ($comFrom == 2) { //获取审批人的审批状态

            $infos  = AuditApprovalModel::getApprovalDetailInfo($auditId, AuditListEnums::APPROVAL_TYPE_OFFER_SIGNATURE, $user);
            $infos  = empty($infos) ? [] : $infos->toArray();
            $state  = $infos['state'] ?? 0;
            $option = ['beforeApproval' => "1,2", 'afterApproval' => '0'];
        } else { //获取申请的审批状态
            $auditInfo = HrInterviewOfferSignApproveModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $auditId],
            ])->toArray();

            $state  = $auditInfo['approve_state'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }

        return $this->getStaffOptions(['option' => $option, 'status' => $state]);
    }


    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $auditApply                = AuditApplyModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type:",
                'bind'       => [
                    'type'  => enums::$audit_type['ST'],
                    'value' => $auditId,
                ],
            ]);
            $offer_sign                = HrInterviewOfferSignApproveModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind'       => [
                    'id' => $auditId,
                ],
            ]);
            $offer_sign->approve_state = $state;
            if ($auditApply && in_array($state, [enums::APPROVAL_STATUS_REJECTED, enums::APPROVAL_STATUS_CANCEL])) {
                $offer_sign->cancel_reason = self::$params['revoke_remark'];
            }
            $db = $this->getDI()->get("db");
            $offer_sign->save();
            //修改offer状态
            $offerInfo = HrInterviewOfferModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => ['resume_id' => $offer_sign->resume_id],
                'order'      => 'id desc',
            ]);
            if ($state == enums::APPROVAL_STATUS_REJECTED) {
                if ($offerInfo) {
                    $offerInfo->status = enums::$hr_interview_offer_state['offer_sign_revoke'];//驳回offer
                    $offerInfo->save();
                }

                $this->insertLog($offer_sign->resume_id, $extend['staff_id'], 19);
            } elseif ($state == enums::APPROVAL_STATUS_CANCEL) {
                //写入hr_log
                $this->insertLog($offer_sign->resume_id, $extend['staff_id'], 18);

                if ($offerInfo) {
                    $offerInfo->status = enums::$hr_interview_offer_state['offer_sign_cancel'];//撤销offer签字
                    if (!$offerInfo->save()) {
                        throw new \Exception("OfferSignApproveServer  undo:offer 签字撤销 修改offer表状态失败 resume_id：{$offer_sign->resume_id} ");
                    }
                }
            } else {
                if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                    $rpcClient = (new ApiClient('winhr_rpc', '', 'addOfferSignDate', $this->lang));
                    $rpcClient->setParams(['resume_id' => $offer_sign->resume_id]);
                    $return = $rpcClient->execute();
                    $this->getDI()->get('logger')->write_log("winhr_rpc addOfferSignDate:".json_encode($return,
                            JSON_UNESCAPED_UNICODE), 'info');
                    if (isset($return['result']['code']) && $return['result']['code'] == 1) {
                        //更新hr_interview_offer_sign_approve pdf地址
                        $new_pdf_path = $return['result']['data']['new_pdf_path'];
                    }

                    //todo 给offer签字发起人发送消息
                    $this->sendOfferSignApprovePassedMsg($offer_sign->resume_id, $offer_sign->submitter_id);
                }
            }

            $update_approve_data = [
                "approve_state" => $state,
                "cancel_reason" => $state == self::AUDIT_STATUS_2 ? '' : self::$params['revoke_remark'],
            ];

            if (isset($new_pdf_path)) {
                $update_approve_data['pdf_path'] = $new_pdf_path;
            }

            // 最终审批
            // 更新 薪资申请表状态
            $salaryStatus = $db->updateAsDict("hr_interview_offer_sign_approve", $update_approve_data, "id=".$auditId);
        }
        //以前没有返回值 现在返回一个
        return true;
    }


}
