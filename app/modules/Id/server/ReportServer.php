<?php

namespace FlashExpress\bi\App\Modules\Id\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditModel;
use FlashExpress\bi\App\Server\AuditServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class ReportServer extends \FlashExpress\bi\App\Server\ReportServer
{

    /**
     * 获取举报详情页 提交 按钮的 展示情况
     * 一级审批人 同意之后，申请人不得撤销
     * @param $comeFrom
     * @param $auditId
     * @param $user
     * @param $result
     * @param $infos
     * @param $option
     * @param $state
     * @return array
     */
    public function getOption($comeFrom, $auditId, $user, $result, $infos, $option, $state)
    {
        //申请人。
        if($comeFrom != 2) {
            // 我申请的
            $info   = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and deleted = 0",
                'bind'       => [
                    'type'  => enums::$audit_type['RE'],
                    'value' => $auditId,
                ],
                'order'      => 'id asc',
            ]);
            $state  = enums::$audit_status['panding'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
            if ($info) {
                $info  = $info->toArray();
                $state = $info['state'];
                if ($state != enums::$audit_status['panding']) {
                    // 一级审批人批过
                    $option = ['beforeApproval' => '0', 'afterApproval' => '0'];
                }
            }
        }
        return [$option, $state];
    }

}