<?php
namespace FlashExpress\bi\App\Modules\Id\Tasks;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Server\StaffServer;
use Phalcon\Db;
use FlashExpress\bi\App\Modules\Id\Server\ProbationServer;
use FlashExpress\bi\App\Server\WorkflowServer;
use ProbationTask AS ProbationBaseTask;
use Exception;


class ProbationTask extends  ProbationBaseTask
{

	
	
	/**
	 * 第一阶段评估插入
	 * 每天执行，
	 * @param array $params
	 */
	
	public function firstAction(array $params=[])
	{
		$start = "";
		if (isset($params[0])) {
			$start = $params[0];
			if (strtotime($start) === false) {
				echo '传入的时间格式不对' . PHP_EOL;
				return;
			}
		}
		
		//第二阶段转正评估
		
		$this->secondAction($params);
		
		
		//处理快要超时的
		
		$this->deal_deadlineAction();
		
		
	
		//修改转正
		$this->save_probation_staffAction();

		//发送结果给员工已通过 发送该给 员工和的 hrbp
		$this->send_msg_to_staffAction($params);
		
		
		//获得考勤每天执行
		
		$this->get_attendanceAction();


        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate('Y-m-d 00:00:00', time() + ($this->add_hour) * 3600);
        }
        $evaluate_day    = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[1]);                          //第一阶段
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day[1] - 1);
        $staffs          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs,'staff_info_id')));
        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin     = $this->getDateByDays($start, $evaluate_day_exceed[1]);
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day_exceed[1] - 1);
        $staffs_two          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, $bll->job_grade_exceed,'',true);    //查询 14 级以上的人
        $staffs              = array_merge($staffs, $staffs_two);                                               //合并两个数据组
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger('第一阶段没有找到符合的数据');
            return false;
        }

        $db = $this->getDI()->get('db');
        $staffIds = array_column($staffs,'staff_info_id');
        $existStaffIds = $bll->getExistHrProbationByStaffIds($staffIds);
        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_FIRST &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' manager_id is null 第一阶段评估没有找到上级','error');
                continue;
            }
            if (in_array($staff['staff_info_id'],$existStaffIds)) {
                $this->myLogger('staff  ' . $staff['staff_info_id'] . ' 已经存在');
                continue;
            }
            $this->myLogger("firstAction 第一阶段评估插入 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                $db->begin();
                //判断此人什么等级
                $formal_days = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->formal_days : $bll->formal_days_exceed;
                $formal_at   = $this->getDateByDays($staff['hire_date'], $formal_days, 1);
                //每阶段评估时间
                $evaluate_time = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                $res = $db->insertAsDict("hr_probation", ['staff_info_id' => $staff['staff_info_id'],'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_RUN, 'created_at' => $start, 'formal_at' => $formal_at]);
                if (!$res) {
                    throw new Exception('hr_probation insert fail');
                }
                $probation_id = $db->lastInsertId();
                $res          = $db->insertAsDict('hr_probation_audit', [
                    'probation_id'  => $probation_id,
                    'staff_info_id' => $staff['staff_info_id'],
                    'audit_id'      => $staff['manager_id'],
                    'tpl_id'        => $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']),
                    'created_at'    => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    //第一次上级评审截止3天以后
                    'deadline_at'   => $this->getDateByDays($start, $evaluate_time['1']['1'] ?? 3, 1),

                    'updated_at' => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    'version'=>$bll->version,
                ]);
                if (!$res) {
                    throw new Exception('hr_probation_audit insert fail');
                }
                $db->commit();
                //发送push
                $bll->push_notice_higher($staff['manager_id'], $staff['staff_info_id']);
            } catch (\Exception $e) {
                $db->rollback();
                //实习只进入一直，大概就是有重复进入的报错。改成info
                $this->myLogger('staff=' . $staff['staff_info_id'] . ' first insert fail,message=' . $e->getMessage(), 'info');
            }
        }


        $this->myLogger('第一阶段评估执行完毕======end');
		
		
	}

    /**
     * 第二阶段评估插入
     * 每天执行，
     * @param array $params
     */
    public function secondAction( array $params = [] ) {
        $start = '';
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate('Y-m-d 00:00:00', time() + ($this->add_hour) * 3600);
        }
        $evaluate_day    = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[2]);
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day[2] - 1);
        $staffs          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . ' ' . implode(',',array_column($staffs,'staff_info_id')));

        //查询 17 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin     = $this->getDateByDays($start, $evaluate_day_exceed[2]);
        $hire_date_end       = $this->getDateByDays($start, $evaluate_day_exceed[2] - 1);
        $staffs_two          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, $bll->job_grade_exceed,'',true);//查询 14 级以上的人
        $staffs              = array_merge($staffs, $staffs_two);                                           //合并两个数据组
//        $this->myLogger("查询第二阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . ' ' . implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger('第二阶段评估没有数据=====end');
            return;
        }

        $db = $this->getDI()->get('db');

        foreach ($staffs as $staff) {
            if (!empty($staff['status']) && $staff['status'] == $bll::STATUS_FORMAL){
                continue;
            }
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_SECOND &&
                !empty($staff['second_audit_status']) &&
                $staff['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' manager_id is null 第二阶段评估没有上级');
                continue;
            }

            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['cur_level'] != 1 || $item['audit_status'] == 1) {
//                $this->myLogger('staff= ' . $staff['staff_info_id'] . ' 第一次评审没有完成或者已经进入第二次评审==' . $item['cur_level'] . '==' . $item['audit_status']);
                continue;
            }
            $this->myLogger("secondAction 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                $db->begin();
                //每阶段评估时间
                $evaluate_time = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day: $bll->duration_day_exceed;

                $res = $db->insertAsDict('hr_probation_audit', [
                    'probation_id'       => $item['probation_id'],
                    'staff_info_id'      => $staff['staff_info_id'],
                    'cur_level'          => 2,
                    'tpl_id'             => $item['tpl_id'],
                    'score'              => $item['score'],
                    'audit_id'           => $staff['manager_id'],
                    'created_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    'deadline_at'        => $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    //第二次上级评审截止6天以后，75天0点，80天24：00，改成85天24:00,86
                    'second_deadline_at' => $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 6, 1),
                    'updated_at'         => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600),
                    'version'=>$bll->version,
                ]);
                if (!$res) {
                    throw new Exception('hr_probation_audit insert fail');
                }

                $res = $db->updateAsDict("hr_probation", [
                    'cur_level'  => 2,
                    "is_active" => HrProbationModel::IS_ACTIVE_DEFAULT,
                    'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate('Y-m-d H:i:s', time() + ($this->add_hour) * 3600)
                ],                       ['conditions' => 'staff_info_id = ' . $staff['staff_info_id']]);
                if (!$res) {
                    throw new Exception('hr_probation update fail');
                }

                $db->commit();

                //发送push
                $bll->push_notice_higher($staff['manager_id'], $staff['staff_info_id']);

            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger('staff=' . $staff['staff_info_id'] . ' second insert fail,message=' . $e->getMessage(), 'error');
            }
        }

        $this->myLogger('第二阶段执行完毕======end');
    }


    /**
     *获得考勤
     *每天执行
     * 纪律考核
     */
    public function get_attendanceAction() {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $now = gmdate('Y-m-d 00:00:00', time() + ($this->add_hour) * 3600);
        //第一阶段时间
        $day_1 = $bll->first_check_days;
        //查询 18 级一下的
        $start_1 = $this->getDateByDays($now, $day_1);     //>=45 天之前
        $end_1   = $this->getDateByDays($now, $day_1 - 1); //<44天
        //第二阶段时间
        $day_2   = $bll->second_check_days;
        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2   = $this->getDateByDays($now, $day_2 - 1);

        $staffs_1 = $bll->getStaffs($start_1, $end_1, 0, 0, $bll->job_grade_exceed); // 查询 17 级及以下的人
        $staffs_2 = $bll->getStaffs($start_2, $end_2, 0, 0, $bll->job_grade_exceed); // 查询 17 级及以下的人

        //查询 18 级以上的
        $day_1   = $bll->first_check_days_exceed;
        $start_1 = $this->getDateByDays($now, $day_1);
        $end_1   = $this->getDateByDays($now, $day_1 - 1);

        $day_2 = $bll->second_check_days_exceed;

        $start_2 = $this->getDateByDays($now, $day_2);
        $end_2   = $this->getDateByDays($now, $day_2 - 1);

        $staffs_1_two = $bll->getStaffs($start_1, $end_1, 0, $bll->job_grade_exceed); // 查询 17 级及以上的人
        $staffs_2_two = $bll->getStaffs($start_2, $end_2, 0, $bll->job_grade_exceed); // 查询 17 级及以上的人
        $staffs_1     = array_merge($staffs_1, $staffs_1_two);                        //合并两个数据组
        $staffs_2     = array_merge($staffs_2, $staffs_2_two);                        //合并两个数据组


        if (empty($staffs_1) && empty($staffs_2)) {
            $this->myLogger('get_attendance staffs on ' . $start_1 . '===' . $end_1 . ' and ' . $start_2 . '===' . $end_2 . ' 为空，不用执行' . '=====end');
            return;
        }

        $data = [];

        if (!empty($staffs_1)) {
            foreach ($staffs_1 as $staff) {
                $tmp                  = [];
                $tmp['cur_level']     = 1;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[]               = $tmp;
            }
        }

        if (!empty($staffs_2)) {
            foreach ($staffs_2 as $staff) {
                $tmp                  = [];
                $tmp['cur_level']     = 2;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[]               = $tmp;
            }
        }


        foreach ($data as $staff) {
            if ($bll->isHaveAttentdance($staff['staff_info_id'], $staff['cur_level'])) {
                continue;
            }
            $flag = $bll->addAttendance($staff);
            if (!$flag) {
                $this->myLogger('get_attendance staffs==add attendance error==' . $staff['staff_info_id'], 'error');
            }
        }
        $this->myLogger('get_attendance staffs on ' . $start_1 . '===' . $end_1 . ' and ' . $start_2 . '===' . $end_2
                        . '=====end');
    }


	

	
	
	
	/**
	 * 给已通过的员工，修改状态成，已转正
	 * 每天执行
	 */
	public function save_probation_staffAction()
	{
		$bll = new ProbationServer($this->lang, $this->add_hour);
		
		//今天
		$start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
		$end = $this->getDateByDays($start, 1, 1);//<
		
		
		$staffs = $bll->getStaffsByFormalDate($start, $end,[$bll::STATUS_PASS],true);
        $staffs = array_filter($staffs, function ($v) {
            return !(
                $v['status'] == HrProbationModel::STATUS_FORMAL &&
                $v['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE &&
                $v['second_status'] == HrProbationModel::SECOND_STATUS_PASS
            );
        });
		if (empty($staffs)) {
			$this->myLogger("修改转正状态-没有数据send msg to staff:no probation staffs on formal_at between" . $start . "===" . $end . "=====end");
			return;
		}
		
		$staffIdArr = array_column($staffs, "staff_info_id");
		$flag = $bll->formalStaffs($staffIdArr);
		
		if (!$flag) {
			$this->myLogger("修改转正状态-失败!send msg to staff: formal staff_info_id in (" . implode(",", $staffIdArr) . ")", "error");
			return;
		}
		
		
		
		$this->myLogger("修改转正状态-成功!send msg to staff: probation staffs on formal_at between" . $start . "===" . $end . "=====end");
	}
	

	/**
	 * 发送结果给员工
	 * 已通过 发送该给 员工和的 hrbp
	 * 每天执行
	 */
	public function send_msg_to_staffAction($params=[])
	{
		$bll = new ProbationServer($this->lang, $this->add_hour);
		
		//今天
		$to_day = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        $start = $end = $this->getDateByDays($to_day, 7, 1);//取 7 天后转正的 发消息
        if (isset($params[0])) {
            $start = $end = $params[0];
            if (strtotime($to_day) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
		$staffs = $bll->getStaffsByFormalDate($start, $end, [ProbationServer::STATUS_PASS, ProbationServer::STATUS_NOT_PASS, ProbationServer::STATUS_FORMAL]);
		if (empty($staffs)) {
			$this->myLogger("没有找到7 天后的转正数据 send msg to staff:no probation staffs on formal_at between" . $start . "===" . $end . "=====end");
			return;
		}

		$WorkflowServer = (new WorkflowServer($this->lang, $this->timezone));
        $staffServer = new StaffServer();
		foreach ($staffs as $staff) {
			//找 hrbp
			$staff_info_ids = [];
			$findHRBP = $WorkflowServer->findHRBP($staff['sys_department_id'], ["store_id" => $staff['sys_store_id']]);
			if (!empty($findHRBP)){
				$staff_info_ids = explode(',', $findHRBP);
			}
            $payRoll = $WorkflowServer->findJurisdictionAreaStaffIds($staff['sys_department_id'], ["store_id" => $staff['sys_store_id']]);
            if (!empty($payRoll)){
                $staff_info_ids = array_merge($staff_info_ids, explode(',', $payRoll));
            }
			$staff_info_ids[] = $staff['staff_info_id']; //发送给员工
            $staff_info_ids[] = $staff['manger']; //发送给员工
			$staff_info_ids = array_values(array_unique(array_filter($staff_info_ids)));
			if(empty($staff_info_ids)) continue;

			foreach($staff_info_ids as $kk=>$vv){
                $lang = $staffServer->getLanguage($vv);
                $t = $staffServer->getTranslation($lang);

                $staff_info_id = $vv;
                if ($staff['status'] == ProbationServer::STATUS_PASS || $staff['status'] == ProbationServer::STATUS_FORMAL) {
                    // 通过
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staff_info_id,"hr_probation_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                            'formal_at'=>$staff['formal_at'],
                        ]) . "</div>");
                } else {
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staff_info_id,"hr_probation_not_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                        ]) . "</div>");
                }
				$id = time() . $staff_info_id . rand(1000000, 9999999);
				$param['staff_users'] = [$staff_info_id];//数组 多个员工id
				$param['message_title'] =$t->_('hr_probation_field_msg_to_staff_title');
				$param['message_content'] = $html;
				$param['staff_info_ids_str'] = $staff_info_id;
				$param['id'] = $id;
				$param['category'] = -1;
				
				$bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
				$bi_rpc->setParams($param);
				$res = $bi_rpc->execute();
				$this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param).'-result:' . json_encode($res), 'info');
				if ($res && $res['result']['code'] == 1) {
					$kitId    = $res['result']['data'][0];
					$this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
				}else{
					$this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staff_info_id, 'error');
				}
			}
			
			
			
		}
		
		$this->myLogger("send msg to staff: probation staffs on formal_at between" . $start . "===" . $end . "=====end");
	}
	
	
	/**
	 * 第二阶段的提醒截止日期前两天，每隔5小时推送一次 给审批人发送 push
	 * 每天7:00,12:00,17:00执行
	 */
	public function send_msg_to_higherAction()
	{
		$bll = new ProbationServer($this->lang, $this->add_hour);
		
		$start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
		
		//开始时间=截止日期比当前时间多1天
		//结束时间=截止日期比当期时间多2天（比开始时间多1天）
		$start = $this->getDateByDays($start, 1, 1);//大于等于
		
		$end = $this->getDateByDays($start, 1, 1);//<
		
		//找第二次评审，待评估的用户发送信息，都是截止日期前两天
		$staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);
		
		if (empty($staffs)) {
			$this->myLogger("send msg to higher:no probation staffs on deadline between" . $start . "===" . $end . "=====end");
			return;
		}
		
		$auditIdArr = array_column($staffs, "audit_id");
		$auditIdArr = array_unique($auditIdArr);
		
		//您有即将超时的员工转正评估未处理，请尽快前往backyard-转正评估进行评估，若未在规定时间内完成评估，可能存在因失职而收到警告书的风险。
		//$approval_html_url  = env('probation_html_url','flashbackyard://fe/html?url=');
		//$approval_index_url= env('probation_index_url','http://192.168.0.222:90/#/EvaluateIndex');
		$approval_html_url = $bll->get_setting_by_code('probation_html_url');
		$approval_index_url = $bll->get_setting_by_code('probation_index_url');


		foreach ($auditIdArr as $k => $v) {
			$data = [];
			$data['staff_info_id'] = $v;
			$data['src'] = 'backyard';
			$data['message_title'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_notice');
			$data['message_content'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_to_higher');
			$data['message_scheme'] = $approval_html_url . urlencode($approval_index_url);
			
			$this->getDI()->get('logger')->write_log('send_msg_to_higherAction_pushMessage params:' . json_encode($data), 'info');
			$ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
			$ret->setParams($data);
			$_data = $ret->execute();
			$this->getDI()->get('logger')->write_log("send_msg_to_higherAction_pushMessage:pushMessage-return- " . json_encode($_data), 'info');
			if (!$_data['result']) {
				$this->myLogger("higher:staff_info_id=" . $v . "  发送push失败", "info");
			}
		}
		
		$this->myLogger("send msg to higher:probation staffs on deadline between" . $start . "===" . $end . "======end");
	}
	
	

	
	
	
	
	
	

	
}