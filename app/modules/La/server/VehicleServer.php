<?php
namespace FlashExpress\bi\App\Modules\La\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;
use FlashExpress\bi\App\Modules\La\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\OvertimeRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\VehicleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Server\AuditDetailOptionServer;
use FlashExpress\bi\App\Server\AuditListServer;
use FlashExpress\bi\App\Server\VehicleServer AS BaseVehicleServer;
use FlashExpress\bi\App\Server\HrStaffInfoServer;

class VehicleServer extends BaseVehicleServer

{

    const OTHER = 100;
    const NETWORK_TAG = 1;//NETWORK部门
    const SHOP_TAG = 2;// SHOP 部门
    /**
     * @var PublicRepository
     */
    private $public;
    /**
     * @var AuditListServer
     */
    private $auditlist;
    /**
     * @var OvertimeRepository
     */
    private $ov;
    /**
     * @var VehicleRepository
     */
    private $vehicle;

    public function __construct($lang, $timezone)
    {
        parent::__construct($lang,$timezone);
        $this->vehicle = new VehicleRepository($lang, $timezone);
        $this->public = new PublicRepository();
        $this->auditlist = new AuditListServer($lang, $timezone);
        $this->ov = new OvertimeRepository($timezone);
    }

    /**
     * 枚举类型(转换为前端口需要的方式)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleS($paramIn = [])
    {
        $returnData = VehicleInfoEnums::CONFIG_VEHICLE_INFO;

        //  油类型
        foreach ($returnData['oil_type'] as $k => $v) {
            $oil_type[] = [
                'value' => $k,
                'label' => $v
            ];
        }
        $returnData['oil_type'] = $oil_type;
        foreach($returnData['vehicle_brand'] as $key => $val){
            if($val['value'] == 99) {
                $returnData['vehicle_brand'][$key]['label'] = $this->getTranslation()->_('2016');
            }
            foreach ($val['data'] as $key1 => $val1) {
                if($val1['value'] == VehicleServer::OTHER){
                    $val1['label'] = $this->getTranslation()->_('2016');
                    $returnData['vehicle_brand'][$key]['data'][$key1] = $val1;
                }
            }
        }

        // 车辆来源
        foreach (VehicleInfoEnums::VEHICLE_SOURCE_ITEM as $vs_k => $vs_v) {
            $returnData['vehicle_source_item'][] = [
                'value' => $vs_k,
                'label' => $this->getTranslation()->_($vs_v),
            ];
        }
        // 驾照类型
        foreach (VehicleInfoEnums::DRIVER_LICENSE_TYPE_ITEM as $l_k => $l_v) {
            $returnData['driver_license_item'][] = [
                'value' => $l_k,
                'label' => $l_v,
            ];
        }

        // 车型
        $returnData['vehicle_size'] = [];
        foreach (self::getVehicleSize(false)  as $k => $v) {
            $returnData['vehicle_size'][] = [
                'value' => strval($k),
                'label' => $v
            ];
        }

        $resData['vehicle_enum'] = $returnData;
        return $resData;
    }

    /**
     * 车辆信息
     * @Access  public
     * @param $paramIn
     * @return array
     */
    public function getVehicleInfoS($paramIn = [])
    {
        $res = $this->vehicle->getVehicleInfoR($paramIn['id']);
        $returnData['vehicle_info'] = array();

        //如果存在车辆信息显示车辆型号品牌名称
        if ($res) {
            //TODO 判断本月是否能换车
            $res['change_car']  = 1;
            $returnData['vehicle_info'] = $res;
        }

        $returnData['vehicle_info'] = $this->handleVehicleInfo($returnData['vehicle_info'], $paramIn);

        return $returnData;
    }

    /**
     * 里程信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageS($paramIn = [], $userinfo = [])
    {
        $returnData = $resImgArr = [];
        //获取里程信息
        $res = $this->vehicle->dayMileageCountR($paramIn['mileage_date'], $userinfo['id']);
        if ($res) {
            //获取里程对应的图片信息SMI-上班 // EMI-下班
            $resImg = $this->vehicle->getMileageImgR($res['id']);
            if ($resImg) {
                foreach ($resImg as $k => $v) {
                    if (strstr($v['object_key'], 'SMI')) {
                        $resImgArr['started_path'] = $v['object_key'];
                        $resImgArr['started_bucket'] = $v['bucket_name'];
                        $resImgArr['started_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    } else if (strstr($v['object_key'], 'EMI')) {
                        $resImgArr['end_path'] = $v['object_key'];
                        $resImgArr['end_bucket'] = $v['bucket_name'];
                        $resImgArr['end_img'] = convertImgUrl($v['bucket_name'], $v['object_key']);
                    }
                }
            }
            $res = $res + $resImgArr;
        } else {
            $res = (object)[];
        }
        //获取本月补里程次数
        $count = $this->vehicle->getMileageCountR($userinfo['id'], $paramIn['mileage_date']);
        //月次数计算和剩余提示
        $count_str = str_replace("{}", (3 - $count['record_count']), $this->getTranslation()->_('7137'));
        //提示已经用完
        $alert = $count['record_count'] >= 3 ? $this->getTranslation()->_('7138') : 3 - $count['record_count'];
        //网点补里程大小控制
        $branchMileageArr = UC('vehicleInfo')['branch_mileage'];
        $lang = $this->getTranslation()->_('7140');
        $branch_mileage_msg = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? str_replace( '50',bcdiv($branchMileageArr[$userinfo['organization_id']],1000,0) ,$lang) : $lang;
        $branch_mileage = key_exists($userinfo['organization_id'] ,$branchMileageArr) ? $branchMileageArr[$userinfo['organization_id']] : 50000;
        //当日里程信息和历史申请次数
        $returnData['data'] = $count;
        $returnData['data']['list'] = $res;
        $returnData['data']['count'] = $count_str;
        $returnData['data']['alert'] = $alert;
        $returnData['data']['branch_mileage'] = bcdiv($branch_mileage,1000,0);
        $returnData['data']['branch_mileage_msg'] = $branch_mileage_msg;
        return $this->checkReturn($returnData);
    }

    /**
     * 创建车辆信息 userinfo 里只有 id 和 job_title 如果要用别的 记得在外层新增参数
     * @Access  public
     * @Param   request
     * @Return  array
     * @throws ValidationException
     */
    public function addVehicleInfoS($paramIn = [], $userinfo,$operate_staff_id = '')
    {
        $returnData['data'] = [];

        // 整合入表字段
        $vehicleData = $this->filterVehicleData($paramIn, $userinfo);

        //查询验证是否有数据
        $vehicleInfo = $this->vehicle->getVehicleInfoR($vehicleData['uid']);
        if (!empty($vehicleInfo) && ($vehicleInfo['approval_status'] == VehicleInfoEnums::APPROVAL_PENDING_CODE)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0006'));
        }

        // 验证车牌号 是否 与 其他在职人的车牌号重复
        if ($this->checkPlateNumberIsExist($vehicleData['plate_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0001'));
        }

        // 验证发动机号 是否 与 其他在职人的发动机号重复
        if ($this->checkEngineNoIsExist($vehicleData['engine_number'], $userinfo['id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0003'));
        }

        // 验证开始用车日期, 不得早于入职日期
        $hire_date = $this->getStaffHireDate($userinfo['id']);
        if ($vehicleData['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE && !empty($vehicleData['vehicle_start_date']) && $vehicleData['vehicle_start_date'] < $hire_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0007'));
        }

        if (empty($vehicleInfo)) {
            $vehicleData['formal_data'] = '';
            $vehicleData['creator_id'] = $userinfo['id'];
            $vehicleData['create_channel'] = VehicleInfoEnums::VEHICLE_ADD_CHANNEL_KIT;

            $res = $this->vehicle->addVehicleInfoR($vehicleData);
        } else {
            $res = $this->vehicle->updateVehicleInfo($vehicleData);
        }

        if ($res) {
            // 车辆信息日志
            $vehicleLogData = [];
            $vehicleLogData['staff_id'] = $vehicleData['uid'];
            $vehicleLogData['operate_staff_id'] = $operate_staff_id ?? $vehicleData['uid'];
            $vehicleLogData['text'] = json_encode($vehicleData, JSON_UNESCAPED_UNICODE);
            $this->vehicle->addVehicleInfoLog($vehicleLogData);

            $returnData['data'] = $vehicleData['uid'];
            return $this->checkReturn($returnData);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4101'));
        }
    }

    /**
     * 格式化车辆信息详情
     * @param array $vehicle_info
     * @param array $paramIn
     * @return array|mixed
     */
    protected function handleVehicleInfo(array $vehicle_info, array $paramIn)
    {
        if (empty($paramIn)) {
            return [];
        }

        // 为空, 补充默认字段 及 默认值
        if (empty($vehicle_info)) {
            // 车辆品牌及车辆型号、购买日期、油卡公司 van
            $vehicle_info['vehicle_brand'] = '';
            $vehicle_info['vehicle_brand_text'] = '';
            $vehicle_info['vehicle_model'] = '';
            $vehicle_info['vehicle_model_text'] = '';
            $vehicle_info['buy_date'] = null;
            $vehicle_info['oil_number'] = '';
            $vehicle_info['oil_company'] = '';

            // 车辆照片/机动车登记证 &&
            $vehicle_info['vehicle_img'] = '';
            $vehicle_info['registration_certificate_img'] = '';

            // 车辆保险 &&
            $vehicle_info['insurance_policy_number'] = '';
            $vehicle_info['insurance_start_date'] = null;
            $vehicle_info['insurance_end_date'] = null;

            // 车辆税 &&
            $vehicle_info['vehicle_tax_expiration_date'] = null;
            $vehicle_info['vehicle_tax_certificate_img'] = '';

            // 驾照信息 &&
            $vehicle_info['driver_license_type'] = [];
            $vehicle_info['driver_license_type_other_text'] = '';
            $vehicle_info['driver_license_start_date'] = null;
            $vehicle_info['driver_license_end_date'] = null;
            $vehicle_info['driving_licence_img'] = '';
        } else {
            // 删除无需字段
            unset($vehicle_info['id']);
            unset($vehicle_info['deleted']);
            unset($vehicle_info['money']);
            unset($vehicle_info['is_open']);
            unset($vehicle_info['open_date']);
            unset($vehicle_info['updated_at']);
            unset($vehicle_info['created_at']);
            unset($vehicle_info['is_cut_money']);
            unset($vehicle_info['balance']);
            unset($vehicle_info['unit_price']);
            unset($vehicle_info['approval_staff_id']);
            unset($vehicle_info['approval_time']);
            unset($vehicle_info['creator_id']);
            unset($vehicle_info['editor_id']);
            unset($vehicle_info['create_channel']);
        }

        // 车辆类型, 职位优先
        $vehicle_info['vehicle_type'] = VehicleInfoEnums::JOB_VEHICLE_TYPE_REL_CODE[$paramIn['job_title']];
        $vehicle_info['vehicle_type_label'] = VehicleInfoEnums::VEHICLE_TYPE_ITEM[$vehicle_info['vehicle_type']];

        // 车辆来源: 同步fbi-hr_is数据
        if (empty($vehicle_info['vehicle_source'])) {
            $hr_staff_info = HrStaffInfoServer::getUserInfoByStaffInfoId($paramIn['id'], 'vehicle_source, vehicle_use_date');
            $hr_staff_info = $hr_staff_info ? $hr_staff_info->toArray() : [];

            $vehicle_info['vehicle_source'] = $hr_staff_info['vehicle_source'] ?? VehicleInfoEnums::VEHICLE_SOURCE_PERSONAL_CODE;
            $vehicle_info['vehicle_start_date'] = $hr_staff_info['vehicle_use_date'] ?? null;
        }

        $vehicle_info['vehicle_source_label'] = $vehicle_info['vehicle_source'] ? $this->getTranslation()->_(VehicleInfoEnums::VEHICLE_SOURCE_ITEM[$vehicle_info['vehicle_source']]) : '';

        // 车牌号 hr-is取默认值, 如没有，则再从whr_is取默认值
        if (empty($vehicle_info['plate_number'])) {
            $vehicle_info['plate_number'] = (new StaffRepository())->getAvatar($paramIn['id'], 'CAR_NO');
        }

        // 上牌地点/发动机号码/驾照号码 whr-is取默认值
        if (
            empty($vehicle_info['plate_number'])
        ||
            empty($vehicle_info['license_location'])
        ||
            empty($vehicle_info['engine_number'])
        ||
            empty($vehicle_info['driver_license_number'])
        ) {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['entry' => HrEntryModel::class]);
            $builder->innerJoin(HrEconomyAbilityModel::class,'entry.resume_id = hr.resume_id', 'hr');
            $builder->where('entry.staff_id = :staff_id:', ['staff_id' => $paramIn['id']]);
            $builder->columns([
                'hr.car_number',// 车牌号
                'hr.driver_number',//驾照号
                'hr.place_cards',//上牌地点
                'hr.car_engine_number',//发动机号
            ]);
            $win_staff_info = $builder->getQuery()->getSingleResult();
            if (!empty($win_staff_info)) {
                $vehicle_info['plate_number'] = !empty($vehicle_info['plate_number']) ? $vehicle_info['plate_number'] : $win_staff_info->car_number ?? '';
                $vehicle_info['license_location'] = !empty($vehicle_info['license_location']) ? $vehicle_info['license_location'] : $win_staff_info->place_cards ?? '';
                $vehicle_info['engine_number'] = !empty($vehicle_info['engine_number']) ? $vehicle_info['engine_number'] : $win_staff_info->car_engine_number ?? '';
                $vehicle_info['driver_license_number'] = !empty($vehicle_info['driver_license_number']) ? $vehicle_info['driver_license_number'] : $win_staff_info->driver_number ?? '';
            }
        }

        $vehicle_setting =  VehicleInfoEnums::CONFIG_VEHICLE_INFO;

        // 油卡企业
        $vehicle_info['oil_company_label'] = '';
        if (!empty($vehicle_info['oil_company'])) {
            $oil_company_conf = array_column($vehicle_setting['oil_company'], 'label', 'value');
            $vehicle_info['oil_company_label'] = $oil_company_conf[$vehicle_info['oil_company']] ?? '';
        }

        // 车辆品牌/车辆型号
        $vehicle_info['vehicle_brand_label'] = '';
        $vehicle_info['vehicle_model_label'] = '';
        if ($vehicle_info['vehicle_type'] == VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE && !empty($vehicle_info['vehicle_brand'])) {
            $vehicle_brand_conf = array_column($vehicle_setting['vehicle_brand'], null, 'value');
            $vehicle_model_conf = array_column($vehicle_brand_conf[$vehicle_info['vehicle_brand']]['data'], 'label', 'value');

            $vehicle_info['vehicle_brand_label'] = $vehicle_brand_conf[$vehicle_info['vehicle_brand']]['label'] ?? '';
            $vehicle_info['vehicle_model_label'] = !empty($vehicle_info['vehicle_model']) ? $vehicle_model_conf[$vehicle_info['vehicle_model']] : '';
        }

        // 车型
        $vehicle_info['vehicle_size'] = $vehicle_info['vehicle_size'] ?? '';
        $vehicle_info['vehicle_size_label'] = self::getVehicleSize(true)[$vehicle_info['vehicle_size']]?? '';

        // 油类型
        $vehicle_info['oil_type'] = $vehicle_info['oil_type'] ?? '';
        $vehicle_info['oil_type_label'] = $vehicle_info['oil_type'] ? $vehicle_setting['oil_type'][$vehicle_info['oil_type']] : '';

        // 驾照图片(两张) &&
        $vehicle_info['driving_licence_img_item'] = [];
        if (!empty($vehicle_info['driving_licence_img'])) {
            $vehicle_info['driving_licence_img_item'] = explode("\n", $vehicle_info['driving_licence_img']);
        }
        //driver_license_type_label 不再返回了
        $vehicle_info['driver_license_type'] = !empty($vehicle_info['driver_license_type'] )?  array_map('intval',explode(',',$vehicle_info['driver_license_type'])) : [] ;

        // 审核状态
        $vehicle_info['approval_status'] = $vehicle_info['approval_status'] ?? VehicleInfoEnums::APPROVAL_UN_SUBMITTED_CODE;

        // 员工入职日期
        $vehicle_info['staff_hire_date'] = $this->getStaffHireDate($paramIn['id']);

        return $vehicle_info;
    }

    /**
     * 提取不同职位需入库的字段
     * @param array $vehicle_data
     * @param array $user_info
     * @return array $data
     */
    protected function filterVehicleData(array $vehicle_data, array $user_info)
    {
        // 公共字段
        $data = [
            'uid' => $user_info['id'],
            'vehicle_source' => $vehicle_data['vehicle_source'],
            'plate_number' => $vehicle_data['plate_number'],
            'license_location' => $vehicle_data['license_location'],
            'registration_certificate_img' => $vehicle_data['registration_certificate_img'],
            'vehicle_img' => $vehicle_data['vehicle_img'],
            'insurance_policy_number' => $vehicle_data['insurance_policy_number'],
            'insurance_start_date' => $vehicle_data['insurance_start_date'],
            'insurance_end_date' => $vehicle_data['insurance_end_date'],
            'vehicle_tax_expiration_date' => $vehicle_data['vehicle_tax_expiration_date'],
            'vehicle_tax_certificate_img' => $vehicle_data['vehicle_tax_certificate_img'],
            'driver_license_type' => implode(',',$vehicle_data['driver_license_type']),
            'driver_license_number' => $vehicle_data['driver_license_number'],
            'driver_license_start_date' => $vehicle_data['driver_license_start_date'],
            'driver_license_end_date' => $vehicle_data['driver_license_end_date'],
            'driving_licence_img' => implode("\n", $vehicle_data['driving_licence_img_item']),
            'vehicle_type' => VehicleInfoEnums::VEHICLE_TYPE_BIKE_CODE,
            'engine_number' => $vehicle_data['engine_number'],

            // 重置审核信息
            'approval_status' => VehicleInfoEnums::APPROVAL_PENDING_CODE,
            'approval_staff_id' => '',
            'approval_time' => null,
            'approval_remark' => '',

            'editor_id' => $user_info['id'],
        ];

        // 用车开始日期
        if ($vehicle_data['vehicle_source'] == VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
            $data['vehicle_start_date'] = $vehicle_data['vehicle_start_date'];
        }

        // van 特有字段
        if (in_array($user_info['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
            $data['vehicle_brand'] = $vehicle_data['vehicle_brand'];
            $data['vehicle_brand_text'] = $vehicle_data['vehicle_brand'] != 99 ? '' : $vehicle_data['vehicle_brand_text'] ?? '';
            $data['vehicle_model'] = $vehicle_data['vehicle_model'];
            $data['vehicle_model_text'] = $vehicle_data['vehicle_model'] != 100 ? '' : $vehicle_data['vehicle_model_text'] ?? '';
            $data['vehicle_size'] = $vehicle_data['vehicle_size'];
            $data['buy_date'] = $vehicle_data['buy_date'];
            $data['oil_type'] = $vehicle_data['oil_type'];
            $data['vehicle_type'] = VehicleInfoEnums::VEHICLE_TYPE_VAN_CODE;
        }

        return $data;
    }

    /**
     * @description 获取详情
     * @param int $auditId
     * @param $user
     * @param $comeFrom
     * @return array
     * @throws BusinessException
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        //获取详情数据
        $result = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($result)) {
            throw new BusinessException('invalid data');
        }
        $result     = $result->toArray();
        $detailInfo = $this->detail($result['serial_no']);

        //组织详情数据
        $detailLists = [
            'apply_parson'       => $detailInfo['apply_parson'],
            'apply_department'   => $detailInfo['apply_department'],
            'mileage_date'       => $detailInfo['mileage_date'],
            'start_kilometres'   => $detailInfo['start_kilometres'],
            'started_img'        => $detailInfo['started_img'],
            'end_kilometres'     => $detailInfo['end_kilometres'],
            'end_img'            => $detailInfo['end_img'],
        ];
        $returnData['data']['detail'] = $this->format($detailLists);

        $auditListRepo = new AuditlistRepository($this->lang, $this->timeZone);
        $add_hour  = $this->config->application->add_hour;
        $data      = [
            'title'       => $auditListRepo->getAudityType(AuditListEnums::APPROVAL_TYPE_VEHICLE),
            'id'          => $result['id'],
            'staff_id'    => $result['submitter_id'],
            'type'        => AuditListEnums::APPROVAL_TYPE_VEHICLE,
            'created_at'  => date('Y-m-d H:i:s', (strtotime($result['created_at']) + $add_hour * 3600)),
            'updated_at'  => date('Y-m-d H:i:s', (strtotime($result['updated_at']) + $add_hour * 3600)),
            'status'      => $result['state'],
            'status_text' => $auditListRepo->getAuditStatus('10' . $result['status']),
            'serial_no'   => $result['serial_no'] ?? '',
        ];

        //当前用户审批操作按钮
        $returnData['data']['head'] = $data;

        return $returnData;
    }

    /**
     * @description 获取详情
     * @param $serial_no
     * @return array
     */
    public function detail($serial_no): array
    {
        $data['serial_no'] = $serial_no;
        $ac                       = new ApiClient('bi_rpcv2', '', 'approval.get_repair_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        $this->logger->write_log("vehicle detail result" . json_encode($result), 'info');

        return $result['result']['data'] ?? [];
    }

    /**
     * @description 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info = SystemExternalApprovalModel::findFirst($auditId);
        if (!empty($info)) {
            $summary = json_decode($info->summary, true);
        } else {
            $summary = json_decode([], true);
        }
        return $summary;
    }

    /**
     * @description 回调接口
     * @param int $auditId
     * @param int $state
     * @param $extend
     * @param $isFinal
     * @return mixed|void
     * @throws \Exception
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $SystemExternalApprovalModel = SystemExternalApprovalModel::findFirst($auditId);
            if (!$SystemExternalApprovalModel) {
                throw new \Exception('setProperty  没有找到数据  '.$auditId);
            }
            $SystemExternalApprovalModel->state      = $state;
            $SystemExternalApprovalModel->updated_at = gmdate('Y-m-d H:i:s', time());
            $SystemExternalApprovalModel->save();

            //向bi同步数据
            $data = [
                'serial_no'              => $SystemExternalApprovalModel->serial_no,
                'status'                 => $state,
                'approval_staff_info_id' => $extend['staff_id'],
            ];
            $ac   = new ApiClient('bi_rpcv2', '', 'approval.approval_repair_mileage', $this->lang);
            $ac->setParams($data);
            $result = $ac->execute();

            $this->logger->write_log("setProperty result" . json_encode($result), 'info');
        }
        return true;
    }

    /**
     * @description  获取审批条件所必须的数据
     * @param $auditId
     * @param $user
     * @param $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = SystemExternalApprovalModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return [];
        }
        $parameters = json_decode($auditInfo->approval_parameters, true);
        return $parameters ?? [];
    }

    /**
     * 里程信息【Van快递员新增“修改里程表数”使用】
     * @param array $paramIn
     * @param array $userinfo
     * @return array
     */
    public function getMileageModifyS($paramIn = [], $userinfo = []): array
    {
        //向bi同步数据
        $data = [
            'staff_info_id' => $userinfo['id'],
            'mileage_date'  => $paramIn['mileage_date'],
        ];
        $ac   = new ApiClient('bi_rpcv2', '', 'mileage.get_modify_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        if (empty($result) || $result['code'] != 1) {
            return $this->checkReturn([]);
        }

        $returnData['data']         = $result['data']['record_count'];
        $returnData['data']['list'] = $result['data']['list'];

        return $this->checkReturn($returnData);
    }
}
