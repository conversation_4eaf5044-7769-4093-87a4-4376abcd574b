<?php
namespace FlashExpress\bi\App\Modules\La\Tasks;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Server\ProbationServer as ProbationServerAlias;
use FlashExpress\bi\App\Server\WorkflowServer;
use Phalcon\Db;
use FlashExpress\bi\App\Modules\La\Server\ProbationServer;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

use Exception;

class ProbationTask extends  \BaseTask
{



    public $country_code = 'th';
    public $add_hour = '7';  //误差时间


    public function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
        $this->country_code = strtolower(env("country_code",'th'));
	    $this->add_hour = $this->getDI()['config']['application']['add_hour'];
	    set_time_limit(60*30); //执行时间 60 分钟
	    $memory_limit = 1024; //内存限制  mb
	    ini_set('memory_limit', $memory_limit.'M');

    }


    /**
     * 第一阶段评估插入
     * 每天执行，
     * @param array $params
     */

    public function firstAction(array $params)
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
	
	
	    //第二阶段转正评估
	    $this->secondAction($params);
	
	
	    //处理快要超时的
	    $this->deal_deadlineAction();
	
	
	    //发送转正通知
	    $this->send_msg_to_staffAction($params);
	
	
	    //获得考勤每天执行

	    $this->get_attendanceAction($params);
	    

        $bll = new ProbationServer($this->lang, $this->add_hour);
        //获取时间
        if (empty($start)) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        }

        $evaluate_day = $bll->evaluate_day;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day[1]);                          //第一阶段
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day[1] - 1);
        $staffs          = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, 0, $bll->job_grade_exceed,true); // 查询 14 级及以下的人
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以下的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs,'staff_info_id')));
        //查询 16 级以上的数据
        $evaluate_day_exceed = $bll->evaluate_day_exceed;
        $hire_date_begin = $this->getDateByDays($start, $evaluate_day_exceed[1]);
        $hire_date_end   = $this->getDateByDays($start, $evaluate_day_exceed[1] - 1);
        $staffs_two      = $bll->getStaffs($hire_date_begin, $hire_date_end, 0, $bll->job_grade_exceed,'',true);    //查询 14 级以上的人
        $staffs          = array_merge($staffs, $staffs_two);                                         //合并两个数据组
//        $this->myLogger("查询第一阶段 {$bll->job_grade_exceed} 级以上的数据" . $hire_date_begin . '===' . $hire_date_end . ' '. implode(',',array_column($staffs_two,'staff_info_id')));

        if (empty($staffs)) {
            $this->myLogger('第一阶段没有找到符合的数据');
            return;
        }

        $db = $this->getDI()->get("db");
        $staffIds = array_column($staffs,'staff_info_id');
        $existStaffIds = $bll->getExistHrProbationByStaffIds($staffIds);
        foreach ($staffs as $staff) {
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_FIRST &&
                !empty($staff['first_audit_status']) &&
                $staff['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null");
                continue;
            }
            if (in_array($staff['staff_info_id'],$existStaffIds)) {
                $this->myLogger('staff  ' . $staff['staff_info_id'] . ' 已经存在');
                continue;
            }
            $this->myLogger("firstAction 第一阶段评估插入 需要执行的数据 staff= " . $staff['staff_info_id']);
            try {
                $db->begin();
                //判断此人什么等级
                $formal_days = (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ?  $bll->formal_days : $bll->formal_days_exceed;
                //转正时间
                $formal_at = $this->getDateByDays($staff['hire_date'],$formal_days, 1);
                //每阶段评估时间
                $evaluate_time =(int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                //获取第一阶段评估开始和结束时间
                $first_evaluate_start = $start;          //第一阶段开始
                //第一阶段结束日期
                $first_check_days =  (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ?  $bll->first_check_days : $bll->first_check_days_exceed;
                $first_check_days =  (int)$staff['job_title_grade_v2'] == $bll->special_job_grade ? $first_check_days - $bll->special_job_day : $first_check_days;
                $first_evaluate_end = $this->getDateByDays($staff['hire_date'], $first_check_days,1);

                //获取第二阶段评估开始和结束时间
                $second_evaluate_start_day =  (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ?  $evaluate_day[2] : $evaluate_day_exceed[2];
                $second_evaluate_start = $this->getDateByDays($staff['hire_date'], $second_evaluate_start_day,1);          //第二阶段开始
                $second_evaluate_end_day =  (int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ?   $bll->second_check_days : $bll->second_check_days_exceed;

                $second_evaluate_end_day =  (int)$staff['job_title_grade_v2'] == $bll->special_job_grade ? $second_evaluate_end_day - $bll->special_job_day : $second_evaluate_end_day;

                $second_evaluate_end = $this->getDateByDays($staff['hire_date'], $second_evaluate_end_day,1);    //第二阶段结束


                $res = $db->insertAsDict("hr_probation",
                    [
                        'staff_info_id'         => $staff['staff_info_id'],
                        'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_RUN,
                        'created_at'            => $start,
                        'formal_at'             => $formal_at,
                        'first_evaluate_start'  => $first_evaluate_start,
                        'first_evaluate_end'    => $first_evaluate_end,
                        'second_evaluate_start' => $second_evaluate_start,
                        'second_evaluate_end'   => $second_evaluate_end,
                    ]);
                if (!$res) {
                    throw new Exception("hr_probation insert fail");
                }
                $probation_id = $db->lastInsertId();
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $probation_id,
                    "staff_info_id" => $staff['staff_info_id'],
                    'audit_id' => $staff['manager_id'],
                    'tpl_id' => $bll->getTplIdByJobTitleGradeV2($staff['job_title_grade_v2']),
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    //第一次上级评审截止3天以后
                    'deadline_at' => $this->getDateByDays($start, $evaluate_time['1']['1'] ?? 3, 1),
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'version'=>$bll->version,

                ]);
                if (!$res) {
                    throw new Exception("hr_probation_audit insert fail");
                }
                $db->commit();
                //发送push
	            
                $pussh = $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);
                if($pussh){
	                $this->myLogger("发送 push 消息成功 staff_id=" . $staff['manager_id'], "info");
                }else{
	                $this->myLogger("发送 push 失败 staff_id=" . $staff['manager_id'], "info");
                }
                
            } catch (\Exception $e) {
                $db->rollback();
                //实习只进入一直，大概就是有重复进入的报错。改成info
                $this->myLogger("staff=" . $staff['staff_info_id'] . " 已经存在了 ,message=" . $e->getMessage(), "info");
            }
        }


        $this->myLogger("probation staffs first" . $hire_date_begin . "===" . $hire_date_end . "======end");


    }

    /**
     * 第二阶段评估插入
     * 每天执行，
     * @param array $params
     */
    public function secondAction(array $params=[])
    {
        $start = "";
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }

        $bll = new ProbationServer($this->lang, $this->add_hour);
        if (empty($start)) {
            $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        }
        $second_evaluate_start_end = date("Y-m-d", strtotime("-1 year", strtotime($start)));
        
        // select h.staff_info_id,h.second_evaluate_start,his.manger as manager_id,his.job_title_grade_v2 from hr_probation as h left join hr_staff_info as his on his.staff_info_id = h.staff_info_id where h.second_evaluate_start  =  '2022-10-28' and h.cur_level = 1 and h.status = 1 and h.is_system=0 and his.state!= 2 and his.is_sub_staff = 0
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => HrProbationModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'his.staff_info_id = h.staff_info_id', 'his');
        $builder->columns('h.staff_info_id,h.second_evaluate_start,his.manger as manager_id,his.job_title_grade_v2,h.second_audit_status,h.cur_level');
        $builder->Where('h.second_evaluate_start  <=  :second_evaluate_start: and h.second_evaluate_start  >=  :second_evaluate_start_end: and h.cur_level = :cur_level: and h.status = :status: and h.is_system=0 and his.state!= :dimission: and his.is_sub_staff = :is_sub_staff: ',
            ['second_evaluate_start' => $start,'second_evaluate_start_end' => $second_evaluate_start_end,'cur_level'=>$bll::CUR_LEVEL_FIRST,'status'=>$bll::STATUS_PROBATION,'dimission'=>enums::$service_status['dimission'],'is_sub_staff'=>HrStaffInfoModel::IS_SUB_STAFF_0]);
        $staff_info_list = $builder->getQuery()->execute()->toArray();


        if(empty($staff_info_list)){
            $this->myLogger('secondAction 第二阶段没有数据! day =>'.$start);
        }



        $db = $this->getDI()->get("db");

        foreach ($staff_info_list as $staff) {
            if (
                !empty($staff['cur_level']) &&
                $staff['cur_level'] == $bll::CUR_LEVEL_SECOND &&
                !empty($staff['second_audit_status']) &&
                $staff['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT
            ){
                // 已执行过的数据
                continue;
            }
            
            if (empty($staff['manager_id'])) {
                $this->myLogger("staff= " . $staff['staff_info_id'] . " manager_id is null");
                continue;
            }

            $item = $bll->getLastestProbationAudit($staff['staff_info_id']);

            //最新的item为空||或者不是第一次评审||或者还是待处理
            if (empty($item) || $item['cur_level'] != 1 || $item['audit_status'] == 1) {
//                $this->myLogger("staff= " . $staff['staff_info_id'] . " 第一次评审没有完成或者已经进入第二次评审==".$item['cur_level']."==".$item['audit_status']);
                continue;
            }
            $this->myLogger("secondAction 需要执行的数据 staff= " . $staff['staff_info_id']);

            try {
                //每阶段评估时间
                $evaluate_time =(int)$staff['job_title_grade_v2'] <= $bll->job_grade_exceed ? $bll->duration_day : $bll->duration_day_exceed;

                $db->begin();
                $res = $db->insertAsDict("hr_probation_audit", [
                    "probation_id" => $item['probation_id'],
                    "staff_info_id" => $staff['staff_info_id'],
                    'cur_level' => $bll::CUR_LEVEL_SECOND,
                    'tpl_id' => $item['tpl_id'],
                    'score' => $item['score'],
                    'audit_id' => $staff['manager_id'],
                    'created_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'deadline_at' => $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 3, 1),
                    //第二次上级评审截止6天以后，75天0点，80天24：00，改成85天24:00,86
                    'second_deadline_at'=> $this->getDateByDays($start, $evaluate_time['2']['1'] ?? 3, 1),
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    'version'=>$bll->version,

                ]);
                if (!$res) {
                    throw new Exception("hr_probation_audit insert fail");
                }

                $res = $db->updateAsDict("hr_probation", [
                    "cur_level" =>  $bll::CUR_LEVEL_SECOND,
                    "is_active" => HrProbationModel::IS_ACTIVE_DEFAULT,
                    'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
                ], ["conditions" => "staff_info_id=?",'bind' => [$staff['staff_info_id']]]);
                if (!$res) {
                    throw new Exception("hr_probation update fail");
                }

                $db->commit();

                //发送push
	            $pussh = $bll->push_notice_higher($staff['manager_id'],$staff['staff_info_id']);
	
	            if($pussh){
		            $this->myLogger("发送 push 消息成功 staff_id=" . $staff['manager_id'], "info");
	            }else{
		            $this->myLogger("发送 push 失败 staff_id=" . $staff['manager_id'], "info");
	            }
	            
            } catch (\Exception $e) {
                $db->rollback();
                $this->myLogger("secondAction staff=" . $staff['staff_info_id'] . " second insert fail,message=" . $e->getMessage(), "error");
            }
        }

        $this->myLogger("secondAction  第二阶段 day => {$start} ======end");
    }


    /**
     * 处理快到截止日期的员工，如7.26日执行，>=7.26 and <7.27
     * 每天凌晨执行
     */
    //处理 快到截止日期的员工，每天凌晨，执行一次，判断截止日期，到今天的。
    public function deal_deadlineAction()
    {

        $bll = new ProbationServer($this->lang, $this->add_hour);
        $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        $end = $this->getDateByDays($start, 1, 1);


        $staffs = $bll->getStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("deal_deadlineAction  转正评估截止 数据无 between" . $start . "===" . $end . "=====end");
            return;
        }

        $staffIds = array_column($staffs, "id");
        $strStaffIds = implode(",", $staffIds);

        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            foreach ($staffs as $hrProbationAudit) {
                if ($hrProbationAudit['cur_level'] == ProbationServerAlias::CUR_LEVEL_FIRST) {
                    $updateData = [
                        'first_audit_status' => HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT,
                        'updated_at'         =>  gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                } else {
                    $updateData = [
                        'second_audit_status' => HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT,
                        'updated_at'          => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
                    ];
                }
                $updateData['is_active'] = HrProbationModel::IS_ACTIVE_DEFAULT;
                $db->updateAsDict("hr_probation", $updateData,
                    ["conditions" => 'id =' . $hrProbationAudit['probation_id']]);
            }
            $res = $db->updateAsDict("hr_probation_audit", [
                "audit_status" => 3,
                'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600)
            ], ["conditions" => "id in (" . $strStaffIds . ")"]);
            if (!$res) {
                throw new Exception("hr_probation_audit update fail");
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->myLogger("deal_deadlineAction staff=" . $strStaffIds . "deal_deadline fail,message=" . $e->getMessage(), "error");
        }

        $this->myLogger("deal_deadlineAction 转正评估截止  between" . $start . "===" . $end . " ======end");
    }


    /**
     * 第二阶段的提醒60,61（60,61）两天，每隔5小时推送一次
     * 每天7:00,12:00,17:00执行
     */
    public function send_msg_to_higherAction()
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);

        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多3天（比开始时间多2天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于

        $end = $this->getDateByDays($start, 2, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("send_msg_to_higherAction 第二阶段提醒 数据无 on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        $auditIdArr = array_column($staffs, "audit_id");
        $auditIdArr = array_unique($auditIdArr);

        //您有即将超时的员工转正评估未处理，请尽快前往backyard-转正评估进行评估，若未在规定时间内完成评估，可能存在因失职而收到警告书的风险。

        /**
         * "{"locale":"en","data":{"staff_info_id":"17074","src":"1","message_title":"需要push的标题","message_content"："需要push的内容","message_scheme":"消息的scheme"}}"
         */

        //$approval_html_url  = env('probation_html_url','flashbackyard://fe/html?url=');
        //$approval_index_url= env('probation_index_url','http://192.168.0.222:90/#/EvaluateIndex');
        $approval_html_url = $bll->get_setting_by_code('probation_html_url');
        $approval_index_url = $bll->get_setting_by_code('probation_index_url');


        foreach ($auditIdArr as $k => $v) {
            $data = [];
            $data['staff_info_id'] = $v;
            $data['src'] = 'backyard';
            $data['message_title'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_notice');
            $data['message_content'] = $bll->getMsgTemplateByUserId($v,'hr_probation_field_msg_to_higher');
            $data['message_scheme'] = $approval_html_url . urlencode($approval_index_url);
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higherAction_pushMessage params:' . json_encode($data), 'info');
	        $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
	        $ret->setParams($data);
	        $_data = $ret->execute();
	        $this->getDI()->get('logger')->write_log("send_msg_to_higherAction_pushMessage:pushMessage-return- " . json_encode($_data), 'info');
	        if (!$_data['result']) {
                $this->myLogger("send_msg_to_higherAction 第二阶段提醒 给上级发送 higher:staff_info_id=" . $v . "  发送push失败", "info");
            }else{
		        $this->myLogger("send_msg_to_higherAction 第二阶段提醒 给上级发送 higher:staff_info_id=" . $v . "  发送push成功", "info");
	        }
        }

        $this->myLogger("send_msg_to_higherAction 第二阶段提醒  between" . $start . "===" . $end . "======end");
    }


    /**
     * 第二阶段的提醒HRBP,85天，（80根据second_deadline_at判断)，
     * 每天7:00执行
     */
    public function send_msg_to_hrbpAction($params)
    {


        $bll = new ProbationServer($this->lang, $this->add_hour);

        if (empty($params[0])) {
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        } else {
            $start = $params[0].' 00:00:00';
        }
        //开始时间=截止日期比当前时间多1天
        //结束时间=截止日期比当期时间多2天（比开始时间多一天）
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<


        //找第二次评审
        $staffs = $bll->getSecondStaffsByDeadlineDate($start, $end);
        if (empty($staffs)) {
            $this->myLogger("send_msg_to_hrbpAction 第二阶段提醒 hrbp probation staffs on deadline " . $start . "=====end");
            return;
        }
        //被评估人
        $evaluatedPerson = array_column($staffs, "staff_info_id");
        $evaluatedPerson = array_unique($evaluatedPerson);

        $hrbpMap = $bll->findHRBP($evaluatedPerson);
        if (empty($hrbpMap)) {
            $this->myLogger("send_msg_to_hrbpAction 第二阶段提醒 hrbp 找不到 hrbp  from staff id in (" . implode(",", $evaluatedPerson) . ')');
            return;
        }
        $languageMap =  (new \FlashExpress\bi\App\Server\StaffServer())->getBatchStaffLanguage(array_keys($hrbpMap));
        foreach ($hrbpMap as $hrbpId => $staffIds) {
            $this->myLogger(json_encode(['hrbp'=>$hrbpId,'被评估人'=>$staffIds],JSON_UNESCAPED_UNICODE));
            $bll->sendPushMessageToHrbp($languageMap[$hrbpId],$hrbpId,$staffIds);
        }

        //根据网点，部门对应出HRBP的关系
        //您负责的区域员工有转正评估未完成，请尽快提醒该员工上级进行转正评估，详情查看FBI-员工试用期管理
        $this->myLogger("send_msg_to_hrbpAction send msg to hrbp:probation staffs on deadline " . $start . "=====end");
    }

    /**
     * 发送审核通过给已通过的员工，并修改状态成，已转正
     * 每天执行
     */
    public function send_msg_to_staffAction($params)
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        //今天
        $start = gmdate("Y-m-d", time() + ( $this->add_hour)*3600);
        if (isset($params[0])) {
            $start = $params[0];
            if (strtotime($start) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
        $end = $this->getDateByDays($start, 1, 1);//<

        $staffs = $bll->getStaffsByFormalDate($start, $end, [ProbationServer::STATUS_PASS, ProbationServer::STATUS_NOT_PASS, ProbationServer::STATUS_FORMAL],true);
        $staffs = array_filter($staffs, function ($v) {
            return !(
                $v['status'] == HrProbationModel::STATUS_FORMAL &&
                $v['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE &&
                $v['second_status'] == HrProbationModel::SECOND_STATUS_PASS
            );
        });
        if (empty($staffs)) {
            $this->myLogger("send_msg_to_staffAction 没有找到数据 send msg to staff:no probation staffs on formal_at between" . $start . "===" . $end . "=====end");
            return;
        }

        $WorkflowServer = (new WorkflowServer($this->lang, $this->timezone));

        foreach ($staffs as $staff) {
            if ($staff['formal_at'] != $start){
                // 保持原发送通知逻辑
                continue;
            }
            /*$html = <<<EOF
    <p>เรียน  {$staff['name']}</p>
    <p>เรื่อง แจ้งผลการทดลองงาน</p><br/>
    <p style='text-indent:2em'>ตามที่บริษัทฯได้รับท่านเข้าปฏิบัติงานทดลองงานนั้น บริษัทฯ ขอแสดงความยินดีที่ท่าน มีผลการปฏิบัติงานเป็นที่น่าพอใจ และผ่านการทดลองงาน มีผลตั้งแต่วันที่{$staff['formal_at']}เป็นต้นไป  ทั้งนี้ท่านสามารถใช้สิทธิการเบิกสวัสดิการพนักงานและสิทธิประโยชน์อื่น ๆ ตามที่บริษัทกำหนด  โดยท่านสามารถขอทราบรายละเอียดเพิ่มเติมได้ที่ฝ่าย HR</p>
    <p style='text-indent:2em'>จึงเรียนมาเพื่อโปรดทราบ</p><br/>
    <p style="text-indent: 18em">ขอแสดงความนับถือ</p>  
    <p style="text-indent: 18em">ฝ่ายบริหารทรัพยากรบุคคล</p>                                                                         
    <p style="text-indent: 18em">บริษัท แฟลช เอ็กซ์เพรส จำกัด</p>
EOF;*/

            $staffIds = $WorkflowServer->findHRBP($staff['node_department_id'], ["store_id" => $staff['sys_store_id']]);
            $staffIds = array_merge(explode(",", $staffIds), explode(",", $WorkflowServer->findJurisdictionAreaStaffIds($staff['node_department_id'], ["store_id" => $staff['sys_store_id']])));
            $staffIds = array_merge($staffIds, [$staff['staff_info_id'], $staff['manger']]);

            foreach ($staffIds as $staffId) {
                if ($staff['status'] == ProbationServer::STATUS_PASS || $staff['status'] == ProbationServer::STATUS_FORMAL) {
                    // 通过
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staffId,"hr_probation_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                            'formal_at'=>$staff['formal_at'],
                        ]) . "</div>");
                } else {
                    // 不通过
                    $html = addslashes("<div style='font-size: 30px'>" . $bll->getMsgTemplateByUserId($staffId,"hr_probation_not_passed_msg",[
                            'name_id' => $staff['name'] . '/' . $staff['staff_info_id'],
                            'department' => $staff['department_name'],
                            'job_name' => $staff['job_name'],
                            'store_name' => $staff['sys_store_id'] == -1 ? "Head Office" : $staff['store_name'],
                            'name' => $staff['name'],
                        ]) . "</div>");
                }
                $id = time() . $staffId . rand(1000000, 9999999);
                $param['staff_users'] = [$staffId]; //数组 多个员工id
                $param['message_title'] = $bll->getMsgTemplateByUserId($staffId,'hr_probation_field_msg_to_staff_title');
                $param['message_content'] = $html;
                $param['staff_info_ids_str'] = $staffId;
                $param['id'] = $id;
                $param['category'] = -1;

                $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-param:' . json_encode($param), 'info');
                $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                $bi_rpc->setParams($param);
                $res = $bi_rpc->execute();
                $this->getDI()->get('logger')->write_log('send_msg_to_staffAction-result:' . json_encode($res), 'info');
                if ($res && $res['result']['code'] == 1) {
                    $kitId    = $res['result']['data'][0];
                    $this->myLogger('send_msg_to_staffAction message_backyard  写入message成功' . $staffId." message_id".$kitId, 'info');
                }else{
                    $this->myLogger('send_msg_to_staffAction message_backyard  写入message失败' . $staffId, 'error');
                }
            }


        }

        foreach ($staffs as $k => $staff) {
            if ($staff['status'] != ProbationServer::STATUS_PASS) {
                unset($staffs[$k]);
            }
            //这里是记录转正日志
            $bll->putFormalLog(-1,$staff['staff_info_id'],ProbationServer::STATUS_PASS,ProbationServer::STATUS_FORMAL);

        }

        $staffIdArr = array_column($staffs, "staff_info_id");
        if ($staffIdArr) {
            $flag = $bll->formalStaffs($staffIdArr);

            if (!$flag) {
                $this->myLogger("send_msg_to_staffAction send msg to staff: formal staff_info_id in (" . implode(",", $staffIdArr) . ")", "error");
                return;
            }

        }

        $this->myLogger("send_msg_to_staffAction send msg to staff: probation staffs on formal_at between" . $start . "===" . $end . "=====end");

    }

    /**
     *获得考勤
     *每天执行
     * 纪律考核
     */
    public function get_attendanceAction($params)
    {
        $bll = new ProbationServer($this->lang, $this->add_hour);

        $day = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
        if (isset($params[0])) {
            $day = $params[0];
            if (strtotime($day) === false) {
                echo '传入的时间格式不对' . PHP_EOL;
                return;
            }
        }
        //第一阶段的
        $first_data = HrProbationModel::find([
            'conditions' => 'first_evaluate_end  = :first_evaluate_end:',
            'bind'       => [
                'first_evaluate_end' => $day,
            ],
            'columns' => 'staff_info_id'
        ])->toArray();

        //第二阶段的
        $second_data = HrProbationModel::find([
            'conditions' => 'second_evaluate_end  = :second_evaluate_end:',
            'bind'       => [
                'second_evaluate_end' => $day,
            ],
            'columns' => 'staff_info_id'
        ])->toArray();



        if (empty($first_data) && empty($second_data)) {
            $this->myLogger("get_attendance 无数据，不用执行=====end");
            return;
        }

        $data = [];

        if (!empty($first_data)) {
            foreach ($first_data as $staff) {
                $tmp = [];
                $tmp['cur_level'] = $bll::CUR_LEVEL_FIRST;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }

        if (!empty($second_data)) {
            foreach ($second_data as $staff) {
                $tmp = [];
                $tmp['cur_level'] = $bll::CUR_LEVEL_SECOND;
                $tmp['staff_info_id'] = $staff['staff_info_id'];
                $data[] = $tmp;
            }
        }


        foreach ($data as $staff) {
            if ($bll->isHaveAttentdance($staff['staff_info_id'], $staff['cur_level'])) {
                continue;
            }
            $flag = $bll->addAttendance($staff);
            if (!$flag) {
                $this->myLogger("get_attendance staffs==add attendance error==" . $staff['staff_info_id'], "error");
            }
        }
        $this->myLogger("get_attendance =====end");
    }

    /*
     * 发送给上级的上级，提示有人没评估
     */
    public function send_msg_to_higher_of_higherAction(){

        $bll = new ProbationServer($this->lang, $this->add_hour);

        $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);


        //截止81天，减一天
        //10-24的时候，找截止时间是25号的
        $start = $this->getDateByDays($start, 1, 1);//大于等于
        $end = $this->getDateByDays($start, 1, 1);//<

        //找第二次评审，待评估的用户发送信息，都是截止日期前两天
        $staffs = $bll->getSecondStaffsBySecondDeadlineDate($start, $end);

        if (empty($staffs)) {
            $this->myLogger("send_msg_to_higher_of_higher:no probation staffs on deadline between" . $start . "===" . $end . "=====end");
            return;
        }

        //员工xxxxx的转正评估，其上级xxxxx尚未进行评估，请及时联系该上级进行评估
     

        foreach ($staffs as $k => $v) {
            if(empty($v['higher_id'])){
                $this->myLogger("send_msg_to_higher_of_higher:staff_info_id=" . $v['staff_info_id'] . "===上级ID=".$v['manager_id']."====没有直线上级", "info");
                continue;
            }


            $staff_info_id = $v['higher_id'];
            $id = time() . $staff_info_id . rand(1000000, 9999999);
            $param['staff_users'] = array($staff_info_id);//数组 多个员工id
            $param['message_title'] = $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_notice');
            $param['message_content'] =  $bll->getMsgTemplateByUserId($v['higher_id'],'hr_probation_field_msg_to_higher_of_higher',$v);
            $param['staff_info_ids_str'] = $staff_info_id;
            $param['id'] = $id;
            $param['category'] = -1;
            
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-param:' . json_encode($param), 'info');
	        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
	        $bi_rpc->setParams($param);
	        $res = $bi_rpc->execute();
	        $this->getDI()->get('logger')->write_log('send_msg_to_higher_of_higherAction-result:' . json_encode($res), 'info');
	        if ($res && $res['result']['code'] == 1) {
		        $kitId    = $res['result']['data'][0];
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message成功' . $staff_info_id." message_id".$kitId, 'info');
	        }else{
		        $this->myLogger('send_msg_to_higher_of_higherAction message_backyard  写入message失败' . $staff_info_id, 'info');
	        }
        }

        $this->myLogger("send_msg_to_higher_of_higher:probation staffs on deadline between" . $start . "===" . $end . "======end");
    }




    public function myLogger($str, $level = "info")
    {
        echo $str . PHP_EOL;
        $now = gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600);
        $this->getDI()->get('logger')->write_log("ProbationTask on " . $now . ":" . $str, $level);
    }



	
	
	
	/**
	 * 获得日期
	 * @param $date
	 * @param $days 40
	 * @param int $flag 0=40天之前，1=40天之后
	 * @return false|string
	 */
	public function getDateByDays($date, $days, $flag = 0)
	{
		
		$default = '-';
		if (!empty($flag)) {
			$default = '+';
		}
		return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
	}
	
	
	


	
	
	/**
	 * @description: 处理历史数据
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/12/1 12:47
	 */
	public function save_log_listAction(array $params){
         $first_days = 23;// 第 23 天发第一阶段
         $first_end = 30;   //第一阶段结束
         $second_days = 53;// 53 天发送第二阶段
         $second_end = 60;  // 第二阶段结束

        //查询出现在有的数据   一共 700 个数据
        $list =HrProbationModel::find()->toArray();
        $staff_info_ids = array_column($list, 'staff_info_id');
        //获取这些人的入职日期
        $staff_info_list = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_info_ids:array})',
                    'bind'       => [
            'staff_info_ids' => $staff_info_ids,
        ],
        ])->toArray();
        $db = $this->getDI()->get('db');

        foreach($staff_info_list as $v ){

            $first_evaluate_start = $this->getDateByDays($v['hire_date'], $first_days,1);          //第一阶段开始
            $first_evaluate_end = $this->getDateByDays($v['hire_date'], $first_end,1);    //第一阶段结束
            $second_evaluate_start = $this->getDateByDays($v['hire_date'], $second_days,1);          //第二阶段开始
            $second_evaluate_end = $this->getDateByDays($v['hire_date'], $second_end,1);    //第二阶段结束

            $info      = $db->updateAsDict("hr_probation",  [
                'first_evaluate_start'  => $first_evaluate_start,
                'first_evaluate_end' => $first_evaluate_end,
                'second_evaluate_start'  => $second_evaluate_start,
                'second_evaluate_end' => $second_evaluate_end,
            ], ['conditions' => 'staff_info_id=?', 'bind' => [$v['staff_info_id']]]);
        }
        $this->myLogger('save_log_listAction:probation ======end');
    }
	
	
}