package models

import (
	"backyard/cloud/api/common/raw"
	"github.com/astaxie/beego/logs"
)

/*
 * 继承基础model
 */
type MailToCeoModel struct {
	BaseModel
}

/*
 * 未读count
 */
func (c *MailToCeoModel) UnReadCount(params raw.MailToCeo) int64 {
	//返回自	定义结构体
	templateList := struct {
		UnReadNum int64
	}{}
	sql := `SELECT
				count(0) as un_read_num 
			FROM  
				mail_to_ceo  
			JOIN 
				mail_reply_from_ceo 
			ON 
				mail_to_ceo.id=mail_reply_from_ceo.mail_id   
			WHERE 
				mail_to_ceo.is_read=0  and  mail_to_ceo.staff_id=? ;`
	err := c.GetDI("db").Query(sql, params.StaffInfoId).FetchOne(&templateList)
	if err != nil {
		logs.Error(err.Error())
	}
	return templateList.UnReadNum
}
