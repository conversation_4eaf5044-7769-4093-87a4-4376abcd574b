package controllers

import (
	"backyard/cloud/api/common/raw"
	"backyard/cloud/api/models"
)

type ExampleController struct {
	BaseController
}

/*
 * 定义返回结构体
 */
type AttendanceJson struct {
	List     []raw.BusinessTripImg `json:"list"`
	Total    int64                 `json:"total"`
	Page     int64                 `json:"page"`
	PageSize int64                 `json:"page_size"`
}

/*
 * 初始化返回json
 */
func (c *ExampleController) _Init() {
	c._init()
}

/*
 * 列表
 */
func (c *ExampleController) List() {
	//捕获异常
	defer c.Catch()
	//获取页数，默认1
	page, _ := c.GetInt64("page", 1)
	//获取偏移量，默认30
	page_size, _ := c.GetInt64("page_size", 30)

	//调用model层
	count, list := new(models.BusinessTripImgModel).GetList(raw.BusinessTripImg{
		Offset: (page - 1) * page_size,
		Limit:  page_size,
	})

	//返回
	c.response.Data = AttendanceJson{
		List:     list,
		Total:    count,
		Page:     page,
		PageSize: page_size,
	}
	c.Data["json"] = c.response
	c.ServeJSON()
}
