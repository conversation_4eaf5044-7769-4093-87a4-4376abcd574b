<?php
/**
 * wget http://phar.phpunit.cn/phpunit-7.2.phar
 * chmod +x phpunit-7.2.phar
 * mv phpunit-7.2.phar /usr/local/bin/phpunit
 * cd /mnt/www/tests/ && phpunit
 */

use Phalcon\Di;
use Phalcon\Di\FactoryDefault;
use Phalcon\Loader;
use Phalcon\Mvc\Application;

ini_set("display_errors", 1);
error_reporting(E_ALL);

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');
define('ROOT_PATH', __DIR__);

set_include_path(
    ROOT_PATH . PATH_SEPARATOR . get_include_path()
);

// Required for phalcon/incubator
include __DIR__ . "/../vendor/autoload.php";
include __DIR__. '/../app/common/functions.php';
include APP_PATH . '/config/services.php';

//加载环境变量
$dotenv = new Dotenv\Dotenv(BASE_PATH);
$dotenv->load();

//设定运行环境变量
$runtime = env('runtime','dev');
define('RUNTIME', $runtime);

$di = new FactoryDefault();

include APP_PATH . '/tasks/services.php';
$config = $di->getConfig();
include APP_PATH . '/tasks/loader.php';

// 配置应用
$app = new Application($di);

$modules = require APP_PATH . '/config/modules.php';
/**
 * 国家模块
 */
$app->registerModules($modules);

/**
 * Include Autoloader
 */
include APP_PATH . '/config/loader.php';

//设置时区
$time_zone_GMT = $di->getConfig()->application->timeZoneGMT;
date_default_timezone_set($time_zone_GMT);

$di->setShared('app', function () use ($app) {
    return $app;
});

